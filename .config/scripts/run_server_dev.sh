#!/bin/bash

cd $(dirname $0)/../../iceberg

if [ -z "$DYN_DB_PORT" ]; then
  # If empty, set a default value
  DYN_DB_PORT="8000"
fi
until DYNDB_UP=$(curl -s http://dynamodb:$DYN_DB_PORT); do
  >&2 echo "Dynamodb is unavailable - sleeping"
  sleep 1
done
>&2 echo "Dynamodb is up"

set -e
export PYTHONOPTIMIZE=0

./manage.py setup_dev_environment

# TODO: activate when redis-py>=3.2.0
# Redis transport requires redis-py versions 3.2.0 or later
# celery_queues="-Q default,cart_notifications,channels,channels_highpriority,channels_index_management,emails,mapper_analysis,mapper_parsing,mapper_processing,mapper_processing_high,cleaning,orders,stock_update,webhooks"
# C_FORCE_ROOT=1 celery \
#     --workdir=$PATHTOCODEIZB \
#     --app=iceberg.celery_app \
#     worker $celery_queues \
#     --beat \
#     --loglevel=INFO \
#     --detach

./manage.py runserver api.local.core.izbgtech.com:8080
