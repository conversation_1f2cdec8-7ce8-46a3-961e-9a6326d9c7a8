# Local Development Environment Configuration
# Use this for completely offline development after initial Docker image pull

# ===================
# = DJANGO SETTINGS =
# ===================
DJANGO_SETTINGS_MODULE=iceberg.settings_local_dev
DEBUG=true
ENVIRONMENT=development
CUSTOM_ENV=local

# ===================
# = DATABASE CONFIG =
# ===================
DATABASE_URL=******************************************/izberg_core
REDIS_URL=redis://redis:6379/0

# ===================
# = AWS ALTERNATIVES =
# ===================
# Fake AWS credentials for local DynamoDB
AWS_ACCESS_KEY_ID=fake
AWS_SECRET_ACCESS_KEY=fake
AWS_DEFAULT_REGION=eu-west-1

# Local DynamoDB endpoint
DYNAMODB_ENDPOINT=http://dynamodb:8000

# Local MinIO (S3 alternative) configuration
AWS_S3_ENDPOINT_URL=http://minio:9000
AWS_STORAGE_BUCKET_NAME=izberg-dev-core-api-temp-envs
AWS_GLACIER_STORAGE_BUCKET_NAME=izberg-dev-archives
AWS_S3_UPLOADER_BUCKET_NAME=izberg-uploader-api-ci

# ===================
# = DISABLE AWS SERVICES =
# ===================
USE_SQS_QUEUE=false
CELERY_BROKER_TRANSPORT=memory
DEFAULT_FILE_STORAGE=django.core.files.storage.FileSystemStorage

# ===================
# = LOCAL SERVICES =
# ===================
# Local API endpoints
CORE_HOST=api.local.core.izbgtech.com
CORE_PORT=8080
IDENTITY_HOST=api.local.izberg.me
IDENTITY_PORT=8081
DOMAIN_HOST=api.local.domains.izbgtech.com
DOMAIN_PORT=8082

# Local service URLs
IDENTITY_API_ENDPOINT=http://api.local.izberg.me:8081
DOMAINS_API_ENDPOINT=http://api.local.domains.izbgtech.com:8082

# ===================
# = EXTERNAL SERVICES =
# ===================
# Disable external services
DISABLE_ELASTICSEARCH=true
DISABLE_ALGOLIA=true
DISABLE_EXTERNAL_APIS=true
DISABLE_WEBHOOKS=true

# Local email backend
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend

# ===================
# = SECURITY SETTINGS =
# ===================
SECRET_KEY=local-development-secret-key-not-for-production
ALLOWED_HOSTS=localhost,127.0.0.1,api.local.core.izbgtech.com,api.local.izberg.me,api.local.domains.izbgtech.com

# Disable HTTPS for local development
SECURE_SSL_REDIRECT=false
USE_TLS=false

# ===================
# = MAPPER SETTINGS =
# ===================
MAPPER_DYNAMODB_HOST=http://dynamodb:8000
MAPPER_DYNDB_TABLE_NAME_PREFIX=local-dev-mapper
MAPPER_ITEM_SPLIT_HANDLER=local
MAPPER_ASYNC_ANALYSIS=false
MAPPER_LOCAL_VALUE_ANALYSIS=true

# ===================
# = DEVELOPMENT FLAGS =
# ===================
PYTHONPATH=${PWD}/modules/:${PYTHONPATH}:${PWD}
PYTHONDONTWRITEBYTECODE=1
PYTHONUNBUFFERED=1

# ===================
# = LOGGING =
# ===================
LOG_LEVEL=INFO
JSON_LOGGING=false

# ===================
# = FEATURE FLAGS =
# ===================
ENABLE_EXTERNAL_APIS=false
ENABLE_ELASTICSEARCH=false
ENABLE_ALGOLIA=false
ENABLE_AWS_SERVICES=false
ENABLE_EMAIL_SENDING=false
ENABLE_WEBHOOKS=false

# ===================
# = LOCAL DEVELOPMENT =
# ===================
# Create media directory for local file storage
MEDIA_ROOT=/app/media
MEDIA_URL=/media/

# Local development client credentials
IDENTITY_CLIENT_ID=local-dev-client
IDENTITY_CLIENT_SECRET=local-dev-secret
