env
*.py[co]
.DS_Store
**/.DS_Store
iceberg/site_media/
marketplace.db
**.db
site_media/
django16
.elasticbeanstalk/config
fig.yml
./Dockerfile
pip/
src/
log/
*~
\#*#
.venv
IcebergConf/
awslambda-psycopg2/
.no-pull
dump.rdb
.vscode
exports/
iceberg/tax_migration_result
*.mpo
*.icloud
.python-version
.env*
!.env.local
Pipefile*
.config/copilot/extractor
settings_docker.py
.idea
*.zip
iceberg/core*

coverage.xml

wtemp*.json
# locally installed venvs