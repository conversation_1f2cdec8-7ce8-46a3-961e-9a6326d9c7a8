language: python

# https://docs.travis-ci.com/user/reference/overview/
os: linux
arch: arm64
dist: jammy

python:
  - 3.10

services:
  - docker

cache: pip

env:
  - AWS_PROFILE=izberg-dev # cf. setup_credentials.sh

# dev branch is necessary here in order to trigger pull request for cust env.
# Make sure that deployment in CI is not done with <PERSON> since
# it's done via Github Action
branches:
  only:
    - dev
    - /^env-.+$/

jobs:
  include:
    - stage: Before tests
      name: "Check translations + run various linters"
      # Bug travis-setuptools .... https://github.com/pypa/setuptools/issues/3118
      install: >
        pip install
        setuptools==60.8.2
        `cat requirements/dev-py310.txt | grep "^invoke=="`
        `cat requirements/dev-py310.txt | grep "^ruff=="`
        `cat requirements/dev-py310.txt | grep "^gitpython=="`
        `cat requirements/dev-py310.txt | grep "^django=="`
        `cat requirements/dev-py310.txt | grep "^black=="`
        `cat requirements/dev-py310.txt | grep "^isort=="`
        `cat requirements/dev-py310.txt | grep "^vulture=="`
      script:
        - invoke travis-check-trads
        - vulture
        - ruff iceberg
        - isort iceberg --check
        - black iceberg --check
      if: type = pull_request AND head_branch !~ ^(translations_.*|transifex)$

    - stage: Before tests
      name: "Prebuild image"
      # Bug travis-setuptools .... https://github.com/pypa/setuptools/issues/3118
      before_install:
        - >
          pip install
          setuptools==60.8.2
          `cat requirements/dev-py310.txt | grep "^invoke=="`
          `cat requirements/dev-py310.txt | grep "^awscli=="`
          `cat requirements/dev-py310.txt | grep "^boto3=="`
        - .config/travis/setup_credentials.sh
        - mkdir -vp ~/.docker/cli-plugins/
        - curl --silent -L "https://github.com/docker/buildx/releases/download/v0.10.2/buildx-v0.10.2.linux-amd64" > ~/.docker/cli-plugins/docker-buildx
        - chmod a+x ~/.docker/cli-plugins/docker-buildx
      install:
        - mkdir -p ~/cached/travis/$TRAVIS_PULL_REQUEST_SHA
        - aws s3 sync s3://izberg-core-travis-build-artifacts/travis/$TRAVIS_PULL_REQUEST_SHA ~/cached/travis/$TRAVIS_PULL_REQUEST_SHA --profile=izberg-dev
      script:
        - .config/scripts/pull_or_build.sh dev core/api
        - .config/scripts/pull_or_build.sh dev core/nginx
      if: type = pull_request AND head_branch !~ ^(translations_.*|transifex)$

    - stage: Unit tests
      name: "Apps 1"
      # Bug travis-setuptools .... https://github.com/pypa/setuptools/issues/3118
      before_install:
        - >
          pip install
          setuptools==60.8.2
          `cat requirements/dev-py310.txt | grep "^invoke=="`
          `cat requirements/dev-py310.txt | grep "^awscli=="`
          `cat requirements/dev-py310.txt | grep "^boto3=="`
        - .config/travis/setup_credentials.sh
        - mkdir -vp ~/.docker/cli-plugins/
        - curl --silent -L "https://github.com/docker/buildx/releases/download/v0.10.2/buildx-v0.10.2.linux-amd64" > ~/.docker/cli-plugins/docker-buildx
        - chmod a+x ~/.docker/cli-plugins/docker-buildx
      install:
        - mkdir -p ~/cached/travis/$TRAVIS_PULL_REQUEST_SHA
        - aws s3 sync s3://izberg-core-travis-build-artifacts/travis/$TRAVIS_PULL_REQUEST_SHA ~/cached/travis/$TRAVIS_PULL_REQUEST_SHA --profile=izberg-dev
      before_script:
        - .config/scripts/pull_or_build.sh dev core/api
      script: invoke travis-test-script test-suite-travis-apps-1
      after_success: aws s3 sync ~/cached/travis/$TRAVIS_PULL_REQUEST_SHA s3://izberg-core-travis-build-artifacts/travis/$TRAVIS_PULL_REQUEST_SHA --profile=izberg-dev
      if: type = pull_request AND head_branch !~ ^(translations_.*|transifex)$

    - stage: Unit tests
      name: "Apps 2"
      # Bug travis-setuptools .... https://github.com/pypa/setuptools/issues/3118
      before_install:
        - >
          pip install
          setuptools==60.8.2
          `cat requirements/dev-py310.txt | grep "^invoke=="`
          `cat requirements/dev-py310.txt | grep "^awscli=="`
          `cat requirements/dev-py310.txt | grep "^boto3=="`
        - .config/travis/setup_credentials.sh
        - mkdir -vp ~/.docker/cli-plugins/
        - curl --silent -L "https://github.com/docker/buildx/releases/download/v0.10.2/buildx-v0.10.2.linux-amd64" > ~/.docker/cli-plugins/docker-buildx
        - chmod a+x ~/.docker/cli-plugins/docker-buildx
      install:
        - mkdir -p ~/cached/travis/$TRAVIS_PULL_REQUEST_SHA
        - aws s3 sync s3://izberg-core-travis-build-artifacts/travis/$TRAVIS_PULL_REQUEST_SHA ~/cached/travis/$TRAVIS_PULL_REQUEST_SHA --profile=izberg-dev
      before_script:
        - .config/scripts/pull_or_build.sh dev core/api
      script: invoke travis-test-script test-suite-travis-apps-2
      after_success: aws s3 sync ~/cached/travis/$TRAVIS_PULL_REQUEST_SHA s3://izberg-core-travis-build-artifacts/travis/$TRAVIS_PULL_REQUEST_SHA --profile=izberg-dev
      if: type = pull_request AND head_branch !~ ^(translations_.*|transifex)$

    - stage: Unit tests
      name: "Apps 3"
      # Bug travis-setuptools .... https://github.com/pypa/setuptools/issues/3118
      before_install:
        - >
          pip install
          setuptools==60.8.2
          `cat requirements/dev-py310.txt | grep "^invoke=="`
          `cat requirements/dev-py310.txt | grep "^awscli=="`
          `cat requirements/dev-py310.txt | grep "^boto3=="`
        - .config/travis/setup_credentials.sh
        - mkdir -vp ~/.docker/cli-plugins/
        - curl --silent -L "https://github.com/docker/buildx/releases/download/v0.10.2/buildx-v0.10.2.linux-amd64" > ~/.docker/cli-plugins/docker-buildx
        - chmod a+x ~/.docker/cli-plugins/docker-buildx
      install:
        - mkdir -p ~/cached/travis/$TRAVIS_PULL_REQUEST_SHA
        - aws s3 sync s3://izberg-core-travis-build-artifacts/travis/$TRAVIS_PULL_REQUEST_SHA ~/cached/travis/$TRAVIS_PULL_REQUEST_SHA --profile=izberg-dev
      before_script:
        - .config/scripts/pull_or_build.sh dev core/api
      script: invoke travis-test-script test-suite-travis-apps-3
      after_success: aws s3 sync ~/cached/travis/$TRAVIS_PULL_REQUEST_SHA s3://izberg-core-travis-build-artifacts/travis/$TRAVIS_PULL_REQUEST_SHA --profile=izberg-dev
      if: type = pull_request AND head_branch !~ ^(translations_.*|transifex)$

    - stage: Unit tests
      name: "Apps 4"
      # Bug travis-setuptools .... https://github.com/pypa/setuptools/issues/3118
      before_install:
        - >
          pip install
          setuptools==60.8.2
          `cat requirements/dev-py310.txt | grep "^invoke=="`
          `cat requirements/dev-py310.txt | grep "^awscli=="`
          `cat requirements/dev-py310.txt | grep "^boto3=="`
        - .config/travis/setup_credentials.sh
        - mkdir -vp ~/.docker/cli-plugins/
        - curl --silent -L "https://github.com/docker/buildx/releases/download/v0.10.2/buildx-v0.10.2.linux-amd64" > ~/.docker/cli-plugins/docker-buildx
        - chmod a+x ~/.docker/cli-plugins/docker-buildx
      install:
        - mkdir -p ~/cached/travis/$TRAVIS_PULL_REQUEST_SHA
        - aws s3 sync s3://izberg-core-travis-build-artifacts/travis/$TRAVIS_PULL_REQUEST_SHA ~/cached/travis/$TRAVIS_PULL_REQUEST_SHA --profile=izberg-dev
      before_script:
        - .config/scripts/pull_or_build.sh dev core/api
      script: invoke travis-test-script test-suite-travis-apps-4
      after_success: aws s3 sync ~/cached/travis/$TRAVIS_PULL_REQUEST_SHA s3://izberg-core-travis-build-artifacts/travis/$TRAVIS_PULL_REQUEST_SHA --profile=izberg-dev
      if: type = pull_request AND head_branch !~ ^(translations_.*|transifex)$

    - stage: Unit tests
      name: "Apps 5"
      # Bug travis-setuptools .... https://github.com/pypa/setuptools/issues/3118
      before_install:
        - >
          pip install
          setuptools==60.8.2
          `cat requirements/dev-py310.txt | grep "^invoke=="`
          `cat requirements/dev-py310.txt | grep "^awscli=="`
          `cat requirements/dev-py310.txt | grep "^boto3=="`
        - .config/travis/setup_credentials.sh
        - mkdir -vp ~/.docker/cli-plugins/
        - curl --silent -L "https://github.com/docker/buildx/releases/download/v0.10.2/buildx-v0.10.2.linux-amd64" > ~/.docker/cli-plugins/docker-buildx
        - chmod a+x ~/.docker/cli-plugins/docker-buildx
      install:
        - mkdir -p ~/cached/travis/$TRAVIS_PULL_REQUEST_SHA
        - aws s3 sync s3://izberg-core-travis-build-artifacts/travis/$TRAVIS_PULL_REQUEST_SHA ~/cached/travis/$TRAVIS_PULL_REQUEST_SHA --profile=izberg-dev
      before_script:
        - .config/scripts/pull_or_build.sh dev core/api
      script: invoke travis-test-script test-suite-travis-apps-5
      after_success: aws s3 sync ~/cached/travis/$TRAVIS_PULL_REQUEST_SHA s3://izberg-core-travis-build-artifacts/travis/$TRAVIS_PULL_REQUEST_SHA --profile=izberg-dev
      if: type = pull_request AND head_branch !~ ^(translations_.*|transifex)$

    - stage: Unit tests
      name: "Module 1 (Shipping)"
      # Bug travis-setuptools .... https://github.com/pypa/setuptools/issues/3118
      before_install:
        - >
          pip install
          setuptools==60.8.2
          `cat requirements/dev-py310.txt | grep "^invoke=="`
          `cat requirements/dev-py310.txt | grep "^awscli=="`
          `cat requirements/dev-py310.txt | grep "^boto3=="`
        - .config/travis/setup_credentials.sh
        - mkdir -vp ~/.docker/cli-plugins/
        - curl --silent -L "https://github.com/docker/buildx/releases/download/v0.10.2/buildx-v0.10.2.linux-amd64" > ~/.docker/cli-plugins/docker-buildx
        - chmod a+x ~/.docker/cli-plugins/docker-buildx
      install:
        - mkdir -p ~/cached/travis/$TRAVIS_PULL_REQUEST_SHA
        - aws s3 sync s3://izberg-core-travis-build-artifacts/travis/$TRAVIS_PULL_REQUEST_SHA ~/cached/travis/$TRAVIS_PULL_REQUEST_SHA --profile=izberg-dev
      before_script:
        - .config/scripts/pull_or_build.sh dev core/api
      script: invoke travis-test-script test-suite-travis-modules-1
      after_success: aws s3 sync ~/cached/travis/$TRAVIS_PULL_REQUEST_SHA s3://izberg-core-travis-build-artifacts/travis/$TRAVIS_PULL_REQUEST_SHA --profile=izberg-dev
      if: type = pull_request AND head_branch !~ ^(translations_.*|transifex)$

    - stage: Unit tests
      name: "Module 2 (mapper)"
      # Bug travis-setuptools .... https://github.com/pypa/setuptools/issues/3118
      before_install:
        - >
          pip install
          setuptools==60.8.2
          `cat requirements/dev-py310.txt | grep "^invoke=="`
          `cat requirements/dev-py310.txt | grep "^awscli=="`
          `cat requirements/dev-py310.txt | grep "^boto3=="`
        - .config/travis/setup_credentials.sh
        - mkdir -vp ~/.docker/cli-plugins/
        - curl --silent -L "https://github.com/docker/buildx/releases/download/v0.10.2/buildx-v0.10.2.linux-amd64" > ~/.docker/cli-plugins/docker-buildx
        - chmod a+x ~/.docker/cli-plugins/docker-buildx
      install:
        - mkdir -p ~/cached/travis/$TRAVIS_PULL_REQUEST_SHA
        - aws s3 sync s3://izberg-core-travis-build-artifacts/travis/$TRAVIS_PULL_REQUEST_SHA ~/cached/travis/$TRAVIS_PULL_REQUEST_SHA --profile=izberg-dev
      before_script:
        - .config/scripts/pull_or_build.sh dev core/api
      script: invoke travis-test-script test-suite-travis-modules-2
      after_success: aws s3 sync ~/cached/travis/$TRAVIS_PULL_REQUEST_SHA s3://izberg-core-travis-build-artifacts/travis/$TRAVIS_PULL_REQUEST_SHA --profile=izberg-dev
      if: type = pull_request AND head_branch !~ ^(translations_.*|transifex)$

    - stage: Unit tests
      name: "Module 3 (other)"
      # Bug travis-setuptools .... https://github.com/pypa/setuptools/issues/3118
      before_install:
        - >
          pip install
          setuptools==60.8.2
          `cat requirements/dev-py310.txt | grep "^invoke=="`
          `cat requirements/dev-py310.txt | grep "^awscli=="`
          `cat requirements/dev-py310.txt | grep "^boto3=="`
        - .config/travis/setup_credentials.sh
        - mkdir -vp ~/.docker/cli-plugins/
        - curl --silent -L "https://github.com/docker/buildx/releases/download/v0.10.2/buildx-v0.10.2.linux-amd64" > ~/.docker/cli-plugins/docker-buildx
        - chmod a+x ~/.docker/cli-plugins/docker-buildx
      install:
        - mkdir -p ~/cached/travis/$TRAVIS_PULL_REQUEST_SHA
        - aws s3 sync s3://izberg-core-travis-build-artifacts/travis/$TRAVIS_PULL_REQUEST_SHA ~/cached/travis/$TRAVIS_PULL_REQUEST_SHA --profile=izberg-dev
      before_script:
        - .config/scripts/pull_or_build.sh dev core/api
      script: invoke travis-test-script test-suite-travis-modules-3
      after_success: aws s3 sync ~/cached/travis/$TRAVIS_PULL_REQUEST_SHA s3://izberg-core-travis-build-artifacts/travis/$TRAVIS_PULL_REQUEST_SHA --profile=izberg-dev
      if: type = pull_request AND head_branch !~ ^(translations_.*|transifex)$

    - stage: Coverage
      name: "Upload coverage"
      # Bug travis-setuptools .... https://github.com/pypa/setuptools/issues/3118
      before_install:
        - >
          pip install
          setuptools==60.8.2
          `cat requirements/dev-py310.txt | grep "^invoke=="`
          `cat requirements/dev-py310.txt | grep "^awscli=="`
          `cat requirements/dev-py310.txt | grep "^boto3=="`
        - .config/travis/setup_credentials.sh
        - mkdir -vp ~/.docker/cli-plugins/
        - curl --silent -L "https://github.com/docker/buildx/releases/download/v0.10.2/buildx-v0.10.2.linux-amd64" > ~/.docker/cli-plugins/docker-buildx
        - chmod a+x ~/.docker/cli-plugins/docker-buildx
      install:
        - mkdir -p ~/cached/travis/$TRAVIS_PULL_REQUEST_SHA
        - aws s3 sync s3://izberg-core-travis-build-artifacts/travis/$TRAVIS_PULL_REQUEST_SHA ~/cached/travis/$TRAVIS_PULL_REQUEST_SHA --profile=izberg-dev
      before_script: pip install "coverage<5"
      script:
        - .config/scripts/install_codecov.sh
        - cd ~/cached/travis/$TRAVIS_PULL_REQUEST_SHA/coverage
        - coverage combine
        - cp .coverage $TRAVIS_BUILD_DIR/.coverage
        - cd $TRAVIS_BUILD_DIR
        - sed -i.bak "s@\"/iceberg/@\"`pwd`/iceberg/@g" .coverage
        - coverage xml -i -o coverage.xml && cp coverage.xml ~/cached/travis/$TRAVIS_PULL_REQUEST_SHA/coverage/
        - ls -la
        - ./codecov -t $CODECOV_TOKEN --branch $TRAVIS_PULL_REQUEST_BRANCH -f ./coverage.xml
      after_success: aws s3 sync ~/cached/travis/$TRAVIS_PULL_REQUEST_SHA s3://izberg-core-travis-build-artifacts/travis/$TRAVIS_PULL_REQUEST_SHA --profile=izberg-dev
      if: type = pull_request AND head_branch !~ ^(translations_.*|transifex)$

    - stage: Deploy branch
      before_install:
        - >
          pip install
          `cat requirements/dev-py310.txt | grep "^invoke=="`
          `cat requirements/dev-py310.txt | grep "^awscli=="`
          `cat requirements/dev-py310.txt | grep "^boto3=="`
        - .config/travis/setup_credentials.sh
        - mkdir -vp ~/.docker/cli-plugins/
        - curl --silent -L "https://github.com/docker/buildx/releases/download/v0.10.2/buildx-v0.10.2.linux-amd64" > ~/.docker/cli-plugins/docker-buildx
        - chmod a+x ~/.docker/cli-plugins/docker-buildx
      install:
        # Download the latest copilot linux binary
        - curl -Lo copilot https://ecs-cli-v2-release.s3.amazonaws.com/copilot-linux && chmod +x copilot && sudo mv copilot /usr/local/bin/copilot && copilot -v
        - mkdir -p ~/cached/travis/$TRAVIS_PULL_REQUEST_SHA
        - aws s3 sync s3://izberg-core-travis-build-artifacts/travis/$TRAVIS_PULL_REQUEST_SHA ~/cached/travis/$TRAVIS_PULL_REQUEST_SHA --profile=izberg-dev
      before_script:
        - .config/scripts/pull_or_build.sh dev core/api
        - .config/scripts/pull_or_build.sh dev core/nginx
      script: true
      deploy:
        - provider: script
          script: .config/scripts/deploy_ecs_custom_env.sh
          on:
            all_branches: true
          edge:
            branch: v2.0.3-beta.4
      # Build is triggered for dev branch on AWS pipeline
      if: type = push AND branch != dev

    - stage: Translations noop
      # Needed to get to mark Pull request tests as success
      script: true
      if: type = pull_request AND head_branch =~ ^(translations_.*|transifex)$

notifications:
  slack: izberg:bDD7Gylz6C3mjNQQInSYAE0m
