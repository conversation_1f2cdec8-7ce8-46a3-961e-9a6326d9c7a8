### **Accès infra à prévoir**

* Accès Github Izberg  
* Accès AWS (dev)  
* User avec les bons groupes sur https://pypi.izbgtech.com/

### **Requirements**

#### WSL2 enabled in your Windows system

* [https://learn.microsoft.com/en-us/windows/wsl/install](https://learn.microsoft.com/en-us/windows/wsl/install)  
  * command windows avec droits admin / powershell  
    * `wsl --install`  
      * par défaut ca install le ubuntu qu’il faut  
      * il faut à minima: Ubuntu 20.04 LTS   
    * check version:   
      * `wsl -l -v`   
      * si \!= 2  
        * `wsl --set-version Ubuntu 2`

#### Docker Desktop installed in your Windows system

* [https://www.docker.com/products/docker-desktop/](https://www.docker.com/products/docker-desktop/)  
* se créer un compte  
  * Si vous avez un message “*failed to complete sign-up*”, c’est que le *username* est déjà utilisé, il faut recommencer avec un autre  
* télécharger et installer Docker Desktop  
* Dans Docker-Desktop il faut activer l'intégration WSL2 pour la distribution utilisée :   
  * Settings \> Resources \> WSL Integration

#### Récupération des sources depuis Github :

* Création d’une clé SSH pour Github :   
  * `ssh-keygen -t ed25519 -C "<EMAIL>"`  
    * `cat ~/.ssh/id_ed25519.pub`  
  * Ajouter la clé SSH affichée sur son compte Github puis :   
    * `git clone --recursive **************:izberg-marketplace/izberg-core.git`  
    * `cd izberg-core/`

#### MAC requirements

* Installer Homebrew : [https://brew.sh/](https://brew.sh/)  
* Installer les packages (pyenv, direnv, postgresql, openssl@1.1, swig)  
* Configurer les variables d’environnement

### **Installation de l’environnement**

#### Une fois sur l’env WSL2 installer les packages nécessaires : 

* sudo apt-get update   
* sudo apt-get upgrade

#### Installer pyenv \+ python 3.10 : 

* curl https://pyenv.run | bash  
* echo 'export PYENV\_ROOT="$HOME/.pyenv"' \>\> \~/.bashrc  
* echo 'command \-v pyenv \>/dev/null || export PATH="$PYENV\_ROOT/bin:$PATH"' \>\> \~/.bashrc  
* echo 'eval "$(pyenv init \-)"' \>\> \~/.bashrc  
* echo 'export PYENV\_ROOT="$HOME/.pyenv"' \>\> \~/.profile  
* echo 'command \-v pyenv \>/dev/null || export PATH="$PYENV\_ROOT/bin:$PATH"' \>\> \~/.profile  
* echo 'eval "$(pyenv init \-)"' \>\> \~/.profile  
* exec "$SHELL"  
* source \~/.bashrc  
* sudo apt update; sudo apt install build-essential libssl-dev zlib1g-dev libbz2-dev libreadline-dev libsqlite3-dev curl libncursesw5-dev xz-utils tk-dev libxml2-dev libxmlsec1-dev libffi-dev liblzma-dev libcurl4-openssl-dev swig  
* pyenv install 3.10  
* pyenv global 3.10  
* pip install \--upgrade pip  
* pip install virtualenv

#### Création/Activation du venv :

* Dans **/izberg-core** :  
  * virtualenv .venv  
  * source .venv/bin/activate

#### Créer un fichier si pas existant \~/.pip/pip.conf avec  infos de connexion au serveur Pypi izberg :

 \[search\]  
	index \= https://**\<username\>:\<password\>**@pypi.izbgtech.com/pypi/  
 \[global\]  
	index-url \= https://**\<username\>:\<password\>**@pypi.izbgtech.com/simple/

#### Variable d'environnement  :

* Faire une copie de **.env.template** \-\> .env :  
  * PYTHONPATH=${PWD}/modules/:${PYTHONPATH}:${PWD}  
* Installation des dépendance :  
* pip install \-r requirements/dev-py310.txt

#### Préparation des dockers / hosts

* Dans **/etc/hosts** penser à ajouter :  
  \#izberg-core-api \- port:8080  
  127.0.0.1	api.local.core.izbgtech.com  
  \#izberg-me-api \- port:8081  
  127.0.0.1	api.local.izberg.me  
  \#izberg-domain-api  \- port:8082  
  127.0.0.1	api.local.domains.izbgtech.com  
  \#izberg-analytics  \- port:8083  
  127.0.0.1	api.local.analytics.izbgtech.com  
  \#izberg-exporter  \- port:8084  
  127.0.0.1	api.local.exporter.izbgtech.com  
  \#izberg-channel-ms  \- port:8085  
  127.0.0.1	api.local.channels.izbgtech.com  
    
  \# dynamodb  \- port: 8000 http://api.local.core.izbgtech.com:8080/\_a\_d\_m\_i\_n\_/  
    
  127.0.0.1	dynamodb  
  \# postgres  \- port: 5432  
  127.0.0.1	postgres  
  \# redis  \- port: 6379  
  127.0.0.1	redis


### **Installation de l’environnement (Mac)**

## *Ces instructions ont été testées sous macOS (zsh). Si vous utilisez bash ou un autre shell, adaptez les fichiers de config (\~/.zshrc) en conséquence.*

#### Installer les packages nécessaires

* brew update  
* brew upgrade

#### Installer pyenv et Python 3.10

* Installer pyenv → `brew install pyenv`  
* Configurer le shell (`nano ~/.zshrc`) →  
  * export PYENV-ROOT=”$HOME/.pyenv”  
  * export PATH=”$PYENV\_ROOT/bin:$PATH”  
  * eval “$(pyenv init –path)”  
  * eval “$(pyenv init \-)  
* Recharger le shell → `source ~/.zshrc`  
* Installer les dépendances nécessaires →  
  * xcode-select –install  
  * brew install postgresql openssl@1.1 swig  
* Installer Python 3.10 →   
  * pyenv install 3.10  
  * pyenv global 3.10 (ou spécifique au projet : pyenv local 3.10)  
* Mettre à jour pip et installer virtualenv →   
  * pip install –upgrade pip  
  * pip install virtualenv

#### Créer et lancer l’environnement virtuel

* Dans `/izberg-core` :   
  * virtualenv .venv  
  * source .venv/bin/activate

#### Variables d’environnement

* Copier .env.template → `cp .env.template .env`  
* Modifier .env → `PYTHONPATH=${PWD}/modules/:{PYTHONPATH}:${PWD}`

⚠️ **macOS ne charge pas les fichiers .env automatiquement**

* Installer direnv → `brew install direnv`  
* Activer le hook dans le shell → `eval “$(direnv hook zsh)”`  
* Recharger le shell → `source ~/.zshrc`  
* Créer un fichier .envrc dans le projet → `echo “dotenv” > .envrc”`  
* Autoriser ce dossier → `direnv allow`  
* Configurer les variables d’environnement →   
  * export LDFLAGS=”-L/opt/homebrew/opt/openssl@1.1/lib”  
  * export CPPFLAGS=”-I/opt/homebrew/opt/openssl@1.1/include”  
  * export CFLAGS=”-I/opt/homebrew/opt/openssl@1.1/include”  
  * export PKG\_CONFIG\_PATH=”/opt/homebrew/opt/openssl@1.1/lib/pkgconfig”  
  * export ARCHFLAGS=”-arch arm64” (pour puces Mx)  
  * export SWIG\_FEATURES=”-I/opt/homebrew/opt/openssl@1.1/include”

#### Installation des dépendances

pip install \-r requirements/dev-py310.txt

### **AWS Installation et récupération des credentials**

#### Création des Accès key AWS :

* Rendez vous sur la console AWS et accedez a [Informations d'identification de sécurité](https://eu-west-1.console.aws.amazon.com/iam/home?region=eu-west-1#security_credential)

	![][image1]  
	

* Vers le bas de la page vous avez **“Clés d’accès “** \-\> vous pouvez “**Créer une clé accè**s”

	![][image2]

* Choisissez seulement “**Interface de ligne de commande”**  
  **![][image3]**  
* Vous allez avoir deux keys  
  * aws\_acces\_key\_id  
  * aws\_secret\_acces\_key

#### Installation de AWS

Doc  :  [https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html](https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html)

Version simplifiée WSL/Linux :

* cd \~  
* curl "https://awscli.amazonaws.com/awscli-exe-linux-x86\_64.zip" \-o "awscliv2.zip"  
* unzip awscliv2.zip  
* sudo ./aws/install


#### Configuration local AWS 

* Après l’installation de AWS vous allez pouvoir faire :  
  * aws configure  
      
* Remplir avec les acces keys :  
  * **AWS Acces Key ID :** \<votre\_aws\_acces\_key\_id\>  
  * **AWS Secret Access Key :** \<votre\_aws\_secret\_acces\_key\>  
  * **Default region name :** eu-west-1  
  * **Default output format :** json  
      
* Vous pouvez aussi faire la config et les crédential dans les fichiers situés dans **\~/.aws** si vous avez plusieurs key/config aws  
  * ex config :  
    \[profile izberg-dev\]  
    region \= eu-west-1  
    output \= json  
      
    \[profile izberg-prod\]  
    region \= eu-west-1  
    output \= json  
* ex credential :

  \[izberg-dev\]

  aws\_access\_key\_id \= \*\*\*\*\*\*\*\*\*\*\*\*\*\*\*AJ5

  aws\_secret\_access\_key \= \*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*zdD


  \[izberg-prod\]

  aws\_access\_key\_id \= \*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*SDM

  aws\_secret\_access\_key \= \*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*tvx

* Connexion AWS  
  * aws ecr get-login-password \--region eu-west-1 | docker login \--username AWS \--password-stdin 171946876898.dkr.ecr.eu-west-1.amazonaws.com

### **Lancement du projet-dev Izberg**

#### Pull et lancement des dockers

* docker compose up \-d

#### Lancement des migrations \+ populate

* Dans **/izberg\_core/iceberg** :  
  * python manage.py migrate  
  * invoke setup \--applications 1 \--merchants 5 \--orders 2

#### Lancement du server en local (sans docker)

* docker compose stop api.local.core.izbgtech.com  
* Dans **/izberg\_core/iceberg:**  
  * ./manage.py runserver api.local.core.izbgtech.com:8080

    

[image1]: <data:image/png;base64,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>

[image2]: <data:image/png;base64,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>

[image3]: <data:image/png;base64,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>