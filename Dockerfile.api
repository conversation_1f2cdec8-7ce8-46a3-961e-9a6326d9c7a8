FROM public.ecr.aws/docker/library/python:3.10

LABEL maintainer="<EMAIL>" \
      vendor="izberg-marketplace"

# ENVIRONMENT VARIABLES --------------------------------------------------------

ENV PATHTOCODEIZB="/iceberg" \
    PATHTOREQS="/requirements" \
    SSL_CERT_DIR=/etc/ssl/certs \
    SSL_CERT_FILE=/etc/ssl/certs/ca-certificates.crt \
    REQUESTS_CA_BUNDLE=/etc/ssl/certs/ca-certificates.crt \
    HTTPLIB2_CA_CERTS=/etc/ssl/certs/ca-certificates.crt \
    DEBIAN_CA_CERTS_PATH=/etc/ssl/certs/ca-certificates.crt

RUN apt-get update

# swig pour m2crypto
RUN apt-get install -y swig ca-certificates tmux postgresql-client && \
    update-ca-certificates --fresh

ENV VIRTUAL_ENV=/opt/venv
RUN python3 -m venv $VIRTUAL_ENV
ENV PATH="$VIRTUAL_ENV/bin:$PATH"

# CONFIG HANDLING --------------------------------------------------------------
# Default directory
WORKDIR ${PATHTOCODEIZB}

# REQUIREMENTS -----------------------------------------------------------------
# Copy requirements only (improves caching)
COPY ./requirements/ ${PATHTOREQS}/
# Evaluate Pypi URL build arg
ARG REQUIREMENTS="prod-py310.txt"
RUN --mount=type=secret,id=PYPI_DOWNLOAD_URL \
    --mount=type=cache,target=/root/.cache \
    pip --disable-pip-version-check install -i $(cat /run/secrets/PYPI_DOWNLOAD_URL) -r ${PATHTOREQS}/${REQUIREMENTS}

# RUN rm /usr/local/lib/python3.10/site-packages/certifi/cacert.pem && \
#     ln -s /etc/ssl/certs/ca-certificates.crt /usr/local/lib/python3.10/site-packages/certifi/cacert.pem

# Copy config only (improves caching)
COPY .config/uwsgi/uwsgi_ecs.ini /etc/uwsgi/uwsgi_ecs.ini
ENV PATH="${PATH}:/.config/scripts"
COPY .config/scripts/*.sh /.config/scripts/

# Set host s3 for boto
RUN echo $'[s3]\n\
host=s3-eu-west-1.amazonaws.com' | sed 's/\$//g' > /root/.boto

# CORE CODE --------------------------------------------------------------------
# Copy code files at the last moment
COPY ./iceberg/ ${PATHTOCODEIZB}/

ENV CELERY_LOGLEVEL="WARNING" \
    AWS_DEFAULT_REGION="eu-west-1" \
    LANG="C.UTF-8" \
    UWSGI_PROCESSES="8" \
    UWSGI_MAX_REQUESTS="5000" \
    UWSGI_RELOAD_ON_RSS="1024" \
    UWSGI_EVIL_RELOAD_ON_RSS="2048" \
    UWSGI_HARAKIRI="3600" \
    ENVIRONMENT="DEVELOPMENT" \
    RUNNING_ON="docker" \
    STANDALONE="0" \
    RELEASE_CANDIDATE="0" \
    PYTHONOPTIMIZE="2"

# Evaluate RELEASE_NAME build arg and make it persistent
ARG RELEASE_NAME
ENV RELEASE_NAME="${RELEASE_NAME}"

EXPOSE 8000
CMD ["launch_ecs_api.sh"]
