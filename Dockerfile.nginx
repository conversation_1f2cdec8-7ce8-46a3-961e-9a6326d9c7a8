ARG AWS_ACCOUNT_ID=************
ARG SOURCE_VERSION=latest
ARG BASE_IMAGE=core/api
FROM ${AWS_ACCOUNT_ID}.dkr.ecr.eu-west-1.amazonaws.com/${BASE_IMAGE}:${SOURCE_VERSION} as builder

RUN LOG_INSTANCE_IDENTITY=0 ./manage.py collectstatic --noinput

FROM public.ecr.aws/nginx/nginx:latest

LABEL maintainer="<EMAIL>" \
      vendor="izberg-marketplace"

COPY .config/nginx/api.conf.template /etc/nginx/templates/default.conf.template
COPY --from=builder /iceberg/site_media/static/ /usr/share/nginx/html/static

ENV UWSGI_HOST=localhost
ENV UWSGI_PORT=8080
ENV NGINX_PORT=80
ENV NGINX_SERVER_NAME="*.izbgtech.com"
ENV ENABLE_DEBUG_UPSTREAM=0

EXPOSE ${NGINX_PORT}
