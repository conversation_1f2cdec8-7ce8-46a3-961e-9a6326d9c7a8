# System setup

## MacOS

Install Homebrew package manager

```console
mkdir homebrew && curl -L https://github.com/Homebrew/brew/tarball/master | tar xz --strip 1 -C homebrew
```

You must install this libs/services too :

```console
$ brew install libjpeg
$ brew install libmagic
$ brew install gettext
$ brew install swig
$ brew install openssl
$ brew install pyenv
$ brew install pyenv-virtualenvwrapper
```

## Other Unix Systems

Install this Dependencies :

```console
$ sudo apt-get install build-essential git python python-dev python-virtualenv libxslt1-dev libxml2-dev swig libjpeg-dev libpng-dev postgresql-dev-all libssl-dev
```

# Virtualenv

## Setup virtualenvwrapper

If not already done for another project

```console
$ pip install virtualenvwrapper
```

Add in you ~/.bash_profile or ~/.zshrc :

```console
# pyenv + virtualenvwrapper
if command -v pyenv 1>/dev/null 2>&1; then
    eval "$(pyenv init -)"
fi
export WORKON_HOME=~/.virtualenvs
export PYENV_VIRTUALENVWRAPPER_PREFER_PYVENV="true"
pyenv virtualenvwrapper
```

## Create Env

```console
$ mkvirtualenv izberg-core
```

## Activate env

```
$ workon izberg-core
```

# Setup

## Run Docker images

Login to AWS ECR to access to docker images.

This step requires to have :

- docker & docker-compose installed (https://docs.docker.com/get-docker/)
- aws-cli installed (https://aws.amazon.com/cli/)
- setup your AWS profile & credentials (https://docs.aws.amazon.com/cli/latest/userguide/cli-configure-quickstart.html)

```console
aws ecr get-login-password --region eu-west-1 | docker login --username AWS --password-stdin 171946876898.dkr.ecr.eu-west-1.amazonaws.com
```

Then run all images :

```console
docker compose up -d
```

At this point, everething you need to play with izberg-core is set up (services, databases, tables and initial data)

You can stop individual container, for example if you want to run the development server alongsside other containers :

```console
docker compose stop api.local.core.izbgtech.com
./manage.py runserver api.local.core.izbgtech.com:8080
```

The data persists until containers are destroyed, so subsequent invokation of "docker compose up" does not remove or recreate data. If you want to destroy all containers, run :

```console
docker compose down
```

## Configuring my machine to use IZBERG pypi server

Add a config file in ~/.pip/pip.conf (the ~.pip folder may not exists. If not, just create it (smile))

```
    [search]
    index = https://<user>:<password>@pypi.izbgtech.com/pypi/

    [global]
    index-url = https://<user>:<password>@pypi.izbgtech.com/simple/
```

Try it!

```
    pip install "izberg-authenticator>=1.2,<2"
```

Add Pypi url to your terminal

```
    export PYPI_DOWNLOAD_URL=`awk -F " = " '/index-url/ {print $2}' ~/.pip/pip.conf`
    export PYPI_URL=`awk -F " = " '/index-url/ {print $2}' ~/.pip/pip.conf`
```

## Setup Project

```console
$ git clone --recursive **************:izberg-marketplace/izberg-core.git
$ cd izberg-core
$ pip install -r requirements/dev-py310.txt
```

## Improve git blame

```console
$ git config --global blame.ignoreRevsFile .git-blame-ignore-revs
```

## Local URLs

Create those URLs in your `/etc/hosts` file:

```
#izberg-core-api - port:8080
127.0.0.1       api.local.core.izbgtech.com
#izberg-me-api - port:8081
127.0.0.1       api.local.izberg.me
#izberg-domain-api  - port:8082
127.0.0.1       api.local.domains.izbgtech.com
#izberg-analytics  - port:8083
127.0.0.1       api.local.analytics.izbgtech.com
#izberg-exporter  - port:8084
127.0.0.1       api.local.exporter.izbgtech.com
#izberg-channel-ms  - port:8085
127.0.0.1       api.local.channels.izbgtech.com

# dynamodb  - port: 8000
127.0.0.1       dynamodb
# postgres  - port: 5432
127.0.0.1       postgres
# redis  - port: 6379
127.0.0.1       redis
```

## Change directory

```console
cd iceberg
```

## Run Server

```console
$ ./manage.py runserver api.local.core.izbgtech.com:8080
```

## Run Tests

```console
$ ./manage.py test -kt .
```

## Build docker image

```console
../.config/scripts/build_api.sh dev
```

# Go

Open http://api.local.core.izbgtech.com:8080/_a_d_m_i_n_/ in your browser and connect with the superuser account : `<EMAIL>` (password `admin`)

Enjoy!!!

# Setup with python3.8 for Mac M1

Install brew in x86 :

```console
arch -x86_64 /bin/bash -c “$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)”
```

Add the following line in your zshrc or bash_profile

```
alias ibrew='arch -x86_64 /usr/local/homebrew/bin/brew'
```

Install pyenv & python 3.8.5 in x86

```console
ibrew install pyenv
arch -x86_64 pyenv install 3.8.5
ibrew install libmagic
```

Activate python 3.8.5 by default for my user

```console
pyenv global 3.8.5
```

Create and activate your virtualenv with python 3.8

Avoid error in troposphere setup command “use_2to3 is invalid”

```console
pip install “setuptools<60.8.2”
```

Install reqs

```console
pip install -i $PYPI_DOWNLOAD_URL -r requirements/dev-py310.txt
```

Replace incompatible requirements

```console
pip uninstall python-magic && pip install python-magic-bin<=0.4.14
pip uninstall -y psycopg2 && pip install psycopg2-binary<=2.8.6
```

Finish your setup following the main instructions above but skipping the steps you already executed.

# VSCode example configuration

Install the python extension

Open `izberg-core` dir

`.vscode/launch.json`

```
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Run server",
            "type": "python",
            "request": "launch",
            "cwd": "${workspaceFolder}/iceberg",
            "program": "manage.py",
            "args": [
                "runserver",
                "api.local.core.izbgtech.com:8080"
            ],
            "django": true,
        },
        {
            "name": "Reset Db",
            "type": "python",
            "request": "launch",
            "cwd": "${workspaceFolder}/iceberg",
            "program": "manage.py",
            "args": [
                "reset_db"
            ],
            "django": true
        },
        {
            "name": "Run all tests",
            "type": "python",
            "request": "launch",
            "cwd": "${workspaceFolder}/iceberg",
            "program": "manage.py",
            "args": [
                "test",
                "--noinput",
                "--keepdb",
                "-t",
                ".",
            ],
            "django": true
        },
        {
            "name": "Run apps tests",
            "type": "python",
            "request": "launch",
            "cwd": "${workspaceFolder}/iceberg",
            "program": "manage.py",
            "args": [
                "test",
                "--noinput",
                "--keepdb",
                "-t",
                ".",
                "apps"
            ],
            "django": true
        },
        {
            "name": "Run test file",
            "type": "python",
            "request": "launch",
            "cwd": "${workspaceFolder}/iceberg",
            "program": "manage.py",
            "args": [
                "test",
                "--noinput",
                "--keepdb",
                "-t",
                ".",
                "-p",
                "${fileBasename}"
            ],
            "django": true
        },
        {
            "name": "Run test symbol",
            "type": "python",
            "request": "launch",
            "cwd": "${workspaceFolder}/iceberg",
            "program": "manage.py",
            "args": [
                "test",
                "--noinput",
                "--keepdb",
                "-t",
                ".",
                "-p",
                "${fileBasename}",
                "-k",
                "${selectedText}"
            ],
            "django": true
        },
        {
            "name": "Generate Trads",
            "type": "python",
            "request": "launch",
            "cwd": "${workspaceFolder}",
            "module": "invoke",
            "args": [
                "generate-trads",
            ],
        },
        {
            "name": "Python: Attach",
            "type": "python",
            "request": "attach",
            "connect": {
              "host": "localhost",
              "port": 5678
            },
            "pathMappings": [
                {
                    "localRoot": "${workspaceFolder}/iceberg",
                    "remoteRoot": "."
                }
            ]
        }
    ]
}
```

Restart VSCode to reload the configuration

Don't forget to activate your virtual environment in VSCode

(reference: https://docs.djangoproject.com/en/4.0/topics/testing/advanced/)

# Technical Documentation

Each module must contain a README.md exposing a basic description and at least 2 sections:

- Key Concepts
- Future Work

It may contain more, such as:

- Technical Diagrams
- Glossary

Each Django model class must have a short description as docstring



# Styleguide

This [Django Styleguide](https://github.com/HackSoftware/Django-Styleguide) is quite in phase with what we try to do in this project.

Here are the main DO's and DON'Ts and how far we are from them:

## In Django, business logic should live in:

>*Services - functions, that mostly take care of writing things to the database.*

- that's what we try to do with our `ActionsManager` logic, but we still have a lot of business logic directly in the models

>*Selectors - functions, that mostly take care of fetching things from the database.*
- Usually done inside the `ActionsManager`
- If the `ActionsManager` is dedicated to data fetching/formatting, we tend to call them `ViewersManager`

>*Model properties (with some exceptions).*
- We do it sometimes (ex.: `Payment().to_collect_amount`), but should be done only for simple and autonomous business logic

> *Model clean method for additional validations (with some exceptions).*
- We do it for almost all the models


## In Django, business logic **should not** live in:

>*APIs and Views*
- though we avoid doing it in new resources/endpoints, there are still a lot of business logic in old API resources (ex: `/v1/cart/mine/` :D)
>*Serializers and Forms*
- not really used
>*Form tags*
- not really used
>*Model save method*
- though we avoid doing it in new models and try to refactor old ones, it still exists (ex: `Cart().save()` :D)
>*Custom managers or querysets*
- we do a bit of business logic in some custom QuerySet (ex. `ConfigurationQueryset.filter_to_import_now()`), but most of the custom querysets are done to implement status filtering (ex. `exclude_deleted()`)

>*Signals.*
- we did a big cleaning on signal usage a few years ago, there shouldn't be much business logic triggered by Django-native signals anymore
- we still use signals for mailer and webhook triggering, but I'm not sure it's a problem

## Model properties vs selectors:

>*If the property spans multiple relations, it should better be a selector.*
- NO COMMENT (this bad pattern is still present a bit everywhere, needs to be cleaned when possible)

>*If the property is non-trivial & can easily cause N + 1 queries problem, when serialized, it should better be a selector.*
- Same

