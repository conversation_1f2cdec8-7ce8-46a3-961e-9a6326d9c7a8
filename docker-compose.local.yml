version: "3.9"

services:
  # Local DynamoDB (already configured correctly)
  dynamodb:
    image: amazon/dynamodb-local
    ports:
      - "8008:8000"
    command: "-jar DynamoDBLocal.jar -sharedDb"
    volumes:
      - dynamodb_data:/home/<USER>/data
    
  # Local PostgreSQL (already configured correctly)
  postgres:
    image: postgres:latest
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=iceberg
      - POSTGRES_PASSWORD=iceberg
      - POSTGRES_DB=izberg_core
    volumes:
      - postgres_data:/var/lib/postgresql/data
    command: ["postgres", "-c", "log_statement=all"]

  # Local Redis (already configured correctly)
  redis:
    image: redis:latest
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  # Local file storage service (MinIO as S3 alternative)
  minio:
    image: minio/minio:latest
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin123
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # Create MinIO buckets
  minio-setup:
    image: minio/mc:latest
    depends_on:
      - minio
    entrypoint: >
      /bin/sh -c "
      /usr/bin/mc alias set myminio http://minio:9000 minioadmin minioadmin123;
      /usr/bin/mc mb myminio/izberg-dev-core-api-temp-envs;
      /usr/bin/mc mb myminio/izberg-dev-archives;
      /usr/bin/mc mb myminio/izberg-uploader-api-ci;
      /usr/bin/mc policy set public myminio/izberg-dev-core-api-temp-envs;
      /usr/bin/mc policy set public myminio/izberg-dev-archives;
      /usr/bin/mc policy set public myminio/izberg-uploader-api-ci;
      exit 0;
      "

  # Local Elasticsearch (optional, for search functionality)
  elasticsearch:
    image: elasticsearch:7.17.0
    ports:
      - "9200:9200"
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data

  # Core API with local configuration
  api.local.core.izbgtech.com:
    image: ${AWS_ACCOUNT_ID-************}.dkr.ecr.${AWS_DEFAULT_REGION-eu-west-1}.amazonaws.com/core/api:latest
    ports:
      - "8080:8080"
    depends_on:
      - dynamodb
      - postgres
      - redis
      - minio
    environment:
      # Database configuration
      - DATABASE_URL=******************************************/izberg_core
      - REDIS_URL=redis://redis:6379/0
      
      # Local DynamoDB configuration
      - DYNAMODB_ENDPOINT=http://dynamodb:8000
      - AWS_ACCESS_KEY_ID=fake
      - AWS_SECRET_ACCESS_KEY=fake
      - AWS_DEFAULT_REGION=eu-west-1
      
      # Local S3 (MinIO) configuration
      - AWS_S3_ENDPOINT_URL=http://minio:9000
      - AWS_STORAGE_BUCKET_NAME=izberg-dev-core-api-temp-envs
      - AWS_GLACIER_STORAGE_BUCKET_NAME=izberg-dev-archives
      - AWS_S3_UPLOADER_BUCKET_NAME=izberg-uploader-api-ci
      
      # Disable AWS services
      - USE_SQS_QUEUE=false
      - CELERY_BROKER_TRANSPORT=memory
      - DEFAULT_FILE_STORAGE=storages.backends.s3boto3.S3Boto3Storage
      
      # Local development settings
      - DJANGO_SETTINGS_MODULE=iceberg.settings_development
      - DEBUG=true
      - ENVIRONMENT=development
      - CUSTOM_ENV=local
      
      # Disable external services
      - DISABLE_ELASTICSEARCH=true
      - EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
      
    command: ["run_server_dev.sh"]
    extra_hosts:
      - "api.local.izberg.me:host-gateway"
      - "api.local.domains.izbgtech.com:host-gateway"
    volumes:
      - ./media:/app/media  # Local media storage

  # Domains API with local configuration
  api.local.domains.izbgtech.com:
    image: ${AWS_ACCOUNT_ID-************}.dkr.ecr.${AWS_DEFAULT_REGION-eu-west-1}.amazonaws.com/domains/api:latest
    ports:
      - "8082:8082"
    depends_on:
      - dynamodb
    environment:
      - DYNAMODB_ENDPOINT=http://dynamodb:8000
      - AWS_ACCESS_KEY_ID=fake
      - AWS_SECRET_ACCESS_KEY=fake
      - AWS_DEFAULT_REGION=eu-west-1
      - DJANGO_SETTINGS_MODULE=domains.settings_development
      - DEBUG=true
      - ENVIRONMENT=development
    command: ["run_server_dev.sh"]
    extra_hosts:
      - "api.local.izberg.me:host-gateway"

  # Identity API with local configuration
  api.local.izberg.me:
    image: ${AWS_ACCOUNT_ID-************}.dkr.ecr.${AWS_DEFAULT_REGION-eu-west-1}.amazonaws.com/identity/api:latest
    ports:
      - "8081:8081"
    depends_on:
      - dynamodb
    environment:
      - DYNAMODB_ENDPOINT=http://dynamodb:8000
      - AWS_ACCESS_KEY_ID=fake
      - AWS_SECRET_ACCESS_KEY=fake
      - AWS_DEFAULT_REGION=eu-west-1
      - DJANGO_SETTINGS_MODULE=identity.settings_development
      - DEBUG=true
      - ENVIRONMENT=development
    command: ["run_server_dev.sh"]
    extra_hosts:
      - "api.local.domains.izbgtech.com:host-gateway"

volumes:
  postgres_data:
  redis_data:
  dynamodb_data:
  minio_data:
  elasticsearch_data:
