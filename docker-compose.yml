version: "3.9"

services:
  dynamodb:
    image: amazon/dynamodb-local
    ports:
      - "8008:8000"
    command: "-jar DynamoDBLocal.jar -sharedDb"
    
  postgres:
    image: postgres:latest
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=iceberg
      - POSTGRES_PASSWORD=iceberg
      - POSTGRES_DB=izberg_core
    command: ["postgres", "-c", "log_statement=all"]

  redis:
    image: redis:latest
    ports:
      - "6379:6379"

  api.local.core.izbgtech.com:
    image: ${AWS_ACCOUNT_ID-************}.dkr.ecr.${AWS_DEFAULT_REGION-eu-west-1}.amazonaws.com/core/api:latest
    ports:
      - "8080:8080"
    depends_on:
      - dynamodb
      - postgres
      - redis
    command: ["run_server_dev.sh"]
    extra_hosts:
      - "api.local.izberg.me:host-gateway"
      - "api.local.domains.izbgtech.com:host-gateway"

  api.local.domains.izbgtech.com:
    image: ${AWS_ACCOUNT_ID-************}.dkr.ecr.${AWS_DEFAULT_REGION-eu-west-1}.amazonaws.com/domains/api:latest
    ports:
      - "8082:8082"
    depends_on:
      - dynamodb
    command: ["run_server_dev.sh"]
    extra_hosts:
      - "api.local.izberg.me:host-gateway"

  api.local.izberg.me:
    image: ${AWS_ACCOUNT_ID-************}.dkr.ecr.${AWS_DEFAULT_REGION-eu-west-1}.amazonaws.com/identity/api:latest
    ports:
      - "8081:8081"
    depends_on:
      - dynamodb
    command: ["run_server_dev.sh"]
    extra_hosts:
      - "api.local.domains.izbgtech.com:host-gateway"
