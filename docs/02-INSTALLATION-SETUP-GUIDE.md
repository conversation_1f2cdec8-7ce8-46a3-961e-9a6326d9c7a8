# IZBERG Core - Installation & Setup Guide

## Table of Contents
1. [Prerequisites](#1-prerequisites)
2. [Environment Setup](#2-environment-setup)
3. [Project Installation](#3-project-installation)
4. [Service Configuration](#4-service-configuration)
5. [Database Setup and Initial Data](#5-database-setup-and-initial-data)
6. [Development Server](#6-development-server)
7. [Testing](#7-testing)
8. [Troubleshooting](#8-troubleshooting)

---

## 1. Prerequisites

### 1.1 System Requirements

**Operating System**: 
- Linux (Ubuntu 20.04+ recommended)
- macOS (10.15+ or macOS Big Sur+)
- Windows with WSL2

**Hardware Requirements**:
- **Memory**: Minimum 8GB RAM (16GB recommended for development)
- **Storage**: At least 20GB free disk space
- **Network**: Stable internet connection for downloading dependencies

### 1.2 Required Software

#### Docker & Docker Compose

**Ubuntu/Debian**:
```bash
# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Add user to docker group
sudo usermod -aG docker $USER
# Logout and login again
```

**macOS**:
```bash
# Install Docker Desktop from https://www.docker.com/products/docker-desktop/
# Or using Homebrew
brew install --cask docker
```

#### AWS CLI

```bash
# Install AWS CLI v2 (Linux)
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install

# Install AWS CLI v2 (macOS)
curl "https://awscli.amazonaws.com/AWSCLIV2.pkg" -o "AWSCLIV2.pkg"
sudo installer -pkg AWSCLIV2.pkg -target /
```

#### Python Environment Management

```bash
# Install pyenv
curl https://pyenv.run | bash

# Add to shell configuration (~/.bashrc, ~/.zshrc, or ~/.profile)
export PYENV_ROOT="$HOME/.pyenv"
command -v pyenv >/dev/null || export PATH="$PYENV_ROOT/bin:$PATH"
eval "$(pyenv init -)"

# Reload shell
exec "$SHELL"
```

### 1.3 Access Requirements

**Required Access**:
- **GitHub Access**: SSH key configured for `izberg-marketplace` organization
- **AWS Account**: Access to IZBERG's ECR repositories in `eu-west-1` region
- **IZBERG PyPI Server**: Credentials for private package repository at `pypi.izbgtech.com`

**SSH Key Setup for GitHub**:
```bash
# Generate SSH key
ssh-keygen -t ed25519 -C "<EMAIL>"

# Add to SSH agent
eval "$(ssh-agent -s)"
ssh-add ~/.ssh/id_ed25519

# Copy public key and add to GitHub account
cat ~/.ssh/id_ed25519.pub
```

---

## 2. Environment Setup

### 2.1 Python Environment

#### Standard Setup

```bash
# Install Python 3.10
pyenv install 3.10
pyenv global 3.10

# Install virtualenv
pip install virtualenv

# Create virtual environment
virtualenv .venv

# Activate virtual environment
source .venv/bin/activate
```

#### Alternative: Using virtualenvwrapper

```bash
# Install virtualenvwrapper
pip install virtualenvwrapper

# Add to shell configuration
export WORKON_HOME=~/.virtualenvs
export PYENV_VIRTUALENVWRAPPER_PREFER_PYVENV="true"
pyenv virtualenvwrapper

# Create and activate environment
mkvirtualenv izberg-core
workon izberg-core
```

#### macOS M1/M2 Specific Setup

```bash
# Install Homebrew in x86 mode for compatibility
arch -x86_64 /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Add alias for x86 brew
echo 'alias ibrew="arch -x86_64 /usr/local/homebrew/bin/brew"' >> ~/.zshrc

# Install dependencies
ibrew install pyenv libmagic postgresql openssl@1.1 swig

# Install Python 3.8.5 for M1 compatibility
arch -x86_64 pyenv install 3.8.5
pyenv global 3.8.5

# Set compilation flags
export LDFLAGS="-L/opt/homebrew/opt/openssl@1.1/lib"
export CPPFLAGS="-I/opt/homebrew/opt/openssl@1.1/include"
export CFLAGS="-I/opt/homebrew/opt/openssl@1.1/include"
export PKG_CONFIG_PATH="/opt/homebrew/opt/openssl@1.1/lib/pkgconfig"
export ARCHFLAGS="-arch arm64"
export SWIG_FEATURES="-I/opt/homebrew/opt/openssl@1.1/include"
```

### 2.2 AWS Configuration

```bash
# Configure AWS credentials
aws configure

# Enter the following when prompted:
# AWS Access Key ID: [Your IZBERG-provided access key]
# AWS Secret Access Key: [Your IZBERG-provided secret key]
# Default region name: eu-west-1
# Default output format: json

# Login to ECR
aws ecr get-login-password --region eu-west-1 | docker login --username AWS --password-stdin 171946876898.dkr.ecr.eu-west-1.amazonaws.com
```

**Alternative: Multiple AWS Profiles**

```bash
# Create ~/.aws/config
[profile izberg-dev]
region = eu-west-1
output = json

[profile izberg-prod]
region = eu-west-1
output = json

# Create ~/.aws/credentials
[izberg-dev]
aws_access_key_id = YOUR_DEV_ACCESS_KEY
aws_secret_access_key = YOUR_DEV_SECRET_KEY

[izberg-prod]
aws_access_key_id = YOUR_PROD_ACCESS_KEY
aws_secret_access_key = YOUR_PROD_SECRET_KEY

# Use specific profile
aws --profile izberg-dev ecr get-login-password --region eu-west-1 | docker login --username AWS --password-stdin 171946876898.dkr.ecr.eu-west-1.amazonaws.com
```

### 2.3 PyPI Server Configuration

Create `~/.pip/pip.conf`:

```ini
[search]
index = https://<username>:<password>@pypi.izbgtech.com/pypi/

[global]
index-url = https://<username>:<password>@pypi.izbgtech.com/simple/
```

**Environment Variables**:
```bash
# Add to ~/.bashrc or ~/.zshrc
export PYPI_DOWNLOAD_URL=`awk -F " = " '/index-url/ {print $2}' ~/.pip/pip.conf`
export PYPI_URL=`awk -F " = " '/index-url/ {print $2}' ~/.pip/pip.conf`

# Test PyPI access
pip install "izberg-authenticator>=1.2,<2"
```

---

## 3. Project Installation

### 3.1 Clone Repository

```bash
# Clone with submodules
git clone --recursive **************:izberg-marketplace/izberg-core.git
cd izberg-core

# Configure git blame to ignore formatting commits
git config --global blame.ignoreRevsFile .git-blame-ignore-revs
```

### 3.2 Install Dependencies

```bash
# Activate virtual environment
source .venv/bin/activate

# Install Python dependencies
pip install -r requirements/dev-py310.txt
```

**macOS M1/M2 Compatibility Issues**:
```bash
# Handle setuptools compatibility
pip install "setuptools<60.8.2"

# Replace incompatible packages
pip uninstall python-magic && pip install python-magic-bin<=0.4.14
pip uninstall -y psycopg2 && pip install psycopg2-binary<=2.8.6

# If installation fails, try with specific index
pip install -i $PYPI_DOWNLOAD_URL -r requirements/dev-py310.txt
```

### 3.3 Environment Configuration

**Create Environment File**:
```bash
# Copy template
cp .env.template .env

# Edit .env file with required settings
PYTHONPATH=${PWD}/modules/:${PYTHONPATH}:${PWD}
```

**macOS with direnv (Recommended)**:
```bash
# Install direnv
brew install direnv

# Add hook to shell
echo 'eval "$(direnv hook zsh)"' >> ~/.zshrc
source ~/.zshrc

# Create .envrc file
echo "dotenv" > .envrc
direnv allow

# The .env file will be automatically loaded when entering the directory
```

---

## 4. Service Configuration

### 4.1 Host Configuration

Add the following entries to `/etc/hosts`:

```bash
# IZBERG Core Services
127.0.0.1       api.local.core.izbgtech.com
127.0.0.1       api.local.izberg.me
127.0.0.1       api.local.domains.izbgtech.com
127.0.0.1       api.local.analytics.izbgtech.com
127.0.0.1       api.local.exporter.izbgtech.com
127.0.0.1       api.local.channels.izbgtech.com

# Database Services
127.0.0.1       dynamodb
127.0.0.1       postgres
127.0.0.1       redis
```

**Automated Host Configuration**:
```bash
# Add all hosts at once
sudo tee -a /etc/hosts << EOF
# IZBERG Core Development Environment
127.0.0.1       api.local.core.izbgtech.com
127.0.0.1       api.local.izberg.me
127.0.0.1       api.local.domains.izbgtech.com
127.0.0.1       api.local.analytics.izbgtech.com
127.0.0.1       api.local.exporter.izbgtech.com
127.0.0.1       api.local.channels.izbgtech.com
127.0.0.1       dynamodb
127.0.0.1       postgres
127.0.0.1       redis
EOF
```

### 4.2 Docker Services

**Start All Services**:
```bash
# Start all services in detached mode
docker compose up -d

# Verify services are running
docker compose ps

# Check service logs
docker compose logs -f
```

**Service Overview**:
- **PostgreSQL**: Main database (Port 5432)
- **Redis**: Caching and sessions (Port 6379)  
- **DynamoDB Local**: NoSQL database (Port 8008)
- **Core API**: Main application (Port 8080)
- **Identity API**: Authentication service (Port 8081)
- **Domains API**: Multi-tenant management (Port 8082)

**Individual Service Management**:
```bash
# Start specific service
docker compose up -d postgres

# Stop specific service
docker compose stop api.local.core.izbgtech.com

# View logs for specific service
docker compose logs -f postgres

# Restart service
docker compose restart redis
```

---

## 5. Database Setup and Initial Data

### 5.1 Database Migration

```bash
# Navigate to Django project
cd iceberg

# Run database migrations
python manage.py migrate

# Create superuser (optional)
python manage.py createsuperuser
```

### 5.2 Initial Data Setup

**Basic Setup**:
```bash
# Setup initial data with default parameters
invoke setup --applications 1 --merchants 5 --orders 2

# This creates:
# - 1 marketplace application
# - 5 merchants with sample data
# - Sample products and offers
# - 2 test orders
```

**Advanced Setup Options**:
```bash
# Custom setup with more data
invoke setup --applications 2 --merchants 10 --products 50 --offers 40 --orders 5

# Setup with specific payment backend
invoke setup --applications 1 --merchants 3 --payment_backend="hipay_tpp"

# Setup with random workflow states
invoke setup --applications 1 --merchants 5 --random_workflow=True
```

**Load Additional Fixtures**:
```bash
# Load basic catalog data
python manage.py loaddata fixtures/basic_catalog.xml

# Load site fixtures
python manage.py loaddata fixtures/site_fixtures.json
```

---

## 6. Development Server

### 6.1 Running the Development Server

```bash
# Stop the containerized API to avoid port conflicts
docker compose stop api.local.core.izbgtech.com

# Navigate to Django project
cd iceberg

# Run development server
./manage.py runserver api.local.core.izbgtech.com:8080
```

### 6.2 Access Points

**Admin Interface**:
- **URL**: `http://api.local.core.izbgtech.com:8080/_a_d_m_i_n_/`
- **Username**: `<EMAIL>`
- **Password**: `admin`

**API Endpoints**:
- **Core API**: `http://api.local.core.izbgtech.com:8080/api/v1/`
- **Identity API**: `http://api.local.izberg.me:8081/api/v1/`
- **Domains API**: `http://api.local.domains.izbgtech.com:8082/api/v1/`

**API Documentation**:
- **Swagger UI**: `http://api.local.core.izbgtech.com:8080/api/docs/`
- **ReDoc**: `http://api.local.core.izbgtech.com:8080/api/redoc/`

### 6.3 Development Tools

**Django Management Commands**:
```bash
# Create Django app
python manage.py startapp myapp

# Create migrations
python manage.py makemigrations

# Apply migrations
python manage.py migrate

# Collect static files
python manage.py collectstatic

# Create superuser
python manage.py createsuperuser

# Django shell
python manage.py shell

# Database shell
python manage.py dbshell
```

---

## 7. Testing

### 7.1 Running Tests

**Basic Test Commands**:
```bash
# Run all tests
./manage.py test -kt .

# Run tests for specific app
./manage.py test apps.products

# Run tests with pattern
./manage.py test -p "test_*.py"

# Run specific test class
./manage.py test apps.products.tests.TestProductModel

# Run specific test method
./manage.py test apps.products.tests.TestProductModel.test_product_creation
```

**Advanced Testing Options**:
```bash
# Run tests with coverage
coverage run --source='.' manage.py test
coverage report
coverage html

# Run tests in parallel
./manage.py test --parallel

# Run tests with verbose output
./manage.py test --verbosity=2

# Keep test database
./manage.py test --keepdb
```

### 7.2 Test Configuration

**pytest Configuration** (if using pytest):
```bash
# Install pytest-django
pip install pytest-django

# Run with pytest
pytest

# Run with coverage
pytest --cov=apps --cov-report=html
```

---

## 8. Troubleshooting

### 8.1 Common Issues

**Docker Permission Issues**:
```bash
# Add user to docker group
sudo usermod -aG docker $USER
# Logout and login again

# Fix docker socket permissions
sudo chmod 666 /var/run/docker.sock
```

**Port Conflicts**:
```bash
# Check what's using a port
sudo netstat -tulpn | grep :8080
sudo lsof -i :8080

# Kill process using port
sudo kill -9 $(sudo lsof -t -i:8080)

# Use different port
./manage.py runserver api.local.core.izbgtech.com:8081
```

**Database Connection Issues**:
```bash
# Reset Docker volumes
docker compose down -v
docker compose up -d

# Check database logs
docker compose logs postgres

# Connect to database directly
docker compose exec postgres psql -U iceberg -d izberg_core

# Reset database
docker compose down -v
docker volume prune -f
docker compose up -d
```

**Python Package Issues**:
```bash
# Clear pip cache
pip cache purge

# Reinstall requirements
pip install --force-reinstall -r requirements/dev-py310.txt

# Check for conflicting packages
pip check

# Update pip
pip install --upgrade pip
```

### 8.2 Environment-Specific Issues

**macOS M1/M2 Issues**:
```bash
# If psycopg2 fails to install
export LDFLAGS="-L/opt/homebrew/opt/openssl@1.1/lib -L/opt/homebrew/opt/libpq/lib"
export CPPFLAGS="-I/opt/homebrew/opt/openssl@1.1/include -I/opt/homebrew/opt/libpq/include"
pip install psycopg2-binary

# If swig compilation fails
brew install swig
export SWIG_FEATURES="-I/opt/homebrew/opt/openssl@1.1/include"

# If libmagic issues
brew install libmagic
pip install python-magic-bin
```

**WSL2 Issues**:
```bash
# If Docker daemon not running
sudo service docker start

# If memory issues
echo 'vm.max_map_count=262144' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p

# If file permission issues
sudo chown -R $USER:$USER /path/to/izberg-core
```

**AWS/ECR Issues**:
```bash
# Re-authenticate with ECR
aws ecr get-login-password --region eu-west-1 | docker login --username AWS --password-stdin 171946876898.dkr.ecr.eu-west-1.amazonaws.com

# Check AWS credentials
aws sts get-caller-identity

# Update AWS CLI
pip install --upgrade awscli
```

### 8.3 Performance Issues

**Slow Database Queries**:
```bash
# Enable query logging in settings
LOGGING = {
    'loggers': {
        'django.db.backends': {
            'level': 'DEBUG',
            'handlers': ['console'],
        }
    }
}

# Use Django Debug Toolbar
pip install django-debug-toolbar
```

**Memory Issues**:
```bash
# Monitor memory usage
docker stats

# Increase Docker memory limit
# In Docker Desktop: Settings > Resources > Memory

# Check Python memory usage
pip install memory-profiler
python -m memory_profiler manage.py runserver
```

---

## Next Steps

After successful installation:

1. **Explore the Admin Interface**: Familiarize yourself with the Django admin
2. **Review API Documentation**: Check the API endpoints and documentation
3. **Run Test Suite**: Ensure all tests pass in your environment
4. **Create Sample Data**: Use the setup commands to create test data
5. **Review Architecture Documentation**: Understand the system architecture
6. **Start Development**: Begin working with the codebase

For additional help, refer to the project's internal documentation or contact the development team.
