# IZBERG Core - System Architecture Documentation

## Table of Contents
1. [Architecture Overview](#1-architecture-overview)
2. [System Architecture Diagram](#2-system-architecture-diagram)
3. [Core Components](#3-core-components)
4. [Data Layer Architecture](#4-data-layer-architecture)
5. [API Layer Design](#5-api-layer-design)
6. [Business Logic Layer](#6-business-logic-layer)
7. [Integration Architecture](#7-integration-architecture)
8. [Security Architecture](#8-security-architecture)
9. [Data Flow Diagrams](#9-data-flow-diagrams)
10. [Scalability & Performance](#10-scalability--performance)

---

## 1. Architecture Overview

IZBERG Core follows a **microservices architecture** with a **multi-tenant design** that enables multiple marketplace applications to operate independently while sharing common infrastructure and services.

### 1.1 Architectural Principles

**Separation of Concerns**: Each service handles specific business domains
**Multi-tenancy**: Application-based data isolation for multiple marketplaces
**API-First Design**: All functionality exposed through RESTful APIs
**Scalable Infrastructure**: Horizontal scaling capabilities
**Modular Design**: Pluggable components and integrations

### 1.2 High-Level Architecture Layers

```
┌─────────────────────────────────────────────────────────────┐
│                    API Gateway Layer                        │
│  Core API (8080) | Identity API (8081) | Domains API (8082) │
├─────────────────────────────────────────────────────────────┤
│                  Business Logic Layer                       │
│   Products | Orders | Payments | Merchants | Users          │
├─────────────────────────────────────────────────────────────┤
│                     Data Layer                              │
│  PostgreSQL | Redis | DynamoDB | Elasticsearch | S3         │
├─────────────────────────────────────────────────────────────┤
│                 External Services                           │
│  Payment PSPs | Shipping Carriers | Email | Analytics       │
└─────────────────────────────────────────────────────────────┘
```

---

## 2. System Architecture Diagram

### 2.1 Complete System Architecture

```mermaid
graph TB
    %% External Users
    Customer[👤 Customer]
    Merchant[🏪 Merchant]
    Operator[⚙️ Marketplace Operator]
    
    %% API Layer
    subgraph "API Layer"
        CoreAPI[🌐 Core API<br/>Port 8080]
        IdentityAPI[🔐 Identity API<br/>Port 8081]
        DomainsAPI[🌍 Domains API<br/>Port 8082]
    end
    
    %% Core Business Layer
    subgraph "Business Logic Layer"
        subgraph "Product Management"
            Products[📦 Products]
            ProductOffers[💰 Product Offers]
            Categories[📂 Categories]
            Attributes[🏷️ Attributes]
        end
        
        subgraph "Order Management"
            Cart[🛒 Cart]
            Orders[📋 Orders]
            MerchantOrders[🏪 Merchant Orders]
            OrderItems[📄 Order Items]
        end
        
        subgraph "User & Store Management"
            Users[👥 Users]
            Stores[🏬 Stores/Merchants]
            Applications[🎯 Applications]
            Permissions[🔒 Permissions]
        end
        
        subgraph "Financial Management"
            Payments[💳 Payments]
            Transactions[💸 Transactions]
            Invoices[📄 Invoices]
            Commissions[💰 Commissions]
        end
    end
    
    %% Payment Backends
    subgraph "Payment Processing"
        HiPay[💳 HiPay TPP]
        PSPGateway[🔗 PSP Gateway]
        ExternalPayment[🔌 External Payment]
    end
    
    %% Data Storage
    subgraph "Data Storage"
        PostgreSQL[(🐘 PostgreSQL)]
        Redis[(⚡ Redis)]
        DynamoDB[(📊 DynamoDB)]
        S3[(☁️ S3)]
    end
    
    %% External Services
    subgraph "External Services"
        Elasticsearch[🔍 Elasticsearch]
        Mailer[📧 Email Service]
        Carriers[🚚 Shipping Carriers]
        AWS[☁️ AWS Services]
    end
    
    %% User Interactions
    Customer --> CoreAPI
    Merchant --> CoreAPI
    Operator --> CoreAPI
    
    %% API Interactions
    CoreAPI --> Products
    CoreAPI --> Cart
    CoreAPI --> Orders
    CoreAPI --> Payments
    
    IdentityAPI --> Users
    IdentityAPI --> Permissions
    DomainsAPI --> Applications
    
    %% Business Logic Flow
    Cart --> Orders
    Orders --> MerchantOrders
    ProductOffers --> OrderItems
    Products --> ProductOffers
    
    %% Payment Flow
    Orders --> Payments
    Payments --> HiPay
    Payments --> PSPGateway
    Payments --> ExternalPayment
    
    %% Data Persistence
    Products --> PostgreSQL
    Orders --> PostgreSQL
    Users --> PostgreSQL
    Cart --> Redis
    Applications --> DynamoDB
    
    %% External Services
    Products --> Elasticsearch
    Orders --> Mailer
    Orders --> Carriers
```

---

## 3. Core Components

### 3.1 API Gateway Layer

**Core API (Port 8080)**:
- Main marketplace functionality
- Product catalog management
- Order processing and fulfillment
- Payment handling and transactions
- Merchant operations

**Identity API (Port 8081)**:
- User authentication and authorization
- JWT token management
- Permission and role management
- User profile services

**Domains API (Port 8082)**:
- Multi-tenant domain management
- Application configuration
- Namespace isolation
- Custom domain handling

### 3.2 Microservices Communication

```python
# Example: Inter-service communication pattern
class CoreAPIService:
    def authenticate_user(self, token):
        # Call Identity API for token validation
        response = requests.post(
            'http://api.local.izberg.me:8081/api/v1/auth/validate',
            headers={'Authorization': f'Bearer {token}'}
        )
        return response.json()
    
    def get_application_config(self, domain):
        # Call Domains API for application configuration
        response = requests.get(
            f'http://api.local.domains.izbgtech.com:8082/api/v1/applications/{domain}'
        )
        return response.json()
```

### 3.3 Service Dependencies

```yaml
# Docker Compose service dependencies
services:
  api.local.core.izbgtech.com:
    depends_on:
      - dynamodb
      - postgres
      - redis
    environment:
      - DATABASE_URL=******************************************/izberg_core
      - REDIS_URL=redis://redis:6379/0
      - DYNAMODB_ENDPOINT=http://dynamodb:8000
```

---

## 4. Data Layer Architecture

### 4.1 Database Strategy

**PostgreSQL (Primary Database)**:
- Transactional data (orders, payments, users)
- Product catalog and inventory
- Merchant and application data
- Relational data with ACID compliance

**Redis (Caching Layer)**:
- Session management
- API response caching
- Real-time data (cart contents)
- Background job queues

**DynamoDB (NoSQL Storage)**:
- Application configuration
- High-volume logging
- Analytics data
- Flexible schema requirements

**S3 (File Storage)**:
- Product images and media
- Document uploads (KYC documents)
- Generated reports and exports
- Static assets and backups

### 4.2 Data Model Relationships

```python
# Core data model relationships
class Application(models.Model):
    """Marketplace instance - tenant isolation"""
    name = models.CharField(max_length=255)
    namespace = models.CharField(max_length=255, unique=True)

class User(models.Model):
    """User belongs to specific application"""
    application = models.ForeignKey(Application, on_delete=models.CASCADE)
    email = models.EmailField()

class Merchant(models.Model):
    """Merchant/Store within an application"""
    application = models.ForeignKey(Application, on_delete=models.CASCADE)
    name = models.CharField(max_length=128)

class Product(models.Model):
    """Global product catalog"""
    external_id = models.CharField(max_length=255, unique=True)
    gtin = models.CharField(max_length=14, unique=True, null=True)

class ProductOffer(models.Model):
    """Merchant's offer for a product"""
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    merchant = models.ForeignKey(Merchant, on_delete=models.CASCADE)
    price = models.DecimalField(max_digits=20, decimal_places=2)
    stock = models.IntegerField(default=0)

class Order(models.Model):
    """Customer order"""
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    application = models.ForeignKey(Application, on_delete=models.CASCADE)

class MerchantOrder(models.Model):
    """Order split by merchant for fulfillment"""
    merchant = models.ForeignKey(Merchant, on_delete=models.CASCADE)
    order = models.ForeignKey(Order, related_name='merchant_orders')
```

### 4.3 Data Partitioning Strategy

**Application-Based Partitioning**:
- All data includes application foreign key
- Enables complete tenant isolation
- Supports independent scaling per marketplace

**Time-Based Partitioning**:
- Log data partitioned by date
- Analytics data by time periods
- Automated archival of old data

---

## 5. API Layer Design

### 5.1 RESTful API Design

**Resource-Based URLs**:
```
GET    /api/v1/products/              # List products
POST   /api/v1/products/              # Create product
GET    /api/v1/products/{id}/         # Get specific product
PUT    /api/v1/products/{id}/         # Update product
DELETE /api/v1/products/{id}/         # Delete product

GET    /api/v1/orders/                # List orders
POST   /api/v1/orders/                # Create order
GET    /api/v1/orders/{id}/           # Get specific order
```

**Nested Resources**:
```
GET    /api/v1/merchants/{id}/products/     # Merchant's products
GET    /api/v1/orders/{id}/items/           # Order items
POST   /api/v1/carts/{id}/items/            # Add item to cart
```

### 5.2 API Authentication & Authorization

**JWT Token-Based Authentication**:
```python
# JWT token structure
{
    "user_id": 123,
    "application_id": 1,
    "permissions": ["read:products", "write:orders"],
    "exp": 1640995200,
    "iat": 1640908800
}
```

**Permission-Based Authorization**:
```python
# Permission checking example
class ProductViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated, HasApplicationPermission]
    
    def get_queryset(self):
        # Filter by user's application
        return Product.objects.filter(
            application=self.request.user.application
        )
```

### 5.3 API Versioning Strategy

**URL-Based Versioning**:
- `/api/v1/` - Current stable version
- `/api/v2/` - Next version with breaking changes
- Backward compatibility maintained for at least 2 versions

---

## 6. Business Logic Layer

### 6.1 Domain-Driven Design

**Product Domain**:
```python
# Product aggregate with business logic
class Product:
    def create_offer(self, merchant, price, stock):
        """Create merchant offer for this product"""
        if not self.is_active():
            raise ProductNotActiveError()
        
        offer = ProductOffer.objects.create(
            product=self,
            merchant=merchant,
            price=price,
            stock=stock
        )
        return offer
    
    def get_best_offer(self, application):
        """Get best price offer for application"""
        return self.product_offers.filter(
            merchant__application=application,
            status='active'
        ).order_by('price').first()
```

**Order Domain**:
```python
# Order processing workflow
class Order:
    def process_payment(self, payment_method):
        """Process order payment"""
        payment = Payment.objects.create(
            order=self,
            amount=self.total_amount,
            payment_method=payment_method
        )
        
        # Delegate to payment backend
        backend = get_payment_backend(payment_method)
        result = backend.authorize(payment)
        
        if result.success:
            self.status = 'payment_authorized'
            self.save()
            self.create_merchant_orders()
        
        return result
    
    def create_merchant_orders(self):
        """Split order by merchant"""
        merchant_groups = self.group_items_by_merchant()
        
        for merchant, items in merchant_groups.items():
            MerchantOrder.objects.create(
                order=self,
                merchant=merchant,
                items=items
            )
```

### 6.2 Workflow Management

**State-Based Workflows**:
```python
# Order workflow states
class OrderWorkflow(xworkflows.Workflow):
    states = (
        ('draft', 'Draft'),
        ('payment_pending', 'Payment Pending'),
        ('payment_authorized', 'Payment Authorized'),
        ('confirmed', 'Confirmed'),
        ('shipped', 'Shipped'),
        ('delivered', 'Delivered'),
        ('cancelled', 'Cancelled'),
    )
    
    transitions = (
        ('authorize_payment', 'draft', 'payment_authorized'),
        ('confirm', 'payment_authorized', 'confirmed'),
        ('ship', 'confirmed', 'shipped'),
        ('deliver', 'shipped', 'delivered'),
        ('cancel', ['draft', 'payment_pending'], 'cancelled'),
    )
```

---

## 7. Integration Architecture

### 7.1 Payment Integration

**Payment Backend Strategy**:
```python
# Payment backend interface
class BasePaymentProcessor:
    def authorize(self, payment):
        """Authorize payment"""
        raise NotImplementedError
    
    def capture(self, payment, amount):
        """Capture authorized payment"""
        raise NotImplementedError
    
    def refund(self, payment, amount):
        """Refund payment"""
        raise NotImplementedError

# HiPay implementation
class HipayTppPayment(BasePaymentProcessor):
    def authorize(self, payment):
        # HiPay API integration
        response = self.hipay_client.authorize(
            amount=payment.amount,
            currency=payment.currency,
            card_token=payment.card_token
        )
        return PaymentResult(
            success=response.status == 'authorized',
            transaction_id=response.transaction_id
        )
```

### 7.2 Shipping Integration

**Carrier Abstraction**:
```python
# Shipping carrier interface
class ShippingCarrierBase:
    name = None
    verbose_name = None
    
    def calculate_rate(self, origin, destination, package):
        """Calculate shipping rate"""
        raise NotImplementedError
    
    def create_shipment(self, order, carrier_service):
        """Create shipment and get tracking number"""
        raise NotImplementedError
    
    def track_package(self, tracking_number):
        """Get package tracking information"""
        raise NotImplementedError

# DHL implementation
class DHL(ShippingCarrierBase):
    name = "dhl"
    verbose_name = "DHL Express"
    
    def calculate_rate(self, origin, destination, package):
        # DHL API integration
        response = self.dhl_client.get_rates(
            origin=origin,
            destination=destination,
            weight=package.weight,
            dimensions=package.dimensions
        )
        return response.rates
```

### 7.3 Search Integration

**Elasticsearch Integration**:
```python
# Product search indexing
class ProductSearchIndex:
    def index_product(self, product):
        """Index product for search"""
        doc = {
            'id': product.id,
            'name': product.name,
            'description': product.description,
            'category': product.category.name,
            'price_range': self.get_price_range(product),
            'availability': product.is_available(),
            'merchant_count': product.merchant_count()
        }
        
        self.es_client.index(
            index='products',
            id=product.id,
            body=doc
        )
    
    def search_products(self, query, filters=None):
        """Search products with filters"""
        search_body = {
            'query': {
                'bool': {
                    'must': [
                        {'multi_match': {
                            'query': query,
                            'fields': ['name^2', 'description']
                        }}
                    ],
                    'filter': self.build_filters(filters)
                }
            }
        }
        
        return self.es_client.search(
            index='products',
            body=search_body
        )
```

---

## 8. Security Architecture

### 8.1 Authentication Flow

```mermaid
sequenceDiagram
    participant Client
    participant CoreAPI
    participant IdentityAPI
    participant Database
    
    Client->>CoreAPI: Request with JWT token
    CoreAPI->>IdentityAPI: Validate token
    IdentityAPI->>Database: Check user & permissions
    Database-->>IdentityAPI: User data
    IdentityAPI-->>CoreAPI: Validation result
    CoreAPI-->>Client: API response
```

### 8.2 Data Security

**Encryption Strategy**:
- Data at rest: Database encryption
- Data in transit: TLS 1.3 encryption
- Sensitive data: Field-level encryption
- PCI DSS compliance for payment data

**Access Control**:
```python
# Role-based access control
class PermissionMixin:
    def has_permission(self, user, action, resource=None):
        """Check if user has permission for action"""
        if user.is_superuser:
            return True
        
        # Check application-level permissions
        app_perms = user.get_application_permissions()
        if f"{action}:{resource}" in app_perms:
            return True
        
        # Check resource-specific permissions
        if resource and hasattr(resource, 'check_user_permission'):
            return resource.check_user_permission(user, action)
        
        return False
```

---

## 9. Data Flow Diagrams

### 9.1 Order Processing Flow

```mermaid
flowchart TD
    A[Customer adds items to cart] --> B[Cart validation]
    B --> C[Checkout process]
    C --> D[Payment authorization]
    D --> E{Payment successful?}
    E -->|Yes| F[Create order]
    E -->|No| G[Payment failed]
    F --> H[Split by merchant]
    H --> I[Create merchant orders]
    I --> J[Notify merchants]
    J --> K[Merchant confirmation]
    K --> L[Payment capture]
    L --> M[Order fulfillment]
    M --> N[Shipping & tracking]
    N --> O[Order completion]
```

### 9.2 Payment Processing Flow

```mermaid
flowchart TD
    A[Payment initiation] --> B[Payment backend selection]
    B --> C[Payment authorization]
    C --> D{Authorization successful?}
    D -->|Yes| E[Hold funds]
    D -->|No| F[Payment declined]
    E --> G[Order confirmation]
    G --> H[Payment capture]
    H --> I[Commission calculation]
    I --> J[Merchant payout processing]
    J --> K[Transaction recording]
    K --> L[Payment completion]
```

### 9.3 Product Management Flow

```mermaid
flowchart TD
    A[Product creation] --> B[Product validation]
    B --> C[Save to database]
    C --> D[Index in Elasticsearch]
    D --> E[Merchant creates offer]
    E --> F[Offer validation]
    F --> G[Price & stock update]
    G --> H[Availability calculation]
    H --> I[Search index update]
    I --> J[Product visible to customers]
```

---

## 10. Scalability & Performance

### 10.1 Caching Strategy

**Multi-Level Caching**:
```python
# Application-level caching
@cache_result(timeout=3600)
def get_product_offers(product_id, application_id):
    return ProductOffer.objects.filter(
        product_id=product_id,
        merchant__application_id=application_id,
        status='active'
    ).select_related('merchant', 'product')

# Database query optimization
class ProductQuerySet(models.QuerySet):
    def with_offers(self):
        return self.prefetch_related(
            'product_offers__merchant',
            'product_offers__variations'
        )
    
    def for_application(self, application):
        return self.filter(
            product_offers__merchant__application=application
        ).distinct()
```

### 10.2 Database Optimization

**Query Optimization**:
- Selective field loading with `only()` and `defer()`
- Prefetch related objects to avoid N+1 queries
- Database indexing on frequently queried fields
- Query monitoring and optimization

**Scaling Strategies**:
- Read replicas for read-heavy operations
- Database partitioning by application
- Connection pooling and optimization
- Automated query performance monitoring

### 10.3 Infrastructure Scaling

**Horizontal Scaling**:
- Stateless application design
- Load balancing across multiple instances
- Auto-scaling based on metrics
- Container orchestration with Kubernetes

**Performance Monitoring**:
- Application performance monitoring (APM)
- Database performance tracking
- Real-time alerting and notifications
- Capacity planning and optimization

---

## 11. Deployment Architecture

### 11.1 Containerization Strategy

**Docker Configuration**:
```dockerfile
# Multi-stage build for optimization
FROM python:3.10-slim as base
WORKDIR /app
COPY requirements/ requirements/
RUN pip install -r requirements/production.txt

FROM base as production
COPY . .
EXPOSE 8080
CMD ["gunicorn", "--bind", "0.0.0.0:8080", "iceberg.wsgi:application"]
```

**Environment-Specific Configurations**:
- Development: Docker Compose with local services
- Staging: Kubernetes with AWS services
- Production: Kubernetes with high availability

### 11.2 CI/CD Pipeline

**Build Process**:
1. Code quality checks (flake8, black, mypy)
2. Security scanning (bandit, safety)
3. Unit and integration tests
4. Docker image building
5. Image security scanning

**Deployment Process**:
1. Blue-green deployments for zero downtime
2. Database migration handling
3. Configuration management with secrets
4. Health checks and rollback capabilities

### 11.3 Monitoring & Observability

**Application Monitoring**:
```python
# Structured logging example
import structlog

logger = structlog.get_logger()

def process_order(order_id):
    logger.info(
        "order_processing_started",
        order_id=order_id,
        user_id=order.user_id,
        application_id=order.application_id
    )

    try:
        # Process order
        result = order.process()

        logger.info(
            "order_processing_completed",
            order_id=order_id,
            status=result.status,
            processing_time=result.duration
        )
    except Exception as e:
        logger.error(
            "order_processing_failed",
            order_id=order_id,
            error=str(e),
            exc_info=True
        )
```

**Health Checks**:
```python
# Health check endpoints
class HealthCheckView(APIView):
    def get(self, request):
        checks = {
            'database': self.check_database(),
            'redis': self.check_redis(),
            'external_services': self.check_external_services()
        }

        all_healthy = all(checks.values())
        status_code = 200 if all_healthy else 503

        return Response(checks, status=status_code)
```

---

## 12. Development Guidelines

### 12.1 Code Organization

**Django App Structure**:
```
apps/products/
├── __init__.py
├── models/
│   ├── __init__.py
│   ├── product_models.py
│   └── offer_models.py
├── api/
│   ├── __init__.py
│   ├── serializers.py
│   └── views.py
├── services/
│   ├── __init__.py
│   └── product_service.py
├── tests/
│   ├── __init__.py
│   ├── test_models.py
│   └── test_api.py
└── migrations/
```

**Service Layer Pattern**:
```python
# Service layer for business logic
class ProductService:
    @staticmethod
    def create_product_with_offers(product_data, offers_data):
        """Create product with initial offers"""
        with transaction.atomic():
            product = Product.objects.create(**product_data)

            for offer_data in offers_data:
                ProductOffer.objects.create(
                    product=product,
                    **offer_data
                )

            # Index in search engine
            ProductSearchIndex().index_product(product)

            return product
```

### 12.2 Testing Strategy

**Test Categories**:
- Unit tests for models and business logic
- Integration tests for API endpoints
- End-to-end tests for critical workflows
- Performance tests for scalability

**Test Example**:
```python
class ProductAPITestCase(APITestCase):
    def setUp(self):
        self.application = Application.objects.create(name="Test App")
        self.user = User.objects.create(
            email="<EMAIL>",
            application=self.application
        )
        self.client.force_authenticate(user=self.user)

    def test_create_product(self):
        data = {
            'name': 'Test Product',
            'external_id': 'TEST001',
            'category': self.category.id
        }

        response = self.client.post('/api/v1/products/', data)

        self.assertEqual(response.status_code, 201)
        self.assertEqual(Product.objects.count(), 1)
```

---

## 13. Performance Considerations

### 13.1 Database Performance

**Query Optimization Techniques**:
```python
# Efficient queryset with prefetch
products = Product.objects.select_related('category', 'brand').prefetch_related(
    'product_offers__merchant',
    'product_offers__variations',
    'images'
).filter(
    product_offers__merchant__application=application
).distinct()

# Use database functions for aggregation
from django.db.models import Count, Avg, Min, Max

product_stats = Product.objects.aggregate(
    total_products=Count('id'),
    avg_price=Avg('product_offers__price'),
    min_price=Min('product_offers__price'),
    max_price=Max('product_offers__price')
)
```

**Database Indexing Strategy**:
```python
class Product(models.Model):
    external_id = models.CharField(max_length=255, unique=True, db_index=True)
    name = models.CharField(max_length=255, db_index=True)
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)

    class Meta:
        indexes = [
            models.Index(fields=['name', 'category']),
            models.Index(fields=['created_at', 'status']),
            models.Index(fields=['application', 'status', 'created_at']),
        ]
```

### 13.2 Caching Strategies

**Redis Caching Patterns**:
```python
# Cache frequently accessed data
def get_application_config(application_id):
    cache_key = f"app_config:{application_id}"
    config = cache.get(cache_key)

    if config is None:
        config = Application.objects.get(id=application_id).get_config()
        cache.set(cache_key, config, timeout=3600)  # 1 hour

    return config

# Cache invalidation
def invalidate_product_cache(product_id):
    cache_keys = [
        f"product:{product_id}",
        f"product_offers:{product_id}",
        f"product_search:{product_id}"
    ]
    cache.delete_many(cache_keys)
```

---

## 14. Security Best Practices

### 14.1 API Security

**Input Validation**:
```python
# Serializer validation
class ProductSerializer(serializers.ModelSerializer):
    class Meta:
        model = Product
        fields = ['name', 'description', 'external_id']

    def validate_external_id(self, value):
        if not re.match(r'^[A-Z0-9_-]+$', value):
            raise serializers.ValidationError(
                "External ID must contain only uppercase letters, numbers, hyphens, and underscores"
            )
        return value
```

**Rate Limiting**:
```python
# API rate limiting
from django_ratelimit.decorators import ratelimit

@ratelimit(key='user', rate='100/h', method='POST')
def create_product(request):
    # Product creation logic
    pass
```

### 14.2 Data Protection

**Sensitive Data Handling**:
```python
# Encrypt sensitive fields
from django_cryptography.fields import encrypt

class PaymentMethod(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    card_number = encrypt(models.CharField(max_length=20))
    expiry_date = encrypt(models.CharField(max_length=7))

    def get_masked_card_number(self):
        """Return masked card number for display"""
        return f"****-****-****-{self.card_number[-4:]}"
```

---

## Conclusion

The IZBERG Core architecture represents a sophisticated, scalable, and secure marketplace platform designed to handle complex multi-vendor e-commerce scenarios. Key architectural strengths include:

**Scalability**: Microservices architecture with horizontal scaling capabilities
**Security**: Multi-layered security with encryption, authentication, and authorization
**Flexibility**: Modular design allowing for customization and extension
**Performance**: Optimized data access patterns and caching strategies
**Maintainability**: Clean code organization and comprehensive testing

This architecture enables IZBERG Core to serve as a robust foundation for enterprise marketplace platforms while maintaining the flexibility to adapt to evolving business requirements.
