# IZBERG Core - Local-Only Development Setup

## Overview

This guide shows you how to set up IZBERG Core for completely local development, using AWS credentials only for the initial Docker image pull from ECR. After setup, the application runs entirely offline using local alternatives to AWS services.

## Prerequisites

1. **Docker & Docker Compose** installed and running
2. **AWS CLI** configured with IZBERG credentials (for initial image pull only)
3. **Git** with access to the IZBERG repository

## Quick Start

### 1. One-Time Setup (Requires AWS Credentials)

```bash
# Clone the repository (if not already done)
git clone --recursive **************:izberg-marketplace/izberg-core.git
cd izberg-core

# Configure AWS credentials (one-time only)
aws configure
# Enter your IZBERG-provided AWS credentials

# Pull Docker images from ECR (one-time only)
./start-local-dev.sh --pull-images
```

### 2. Start Local Development Environment

```bash
# Start all services locally
./start-local-dev.sh --setup-data

# This will:
# - Start PostgreSQL, Redis, DynamoDB Local, and MinIO
# - Start all API services with local configuration
# - Run database migrations
# - Create initial test data
```

### 3. Verify Setup

```bash
# Verify everything is working locally
./verify-local-setup.sh
```

## What's Included in Local Setup

### Local Services (No AWS Required)

- **PostgreSQL**: Main database (port 5432)
- **Redis**: Caching and sessions (port 6379)
- **DynamoDB Local**: NoSQL database (port 8008)
- **MinIO**: S3-compatible file storage (ports 9000, 9001)
- **Elasticsearch**: Search engine (port 9200) - optional

### API Services

- **Core API**: `http://api.local.core.izbgtech.com:8080`
- **Identity API**: `http://api.local.izberg.me:8081`
- **Domains API**: `http://api.local.domains.izbgtech.com:8082`

### Local Storage

- **Media Files**: `./media/` directory
- **Database Data**: Docker volumes
- **Cache Data**: Redis in-memory

## Configuration Files

### Environment Configuration (`.env.local`)

The local environment file configures all services to use local alternatives:

```bash
# Django settings
DJANGO_SETTINGS_MODULE=iceberg.settings_local_dev
DEBUG=true

# Local database
DATABASE_URL=******************************************/izberg_core

# Local DynamoDB
DYNAMODB_ENDPOINT=http://dynamodb:8000
AWS_ACCESS_KEY_ID=fake
AWS_SECRET_ACCESS_KEY=fake

# Local file storage
DEFAULT_FILE_STORAGE=django.core.files.storage.FileSystemStorage

# Disable AWS services
USE_SQS_QUEUE=false
CELERY_BROKER_TRANSPORT=memory
```

### Docker Compose Configuration (`docker-compose.local.yml`)

The local Docker Compose file includes:

- All infrastructure services with persistent volumes
- MinIO as S3 replacement
- Local DynamoDB
- API services with local environment variables

### Django Settings (`iceberg/settings_local_dev.py`)

Local Django settings that override AWS configurations:

- Local file storage instead of S3
- Memory-based Celery instead of SQS
- Console email backend
- Disabled external service integrations

## Manual Setup Steps

If you prefer to set up manually:

### 1. Copy Configuration Files

```bash
# Copy local environment
cp .env.local .env

# Create media directory
mkdir -p ./media
chmod 755 ./media
```

### 2. Start Infrastructure Services

```bash
# Use local Docker Compose configuration
export COMPOSE_FILE=docker-compose.local.yml

# Start infrastructure
docker compose up -d postgres redis dynamodb minio

# Setup MinIO buckets
docker compose up -d minio-setup
```

### 3. Start API Services

```bash
# Start supporting APIs
docker compose up -d api.local.domains.izbgtech.com api.local.izberg.me

# Start Core API
docker compose up -d api.local.core.izbgtech.com
```

### 4. Setup Database

```bash
# Run migrations
docker compose exec api.local.core.izbgtech.com python manage.py migrate

# Create initial data
docker compose exec api.local.core.izbgtech.com invoke setup --applications 1 --merchants 3 --orders 1
```

## Development Workflow

### Daily Development

```bash
# Start all services
./start-local-dev.sh

# View logs
docker compose logs -f

# Stop all services
docker compose down
```

### Running Django Locally (Alternative)

If you prefer to run the Django application locally instead of in Docker:

```bash
# Stop the containerized API
docker compose stop api.local.core.izbgtech.com

# Activate virtual environment
source .venv/bin/activate

# Set environment variables
export DJANGO_SETTINGS_MODULE=iceberg.settings_local_dev
export DATABASE_URL=postgresql://iceberg:iceberg@localhost:5432/izberg_core
export REDIS_URL=redis://localhost:6379/0
export DYNAMODB_ENDPOINT=http://localhost:8008

# Run Django development server
cd iceberg
python manage.py runserver api.local.core.izbgtech.com:8080
```

### Useful Commands

```bash
# View service logs
docker compose logs -f [service_name]

# Access Django shell
docker compose exec api.local.core.izbgtech.com python manage.py shell

# Access database
docker compose exec postgres psql -U iceberg -d izberg_core

# Access Redis CLI
docker compose exec redis redis-cli

# Access MinIO console
# Open http://localhost:9001 (minioadmin/minioadmin123)

# Restart specific service
docker compose restart api.local.core.izbgtech.com
```

## Access Points

### Web Interfaces

- **Django Admin**: `http://api.local.core.izbgtech.com:8080/_a_d_m_i_n_/`
  - Username: `<EMAIL>`
  - Password: `admin`

- **API Documentation**: `http://api.local.core.izbgtech.com:8080/api/docs/`

- **MinIO Console**: `http://localhost:9001`
  - Username: `minioadmin`
  - Password: `minioadmin123`

### API Endpoints

- **Core API**: `http://api.local.core.izbgtech.com:8080/api/v1/`
- **Identity API**: `http://api.local.izberg.me:8081/api/v1/`
- **Domains API**: `http://api.local.domains.izbgtech.com:8082/api/v1/`

## Troubleshooting

### Common Issues

**Containers not starting:**
```bash
# Check Docker is running
docker info

# Check logs
docker compose logs [service_name]

# Restart services
docker compose down && docker compose up -d
```

**Port conflicts:**
```bash
# Check what's using ports
sudo netstat -tulpn | grep :8080

# Kill conflicting processes
sudo kill -9 $(sudo lsof -t -i:8080)
```

**Database connection issues:**
```bash
# Reset database
docker compose down -v
docker compose up -d postgres
sleep 10
docker compose exec api.local.core.izbgtech.com python manage.py migrate
```

**File permission issues:**
```bash
# Fix media directory permissions
sudo chown -R $USER:$USER ./media
chmod 755 ./media
```

### Verification Steps

Run the verification script to check everything is working:

```bash
./verify-local-setup.sh
```

This will check:
- All containers are running
- Services are accessible
- Local storage is configured
- No AWS service calls are being made

## Benefits of Local Setup

✅ **Complete Offline Development**: Work without internet after initial setup
✅ **No AWS Costs**: No charges for development usage
✅ **Fast Development**: Local services are faster than remote AWS services
✅ **Data Privacy**: All data stays on your local machine
✅ **Consistent Environment**: Same setup across all developers
✅ **Easy Reset**: Simple to reset data and start fresh

## Next Steps

After successful setup:

1. **Explore the Admin Interface**: Familiarize yourself with Django admin
2. **Test API Endpoints**: Use the API documentation to test functionality
3. **Create Sample Data**: Use the setup commands to create test data
4. **Start Development**: Begin working with the codebase locally

Your local development environment is now completely independent of AWS services and ready for development!
