    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%,%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%/  %%%%%%%%%%%%%%%%%%%%%%%%%%%%
    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%#    (%%%%%%%%%%%%%%%%%%%%%%%%%%
    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%( .%%%%%      *%%%%%%%%%%%%%%%%%%%%%%%%%
    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%/     %%        .%%%%%%%%%%%%%%%%%%%%%%%%
    %%%%%%%%%%%%%%%%%%%%%%%#.%%%*                  %%%%%%%%%%%%%%%%%%%%%%%
    %%%%%%%%%%%%%%%%%%%%%%/    .                    %%%%%%%%%%%%%%%%%%%%%%
    %%%%%%%%%%%%%%%%%%%%%,                           #%%%%%%%%%%%%%%%%%%%%
    %%%%%%%%%%%%%%%%%%%%.                             /%%%%%%%%%%%%%%%%%%%
    %%%%%%%%%%%%%%%%%%%////////////////////////////////(%%%%%%%%%%%%%%%%%%
    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
    %%%%%%%%%%%%%%%%                                      .%%%%%%%%%%%%%%%
    %%%%%%%%%%%%%%%%%.                                   /%%%%%%%%%%%%%%%%
    %%%%%%%%%%%%%%%%%%*                                 #%%%%%%%%%%%%%%%%%
    %%%%%%%%%%%%%%%%%%%#                               %%%%%%%%%%%%%%%%%%%
    %%%%%%%%%%%%%%%%%%%%%                             %%%%%%%%%%%%%%%%%%%%
    %%%%%%%%%%%%%%%%%%%%%%                (*        .%%%%%%%%%%%%%%%%%%%%%
    %%%%%%%%%%%%%%%%%%%%%%%.             *%%*      *%%%%%%%%%%%%%%%%%%%%%%
    %%%%%%%%%%%%%%%%%%%%%%%%*            %%%%*    (%%%%%%%%%%%%%%%%%%%%%%%
    %%%%%%%%%%%%%%%%%%%%%%%%%(          %%%%%%,  %%%%%%%%%%%%%%%%%%%%%%%%%
    %%%%%%%%%%%%%%%%%%%%%%%%%%#        %%%%%%%%,%%%%%%%%%%%%%%%%%%%%%%%%%%
    %%%%%%%%%%%%%%%%%%%%%%%%%%%%      #%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%    ,%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%, .%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%/%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

                        ╭╮╭╮╭┳━━━┳╮╱╱╭━━━┳━━━┳━╮╭━┳━━━╮
                        ┃┃┃┃┃┃╭━━┫┃╱╱┃╭━╮┃╭━╮┃┃╰╯┃┃╭━━╯
                        ┃┃┃┃┃┃╰━━┫┃╱╱┃┃╱╰┫┃╱┃┃╭╮╭╮┃╰━━╮
                        ┃╰╯╰╯┃╭━━┫┃╱╭┫┃╱╭┫┃╱┃┃┃┃┃┃┃╭━━╯
                        ╰╮╭╮╭┫╰━━┫╰━╯┃╰━╯┃╰━╯┃┃┃┃┃┃╰━━╮
                        ╱╰╯╰╯╰━━━┻━━━┻━━━┻━━━┻╯╰╯╰┻━━━╯



# Index

Links to other documentation:

- [Views and serializer](/iceberg/drf_izberg/views/README.md)
- [Tests](/iceberg/drf_izberg/tests/README.md)
- [Permissions](/iceberg/drf_izberg/permissions/README.md)
- [Filters](/iceberg/drf_izberg/filters/README.md)
- [Paginations](/iceberg/drf_izberg/paginations/README.md)


# Tastypie to Django rest framework

This chapter will explain the tips and tricks of migrating a Tastypie resource
to Django rest framework

> It is not a complete guide.

### Half views and serializer

If your resource has any foreign key towards un-migrated resources, you will need to
create a view and serializer for all of them. They will not be complete (actions, post, delete ect)
and therefore, not ready to be consumed by any front.
Drf needs these view because when generating the resource_uri of any relationship, it will
perform a reverse on the "view-detail" of that resource. But we don't want to serve these
resources to our front.
So we created a middleware that will check the request and based on the path, will change
the urlsconf. Inside this middleware we maintain a list of the resources that are only partially
migrated and SHOULD NOT be served to our client through DRF but using Tastypie.

> Do not forget to update this list if needed.

``` Python
RESOURCE_PATH_STUB_MIGRATED_TO_DRF = [
    "/v1/application,
    "/v1/user",
]
```
To learn more about it, read the comment in: `drf_izberg/middlewares.py`


### Filtering

Tastypie offer a simple way of providing query filtering through url parameters.
With DRF, we need a plugin (`Django-filters`) and it is a bit more verbose.
For each field that need filtering, you will need to declare it in a class.
This class is then attached to the view `filterset_class = AddressFilter`

Simple example:
```Python
class StateFilter(filters.FilterSet):
    country__lt = filters.NumberFilter(field_name="country", lookup_expr="lt")
    country__lte = filters.NumberFilter(field_name="country", lookup_expr="lte")
    country__gt = filters.NumberFilter(field_name="country", lookup_expr="gt")
    country__gte = filters.NumberFilter(field_name="country", lookup_expr="gte")
    country__in = NumberInFilter(field_name="country", lookup_expr="in")

    class Meta:
        model = State
        fields = [
            "country",
        ]
```



### Filtering by application and merchant

Our API depends heavily on the separation by Application.
To put it simply, we don't want a client to access the order/cart/... of another application.  

So it is important to add this filtering on each view that needs it:

Example:
```Python
filter_backends = [
    ApplicationFilterBackend,
    MerchantFilterBackend,
    DjangoFilterBackend,
    OrderingFilter,
]
# path to the application using django query.
application_filter_relation = "application_id"
merchant_filter_relation = "merchant_order__merchant_id"
```


### Testing Schema

When migrating a Tastypie resource to DRF, you want to test that the schema didn't change, at least the keys.
But you cannot simply use the generated schema json (`apps/testing/resources_schemas_cache/en-US/XXXX.json`) as a base for you test.
Some values that were not correct from Tastypie (blank, default ect) will be changed with DRF.

> We also have added a new attribute in each fields: `required`
> This will also cause a change in our schema compared to Tastypie schema.

The best way to proceed is to check the difference between DRF schema and the previous `.json`.
If there is a difference:
- Check if DRF schema is correct (Check the model, Tastypie resource, DRF serializer)
  Most of the time, you will want to match the values in the model.
- If DRF Schema matches the model, you are good to use this schema.
- If not, you might have found a bug in our Schema generator.



### Testing Serializers

Testing our serializer assure that our API is outputing the correct payload for a given resource.
Each field must be tested using the value of the field on the instance of the model used.

We also check the validation of data.
Given a payload, serializing it and saving it should generate a correct instance of the model.

> To see a concrete example, check `apps/address/tests/address/test_api_serializers.py`



# TODO

- _is_decommissioned
- Deprecated field (schema and FILTER/ORDERING)
