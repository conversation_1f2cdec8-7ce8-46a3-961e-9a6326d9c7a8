# Resources

This file contains the list of all resources within our API.
We also indicate if the resource has been migrated to DRF.

> [X] indicate that the resource has been migrated
> [P] indicate that the resource has been partially migrated.
      Reason could be that some resources inside should be moved elsewhere, or might be
      dropped etc.


## Apps folder:

List of resources in apps/

### [X] address

**address**:

- Has custom endpoint:
    * address/actives/
- Has custom filtering:
    * user
    * user__lt
    * user__lte
    * user__gt
    * user__gte
    * user__in
    * application
    * external_id
    * status
    * status__in
- Has full representation:
    * state
- Has potential full representation (through ?full_xxx=1):
    * user
    * application


**country**:

- Has custom endpoint:
    * country/all/
    * country/languages/
- Has custom filtering:
    * code
    * sort_order
    * sort_order__lt
    * sort_order__lte
    * sort_order__gt
    * sort_order__gte
    * sort_order__in
    * weight
    * weight__lt
    * weight__lte
    * weight__gt
    * weight__gte
    * weight__in
    * name

**state**:

- Has custom filtering:
    * country
    * country__lt
    * country__lte
    * country__gt
    * country__gte
    * country__in



### [X] assets

**Image**

- Has custom filtering:
    * status
    * status__in
    * merchant
    * merchant__in
    * merchant__isnull
    * file_name__icontains

- Has full representation:
    * application
    * merchant

### [ ] attributes
- attribute resources allow filtering on `localized_infos` which is actually not exposed (see `use_in=lambda bundle: False` hack), but probably doable as a custom filter in DRF


### [ ] cart
- the `mine` logic is (hopefully) deprecated and don't need migration

### [ ] cart_notifications

### [X] categories

**Application_category**:

- Has custom endpoint:
    * application-category/can-delete/
- Has custom filtering:
    * id
    * id__in
    * parents
    * parents__isnull

**App_localized_category**:

- Has custom filtering:
    * id
    * id__in
    * category
    * language
    * language__in

**Locale_config**:

- Has custom endpoint:
    * application-category-locale-config/all/
- Has custom filtering:
    * id

### [ ] channels
### [X] companies

**company**:

- Has custom filtering:
    * id
    * id__in
    * application
    * merchant
    * country
    * vat_number
    * siret
    * code_naf
- Has full representation:
    * country
    * merchant

### [X] currencies

**currency**:

- Has custom filtering:
    * code
    * code__in

### [ ] deprecation


### [P] ice_applications

**application**:

- Has custom endpoint:
    * application/mine/
    * application/<id>/settings/
    * application/<id>/activate/
    * application/<id>/pause/
    * application/<id>/link-domain/
    * application/<id>/unlink-domain/
    * application/<id>/reset-pim-data/

- Has custom filtering:
    * id
    * id__in
    * contact_user
    * namespace
    * name
    * status
    * status__in
    * created_on
    * last_modified
    * domain_id
- Has full representation:
    * contact_user
    * default_currency


**application_configuration**:

- Has custom endpoint:
    * application_configuration/<id>/settings/
- Has custom filtering:
    * status
    * status__in
    * id
    * application
- Has full representation:
    * settings


**application_settings**:

- Has custom filtering:
    * application
    * application_configuration
    * application_configuration__status
    * application_configuration__status__in
    * key
    * key__in


⚠️ Resources that needs to be moved in another resource:
**! ApplicationBalanceResource**.
**! AppBalanceTransactionResource**
**! ApplicationCommissionSettingsResource**
**! ApplicationUserPermissionResource**
**! ApplicationPaymentSettingsResource**





### [ ] invoices
### [ ] kyc
### [X] marketplace_permissions

**permission summary**

- Has custom endpoint:
    * permission/permissions_list

- Has custom filtering:
    * id
    * user
    * merchant
    * active
    * full_access

- Has full representation:
    * user
    * merchant

### [X] mp_messages

**message**

- Has custom endpoint:
    * message/<id>/reply/
    * message/<id>/close/
    * message/<id>/read/
    * message/<id>/unread/
    * message/<id>/toggle-starred/
- Has custom filtering:
    * id
    * id__in
    * attachment_count
    * attachment_count__lt
    * attachment_count__lte
    * attachment_count__gt
    * attachment_count__gte
    * from_application
    * from_merchant
    * from_user
    * to_application
    * to_merchant
    * to_user
    * to_id
    * status
    * status__in
    * application_id
    * application
    * starred
    * merchant_order
    * root_msg

**message-attachment**

- Has custom endpoint:
    * message-attachment/<id>/download/
- Has custom filtering:
    * id
    * id__in
    * message

**message-label**

- Has custom filtering:
    * id
    * id__in
    * key
    * name

**message-type**

- Has custom filtering:
    * id
    * id__in
    * key
    * name

### [ ] orders

- /v1/merchant_order/ is the most requested API resource on Core (~4M calls per day) and highly rely on tastypie caching mechanism >> should either do a 'big vache' to make it efficient in DRF (killing N+1 & on-the-fly computed fields), or reimplement the caching mechanism
- merchant_order resource allows filtering on `items` which is actually not exposed (see `use_in=lambda bundle: False` hack), but probably doable as a custom filter in DRF

### [ ] payment

**payment**:

- Has custom endpoint:
    * payment/<id>/cancel/
    * payment/<id>/collect/
    * payment/<id>/authorize/
    * payment/<id>/pending_authorization/
    * payment/<id>/backend_form_data/
    * payment/<id>/backend_raw_data/
    * payment/<id>/backend_summary/
    * payment/<id>/summary/
    * payment/<id>/check_backend_consistency/
    * payment/<id>/create_transactions_if_needed/

- Has custom filtering:
    * id
    * id__in
    * status
    * order
    * payment_method
    * external_id
    * customer_invoice

- TODO :
    * merchant_orders : implement or drop it (problematic field with performance impact)
    * transactions : backbone_friendly_extra_fields & read_only=True
    * payment_method : backbone_friendly_extra_fields & read_only=True

**payment-term**:

- Has custom endpoint:
    * payment-term/<id>/activate/
    * payment-term/<id>/deactivate/

- Has custom filtering:
    * id
    * id__in
    * application

**payment-term-line**:

- Has custom filtering:
    * id
    * id__in
    * payment_term

**payment-method**

- Has custom filtering:
    * id
    * id__in
    * code
    * code_in


### [ ] products

### [X] promotions

**discount**:

- Has custom endpoint:
    * discount/<id>/pause/
    * discount/<id>/activate/

- Has custom filtering:
    * id
    * id__in
    * status
    * status__in
    * application
    * merchant
    * reduction_type
    * reduction_type__in
    * merchant__isnull
    * discount_code
    * name
    * external_id

**cart-item-external-discount**:

- Has custom filtering:
    * id
    * id__in
    * cart_item
    * cart_item__in
    * discount_code
    * external_id

**order-item-external-discount**:

- Has custom filtering:
    * id
    * id__in
    * order_item
    * order_item__in
    * discount_code
    * external_id

### [ ] returns
### [ ] reviews
### [ ] shipping
### [ ] slip
### [ ] stores
### [ ] tasking
### [ ] tax
### [ ] transactions
### [X] user

**user**:

- Has custom endpoint:
    * user/batch-anonymization/
    * user/<id>/inbox/
    * user/<id>/outbox/

- Has custom filtering:
    * id
    * id__in
    * external_id
    * external_id__in
    * application
    * username
    * username__icontains
    * email
    * first_name
    * last_name
- Has full representation:
    * application

### [X] user-profile

**user-profile**:

- Has custom filtering:
    * id__in
    * application
    * user
    * company_name__icontains

### [ ] webhooks
### [ ] workflows

## Module folder:

### [X] commissions

**ApplicationCommissionSettings**:
- Has custom filtering:
    * application

- Has full representation:
    * application
    * merchant

**CommissionLine**:
- Has custom filtering:
    * id
    * id__in
    * status
    * status__in
    * merchant_order
    * order_item
    * invoice_line
    * invoice
    * created_on

- Has full representation:
    * application
    * order_item
    * merchant_order
    * invoice_line
    * invoice
    * commission_currency
    * applied_item_commission_rule
    * applied_merchant_commission_rule

**ItemCommissionRule**:

- Has custom endpoint:
    * order-item-commission-rule/<id>/activate/
    * order-item-commission-rule/<id>/deactivate/

- Has custom filtering:
    * id
    * id__in
    * status
    * status__in
    * name
    * priority
    * external_id
    * starting_date
    * ending_date
    * commission_method
    * behaviour_type
    * min_threshold
    * max_threshold
    * product_families
    * product_categories
    * product_brands
    * origin_countries
    * destination_countries
    * merchants
    * application

- Has full representation:
    * application
    * merchants
    * product_families
    * product_brands
    * product_categories
    * origin_countries
    * destination_countries
    * price_currency

**MerchantCommissionRule**:

- Has custom endpoint:
    * merchant-order-commission-rule/<id>/activate/
    * merchant-order-commission-rule/<id>/deactivate/

- Has custom filtering:
    * id
    * id__in
    * status
    * status__in
    * name
    * priority
    * external_id
    * starting_date
    * ending_date
    * commission_method
    * behaviour_type
    * min_threshold
    * max_threshold
    * origin_countries
    * destination_countries
    * merchants
    * application
- Has full representation:
    * application
    * merchants
    * origin_countries
    * destination_countries
    * price_currency

**MerchantLicenceRule**:

- Has custom endpoint:
    * merchant-licence-rule/<id>/subscribe_merchant/
    * merchant-licence-rule/<id>/unsubscribe_merchant/

- Has custom filtering:
    * id
    * id__in
    * status
    * status__in
    * name
    * name__icontains
    * priority
    * external_id
    * starting_date
    * ending_date
    * pricing_method
    * min_threshold
    * max_threshold
    * merchants
    * merchant_groups
    * application
- Has full representation:
    * application
    * merchants
    * merchant_groups
    * licence_currency

### [X] invoicing

**CreditNote**:
- Has custom filtering:
    * application
    * status
    * status__in
    * external_status
    * external_status__in
    * payment_status
    * payment_status__in
    * issuer
    * receiver
    * invoice


- Has full representation:
    * application
    * issuer
    * receiver
    * uploaded_file
    * currency

**CreditNoteLine**
- Has custom filtering:
    * credit_note

- Has full representation:
    * credit_note
    * invoice_line
    * order_item
    * merchant_order

**CustomerInvoice**
- Has custom filtering:
    * application
    * id
    * id_number
    * id_number__in
    * status
    * status__in
    * payment_status
    * issuer
    * receiver
    * external_status

- Has full representation:
    * application
    * issuer
    * receiver
    * uploaded_file
    * currency

**CustomerInvoiceLine**

**CustomerInvoiceTransitionLog**
- Has custom filtering:
    * invoice

- Has full representation:
    * invoice
    * user

**ExternalCreditNoteFile**

**ExternalInvoiceFile**


### [ ] mailer
### [ ] mailer_trigger
### [ ] mapper

- the `/v1/mapper_entity/` resource exposes static data, not a django model

- for performance reason, the `meta.total_count` on `/v1/mapper_transformation_log/` is handled manually using counters of the object

### [ ] merchant_groups
### [ ] shipping2
### [ ] uploader
### [ ] zone
