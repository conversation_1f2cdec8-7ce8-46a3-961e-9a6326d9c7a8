# Deprecation

We can deprecate a field or a method from any resources. To do so, add on the view a 
deprecation dict with the list of fields and/or endpoints you need to deprecate. 
For each you need to specify the deprecated_key. Like so:

    deprecation = {
        "fields": {
            "marketplace_type": DeprecatedFeature.SERVICE_LINKS,
        },
        "urls": {
            "mine_view": DeprecatedFeature.SERVICE_LINKS,
        }
    }

Then, based on what you need to deprecate, read the following:


## Field: 

To deprecate a field use in a serializer, you must add on the given serializer this
mixin: `DeprecatedSerializerAttributesMixin`.
This mixin will remove the field which is specified in the `deprecation` dict on the 
view if the date has passed the deprecation date. 
Meaning, the input and output will completely ignore each fields specified in the 
`deprecation` dict.

Note: The mixin has to be placed before `IzbergHyperlinkedModelSerializer`, like so:

    class ApplicationSerializer(
        DeprecatedSerializerAttributesMixin,
        IzbergHyperlinkedModelSerializer,
    ):



## Filtering: 

To deprecate a field use in a filter, you must add on the given filter this
mixin: `DeprecatedFilterAttributesMixin`.
This mixin will remove the field which is specified in the `deprecation` dict on the 
view if the date has passed the deprecation date.
Meaning, the filter will completely ignore each fields specified in the 
`deprecation` dict.

Note: The mixin has to be placed before `filters.FilterSet`, like so:

    class ApplicationFilter(
        DeprecatedFilterAttributesMixin,
        filters.FilterSet,
    ):



## Ordering: 

To deprecate a field use in the ordering, you must add on the view this mixin: 
`DeprecatedOrderingAttributesMixin`.
This mixin will remove the field which is specified in the `deprecation` dict on the 
view if the date has passed the deprecation date. 
Meaning, the ordering from the view will completely ignore each fields specified in the 
`deprecation` dict.

Note: The mixin has to be placed before `IzbergModelViewSet`, like so:

    class ApplicationViewSet(
        DeprecatedOrderingAttributesMixin,
        IzbergModelViewSet,
    ):



## Custom endpoints: 

To deprecate a custom endpoint use in the view, you must add on the view this mixin: 
`DeprecatedCustomEndpointsMixin`.
This mixin will remove the endpoint which is specified in the `deprecation` dict on the 
view if the date has passed the deprecation date. 
Meaning, the view will completely ignore a request with an url name specified in the 
`deprecation` dict.

Note: The mixin has to be placed before `IzbergModelViewSet`, like so:

    class ApplicationViewSet(
        DeprecatedCustomEndpointsMixin,
        IzbergModelViewSet,
    ):

Note 2: the key used in `deprecation` dict must be the name of the method to be deprecated:

    deprecation = {
        "urls": {
            "settings_view": DeprecatedFeature.SERVICE_LINKS,
        }
    }

## Schema: 

THe schema is a special case. It has to update the fields, ordering, filtering and 
custom endpoints (named methods).
Like above, you must add on the view this mixin: `DeprecatedSchemaMixin`
Based on what is specified in the `deprecation` dict on the view AND the date for each 
key, we have 2 cases:


### Case with a date of deprecation in the future:

- fields: Their value is updated with the following:


    {
        "information": "This field is deprecated",
        "decommission_date": "2022-08-11T10:00:00Z",
    }

- filtering: No changes
- ordering: No changes

- custom endpoints: Their value is updated with the following:


    {
        "regex": "/v1/application/fake_custom_endpoint/",
        "doc_text": "",
        "doc_html": "",
        "allowed_http_methods": ["get"],
        "information": "This endpoint is deprecated",
        "decommission_date": "2022-08-11T10:00:00Z",
    }



### Case with a date of deprecation in the past:

- fields: The field is deleted from the schema
- filtering: The value is deleted from the schema
- ordering: The value is deleted from the schema
- custom endpoints: The value is deleted from the schema




Note: The mixin has to be placed before `IzbergModelViewSet`, like so:

    class ApplicationViewSet(
        DeprecatedSchemaMixin,
        IzbergModelViewSet,
    ):
