from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from drf_izberg.deprecation.fetcher import DeprecatedFetcher
from rest_framework.exceptions import NotFound


class DeprecatedCustomEndpointsMixin:
    """
    This mixin is to be added on any view that need to deprecate a custom endpoint.
    It has to be placed BEFORE anything else, the order matters.
    This mixin will return a 404 based on the path of the current request if it is
    deprecated.
    """

    def initial(self, request, *args, **kwargs):
        deprecation = self.deprecation.get("urls")

        if not deprecation:
            return super().initial(request, *args, **kwargs)

        now = timezone.now()
        for endpoint_action, deprecated_key in deprecation.items():
            deprecated_date = DeprecatedFetcher(request).retrieve_date(deprecated_key)

            if (
                endpoint_action == self.action
                and deprecated_date
                and now > deprecated_date
            ):
                raise NotFound(detail=_("This action doesn't exist"))

        return super().initial(request, *args, **kwargs)
