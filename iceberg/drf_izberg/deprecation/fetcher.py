from apps.deprecation.models import DeprecatedFeature


class DeprecatedFetcher:
    """
    Simple class that offer a way to retrieve decommission date from a deprecated
    feature that matches the given key. It also caches the result of the query
    DeprecatedFeature.objects.all() into the request to avoid multiple calls of the DB.
    """

    def __init__(self, request):
        self.request = request

    def _get_or_create_cache(self):
        if not hasattr(self.request, "_cache_deprecated_features"):
            # cache the list of deprecated features on the request for the
            # duration of his lifetime
            self.request._cache_deprecated_features = list(
                DeprecatedFeature.objects.all()
            )

        return self.request._cache_deprecated_features

    def retrieve_date(self, key):
        for deprecated_feature in self._get_or_create_cache():
            if deprecated_feature.key == key:
                return deprecated_feature.decommission_after

        return None
