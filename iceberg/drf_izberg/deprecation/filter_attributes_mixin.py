from django.utils import timezone
from drf_izberg.deprecation.fetcher import DeprecatedFetcher


class DeprecatedFilterAttributesMixin:
    """
    This mixin is to be added on any filters that need to deprecate a field.
    It has to be placed BEFORE anything else, the order matters.
    This mixin will pop out any field from the filter listed in self.deprecation with a
    date of deprecation past now()
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # To retrieve the view we are using the parser_context that is used by DRF.
        # There is no other clean way to retrieve the view from here.
        view = kwargs["request"].parser_context["view"]
        deprecation = view.deprecation.get("fields")

        if deprecation:
            # make copies of base_filters and declared_filters because the filter is
            # never instantiated. Therefore, any change on these properties are
            # permanent.
            if not hasattr(self, "_copy_base_filters"):
                self._copy_base_filters = self.base_filters
            if not hasattr(self, "_copy_declared_filters"):
                self._copy_declared_filters = self.declared_filters

            # Assign the copy to the real properties before any potential changes.
            self.base_filters = self._copy_base_filters.copy()
            self.declared_filters = self._copy_declared_filters.copy()

            now = timezone.now()
            for field_name, deprecated_key in deprecation.items():
                deprecated_date = DeprecatedFetcher(kwargs["request"]).retrieve_date(
                    deprecated_key
                )

                if deprecated_date and now > deprecated_date:
                    self.base_filters.pop(field_name, None)
                    self.declared_filters.pop(field_name, None)
