from django.utils import timezone
from drf_izberg.deprecation.fetcher import DeprecatedFetcher


class DeprecatedOrderingAttributesMixin:
    """
    This mixin is to be added on any view that need to deprecate a field listed in
    ordering_fields. It has to be placed BEFORE anything else, the order matters.
    This mixin will pop out any field from self.ordering_fields listed in
    self.deprecation with a date of deprecation past now()
    """

    def initial(self, request, *args, **kwargs):
        """
        Since we are overriding the behavior of the view and we don't have access to the
        request when the view is just instantiated, we are using the initial method that
        is called in each dispatch and offer a good way to access the request.
        Request that is used for the DeprecatedFetcher that offer caching and therefore
        avoid to call the DB for the list of deprecated feature
        """
        deprecation = self.deprecation.get("fields")
        if not deprecation:
            return super().initial(request, *args, **kwargs)

        # make copies of ordering_fields because this an attribute of a class.
        # Therefore, any change on these properties are permanent.
        if not hasattr(self, "_ordering_fields"):
            self._ordering_fields = self.ordering_fields

        # Assign the copy to the real properties before any potential changes.
        self.ordering_fields = self._ordering_fields.copy()

        now = timezone.now()
        for field_name, deprecated_key in deprecation.items():
            deprecated_date = DeprecatedFetcher(request).retrieve_date(deprecated_key)

            if (
                deprecated_date
                and now > deprecated_date
                and field_name in self.ordering_fields
            ):
                self.ordering_fields.remove(field_name)

        return super().initial(request, *args, **kwargs)
