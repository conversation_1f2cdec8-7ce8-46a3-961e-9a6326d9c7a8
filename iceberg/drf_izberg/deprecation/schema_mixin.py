from django.utils import timezone
from drf_izberg.deprecation.fetcher import DeprecatedFetcher


class DeprecatedSchemaMixin:
    """
    This mixin is to be added on any view that need to deprecate a field, filtering,
    ordering or endpoint.
    It has to be placed BEFORE anything else, the order matters.
    This mixin will update the schema based on what is listed in deprecation
    """

    def _update_schema_fields(self, now, request, deprecation_fields, schema_data):
        for field_name, deprecated_key in deprecation_fields.items():
            deprecated_date = DeprecatedFetcher(request).retrieve_date(deprecated_key)

            if deprecated_date and now > deprecated_date:
                if field_name in schema_data["fields"].keys():
                    del schema_data["fields"][field_name]
                # if field_name in schema_data["ordering"]:
                #     del schema_data["ordering"][field_name]
                if field_name in schema_data["filtering"].keys():
                    del schema_data["filtering"][field_name]

            elif field_name in schema_data["fields"].keys():
                schema_data["fields"][field_name] = {
                    "information": "This field is deprecated",
                    "decommission_date": deprecated_date,
                }

    def _update_schema_endpoints(self, now, request, deprecation_urls, schema_data):
        for endpoint_action, deprecated_key in deprecation_urls.items():
            deprecated_date = DeprecatedFetcher(request).retrieve_date(deprecated_key)

            new_methods = []
            if deprecated_date and now > deprecated_date:
                for method in schema_data["methods"]:
                    url_path = getattr(self, endpoint_action).url_path
                    # We add a trailing slash to url_path to avoid matching
                    # on urls with a substring of url_path.
                    # ex: v2/configuration/ VS v2/configuration_settings/
                    if f"{url_path}/" not in method["regex"]:
                        # We only keep the endpoints NOT listed in our deprecation dict.
                        new_methods.append(method)
                schema_data["methods"] = new_methods
            else:
                for method in schema_data["methods"]:
                    url_path = getattr(self, endpoint_action).url_path
                    # Using trailing slash as explained above.
                    if f"{url_path}/" in method["regex"]:
                        method["information"] = "This endpoint is deprecated"
                        method["decommission_date"] = deprecated_date

    def generate_schema_data(self, request, serializer_class):
        view = request.parser_context["view"]
        deprecation_fields = view.deprecation.get("fields")
        deprecation_urls = view.deprecation.get("urls")

        schema_data = super().generate_schema_data(request, serializer_class)

        if not deprecation_fields or not deprecation_urls:
            return schema_data

        now = timezone.now()
        self._update_schema_fields(now, request, deprecation_fields, schema_data)
        self._update_schema_endpoints(now, request, deprecation_urls, schema_data)

        return schema_data
