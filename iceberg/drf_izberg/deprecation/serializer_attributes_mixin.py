from django.utils import timezone
from drf_izberg.deprecation.fetcher import DeprecatedFetcher


class DeprecatedSerializerAttributesMixin:
    """
    This mixin is to be added on any serializer that need to deprecate a field.
    It has to be placed BEFORE anything else, the order matters.
    This mixin will pop out any field from the serializer listed in self.deprecation
    with a date of deprecation past now()
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Do nothing we don't have any context. Could be because the serializer
        # was called in the schema generator or in a test
        if "context" in kwargs:
            view = kwargs["context"]["view"]
            deprecation = view.deprecation.get("fields")

            if deprecation:
                now = timezone.now()
                for field_name, deprecated_key in deprecation.items():
                    deprecated_date = DeprecatedFetcher(
                        kwargs["context"]["request"]
                    ).retrieve_date(deprecated_key)

                    if deprecated_date and now > deprecated_date:
                        self.fields.pop(field_name, None)
