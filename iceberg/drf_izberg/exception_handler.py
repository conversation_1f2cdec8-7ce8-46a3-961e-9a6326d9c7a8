from django.core.exceptions import ValidationError as DjangoValidationError
from rest_framework.exceptions import ValidationError as DRFValidationError
from rest_framework.views import exception_handler as drf_exception_handler


def izberg_exception_handler(exc, context):
    """
    Sometimes in your Django model you want to raise a ``ValidationError`` in
    the ``save`` method, for some reason.
    This exception is not managed by Django Rest Framework because it occurs after
    its validation process. So at the end, you'll have a 500.
    Correcting this by overriding the exception handler, converting the Django
    ``ValidationError`` to a DRF one.
    """
    if isinstance(exc, DjangoValidationError):
        try:
            exc = DRFValidationError(detail=exc.message_dict)
        except AttributeError:
            # In some case, validationError doesn't have a dict but simply a message
            # We transform it to a dict with __all__ as key.
            exc = DRFValidationError(detail={"__all__": exc.message})

    if isinstance(exc, DRFValidationError):
        # This section assures that the error detail is the same
        # as django validation Error structure.
        # Compatibility with how <PERSON><PERSON><PERSON> is managing ValidationError
        django_detail = {"errors": []}
        for error in exc.detail:
            django_detail["errors"].append(
                {
                    "field": error,
                    "msg": exc.detail[error],
                }
            )

        exc.detail = django_detail

    return drf_exception_handler(exc, context)
