import decimal
from decimal import Decimal

from django.utils.formats import localize_input
from rest_framework.fields import DecimalField
from rest_framework.settings import api_settings


def remove_exponent(d):
    return d.quantize(Decimal(1)) if d == d.to_integral() else d.normalize()


class NormalizedDecimalField(DecimalField):
    """
    Custom Field used to normalize DecimalField as a workaround
    until https://github.com/encode/django-rest-framework/pull/6514
    is released

    TODO: Drop me and set "NORMALIZE_DECIMAL_OUTPUT": True,
    in REST_FRAMEWORK settings
    """

    def to_representation(self, value):
        coerce_to_string = getattr(
            self, "coerce_to_string", api_settings.COERCE_DECIMAL_TO_STRING
        )

        if value is None:
            if coerce_to_string:
                return ""
            else:
                return None

        if not isinstance(value, decimal.Decimal):
            value = decimal.Decimal(str(value).strip())

        quantized = self.quantize(value)

        quantized = remove_exponent(quantized)

        if not coerce_to_string:
            return quantized
        if self.localize:
            return localize_input(quantized)

        return "{:f}".format(quantized)
