# Filters

Django rest framework offers filtering, but it is not enough for our needs.
To have the same kind of filtering that <PERSON><PERSON><PERSON> is providing we added Django-filter.
It does the same things with the benefit and downside of being more verbose...

We have 2 types of filters:


## filterset: 

They are defined next to the view that use it (check Address) and provide
the same functionality as Tastypie.  
You could even create your own type of filtering if necessary.

ex:

    class AddressFilter(filters.FilterSet):
        user__lt = filters.NumberFilter(field_name="user", lookup_expr="lt")
        user__lte = filters.NumberFilter(field_name="user", lookup_expr="lte")
        user__gt = filters.NumberFilter(field_name="user", lookup_expr="gt")
        user__gte = filters.NumberFilter(field_name="user", lookup_expr="gte")
        user__in = NumberInFilter(field_name="user", lookup_expr="in")
    
        status = filters.ChoiceFilter(
            field_name="status", choices=reference_status.ADDRESS_STATUSES
        )
        status__in = ChoiceInFilter(
            field_name="status", choices=reference_status.ADDRESS_STATUSES, lookup_expr="in"
        )
    
        class Meta:
            model = Address
            fields = ["external_id", "application", "user", "status"]

> Note:
> NumberInFilter and ChoiceInFilter are special filter that mix a specific filter (number, choice ect) 
> with Django Filter "in" mixin


## backend: 

They are applied after the permissions on each view that set them (Should be all of our views but exceptions exists)
The queryset is filtered against Application id and Merchant id. These last 2 Ids are retrieve from the user JWT token. 

These 2 filters are as important as the permissions!
It limits the access of Database objects that are not bound to your application (and merchant)

ApplicationFilterBackend can be used for filtering against the Application ID whereas MerchantFilterBackend and 
MerchantWriteFilterWithReadAllowedBackend can be used for the Merchant ID.  

MerchantFilterBackend will filter for all HTTP methods and MerchantWriteFilterWithReadAllowedBackend
for all except GET method. 

> Note:
> Merchant filter is only use with Merchant Token
> 
> Also as a security measure we have created disallow_filter that empty the queryset
> It is set as a default in our settings. It should always be overridden by the value in 
> the view, but in case of misconfigured view, it should alert the dev of his mistake.

