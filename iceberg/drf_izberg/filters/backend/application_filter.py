from drf_izberg.filters.backend.log_filter import APPLICATION_FILTER, log_filter
from drf_izberg.permissions.constants.owner import NO_OWNER_RESTRICTION
from ims.api.const import INTERNAL_OWNER
from rest_framework.filters import BaseFilterBackend


class ApplicationFilterBackend(BaseFilterBackend):
    def filter_queryset(self, request, queryset, view):
        assert hasattr(view, "application_filter_relation"), (
            "You must declare in the view the following "
            "attribute: application_filter_relation"
        )

        # No filtering if wildcard
        if view.application_filter_relation == NO_OWNER_RESTRICTION:
            return queryset

        # No filtering by application if owner is internal
        if request.owner_type == INTERNAL_OWNER:
            return queryset

        application_id = int(request.application_id)

        filters = {view.application_filter_relation: application_id}

        log_filter(request, APPLICATION_FILTER, filters)

        # filter the query set with the application
        return queryset.filter(**filters)
