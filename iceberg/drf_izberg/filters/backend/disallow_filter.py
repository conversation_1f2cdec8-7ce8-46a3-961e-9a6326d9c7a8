from rest_framework.filters import BaseFilterBackend


class DisallowFilterBackend(BaseFilterBackend):
    """
    Filter class use as default in DRF settings.
    It protects us from a misconfigured view.
    """

    def filter_queryset(self, request, queryset, view):
        assert (
            False
        ), "You must declare in the view the following attribute: filter_backends"

        return queryset.none()
