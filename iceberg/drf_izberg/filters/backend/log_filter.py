APPLICATION_FILTER = "application_filter"
MERCHANT_FILTER = "merchant_filter"


def log_filter(request, type, filters):
    """
    Log the filter to make it visible in our Open Search Dashboard log aggregator
    """

    # initialize the value if it doesn't exist
    if not hasattr(request, "authorization_filters") or not isinstance(
        request.authorization_filters, dict
    ):
        request.authorization_filters = {}

    request.authorization_filters[type] = filters
