from drf_izberg.filters.backend.log_filter import MERCHANT_FILTER, log_filter
from drf_izberg.permissions.constants.owner import (
    INTERNAL_OWNER,
    NO_OWNER_RESTRICTION,
    OPERATOR_OWNER,
)
from rest_framework.filters import BaseFilterBackend


class MerchantWriteFilterWithReadAllowedBackend(BaseFilterBackend):
    """
    This class is slightly different to MerchantFilterBackend. It allows to get
    resources when the calling HTTP method is a GET without filtering on merchant_id
    """

    def filter_queryset(self, request, queryset, view):
        assert hasattr(view, "merchant_filter_relation"), (
            "You must declare in the view the following ",
            "attribute: merchant_filter_relation",
        )

        # No filtering if wildcard
        if view.merchant_filter_relation == NO_OWNER_RESTRICTION:
            return queryset

        # No filtering by merchant if owner is internal or operator
        if request.owner_type in [INTERNAL_OWNER, OPERATOR_OWNER]:
            return queryset

        # Since read is allowed, no filtering needed for GET method
        if request.method == "GET":
            return queryset

        merchant_id = int(request.merchant_id)

        filters = {view.merchant_filter_relation: merchant_id}

        log_filter(request, MERCHANT_FILTER, filters)

        # filter the query set with the merchant
        return queryset.filter(**filters)
