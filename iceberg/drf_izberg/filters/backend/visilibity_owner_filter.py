from django.db.models import QuerySet
from drf_izberg.permissions.constants.actions import (
    CREATE,
    DESTROY,
    PARTIAL_UPDATE,
    UPDATE,
)
from drf_izberg.permissions.constants.owner import (
    INTERNAL_OWNER,
    MERCHANT_OWNER,
    OPERATOR_OWNER,
)
from lib.models import Visibility
from rest_framework.filters import BaseFilterBackend
from rest_framework.request import Request


class VisibilityOwnerFilterBackend(BaseFilterBackend):
    """
    Filter backend used on resource that has read/write access like Attributes,
    application settings ect.
    This class will add the proper django query filter based on your token.
    """

    def _get_operator_filter(self, request: Request, action: str) -> dict:
        # An Operator can edit
        # therefore we need to adapt the key based on the action performed.
        permission_key = "read_permission"
        if action in [CREATE, UPDATE, PARTIAL_UPDATE, DESTROY]:
            permission_key = "write_permission"

        scopes = request.scopes
        # Based on the scope, the visibility can be upgraded to admin
        visibility = Visibility.APPLICATION_STAFF
        if "application:admin" in scopes or "admin" in scopes:
            visibility = Visibility.APPLICATION_ADMIN

        filters = {permission_key + "__lte": visibility}
        return filters

    def _get_merchant_filter(self) -> dict:
        filters = {"read_permission__lte": Visibility.APPLICATION_MERCHANT}
        return filters

    def _get_internal_filter(self) -> dict:
        filters = {"read_permission__lte": Visibility.IZBERG_STAFF}
        return filters

    def filter_queryset(self, request: Request, queryset: QuerySet, view) -> QuerySet:
        if request.owner_type == OPERATOR_OWNER:
            filters = self._get_operator_filter(request, view.action)
        elif request.owner_type == MERCHANT_OWNER:
            filters = self._get_merchant_filter()
        elif request.owner_type == INTERNAL_OWNER:
            filters = self._get_internal_filter()
        else:
            raise Exception(f"Unhandled owner type {request.owner_type}")

        return queryset.filter(**filters)
