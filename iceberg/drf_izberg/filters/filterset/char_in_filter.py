from django_filters import rest_framework as filters


class CharInFilter(filters.BaseInFilter, filters.CharFilter):
    """
    The purpose of this class is to offer the possibility to query a
    CharField (most likely a foreign_key) with the field lookup: in
    ex:
    external_id__in = CharInFilter(field_name="external_id", lookup_expr="in")

    Used in:
    /v2/user/?external_id__in=ext-1,ext-2
    """
