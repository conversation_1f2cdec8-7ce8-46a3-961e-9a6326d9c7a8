from django_filters import rest_framework as filters


class ChoiceInFilter(filters.BaseInFilter, filters.ChoiceFilter):
    """
    The purpose of this class is to offer the possibility to query a
    choice (most likely a status) with the field lookup: in
    ex:
    status__in = ChoiceInFilter(
        field_name="status", choices=status.ADDRESS_STATUSES, lookup_expr="in"
    )

    Used in:
    /v2/address/?status__in=10,90
    """
