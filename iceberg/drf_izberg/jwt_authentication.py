# -*- coding: utf-8 -*-

import logging

from apps.ice_applications.models import Application
from apps.stores.models import Merchant
from apps.user.models import InMemoryUser
from constance import config as live_settings
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from ims.api.const import INTERNAL_OWNER
from ims.api.exceptions import InsufficientScope, InvalidToken
from ims.api.jwt_utils import extract_jwt_token
from izberg_authenticator.authenticator.identity import IdentityAuthenticator
from mp_utils.ip_tools import get_ip
from rest_framework.authentication import BaseAuthentication

logger = logging.getLogger(__name__)


class CoreAuthenticator(IdentityAuthenticator):
    def _validate_claims(self, user_claims):
        super()._validate_claims(user_claims)
        if user_claims["owner_type"] != INTERNAL_OWNER:
            environment = user_claims.get("environment")
            if environment != settings.ENVIRONMENT_SHORT:
                raise InvalidToken(
                    _("Invalid environment"),
                    environment=environment,
                    expected_environment=settings.ENVIRONMENT_SHORT,
                )


core_authenticator = CoreAuthenticator(
    settings.JWT_CONFIG_URI,
    jwt_keys_uri=getattr(settings, "JWT_KEYS_URI", None),
    accepted_audience=settings.JWT_AUDIENCE,
    allow_local_keys=settings.TEST_IS_RUNNING or settings.DEBUG,
)


class IzbergJWTAuthentication(BaseAuthentication):
    """
    JWT Authentication working with IZBERG.me:
    - Fetches dynamically the OIDC configuration from
        JWT_CONFIG_URI and (optionally) JWT_KEYS_URI settings
    - If JWT_AUDIENCE setting is set, it verifies the token "aud" against it

    NB: to benefit from config caching, the IzbergJWTAuthentication is made
    to be instanciated only once.
    """

    @classmethod
    def is_jwt_token(cls, request):
        return bool(extract_jwt_token(request.headers.get("Authorization", "")))

    @property
    def authenticator(self):
        # TODO: drop me when audience check fully activated everywhere
        if not hasattr(core_authenticator, "verify_audience"):
            raise Exception(
                "Expected 'verify_audience' attribute on IdentityAuthenticator, "
                "something must have changed in izberg-authenticator-py !"
            )
        core_authenticator.verify_audience = (
            live_settings.ENABLE_IDENTITY_AUDIENCE_VALIDATION
        )
        return core_authenticator

    def authenticate(self, request):
        if not self.is_jwt_token(request):
            return None
        auth_result = self.authenticator.authenticate_request(request.META)
        if auth_result["is_authenticated"] is False:
            return None
        if not auth_result["scopes"]:
            raise InsufficientScope()

        application, merchant = self._get_application_and_merchant_or_raise(auth_result)
        request.application_id = application.id if application else None
        request.application = application
        request.merchant_id = merchant.id if merchant else None
        request.merchant = merchant

        request.client_id = auth_result["client_id"]
        request.raw_scope = auth_result["raw_scope"]
        request.scopes = auth_result["scopes"]
        request.scope_filters = auth_result["scope_filters"]
        request.owner_type = auth_result["owner_type"]
        request.iss = auth_result["issuer"]
        request.aud = auth_result["audience"]
        request.sub = auth_result["author_id"]
        request.author_uri = auth_result["author_uri"]
        request.author_name = auth_result["author_name"]
        request.author_type = auth_result["owner_type"]
        request.user = InMemoryUser(username=auth_result["author_id"])
        request.is_authenticated_staff = False
        request.is_authenticated_application = True
        request.is_authenticated_application_user = True
        request.is_application_staff_user = "admin" in request.scopes

        request.auth_identifier = self._get_identifier(request)
        return request.user, None

    def _get_application_and_merchant_or_raise(self, auth_result):
        application_id = (
            auth_result["application_id"] if auth_result["application_id"] else None
        )
        merchant_id = auth_result["merchant_id"] if auth_result["merchant_id"] else None

        try:
            application = (
                Application.objects.exclude_deleted().get(id=application_id)
                if application_id
                else None
            )
            merchant = (
                Merchant.objects.exclude_deleted().get(
                    application_id=application_id,
                    id=merchant_id,
                )
                if merchant_id
                else None
            )
        except (ValueError, Application.DoesNotExist, Merchant.DoesNotExist):
            raise InvalidToken(_("Invalid application and/or merchant"))

        return application, merchant

    def _get_identifier(self, request):
        """Throttle identifier is the client_id"""
        try:
            if not getattr(request, "client_id", False):
                ip_addr = get_ip(request) or "noaddr"
                host = request.META.get("REMOTE_HOST", "nohost")
                request.auth_identifier = f"{ip_addr}_{host}"
                return request.auth_identifier
            identifier = request.client_id
            if getattr(request, "sub", False) and request.sub != identifier:
                # adding sub in addition to client_id if sub
                # is different from client_id (same for M2M)
                identifier += f":{request.sub}"
            return identifier
        except Exception:
            if settings.RAISE_SILENT_ERRORS:
                raise
            logger.exception("could not generate identifier based on client/sub")


def extract_user_tracking_info(request):
    """
    extract ip, author_uri, author_name and author_type from request
    """
    ip = request.headers.get("X-Forwarded-For", None)
    if ip:
        # X_FORWARDED_FOR returns client1, proxy1, proxy2,...
        ip = ip.split(", ")[0]
    else:
        ip = request.META.get("REMOTE_ADDR", "")

    data = {
        "ip": ip,
        "author_uri": request.author_uri,
        "author_name": request.author_name,
        "author_type": request.author_type,
    }
    user = getattr(request, "user", None)
    is_valid_user = (
        user and bool(user.is_authenticated) and not isinstance(user, InMemoryUser)
    )
    if is_valid_user:
        data["user"] = user
    return data
