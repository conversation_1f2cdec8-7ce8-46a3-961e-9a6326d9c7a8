import re

from constance import config as live_config
from django.conf import settings
from drf_izberg.jwt_authentication import IzbergJWTAuthentication

# List of resources that have been migrated to DRF.
MIGRATED_DRF_RESOURCES = [
    "address",
    "country",
    "company",
    "state",
    "application_configuration",
    "application_setting",
    "application",
    "application-category",
    "application-localized-category",
    "application-category-locale-config",
    "currency",
    "image",
    "user",
    "user-profile",
    "discount",
    "cart-item-external-discount",
    "order-item-external-discount",
    "permission",
    "message",
    "message-attachment",
    "message-label",
    "message-type",
    "merchant-licence-rule",
    "application-commission-settings",
    "merchant-order-commission-rule",
    "order-item-commission-rule",
    "commission-line",
    "payment-term",
    "payment-term-line",
    "payment-method",
    "payment",
    "webhook",
    "webhook-trigger",
    "webhook-trigger-attempt",
    "async-action",
    "invoice-line",
    "customer-invoice",
    "invoice-transition-log",
    "credit-note-line",
    "credit-note",
    "external-credit-note-file",
    "external-invoice-file",
]


class DRFUrlConfMiddleware:
    """
    This middleware will change the urlconf based on live settings AND the token
    associated with the request (if any). It also checks the 'request.path' to not
    overwrite the urlconf on non migrated resources.

    We can activate or deactivate DRF all together and choose which endpoint
    to use (v1 or v2) for DRF.

    If the request try to access a resource not listed in MIGRATED_DRF_RESOURCES, then
    we don't change the urlconf. We do the same for stub resources (a resource that
    exist in drf only for the sake of generating their resource_uri on
    related relations)

    Also, any resources listed in DJANGO_REST_FRAMEWORK_DISABLED_RESOURCES (live config)
    will be subtracted from the list of drf resources available.

    For instance, the resource Address is fully converted to DRF but need a DRF stub
    User resource (view + minimal serializer) for generating the resource_uri.
    But the resource are still exposed as follow :
    /v1/address/ with identity token -> urls_drf_on_v1 (DRF)
    /v1/user/1/ with identity token -> default urlconf, iceberg.urls.base (Tastypie)
    /v1/another_url/ with identity token -> default urlconf  (Tastypie)

    One could imagine a scenario as follows for our envs:
    - Prod: ENABLE_DJANGO_REST_FRAMEWORK is False, so no DRF at all
    - Sandbox: ENABLE_DJANGO_REST_FRAMEWORK is True, v1 is serve by DRF if the resource
            exist and if it is completely migrated (not stub)
    - CI: same as sandbox

    Note:
    Tests runs with drf using v2 (urls_drf_on_v2) set in settings_tests.py
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def _request_path_matches_drf_resources(self, path):
        """
        Regex that will match any path from the list of drf resources
        (minus any disabled resources from our live settings)

        ex:
        /v1/address/ -> will match
        /v2/address/ -> will match
        /v3/address/ -> will NOT match
        /v1/batman/ -> will NOT match
        """

        disabled_resources = [
            r.strip()
            for r in live_config.DJANGO_REST_FRAMEWORK_DISABLED_RESOURCES.split(",")
        ]

        # Subtract disabled resources from MIGRATED_DRF_RESOURCES
        resources = list(set(MIGRATED_DRF_RESOURCES) - set(disabled_resources))

        # Add a trailing slash on all resources to avoid matching against resources
        # with same starting string: application VS application_configuration
        resources = [f"{resource}/" for resource in resources]

        regex_drf_resources_path = "^/v[1-2]/({regex})".format(
            regex="|".join(resources)
        )
        p = re.compile(regex_drf_resources_path)
        return p.match(path)

    def __call__(self, request):
        if live_config.ENABLE_DJANGO_REST_FRAMEWORK:
            # check the following:
            # - token must be from identity (JWT)
            # - request.path MUST PASS the regex regex_authorized_drf_resources
            if IzbergJWTAuthentication.is_jwt_token(
                request
            ) and self._request_path_matches_drf_resources(request.path):
                version = live_config.DJANGO_REST_FRAMEWORK_API_VERSION
                if version == "v1":
                    request.urlconf = settings.ROOT_URLCONF_DRF_V1
                elif version == "v2":
                    request.urlconf = settings.ROOT_URLCONF_DRF_V2
        response = self.get_response(request)
        return response
