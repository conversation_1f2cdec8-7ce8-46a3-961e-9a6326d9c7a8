# Paginations

While the Django Rest Framework provides its pagination class, 
we need to redefine a custom pagination class to replicate <PERSON>sty<PERSON>'s behavior. 
This custom pagination provides additional information or behavior specific to the needs of <PERSON><PERSON><PERSON>.

List of existing pagination classes:
- [IzbergPagination](#IzbergPagination)
- [IzbergAggregatePagination](#IzbergAggregatePagination)


Each class has a set of configuration variables that can be adjusted to modify its behavior for specific use cases.
So you can define a custom pagination for a specific ViewSet or directly use in endpoint method.

ex:

``` Python
class CountryPagination(IzbergPagination):
    """
    Country need a specific pagination default limit and max limit
    """

    default_limit = 100
    max_limit = 300

````

To learn more about pagination, read this documentation: [DRF Pagination](https://www.django-rest-framework.org/api-guide/pagination/)

> Note:
    By default, IzbergPagination is used on every view.
> 
> See ```iceberg/iceberg/settings_base/pre_overrides/410_django_rest_framework.py```
> 
>   if you which to use a specific paginator add the following attribute to your view:
> ``` Python
> pagination_class = your_pagination_class
> ```


## IzbergPagination

This is our default pagination class that redefined the response payload to match Tastypie.
If you only wish to retrieve the meta information of an endpoint you can add this URL parameter: `?meta_only=True`

## IzbergAggregatePagination

Some of our resources provided an URL parameter to add pagination information `aggregate_count`.
This parameter trigger a method for count how many objects have the same value for the given field,
and sort them in a list of dict for all value found in accordance of the limit of `max_aggregate_count` order by decrease count.
You can define your own class, for change value of `max_aggregate_count`. (default: 50)

you can add your own filter of the indexed field with `allowed_fields` variable.

ex for Product:

``` Python
class SampleAggregateCountPagination(IzbergAggregateCountPagination):
    allowed_fields = ["gtin", "status"]

````
In this case, you allow `aggregate_count` only on `gtin` and `status` fields.

