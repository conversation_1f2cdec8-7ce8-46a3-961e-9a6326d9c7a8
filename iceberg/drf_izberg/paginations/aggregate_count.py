from collections import OrderedDict

from django.core.exceptions import ValidationError
from django.db.models import Count
from django.utils.translation import gettext_lazy as _
from rest_framework.pagination import LimitOffsetPagination
from rest_framework.response import Response


class IzbergAggregateCountPagination(LimitOffsetPagination):
    max_aggregate_count = 50
    allowed_fields = None

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.aggregate_count = None

    def get_paginated_response(self, data):
        meta = OrderedDict(
            [
                ("limit", self.limit),
                ("offset", self.offset),
                ("next", self.get_next_link()),
                ("previous", self.get_previous_link()),
                ("total_count", self.count),
            ]
        )

        # It's for keep Tastypie behavior
        # if aggregate_count was not precise in url
        if self.aggregate_count is not None:
            meta.update({"aggregate_count": self.aggregate_count})
            meta.move_to_end("aggregate_count", last=False)

        return Response(OrderedDict([("meta", meta), ("objects", data)]))

    def paginate_queryset(self, queryset, request, view=None):
        if "aggregate_count_on" in request.query_params.keys():
            self.compute_aggregate_count(
                request.query_params.get("aggregate_count_on", None), queryset
            )
        return super().paginate_queryset(queryset, request, view=view)

    def raise_on_unavailable_aggregate_count(self, field, queryset):
        if self.allowed_fields and field not in self.allowed_fields:
            raise ValidationError(
                {
                    "aggregate_count_on": _(
                        'Aggregate count on field "{field_name}"' " is not available"
                    ).format(field_name=field)
                }
            )

    def compute_aggregate_count(self, field, queryset):
        self.raise_on_unavailable_aggregate_count(field, queryset)
        self.aggregate_count = list(
            queryset.values(field)
            .annotate(count=Count(field))
            .order_by("-count")[: self.max_aggregate_count]
        )
