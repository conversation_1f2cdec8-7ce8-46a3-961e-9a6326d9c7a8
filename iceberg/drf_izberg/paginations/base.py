from collections import OrderedDict

from rest_framework.pagination import LimitOffsetPagination
from rest_framework.response import Response


class IzbergPagination(LimitOffsetPagination):
    max_limit = 100

    def get_paginated_response(self, data):
        meta = OrderedDict(
            [
                ("limit", self.limit),
                ("offset", self.offset),
                ("next", self.get_next_link()),
                ("previous", self.get_previous_link()),
                ("total_count", self.count),
            ]
        )
        if self.request.query_params.get("meta_only", None):
            return Response(OrderedDict([("meta", meta), ("objects", [])]))
        return Response(OrderedDict([("meta", meta), ("objects", data)]))
