# Permissions

Django rest framework offers a nice way to handle permissions using composition.
We have defined 5 mandatory permissions that validate the request action based on the request token (JWT).
If the token doesn't provide the proper permissions, then we raise an exception.

List of mandatory permissions:
- [HTTP Methods](#HTTP Methods)
- [Owner](#Owner)
- [Scopes](#Scopes)
- [Actions](#Actions)
- [Roles](#Roles)

Each of these permissions uses special attributes set on the view to dictate needed scope/roles per actions.
More on that in each of the section below.
ex:


``` Python

    permission_constraints = {
        OPERATOR_OWNER: {
            LIST: ALL_ROLES,
            CREATE: EDIT_ROLES,
            RETRIEVE: ALL_ROLES,
            UPDATE: EDIT_ROLES,
            PARTIAL_UPDATE: EDIT_ROLES,
            DESTROY: EDIT_ROLES,
            # Custom action
            "active_addresses": ALL_ROLES,
        },
        MERCHANT_OWNER: {
            LIST: ALL_ROLES,
            RETRIEVE: ALL_ROLES,
        },
    }
    permission_scopes = [CUSTOMER_SCOPE]

    http_method_names = ["get", "post", "put", "patch", "delete"]
````

To learn more about the structure and creation of new permissions, read this documentation:  
[DRF Permissions](https://www.django-rest-framework.org/api-guide/permissions/)


> Note:
    As a security measure we have created permissions that disallow all request: disallow.py
    It is set as a default in our settings. It should always be overridden by the value in
    the view, but in case of misconfigured view, it should alert the dev of his mistake.

## Request structure

To perform the permissions we need information from the user token.  
When the authentication is performed, we set in the request the owner type, scope+role and also the application.  

List of information in the request that are used in the permissions:

- application: Application instance
- merchant: Merchant instance | None
- owner_type: operator | merchant | internal
- scopes: List of "scope:role", ex: ["operator:read", "operator:write"]


## HTTP Methods

Check that the `request.method` is present in http_method_names (set in the view)

DRF already do a validation of the request method against http_method_names in the
view. But it does it too late and our other permissions raise an error before
(Wrong action and/or role).
it's misleading and hard to understand for our customers.

Therefore, we have added this permission that mostly do the same thing as DRF and raise a `MethodNotAllowed`
if the request method is not listed in `http_method_names`


## Owner

Represent the type of the user:
- internal
- operator: An operator manage multiple merchant. Is limited only by his application
- merchant

The owner permission class will check if `request.owner_type` is in the keys() of permission_constraints:

``` Python
    permission_constraints = {
        OPERATOR_OWNER: {
            ...
        },
        MERCHANT_OWNER: {
            ...
        },
    }
```

## Scopes

Our ressources are grouped by business/domain logic:

``` Python
    APPLICATION_SCOPE = "application"
    MERCHANT_SCOPE = "merchant"
    FINANCE_SCOPE = "finance"
    PIM_SCOPE = "pim"
    OMS_SCOPE = "oms"
    DEPRECATION_SCOPE = "deprecation"
    CUSTOMER_SCOPE = "customer"
    TECHNICAL_SCOPE = "technical"
    IDENTITY_SCOPE = "identity"
```

Each generated token have a set of scopes associated.
The scopes permissions check the view scope against each given scopes in a token.
Example of view scope:

``` Python
    permission_scopes = [CUSTOMER_SCOPE]
```

> Note:
> a wildcard (*) set as a permission_scopes will allow any user access to the view.


## Actions

Django Rest framework associate an action for each HTTP methods with some specificity:

(DRF action = HTTP method)
- list = GET
- retrieve = GET a specific entity
- create = POST
- update = PUT
- partial_update = PATCH
- destroy = DELETE

And it will add an action for each custom endpoint created with the action decorator.
Example:

``` Python
    @action(detail=False, url_path="actives", name="List actives addresses")
    def active_addresses(self, request):
```

- active_addresses = GET/POST

A permission is defined by a role: a pair `action: constraint` for a given owner type.
So to fetch the role to apply, we need to get the owner type 
(retrieved from `request.owner_type`) to access the applicable pair policy,
and then fetch the role associated to the current action (retrieved from `view.action`)

Therefore each of our view has to define a list of authorized action per Roles, example:

``` Python
    permission_constraints = {
        OPERATOR_OWNER: {
            LIST: ALL_ROLES,
            CREATE: EDIT_ROLES,
            RETRIEVE: ALL_ROLES,
            UPDATE: EDIT_ROLES,
            PARTIAL_UPDATE: EDIT_ROLES,
            DESTROY: EDIT_ROLES,
            # Custom action
            "active_addresses": ALL_ROLES,
        },
        MERCHANT_OWNER: {
            LIST: ALL_ROLES,
            RETRIEVE: ALL_ROLES,
        },
    }
```


## Roles

Finally, for each pair of owner/action we set the authorized roles from:

- READ
- EDIT
- ADMIN
- EDIT_ROLES  (WRITE_ROLE, ADMIN_ROLE)
- ALL_ROLES (READ_ROLE, WRITE_ROLE, ADMIN_ROLE)

The roles permissions check for a given owner/action with an associated role that our view authorize it.

> Roles being the last check, it does a bit more that explained above.
> You should read the code and comments of `roles.py` to understand.


# Other permissions

List of other permissions:
- [Writable fields](#write_fields)

## write_fields

Our serializer defined what fields can be set when creating the resource
`creatable_fields` and what fields can be editable `editable_fields`.
There is also a list of fields editable only by an Operator `operator_writeable_fields`

``` Python
    creatable_fields = ("user",)
    editable_fields = (
        "external_id",
        "physical_person",
        "gender",
        "first_name",
        ...
    )
    read_only_fields= (
        "id",
        "resource_uri",
        ...
    )
    operator_writeable_fields = ("tax_zone_key",)
```

This permissions check for a given token and request payload, that you can perform your
action. 
We check only when the request is POST, PUT or PATCH.

For a POST, we only check that the request payload fields key are in:
- creatable_fields
- editable_fields
- operator_writeable_fields (Only if the token is Operator)

For a PUT, we only check that the request payload fields key are in:
- editable_fields
- operator_writeable_fields (Only if the token is Operator)

> We will not raise an error for a PUT if some fields are not in editable_fields or 
> operator_writeable_fields but we will "pop" the value from the request.data


For a PATCH, we only check that the request payload fields key are in:
- editable_fields
- operator_writeable_fields (Only if the token is Operator)

> We will raise an error only if RAISE_SILENT_ERRORS is set to True (not sandbox or
> production). Otherwise we "pop" the field key in request.data, same as a PUT
