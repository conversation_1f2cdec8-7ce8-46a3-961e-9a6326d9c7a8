from drf_izberg.permissions.constants.actions import DRF_ACTION_TO_TASTYPIE
from ims.api.exceptions import InvalidToken
from rest_framework import permissions


class ActionsPermissions(permissions.BasePermission):
    def has_permission(self, request, view):
        # retrieve owners from permission_constraints
        # check view.action with list of authorised action for specific owner
        authorized_owner = view.permission_constraints
        # At that point we already validated the owner, assuming it exists
        owner_action = authorized_owner[request.owner_type]
        if view.action in owner_action:
            return True

        self._raise_invalid_token_for_action(request, view.action)

    def _raise_invalid_token_for_action(self, request, action):
        raise InvalidToken(
            msg="Invalid access token for this action",
            action=DRF_ACTION_TO_TASTYPIE.get(action, action),
            scope=request.raw_scope,
            application_id=request.application_id,
            merchant_id=request.merchant_id,
        )
