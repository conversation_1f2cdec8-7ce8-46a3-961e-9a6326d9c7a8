from django.utils.translation import gettext_lazy as _

""" Special owner path value(s) """
NO_OWNER_RESTRICTION = "*"

""" Owner types """
INTERNAL_OWNER = "internal"
OPERATOR_OWNER = "operator"
MERCHANT_OWNER = "merchant"
CUSTOMER_OWNER = "customer"
BUYER_OWNER = "buyer"


OWNER_TYPES = (
    (INTERNAL_OWNER, _("Internal")),
    (OPERATOR_OWNER, _("Operator")),
    (MERCHANT_OWNER, _("Merchant")),
    (CUSTOMER_OWNER, _("Customer")),
    (BUYER_OWNER, _("Buyer")),
)
