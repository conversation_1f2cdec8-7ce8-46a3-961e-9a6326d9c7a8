from rest_framework.permissions import BasePermission


class DisallowPermission(BasePermission):
    """
    Permission class used as default in DRF settings.
    It protects us from a misconfigured view.
    """

    def has_permission(self, request, view):
        assert (
            False
        ), "You must declare in the view the following attribute: permission_classes"

        return False

    def has_object_permission(self, request, view, obj):
        assert (
            False
        ), "You must declare in the view the following attribute: permission_classes"

        return False
