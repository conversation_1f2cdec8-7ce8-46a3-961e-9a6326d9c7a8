from rest_framework import permissions
from rest_framework.exceptions import MethodNotAllowed


class HTTPMethodsPermissions(permissions.BasePermission):
    """
    DRF already do a validation of the request method against http_method_names in the
    view. But it does it too late and some of our other permissions might raise an
    error before (Wrong action and/or role).
    it's misleading and hard to understand for our customers. Therefore, we have added
    this permission that mostly do the same thing as DRF and raise a MethodNotAllowed
    if the request method is not listed in http_method_names

    """

    def has_permission(self, request, view):
        if request.method.lower() in view.http_method_names:
            return True

        raise MethodNotAllowed(request.method)
