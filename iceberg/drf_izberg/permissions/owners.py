from drf_izberg.permissions.constants.actions import DRF_ACTION_TO_TASTYPIE
from ims.api.exceptions import InvalidOwnerType
from rest_framework import permissions


class OwnersPermissions(permissions.BasePermission):
    def has_permission(self, request, view):
        # retrieve owners from permission_constraints
        authorized_owner = view.permission_constraints.keys()
        if request.owner_type in authorized_owner:
            return True

        self._raise_invalid_owner_type(
            view.resource_name, view.action, request.owner_type, list(authorized_owner)
        )

    def _raise_invalid_owner_type(
        self, resource_name, action, owner_type, accepted_owner_types
    ):
        error_kwargs = dict(
            action=DRF_ACTION_TO_TASTYPIE.get(action, action),
            owner_type=owner_type,
            accepted_owner_types=accepted_owner_types,
            resource_name=resource_name,
        )

        raise InvalidOwnerType(**error_kwargs)
