from drf_izberg.permissions.constants.actions import DRF_ACTION_TO_TASTYPIE
from drf_izberg.permissions.constants.roles import SCOPE_ROLE_SEPARATOR
from drf_izberg.permissions.constants.scopes import ANY_SCOPE
from drf_izberg.permissions.scopes import ScopesPermissions
from ims.api.exceptions import InsufficientScope
from rest_framework import permissions


class RolesPermissions(permissions.BasePermission):
    def has_permission(self, request, view):
        authorized_owner = view.permission_constraints
        # At that point we already validated owner and action.
        # We can assume they exist
        authorized_role_per_action = authorized_owner[request.owner_type][view.action]

        # first looping through each listed scope in our view
        for view_scope in view.permission_scopes:
            # then for each listed scopes+role in the request
            for scope_with_role in request.scopes:
                # We have 2 cases to treat:
                # 1: Check for special roles that have no scope (admin, izberg user etc)
                #    and validate them against authorized_role_per_action
                # 2: Check for scope + role and validate them against view_scope and
                #    authorized_role_per_action

                # Case 1:
                if SCOPE_ROLE_SEPARATOR not in scope_with_role:
                    if scope_with_role in authorized_role_per_action:
                        return True
                    else:
                        continue

                # Case 2:
                # retrieve the scope and role separately
                scope, role = scope_with_role.split(SCOPE_ROLE_SEPARATOR)
                # To be valid we need:
                # - Request scope to be IN view scope
                #   OR view scope to be ANY_SCOPE
                # - Request role to be in authorized_role_per_action
                if (
                    view_scope in {scope, ANY_SCOPE}
                    and role in authorized_role_per_action
                ):
                    return True

        self._raise_insufficient_scope(request, view)

    def _raise_insufficient_scope(self, request, view):
        error_kwargs = dict(
            action=DRF_ACTION_TO_TASTYPIE.get(view.action, view.action),
            scope=request.raw_scope,
            accepted_scopes=ScopesPermissions.build_accepted_scopes(request, view),
            resource_name=view.resource_name,
        )
        raise InsufficientScope(**error_kwargs)
