from drf_izberg.permissions.constants.actions import DRF_ACTION_TO_TASTYPIE
from drf_izberg.permissions.constants.roles import ALL_ROLES, SCOPE_ROLE_SEPARATOR
from drf_izberg.permissions.constants.scopes import ANY_SCOPE
from ims.api.exceptions import InsufficientScope
from rest_framework import permissions


class ScopesPermissions(permissions.BasePermission):
    def has_permission(self, request, view):
        # Meaning that this view accept any scopes.
        if ANY_SCOPE in view.permission_scopes:
            return True

        # Retrieve scope with role from request
        for scope_with_role in request.scopes:
            # request.scopes might contain "special" roles that have no scope
            # -> scopes: ["admin", "edit", "customer:read"]
            # The first two don't have a scope, just a role.
            # We check for these and will treat them later in the role permission.
            if (
                SCOPE_ROLE_SEPARATOR not in scope_with_role
                and scope_with_role in ALL_ROLES
            ):
                return True

            # Split to retrieve only the scope
            scope = scope_with_role.split(SCOPE_ROLE_SEPARATOR)[0]

            # check if user scope is in resource scope
            if scope in view.permission_scopes:
                return True

        self._raise_insufficient_scope(request, view)

    def _raise_insufficient_scope(self, request, view):
        error_kwargs = dict(
            action=DRF_ACTION_TO_TASTYPIE.get(view.action, view.action),
            scope=request.raw_scope,
            accepted_scopes=ScopesPermissions.build_accepted_scopes(request, view),
            resource_name=view.resource_name,
        )
        raise InsufficientScope(**error_kwargs)

    @classmethod
    def build_accepted_scopes(cls, request, view):
        """
        Return a list of accepted 'scopes' (meaning: scope:role) for a given view
        Ex:
        ['customer:read', 'customer:write', 'customer:admin', 'read', 'write', 'admin']
        """
        accepted_roles = {}

        if isinstance(
            view.permission_constraints[request.owner_type][view.action], str
        ):
            # Roles could be a simple string (instead of a list)
            # It is easier to cast it as a list than dealing with a string
            roles = [view.permission_constraints[request.owner_type][view.action]]
        else:
            # we assume it's a list
            roles = view.permission_constraints[request.owner_type][view.action]

        # build accepted_roles
        for role in roles:
            if role not in accepted_roles:
                accepted_roles[role] = True

        # remove duplicates
        accepted_roles = list(accepted_roles)

        accepted_scopes = []
        for scope in view.permission_scopes:
            for role in accepted_roles:
                accepted_scopes.append(SCOPE_ROLE_SEPARATOR.join((scope, role)))

        # add global roles
        for role in accepted_roles:
            accepted_scopes.append(role)

        return accepted_scopes
