from django.conf import settings
from django.http import QueryDict
from django.utils.translation import gettext_lazy as _
from ims.api.const import OPERATOR_OWNER
from rest_framework import permissions
from rest_framework.exceptions import ValidationError


class WriteFieldsPermissions(permissions.BasePermission):
    def has_permission(self, request, view):
        if request.method not in ["POST", "PUT", "PATCH"]:
            return True

        is_user_operator = request.owner_type == OPERATOR_OWNER
        serializer_meta = view.serializer_class.Meta

        if request.method == "POST":
            self.can_create_fields_or_raise(request, serializer_meta, is_user_operator)

        elif request.method in ["PUT", "PATCH"]:
            self.can_edit_fields_or_raise(request, serializer_meta, is_user_operator)

        return True

    def can_create_fields_or_raise(self, request, serializer_meta, is_user_operator):
        allowed_fields = []
        allowed_fields.extend(serializer_meta.creatable_fields)
        allowed_fields.extend(serializer_meta.editable_fields)

        if is_user_operator:
            allowed_fields.extend(serializer_meta.operator_writeable_fields)

        self.request_fields_allowed_or_raise(request.data.keys(), allowed_fields)

    def can_edit_fields_or_raise(self, request, serializer_meta, is_user_operator):
        allowed_fields = []
        # Avoid mutation of serializer Meta class
        allowed_fields.extend(serializer_meta.editable_fields)

        if is_user_operator:
            allowed_fields.extend(serializer_meta.operator_writeable_fields)

        if request.method == "PATCH" and settings.RAISE_SILENT_ERRORS:
            # Will be the futur and only behavior after dropping tastypie
            # For now, we raise only for patch and when RAISE_SILENT_ERRORS is set to
            # True, meaning local/dev/tests/ but NOT sandbox and production
            self.request_fields_allowed_or_raise(request.data.keys(), allowed_fields)
        else:
            # Tastypie compatibility...
            # Should be removed eventually after dropping Tastypie.
            self.request_fields_allowed_or_pop(request, allowed_fields)

    def request_fields_allowed_or_raise(self, request_fields, allowed_fields):
        for field in request_fields:
            if field not in allowed_fields:
                raise ValidationError(
                    {field: _("Current status doesn't allow edition")}
                )

    def request_fields_allowed_or_pop(self, request, allowed_fields):
        """
        Removes fields that are not allowed from `request.data`
        """
        # POST request use a queryDict instead of a Dict
        if isinstance(request.data, QueryDict):
            request.data._mutable = True
        request_data_keys = list(request.data.keys())  # copy data keys
        for field in request_data_keys:
            if field not in allowed_fields:
                request.data.pop(field)
