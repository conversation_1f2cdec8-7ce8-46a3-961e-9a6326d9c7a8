from collections import OrderedDict

from django.urls import NoReverseMatch
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from rest_framework import views
from rest_framework.response import Response
from rest_framework.reverse import reverse
from rest_framework.routers import DefaultRouter


class I<PERSON><PERSON>Router(DefaultRouter):
    """
    Simple default router (use in urls.py) but with a specific APIRootView
    In order to be compatible with Tastypie API router, we need to provide to our
    own APIRootView: a dict with the list of endpoints (list and detail) for each view.
    Then APIRootView will take care of the rendering.
    """

    def get_api_root_view(self, api_urls=None):
        api_root_dict = OrderedDict()
        list_name = self.routes[0].name
        detail_name = self.routes[2].name

        for view_name, viewset, basename in self.registry:
            doc = viewset.doc if hasattr(viewset, "doc") else None
            # Show endpoints from a view only if doc is set and show is True
            if doc and doc["show"] is True:
                api_root_dict[view_name] = {
                    "endpoints": {
                        "list_endpoint": list_name.format(basename=basename),
                        "detail_endpoint": detail_name.format(basename=basename),
                    },
                    "doc_order": doc.get("order", None),
                }

        return APIRootView.as_view(api_root_dict=api_root_dict)


class APIRootView(views.APIView):
    _ignore_model_permissions = True
    schema = None  # exclude from schema
    api_root_dict = None

    @method_decorator(cache_page(60 * 60 * 24))
    def get(self, request, *args, **kwargs):
        """
        Serve /v1/ with every resource that our API provide.
        Each resource is split between 4 values:
        - list_endpoint
        - detail_endpoint
        - schema
        - doc (if the view has a doc associated)
        """
        resp = OrderedDict()
        namespace = request.resolver_match.namespace

        for view_name, data in self.api_root_dict.items():
            payload = OrderedDict()
            # set the doc order
            payload["doc_order"] = data["doc_order"]

            # run through list_endpoint and detail_endpoint
            for endpoint_type, url in data["endpoints"].items():
                if namespace:
                    url = namespace + ":" + url

                if endpoint_type == "list_endpoint":
                    url = self.add_endpoint(url, request, *args, **kwargs)
                    if url:
                        payload[endpoint_type] = url

                elif endpoint_type == "detail_endpoint":
                    # We need to make a copy of kwargs to avoid mutation
                    # Add a "fake" pk so that reverse can do it's magic
                    copied_kwargs = {**kwargs, **{"pk": 1}}
                    url = self.add_endpoint(url, request, *args, **copied_kwargs)
                    if url:
                        # We need to format the url as follows:
                        # /resource_name/:id/
                        url = url.replace("/1/", "/:id/")
                        payload[endpoint_type] = url

            # If we have a list endpoint then we can create the schema url
            if "list_endpoint" in payload:
                payload["schema"] = payload["list_endpoint"] + "schema/"

            # set payload for current view
            resp[view_name] = payload

        return Response(resp)

    def add_endpoint(self, url_name, request, *args, **kwargs):
        url = None
        try:
            url = reverse(
                url_name,
                args=args,
                kwargs=kwargs,
                request=request,
                format=kwargs.get("format", None),
            )
        except NoReverseMatch:
            # Don't bail out if eg. no list routes exist, only detail routes.
            pass
        return url
