import logging
from collections import OrderedDict

import pytz
from django.conf import settings
from django.core.exceptions import FieldDoesNotExist
from django.db.models.fields import NOT_PROVIDED
from mp_utils.functions import plaintext2html
from rest_framework.fields import empty
from rest_framework.utils.model_meta import get_field_info

logger = logging.getLogger(__name__)


class IzbergSchemaGenerator:
    """
    For the brave souls who get to update our schema generator:

    You need to understand that our API was first build with Tastypie and was
    using Tastypie schema generator. But... we added a lot of custom logic and info.
    When migrating to DRF, we couldn't use OpenApi (bundled with DRF) because our
    clients and BO are built on top of our schema. So, for the sake of compatibility we
    have to generate the "same" schema using DRF.
    This class generate a schema as json using the current request, current drf view and
    the serializer class use by the view.
    """

    def __init__(
        self,
        request,
        view,
        serializer_class,
    ):
        self.request = request
        self.view = view
        self.serializer_class = serializer_class

        assert hasattr(
            self.view, "legacy_schema_extra_info"
        ), f"legacy_schema_extra_info must be defined in view:{self.view.resource_name}"

        assert (
            "filters" in self.view.legacy_schema_extra_info
        ), "Following key must be defined in legacy_schema_extra_info: filters"

        assert "allowed_detail_http_methods" in self.view.legacy_schema_extra_info, (
            "Following key must be defined in "
            "legacy_schema_extra_info: allowed_detail_http_methods"
        )

        assert "allowed_list_http_methods" in self.view.legacy_schema_extra_info, (
            "Following key must be defined in "
            "legacy_schema_extra_info: allowed_list_http_methods"
        )

    def generate(self):
        data = OrderedDict()

        self._populate_allowed_methods(data)
        self._populate_format(data)
        self._populate_limit(data)
        self._populate_doc(data)
        self._populate_fields(data)
        self._populate_filtering(data)
        self._populate_methods(data)
        self._populate_ordering(data)

        self._enrich_with_status_transitions(data)
        self._enrich_with_localized_fields(data)

        # Sorting the dict keys to be as close as possible to Tastypie schema order
        return self._sorted_dict(data)

    def _sorted_dict(self, unsorted_dict):
        return dict(sorted(unsorted_dict.items()))

    def _populate_allowed_methods(self, data):
        data["allowed_detail_http_methods"] = self.view.legacy_schema_extra_info[
            "allowed_detail_http_methods"
        ]
        data["allowed_list_http_methods"] = self.view.legacy_schema_extra_info[
            "allowed_list_http_methods"
        ]

    def _populate_filtering(self, data):
        if hasattr(self.view, "filterset_class"):
            assert (
                "filters" in self.view.legacy_schema_extra_info
            ), "Following key must be defined in legacy_schema_extra_info: filters"
            # making a copy to avoid any data deletion when deprecating a field use
            # in the filter. See DeprecatedFilterAttributesMixin
            data["filtering"] = self.view.legacy_schema_extra_info["filters"].copy()

    def _populate_format(self, data):
        data["default_format"] = "application/json"

    def _populate_limit(self, data):
        data["default_limit"] = self.view.pagination_class.default_limit

    def _populate_doc(self, data):
        data["doc"] = self.view.doc["doc"] if hasattr(self.view, "doc") else ""

    def _populate_ordering(self, data):
        # making a copy to avoid any data deletion when deprecating a field use
        # in ordering. See DeprecatedOrderingAttributesMixin
        data["ordering"] = self.view.ordering_fields.copy()

    def _populate_methods(self, data):
        # Retrieve all custom action on view except schema
        view_actions = [
            action
            for action in self.view.get_extra_actions()
            if action.url_name != "schema"
        ]

        data["methods"] = []

        for action in view_actions:
            methods_info = {}
            detail = action.detail
            # Generate url from action name.
            # Provide args when the action is on instance of a model (detail=True)
            url = self.view.reverse_action(
                action.url_name, args=["1"] if detail else []
            )
            url = url.replace("/1/", "/:id/")
            # - When running test, convert /v2/ to /v1/
            # - Support v2 for drf when namespace is V2
            #   Will be removed when migration completely to DRF
            # - Use v1 as default
            if settings.TEST_IS_RUNNING:
                url = url.split("/v2/")[1]
                methods_info["regex"] = "/v1/" + url
            elif "/v2/" in url:
                url = url.split("/v2/")[1]
                methods_info["regex"] = "/v2/" + url
            else:
                url = url.split("/v1/")[1]
                methods_info["regex"] = "/v1/" + url

            methods_info["doc_text"] = ""
            methods_info["doc_html"] = ""
            description = action.kwargs["description"]
            if description and "[Doc]" in description:
                generated_doc = description.replace("[Doc]", "").strip()
                methods_info["doc_text"] = generated_doc
                methods_info["doc_html"] = plaintext2html(generated_doc)

            # Might be a good idea to add the http methods allowed for this action
            methods_info["allowed_http_methods"] = action.mapping.keys()

            data["methods"].append(methods_info)

    def _populate_fields(self, data):
        """
        This method populate the schema with all the fields on our serializers.
        We use different methods to retrieve the information:
         - Using the associated model to get some information base on the field
           name.
        - Using get_field_info(model) from DRF to get some extra meta info on the model
        - Using drf get_fields() to get all the field on the serializer
        - Using _get_model_field() to retrieve the field info on the model
          Could be None if the field is a property and we expose it on the Serializer
        - Finally using various method to get the information from the
          serializer_class, model_fields or model_field

        The expected schema is slightly different from the actual Tastypie schema.
        - Most listed fields have their attributes blank and default changed.
          DRF will use the value of the model by default OR the value set on the
          serializer. Tastypie seems to be different and are sometimes wrong.
          Example: id with blank: True -> Wrong
        - We are adding the attribute "required" that defined if the field is required
          by our serializer to pass validation.
        """

        fields_data = OrderedDict()

        creatable_fields = self.serializer_class.Meta.creatable_fields
        editable_fields = self.serializer_class.Meta.editable_fields

        # Django model
        model = self.serializer_class.Meta.model
        # Extra info on the model from DRF
        model_info = get_field_info(model)
        model_relations = model_info.relations
        model_fields = OrderedDict()
        model_fields.update(model_info.fields_and_pk)
        model_fields.update(model_info.relations)

        # Sorted to have same ordered as Tastypie
        serializer_fields_sorted = self._sorted_dict(
            self.serializer_class().get_fields()  # List of field from the serializer
        )

        for field_name, serializer_field in serializer_fields_sorted.items():
            # We dont provide information on our PK
            if field_name == "pk":
                continue

            info = {}

            # Field from the django model, could be None (properties..)
            model_field = self._get_model_field(model, field_name)

            # Allow_empty will be true for manuToMany but allow_null seems to be False
            # even when set in the serializer... bug?
            info["nullable"] = serializer_field.allow_null or getattr(
                serializer_field, "allow_empty", False
            )
            info["required"] = serializer_field.required
            info["help_text"] = self._get_help_text(serializer_field, model_field)
            info["name"] = self._get_field_name(
                field_name, model_field, serializer_field
            )

            info["unique"] = model_field.auto_created if model_field else False

            info["type"] = self._get_type(
                model_field, field_name, model_fields, serializer_field
            )

            info["default"] = self._get_default(model_field, serializer_field)

            # Deal with relation type field (ForeignKey/ManyToMany)
            if field_name in model_relations:
                info["related_type"] = self._get_related_type(
                    model_relations, field_name
                )

            info["editable"] = True if field_name in editable_fields else False
            info["readonly"] = False if field_name in editable_fields else True

            info["creatable"] = False
            if field_name in creatable_fields or field_name in editable_fields:
                info["creatable"] = True

            info["blank"] = (
                serializer_field.allow_blank
                if hasattr(serializer_field, "allow_blank")
                else False
            )

            if info["type"] in ["string", "uuid string"]:
                info["max_length"] = self._get_max_length(model_field, serializer_field)
            if model_field:
                choices = self.get_choices(model_field)
                if choices:
                    info["choices"] = choices
                    info["choices_to_hide"] = self._get_choices_to_hide(field_name)
                    info["virtual_choices"] = self._get_virtual_choices(field_name)

            # Sorted to have same ordered as Tastypie
            fields_data[field_name] = self._sorted_dict(info)

        data["fields"] = fields_data

    def _enrich_with_status_transitions(self, data):
        try:
            model = self.serializer_class.Meta.model
            should_skip = (
                "status" not in data["fields"]
                or model is None
                or not hasattr(model, "status")
                or not hasattr(model.status, "transitions")
            )
            if should_skip:
                return
            transitions = model.status.transitions
            dict_transition = self.convert_transitions_to_dict(transitions)
            data["fields"]["status"]["transitions"] = dict_transition
            data["editable_for_statuses"] = getattr(
                self.serializer_class.Meta, "fields_editable_for_statuses", []
            )
        except Exception:
            if settings.RAISE_SILENT_ERRORS:
                raise
            logger.exception("in enrich_with_status_transitions: ")

    def _enrich_with_localized_fields(self, data):
        try:
            try:
                descriptor = self.serializer_class.Meta.model.localized_infos
            except AttributeError:  # Non localized models
                return
            localized_infos_class = descriptor.rel.related_model
            localized_fields = localized_infos_class.as_helper_dict()
            data["fields"].update(localized_fields)
        except Exception:
            if settings.DEBUG:
                raise
            logger.exception("in enrich_with_localized_fields: ")

    def convert_transitions_to_dict(self, transitions):
        transitions_dict = {}
        for trans in transitions:
            transitions_dict[trans.name] = {
                "to_state": trans.target.name,
                "from_states": [source.name for source in trans.source],
            }
        return transitions_dict

    def get_choices(self, model_field, on_values=None):
        """returns choices based on model_field type"""
        if hasattr(model_field, "set_choices"):
            return self._handle_set_choices(
                model_field.set_choices, on_values=on_values
            )
        elif hasattr(model_field, "key_choices"):
            return [
                {"value": value, "help_text": help_text}
                for value, help_text in model_field.key_choices
                if on_values is None or value in on_values
            ]
        elif hasattr(model_field, "base_field"):
            # In case of the field is an ArrayField of ChoiceField
            return self.get_choices(model_field.base_field)
        elif hasattr(model_field, "flatchoices"):
            # We check for isinstance(value, pytz.BaseTzInfo) and convert value in str
            # because DRF json encoding cannot handle timezone instance.
            return [
                {
                    "value": (
                        str(value) if isinstance(value, pytz.BaseTzInfo) else value
                    ),
                    "help_text": str(help_text),
                }
                for value, help_text in model_field.flatchoices
                if on_values is None or value in on_values
            ]
        return None

    def _handle_set_choices(self, set_choices, on_values=None):
        choices = []
        for choice in set_choices:
            # check if choice is list or tuple to avoid exploiting for
            # set_choices made of simple value
            if isinstance(choice, (tuple, list)):
                value, help_text = choice
                if on_values is None or value in on_values:
                    choices.append({"value": value, "help_text": help_text})
        return choices
        # following code is more efficient than above and may be used again
        # when all set_choices are set/list of tuples
        # return [
        #     {"value": value, "help_text": help_text}
        #     for value, help_text in model_field.set_choices
        #     if on_values is None or value in on_values
        # ]

    def _get_choices_to_hide(self, field_name):
        if not hasattr(self.serializer_class.Meta, "schema_choices_to_hide"):
            return []
        return self.serializer_class.Meta.schema_choices_to_hide.get(field_name, [])

    def _get_virtual_choices(self, field_name):
        virtual_choices = []

        if not hasattr(self.serializer_class.Meta, "schema_virtual_choices"):
            return virtual_choices

        if field_name in self.serializer_class.Meta.schema_virtual_choices:
            virtual_choices_raw = self.serializer_class.Meta.schema_virtual_choices[
                field_name
            ]
            virtual_choices = [
                {"value": value, "help_text": help_text}
                for (value, help_text) in virtual_choices_raw
            ]
        return virtual_choices

    def _get_model_field(self, model, field_name):
        if not getattr(model, "_meta", None):
            return None
        try:
            return model._meta.get_field(field_name)
        except FieldDoesNotExist:
            try:
                # may be a measurement field with _value suffix
                return model._meta.get_field(f"{field_name}_value")
            except FieldDoesNotExist:
                return None

    def _get_field_name(self, field_name, model_field, serializer_field):
        """
        Checks all the fields that could contain a pretty name,
        falling back on attribute value

        Use in order and if not none:
        - serializer label
        - model verbose name
        - field name with replace "_" to " "
        """
        if hasattr(serializer_field, "label") and serializer_field.label is not None:
            return serializer_field.label.capitalize()

        elif (
            hasattr(model_field, "verbose_name")
            and model_field.verbose_name is not None
        ):
            return model_field.verbose_name.capitalize()

        return field_name.replace("_", " ").strip().capitalize()

    def _get_help_text(self, serializer_field, model_field):
        """
        Return the help text by trying first the serializer help text, or
        the model help text.
        """
        if serializer_field.help_text is not None:
            return serializer_field.help_text
        elif hasattr(model_field, "help_text"):
            return model_field.help_text
        else:
            return ""

    def _get_type(self, model_field, field_name, model_fields, serializer_field):
        """
        Return the field type by trying in order:
        - fetching it from the model by name and mapped to tastypie value
          (model_field could be None, see "_get_model_field"
        - same as the above but with "_" prepend to the field name
        - fetching it from the serializer and mapped to tastypie value
        - returns None otherwise
        """
        TYPE_MAPPING = {
            "AutoField": "integer",
            "IntegerField": "integer",
            "SmallIntegerField": "integer",
            "PositiveSmallIntegerField": "integer",
            "PositiveIntegerField": "integer",
            "CharField": "string",
            "NoChoiceMigrationCharfield": "string",
            "ChoiceField": "string",
            "BooleanField": "boolean",
            "ForeignKey": "related",
            "StateField": "string",
            "RelationInfo": "related",
            "ManyToOneRel": "related",
            "ManyToManyRel": "related",
            "ManyToManyField": "related",
            "IzbergHyperlinkedRelatedField": "related",
            "HyperlinkedIdentityField": "string",
            "ArrayField": "list",
            "DecimalField": "decimal",
            "NormalizedDecimalField": "decimal",
            "DateTimeField": "datetime",
            "ImageField": "string",
            "TextField": "string",
            "URLField": "string",
            "TimeZoneField": "string",
            "ApplicationSettingValueField": "string",
            "EmailField": "string",
            "OneToOneField": "related",
            "IzbergForeignKey": "related",
            "FileField": "string",
            "UUIDField": "string",
            "SlugRelatedField": "string",
            "MessageURIContactRelatedField": "dict",
            "ListField": "list",
            "SlugField": "string",
            "LocalizedCharField": "string",
            "JSONField": "json",
            "ActionResourceUriField": "string",
            "SerializerMethodField": "various",
            "ArrayTaxesField": "json",
        }

        if model_field:
            return TYPE_MAPPING[model_field.__class__.__name__]

        try:
            # Check for attributes that start with "_" on Model
            attribute_name = "_" + field_name
            return TYPE_MAPPING[model_fields[attribute_name].__class__.__name__]
        except Exception:
            try:
                # Check for field in serializer
                return TYPE_MAPPING[serializer_field.__class__.__name__]
            except Exception:
                # Dunno what to do, return None
                logger.exception(f"Cannot find field type of: {field_name}")
                return None

    def _get_default(self, model_field, serializer_field):
        if serializer_field.default != empty:
            try:
                is_dict_or_list = issubclass(
                    serializer_field.default, dict
                ) or issubclass(serializer_field.default, list)
            except TypeError:
                is_dict_or_list = False

            if is_dict_or_list:
                return serializer_field.default()
            return serializer_field.default

        if model_field and hasattr(model_field, "default"):
            try:
                instance = model_field.default()
                if not isinstance(instance, NOT_PROVIDED):
                    return model_field.default()
            except TypeError:
                return model_field.default

        return ""

    def _get_max_length(self, model_field, serializer_field):
        if (
            hasattr(serializer_field, "max_length")
            and serializer_field.max_length is not None
        ):
            return serializer_field.max_length
        elif hasattr(model_field, "max_length"):
            return model_field.max_length

        return None

    def _get_related_type(self, model_relations, field_name):
        if model_relations[field_name].to_many:
            return "to_many"
        return "to_one"
