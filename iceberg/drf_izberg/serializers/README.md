# Serializers

Our serializer are right now in `iceberg/drf_izberg/serializers.py` but it makes senses to add some documentation here
as a serializer is most of the time linked to a view.


We have 2 base serializer:
- [IzbergHyperlinkedModelSerializer](#IzbergHyperlinkedModelSerializer)
- [IzbergHyperlinkedRelatedField](#IzbergHyperlinkedRelatedField)


## IzbergHyperlinkedModelSerializer

This serializer should always be used by default when creating a serializer for a django model.  
It is based on DRF HyperlinkedModelSerializer, simple model serializer that use hyperlink for describing related field (foreign key, many to many ect)  

We override `serializer_related_field` with our own serializer described below

### IzbergOnlyAskedFieldsMixin

The goal of this mixin is to reduce the amount of fields serialized when the user needs only a bunch.
This mixin removes any field from a serializer not present in the request params: only=field_1,field_2

if a field listed in "only" is not present in the serializer, it is simply ignored.

This feature works with the filtering and ordering, even if the field filtered is not present in "only" list.

It also works with the feature full representation of a relation ("?full_field=1").
BUT the field asked as full must be present in "only" list


## IzbergHyperlinkedRelatedField

Our version of DRF `HyperlinkedRelatedField`.

This class is use for every foreign relationship in a serializers.  
It is also use in `IzbergHyperlinkedModelSerializer` as a default serializer_related_field.  

This class deals with:  

  - Legacy backbone compatibility:  
    Django rest framework HyperlinkedRelatedField uses resource_uri to represent a relation,
    but izberg use id/pk/resource_uri when backbone_friendly is set to True.  
    This class override the `to_representation` to add the missing `id` and `pk`.  
    It also deals with `backbone_friendly_extra_fields` but that should be gone soon (lol)  

  - Full representation of a related field.
    Using the parameter "?full_FIELD_NAME=1" in an url will force the representation
    of the related field as a full dict instead of a resource_uri.  
    It is obviously not performant, but we need to be 100% compatible with Tastypie.  
    To see how it works, read the class description of `IzbergHyperlinkedRelatedField` in `drf_izberg/serializers`  


> This class should be used for every foreign relationship in a serializers.


## Tastypie PUT adapted to DRF

DRF expects ALL fields to be present when performing a PUT, but Izberg has some logic
where a field is writable when creating the resource but is not editable afterward.
Therefore, we make custom serializer that will change the attribute `required` to False
from all related fields listed in `creatable_fields`. 
This is only to trick DRF so that a POST required the fields in `creatable_fields` but a
PUT will not.

ex from Address:

``` Python
    class AddressSerializer(IzbergHyperlinkedModelSerializer):
        class Meta:
              model = Address
              depth = 0  # same as full = 0
              creatable_fields = ("user",)
              ...
        ...
      
    class AddressSerializerPUT(AddressSerializer):
        user = IzbergHyperlinkedRelatedField(
            view_name="user-detail",
            many=False,
            read_only=True,
            required=False,
            backbone_friendly=True,
        )
```

Then in the view we can choose the correct serializer based on the request method:

``` Python
    def get_serializer_class(self):
        if self.request.method == "PUT":
            return AddressSerializerPUT

        return AddressSerializer
```

> In october 2022, this behavior is still in use, but it should be slowly removed
> since our PUT are use as a PATCH, it is better to just use PATCH directly.
> Also, we need specific behavior for PUT as seen above but these issues are not present
> when using a PATCH. 
