from drf_izberg.serializers.hyper_linked_related_field import (
    IzbergHyperlinkedRelatedField,
)
from rest_framework import serializers
from rest_framework.fields import empty


class IzbergOnlyAskedFieldsMixin:
    """
    The goal of this mixin is to reduce the amount of fields serialized when the user
    needs only a bunch. This mixin removes any field from a serializer not present in
    the request params: only=field_1,field_2
    """

    def __init__(self, instance, data, **kwargs):
        super().__init__(instance=instance, data=data, **kwargs)

        # Condition are as follows:
        # - request must be in context (In some cases, the context will be emptied)
        # - query_param exist on the request
        # - "only" must be in the request parameters
        # - The current class must be the same as the one set up in the view.
        #   This prevents the "asked only feature" to be applied on nested resources
        #   or resources asked as full using "full__field"
        if (
            "request" in self.context
            and hasattr(self.context["request"], "query_params")
            and "only" in self.context["request"].query_params
            and self.__class__ == self.context["view"].serializer_class
        ):
            asked_fields = self.context["request"].query_params["only"].split(",")

            for field in list(self.fields.keys()):
                if field not in asked_fields:
                    self.fields.pop(field, None)


class IzbergHyperlinkedModelSerializer(
    IzbergOnlyAskedFieldsMixin,
    serializers.HyperlinkedModelSerializer,
):
    """
    Simple HyperlinkedModelSerializer that uses IzbergHyperlinkedRelatedField as
    a default serializer_related_field.
    """

    serializer_related_field = IzbergHyperlinkedRelatedField

    def __init__(self, instance=None, data=empty, **kwargs):
        super().__init__(instance=instance, data=data, **kwargs)

        # Ensure that creatable_fields, editable_fields and operator_writeable_fields
        # are explicitly declare in the serializer.
        # We could have added a Meta class here and inherit from it in our serializer
        # but the syntax sucks...
        # https://stackoverflow.com/questions/49900629/django-serializer-inherit-and-extend-fields
        # and DRF advice against it:
        # "Typically we would recommend not using inheritance on inner Meta classes,
        # but instead declaring all options explicitly."

        # Therefore, having safeguard here is ok-ish.
        assert hasattr(
            self.Meta, "creatable_fields"
        ), f"creatable_fields is missing in Meta class from {self.__class__.__name__}"

        assert hasattr(
            self.Meta, "editable_fields"
        ), f"editable_fields is missing in Meta class from {self.__class__.__name__}"

        assert hasattr(self.Meta, "operator_writeable_fields"), (
            f"operator_writeable_fields in missing on Meta class "
            f"from {self.__class__.__name__}"
        )
