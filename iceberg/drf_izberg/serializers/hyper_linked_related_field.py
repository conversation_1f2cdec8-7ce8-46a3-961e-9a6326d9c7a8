from rest_framework.relations import HyperlinkedRelatedField


class IzbergHyperlinkedRelated<PERSON>ield(HyperlinkedRelatedField):
    """
    This class is use for every foreign relationship in a serializers.
    It is also use below in IzbergHyperlinkedModelSerializer as a default
    serializer_related_field.

    This class deals with:

    - Legacy backbone compatibility:
      Django rest framework HyperlinkedRelatedField uses resource_uri to
      represent a relation, but izberg use id/pk/resource_uri when backbone_friendly is
      set to True.

    - Full representation of a related field.
      Using the parameter "?full_FIELD_NAME=1" in an url will force the representation
      of the related field as a full dict instead of a resource_uri.
      It is obviously not performant, but we need to be 100% compatible with Tastypie.
      here is how it works:
      1) Adding to a related field the dict full_representation with the needed info:
         - queryset
         - serializer
      2) When drf call to_representation, we check if the request has a full_FIELD_NAME
         to true
      3) We manually call to_internal_value, this will retrieve the object using our
      queryset from our get_queryset
      4) We serialize the object using the serializer defined in full_representation
      5) We return the serializer data
    """

    def __init__(self, view_name=None, **kwargs):
        self.backbone_friendly_extra_fields = kwargs.pop(
            "backbone_friendly_extra_fields", None
        )
        self.backbone_friendly = kwargs.pop("backbone_friendly", False)
        self.full_representation = kwargs.pop("full_representation", False)

        if self.full_representation:
            assert "queryset" in self.full_representation, (
                "'queryset' key in full_representation is missing. "
                "Verify your serializer."
            )

            assert "serializer" in self.full_representation, (
                "'serializer' key in full_representation is missing. "
                "Verify your serializer."
            )

        super().__init__(view_name, **kwargs)

    def is_full_representation(self):
        """
        Return True if all are true:
        - The user is requesting the field as full representation
        - The param has a value of true/1 ect
        - this field authorized a full representation.
          self.full_representation would be None for a field that doesn't allow a full
          representation.
        """

        if not hasattr(self.context["request"], "query_params"):
            return False

        key = f"full_{self.field_name}"
        query_param = self.context["request"].query_params.get(key, None)
        if (
            self.full_representation
            and query_param
            and query_param in ["true", "True", True, 1, "1"]
        ):
            return True

        return False

    def get_queryset(self):
        """
        Override this method to provide the needed queryset when trying to retrieve the
        object with self.to_internal_value(repr) below.
        We are using what is provided in self.full_representation.

        Warning: We could be tempted to change the serializer field by providing
        directly the queryset, but DRF would force us to set the attribute read_only
        to True. This is dangerous, because we want some field that are editable during
        creation but not afterward.
        Also, some field needs to stay in read_only (like Application) but we need the
        full_application=1 to work.
        It is safer to override the get_queryset here and ot not touch the value of
        read_only.
        """
        queryset = super().get_queryset()
        if self.is_full_representation():
            return self.full_representation["queryset"]
        return queryset

    def to_representation(self, value):
        repr = super().to_representation(value)

        # Deal with full representation by retrieving the object with to_internal_value
        # and then serializing it with what is provided in self.full_representation
        if self.is_full_representation():
            obj = self.to_internal_value(repr)
            return self.full_representation["serializer"](
                obj, context=self.context
            ).data

        # Legacy behavior Tastypie
        # Always return the resource_uri for a PATCH
        if self.context["request"].method == "PATCH":
            return repr

        # When backbone_friendly is not set, we simply return the resource_uri
        # This should be the correct behavior after dropping backbone support
        if not self.backbone_friendly:
            return repr

        # Legacy backbone format
        # building the payload with id, pk and resource_uri in a dictionary
        payload = {
            "id": value.pk,
            "pk": value.pk,
            "resource_uri": repr,
        }
        # add extra fields from backbone_friendly_extra_fields
        if self.backbone_friendly_extra_fields:
            # This is quite slow as we need to retrieve the model object
            obj = self.to_internal_value(repr)

            for field in self.backbone_friendly_extra_fields:
                payload[field] = getattr(obj, field, None)
        return payload
