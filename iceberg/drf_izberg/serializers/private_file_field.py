import fnmatch
import os
from datetime import datetime, timezone

import boto3
from django.conf import settings
from django.utils.translation import override
from ims.storage import create_s3_presigned_url
from lib.api.file_path import FilePathGenerator
from rest_framework.exceptions import ValidationError
from rest_framework.serializers import <PERSON>Field
from storages.backends.s3boto3 import S3Boto3StorageFile

DEFAULT_ACCESSIBLE_TIME_PRIVATE_FIELD = 300
DEFAULT_FILE_MAX_SIZE = 200000000  # value in bytes, which represent 200mo
DEFAULT_ALLOWED_MIMETYPES = (
    "application/json",
    "application/octet-stream",
    "application/pdf",
    "application/x-empty",
    "application/xhtml+xml",
    "application/xml",
    # Microsoft Word (OpenXML)
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",  # noqa
    "image/*",
    "text/*",
)


IMAGE_EXTENTIONS = (
    ".png",
    ".mng",
    ".tiff",
    ".jpeg",
    ".jpg",
    ".gif",
    ".tga",
    ".openexr",
    ".bmp",
    ".fits",
    ".webp",
)

DOCUMENT_EXTENTIONS = (
    ".odt",
    ".txt",
    ".doc",
    ".docx",
    ".xls",
    ".xlsx",
    ".rtf",
    ".pdf",
)

ARCHIVE_EXTENTIONS = (
    ".7z",
    ".tar",
    ".gzip",
    ".zip",
    ".lzw",
    ".arj",
    ".rar",
    ".sdc",
)

DATA_EXTENSIONS = (
    ".json",
    ".xml",
    ".csv",
)

DEFAULT_ALLOWED_EXTENSIONS = (
    IMAGE_EXTENTIONS + DOCUMENT_EXTENTIONS + ARCHIVE_EXTENTIONS + DATA_EXTENSIONS
)


class GenerateFilePath(FilePathGenerator):
    def get_data(self, instance_data_dict, filename):
        now = datetime.utcnow()
        app_id = instance_data_dict["app_id"]

        with override("en"):
            resource_name = (
                instance_data_dict["for_class"]
                ._meta.verbose_name.replace(" ", "_")
                .lower()
            )

        return {
            "now": now,
            "app_id": app_id,
            "resource_name": resource_name,
            "filename": self.generate_file_name(filename),
        }


class PrivateFileField(FileField):
    expires_in = DEFAULT_ACCESSIBLE_TIME_PRIVATE_FIELD
    file_max_size = DEFAULT_FILE_MAX_SIZE
    allowed_mimetypes = DEFAULT_ALLOWED_MIMETYPES
    allowed_extensions = DEFAULT_ALLOWED_EXTENSIONS

    s3_uploader_bucket_name = getattr(settings, "AWS_S3_UPLOADER_BUCKET_NAME", None)
    s3_core_bucket_name = getattr(settings, "AWS_STORAGE_BUCKET_NAME", None)
    s3_client = boto3.client("s3")

    def __init__(self, **kwargs):
        self.expires_in = kwargs.pop("expires_in", self.expires_in)
        self.file_max_size = kwargs.pop("file_max_size", self.file_max_size)
        self.allowed_mimetypes = kwargs.pop("allowed_mimetypes", self.allowed_mimetypes)
        self.allowed_extensions = kwargs.pop(
            "allowed_extensions", self.allowed_extensions
        )

        kwargs["use_url"] = True
        super().__init__(**kwargs)

    def to_representation(self, value):
        try:
            file = value.file
        except ValueError:
            file = None

        if file and isinstance(file, S3Boto3StorageFile):
            s3_obj = value.file.obj
            return create_s3_presigned_url(
                s3_obj.bucket_name, s3_obj.key, self.expires_in
            )

        return super().to_representation(value)

    def _extract_file_key_from_url(self, url):
        from urllib.parse import urlparse

        parsed_url = urlparse(url)
        path = parsed_url.path

        # Remove the leading slash if present
        if path.startswith("/"):
            path = path[1:]
        return path

    def _extract_file_name_from_url(self, url):
        # Get the last part of the URL path (the filename with extension)
        filename_with_extension = os.path.basename(url)

        # Extract the filename without the leading directories
        filename = filename_with_extension.split("/")[-1]

        return filename

    def _get_file_metadata(self, file_key):
        # Get the metadata for the object
        try:
            response = self.s3_client.head_object(
                Bucket=self.s3_uploader_bucket_name, Key=file_key
            )
            _, extension = os.path.splitext(file_key)
            return {
                "content_length": response["ContentLength"],
                "content_type": response["ContentType"],  # mimetype
                "extension": extension,
            }

        except Exception:
            raise ValidationError({self.field_name: "provided file is not found"})

    def _validate_file(self, data):
        # content_type validation
        is_valid = False
        for extension_pattern in self.allowed_mimetypes:
            if fnmatch.fnmatch(data["content_type"], extension_pattern):
                is_valid = True
                break
        if not is_valid:
            raise ValidationError({self.field_name: "content_type is not valid"})

        # extension validation
        if data["extension"] not in self.allowed_extensions:
            raise ValidationError({self.field_name: "extension is not valid"})

        # file size validation
        if data["content_length"] > self.file_max_size:
            raise ValidationError({self.field_name: "content_length is too high"})

        return True

    def _copy_file(self, source_key, destination_key):
        self.s3_client.copy_object(
            Bucket=self.s3_core_bucket_name,
            CopySource={"Bucket": self.s3_uploader_bucket_name, "Key": source_key},
            Key=destination_key,
        )

    def _add_tags_for_soft_deleted_file(self, file_key):
        utc_datetime_now = datetime.now(timezone.utc)
        tags = [
            {"Key": "soft_deleted", "Value": str(True)},
            {"Key": "soft_deleted_date", "Value": str(utc_datetime_now)},
        ]

        try:
            # Add tags to the file
            self.s3_client.put_object_tagging(
                Bucket=self.s3_core_bucket_name,
                Key=file_key,
                Tagging={"TagSet": tags},
            )
        except Exception:
            return

    def to_internal_value(self, data):
        app_id = self.context["request"].application_id
        file_key = self._extract_file_key_from_url(data)

        # Get file meta and do validation regarding size, mimetype, etc
        # raise exception if needed.
        # It should raise also an error if the provided file is not found in our bucket.
        file_meta = self._get_file_metadata(file_key)

        # When the request is for updating the file field, we need to check if
        # a file already exists in order to tag it as "deleted".
        # By tagging it, we can then archive it easily since it would be detached
        # from the database
        old_file_key = None
        if self.context["request"].method in ["PUT", "PATCH", "DELETE"]:
            # check if a file already exists or different
            old_file = getattr(self.parent.instance, self.field_name, None)
            old_file_key = str(old_file)

        self._validate_file(file_meta)
        file_name = self._extract_file_name_from_url(data)

        file_path_generator = GenerateFilePath()
        final_path_to_core_bucket = file_path_generator(
            {"app_id": app_id, "for_class": self.parent.Meta.model}, file_name
        )

        self._copy_file(source_key=file_key, destination_key=final_path_to_core_bucket)

        if old_file_key:
            self._add_tags_for_soft_deleted_file(old_file_key)

        data = final_path_to_core_bucket
        return data
