from apps.categories.models import ApplicationCategory
from drf_izberg.serializers.hyper_linked_model import IzbergHyperlinkedModelSerializer
from drf_izberg.views.base import IzbergModelViewSet


class ApplicationCategorySerializer(IzbergHyperlinkedModelSerializer):
    class Meta:
        model = ApplicationCategory
        creatable_fields = ()
        editable_fields = ()
        read_only_fields = (
            "id",
            "resource_uri",
        )
        fields = creatable_fields + editable_fields + read_only_fields
        operator_writeable_fields = ()


class ApplicationCategoryViewSet(IzbergModelViewSet):
    queryset = ApplicationCategory.objects.all()
    serializer_class = ApplicationCategorySerializer
    http_method_names = ["get"]
