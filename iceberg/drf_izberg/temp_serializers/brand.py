from apps.products.models import Brand
from drf_izberg.serializers.hyper_linked_model import IzbergHyperlinkedModelSerializer
from drf_izberg.views.base import IzbergModelViewSet


class BrandSerializer(IzbergHyperlinkedModelSerializer):
    class Meta:
        model = Brand
        creatable_fields = ()
        editable_fields = ()
        read_only_fields = (
            "id",
            "resource_uri",
        )
        fields = creatable_fields + editable_fields + read_only_fields
        operator_writeable_fields = ()


class BrandViewSet(IzbergModelViewSet):
    queryset = Brand.objects.all()
    serializer_class = BrandSerializer
    http_method_names = ["get"]
