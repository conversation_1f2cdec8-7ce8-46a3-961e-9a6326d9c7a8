from apps.cart.models import CartItem
from drf_izberg.serializers.hyper_linked_model import IzbergHyperlinkedModelSerializer
from drf_izberg.views.base import IzbergModelViewSet


class CartItemSerializer(IzbergHyperlinkedModelSerializer):
    class Meta:
        model = CartItem
        creatable_fields = ()
        editable_fields = ()
        read_only_fields = (
            "id",
            "resource_uri",
        )
        fields = creatable_fields + editable_fields + read_only_fields

        operator_writeable_fields = ()


class CartItemViewSet(IzbergModelViewSet):
    queryset = CartItem.objects.all()
    serializer_class = CartItemSerializer
    http_method_names = ["get"]
