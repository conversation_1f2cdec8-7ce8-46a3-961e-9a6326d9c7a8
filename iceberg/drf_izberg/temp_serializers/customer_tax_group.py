from apps.tax.models import CustomerTaxGroup
from drf_izberg.serializers.hyper_linked_model import IzbergHyperlinkedModelSerializer
from drf_izberg.views.base import IzbergModelViewSet


class CustomerTaxGroupSerializer(IzbergHyperlinkedModelSerializer):
    class Meta:
        model = CustomerTaxGroup
        creatable_fields = ()
        editable_fields = ()
        read_only_fields = (
            "id",
            "pk",
            "resource_uri",
        )
        fields = creatable_fields + editable_fields + read_only_fields

        operator_writeable_fields = ()


class CustomerTaxGroupViewSet(IzbergModelViewSet):
    queryset = CustomerTaxGroup.objects.all()
    serializer_class = CustomerTaxGroupSerializer
    http_method_names = ["get"]
