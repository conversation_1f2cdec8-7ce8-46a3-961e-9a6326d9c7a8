from apps.stores.models import Merchant
from drf_izberg.serializers.hyper_linked_model import IzbergHyperlinkedModelSerializer
from drf_izberg.views.base import IzbergModelViewSet


class MerchantSerializer(IzbergHyperlinkedModelSerializer):
    class Meta:
        model = Merchant
        creatable_fields = ()
        editable_fields = ()
        read_only_fields = (
            "id",
            "pk",
            "resource_uri",
        )
        fields = creatable_fields + editable_fields + read_only_fields

        operator_writeable_fields = ()


class MerchantViewSet(IzbergModelViewSet):
    queryset = Merchant.objects.all()
    serializer_class = MerchantSerializer
    http_method_names = ["get"]
