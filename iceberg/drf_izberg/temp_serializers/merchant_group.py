from drf_izberg.serializers.hyper_linked_model import IzbergHyperlinkedModelSerializer
from drf_izberg.views.base import IzbergModelViewSet
from merchant_groups.models import MerchantGroup


class MerchantGroupSerializer(IzbergHyperlinkedModelSerializer):
    class Meta:
        model = MerchantGroup
        creatable_fields = ()
        editable_fields = ()
        read_only_fields = (
            "id",
            "resource_uri",
        )
        fields = creatable_fields + editable_fields + read_only_fields
        operator_writeable_fields = ()


class MerchantGroupViewSet(IzbergModelViewSet):
    queryset = MerchantGroup.objects.exclude_deleted()
    serializer_class = MerchantGroupSerializer
    http_method_names = ["get"]
