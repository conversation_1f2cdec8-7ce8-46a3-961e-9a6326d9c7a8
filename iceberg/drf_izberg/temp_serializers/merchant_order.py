from apps.orders.models import MerchantOrder
from drf_izberg.serializers.hyper_linked_model import IzbergHyperlinkedModelSerializer
from drf_izberg.views.base import IzbergModelViewSet


class MerchantOrderSerializer(IzbergHyperlinkedModelSerializer):
    class Meta:
        model = MerchantOrder
        creatable_fields = ()
        editable_fields = ()
        read_only_fields = (
            "id",
            "resource_uri",
        )
        fields = creatable_fields + editable_fields + read_only_fields

        operator_writeable_fields = ()


class MerchantOrderViewSet(IzbergModelViewSet):
    queryset = MerchantOrder.objects.all()
    serializer_class = MerchantOrderSerializer
    http_method_names = ["get"]

    resource_name = "merchant-order"
