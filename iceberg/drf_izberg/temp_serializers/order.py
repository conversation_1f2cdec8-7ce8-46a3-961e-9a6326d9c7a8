from apps.orders.models import Order
from drf_izberg.serializers.hyper_linked_model import IzbergHyperlinkedModelSerializer
from drf_izberg.views.base import IzbergModelViewSet


class OrderSerializer(IzbergHyperlinkedModelSerializer):
    class Meta:
        model = Order
        creatable_fields = ()
        editable_fields = ()
        read_only_fields = (
            "id",
            "resource_uri",
        )
        fields = creatable_fields + editable_fields + read_only_fields

        operator_writeable_fields = ()


class OrderViewSet(IzbergModelViewSet):
    queryset = Order.objects.all()
    serializer_class = OrderSerializer
    http_method_names = ["get"]
