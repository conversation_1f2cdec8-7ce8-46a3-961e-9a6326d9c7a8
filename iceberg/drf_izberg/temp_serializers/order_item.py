from apps.orders.models import OrderItem
from drf_izberg.serializers.hyper_linked_model import IzbergHyperlinkedModelSerializer
from drf_izberg.views.base import IzbergModelViewSet
from rest_framework import serializers


class OrderItemSerializer(IzbergHyperlinkedModelSerializer):
    class Meta:
        model = OrderItem
        creatable_fields = ()
        editable_fields = ()
        read_only_fields = (
            "id",
            "resource_uri",
            "product_offer",
            "max_invoiceable",
            "created_on",
            "discount_amount_vat_included",
            "gtin",
            "previous_price",
            "variation_external_id",
            "size",
            "sku",
            "merchant_order",
            "offer_absolute_url",
            "shipped_on",
            "amount",
            "item_type",
            "discounts",
            "item_image_url",
            "status_localized",
            "price_vat_included",
            "previous_price_without_vat",
            "extra_info",
            "offer_id",
            "vat",
            "stock",
            "status",
            # "product",
            "invoiceable_quantity",
            "product_variation",
            "workflow",
            "price",
            "variation",
            "discount_amount_financed_by_application_with_tax",
            "last_modified",
            "tax_rate",
            "tax_rate_key",
            "customization_file",
            "amount_vat_included",
            "invoiced_quantity",
            "number_of_people",
            "received",
            "name",
            "gift",
            "variation_name",
            "external_id",
            "variation_stock",
            "custom_name",
            "shipping",
            "offer_external_id",
            "customization_value",
            "product_id",
            "image_url",
            "bundled",
            "quantity",
            "variation_sku",
            "ordered_offer",
            "ordered_product",
            "ordered_variation",
        )
        fields = creatable_fields + editable_fields + read_only_fields

        operator_writeable_fields = ()

    previous_price = serializers.ReadOnlyField(source="product_previous_price")
    size = serializers.ReadOnlyField(source="variation_size")

    offer_absolute_url = serializers.ReadOnlyField(default="")
    item_image_url = serializers.ReadOnlyField(source="image_url")

    status_localized = serializers.ReadOnlyField(source="get_status_display")

    previous_price_without_vat = serializers.ReadOnlyField(
        source="product_previous_price_without_vat"
    )

    stock = serializers.ReadOnlyField(source="offer__stock")

    product_variation = serializers.ReadOnlyField(source="product_offer_variation")

    discount_amount_financed_by_application_with_tax = serializers.ReadOnlyField(
        source="get_discount_amount_financed_by_application"
    )

    variation_stock = serializers.ReadOnlyField(source="variation__stock")


class OrderItemViewSet(IzbergModelViewSet):
    permission_classes = []
    filter_backends = []
    queryset = OrderItem.objects.all()
    serializer_class = OrderItemSerializer
    http_method_names = ["get", "patch"]
