from apps.products.models import ProductFamily
from drf_izberg.serializers.hyper_linked_model import IzbergHyperlinkedModelSerializer
from drf_izberg.views.base import IzbergModelViewSet


class ProductFamilySerializer(IzbergHyperlinkedModelSerializer):
    class Meta:
        model = ProductFamily
        creatable_fields = ()
        editable_fields = ()
        read_only_fields = (
            "id",
            "pk",
            "resource_uri",
        )
        fields = creatable_fields + editable_fields + read_only_fields

        operator_writeable_fields = ()


class ProductFamilyViewSet(IzbergModelViewSet):
    queryset = ProductFamily.objects.all()
    serializer_class = ProductFamilySerializer
    http_method_names = ["get"]
