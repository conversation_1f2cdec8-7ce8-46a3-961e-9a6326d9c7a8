from apps.products.models import ProductOffer
from drf_izberg.serializers.hyper_linked_model import IzbergHyperlinkedModelSerializer
from drf_izberg.views.base import IzbergModelViewSet


class ProductOfferSerializer(IzbergHyperlinkedModelSerializer):
    class Meta:
        model = ProductOffer
        creatable_fields = ()
        editable_fields = ()
        read_only_fields = ()

        fields = creatable_fields + editable_fields + read_only_fields

        operator_writeable_fields = ()


class ProductOfferViewSet(IzbergModelViewSet):
    permission_classes = []
    filter_backends = []
    queryset = ProductOffer.objects.all()
    serializer_class = ProductOfferSerializer
    http_method_names = ["get"]
