from apps.returns.models import Refund
from drf_izberg.permissions.actions import ActionsPermissions
from drf_izberg.permissions.constants.actions import (
    CREATE,
    DESTROY,
    LIST,
    PARTIAL_UPDATE,
    RETRIEVE,
    UPDATE,
)
from drf_izberg.permissions.constants.owner import MERCHANT_OWNER, OPERATOR_OWNER
from drf_izberg.permissions.constants.roles import ALL_ROLES, EDIT_ROLES
from drf_izberg.permissions.constants.scopes import OMS_SCOPE
from drf_izberg.permissions.http_methods import HTTPMethodsPermissions
from drf_izberg.permissions.owners import OwnersPermissions
from drf_izberg.permissions.roles import RolesPermissions
from drf_izberg.permissions.scopes import ScopesPermissions
from drf_izberg.permissions.write_fields import WriteFieldsPermissions
from drf_izberg.serializers.hyper_linked_model import IzbergHyperlinkedModelSerializer
from drf_izberg.views.base import IzbergModelViewSet
from rest_framework.permissions import IsAuthenticated


class RefundSerializer(IzbergHyperlinkedModelSerializer):
    class Meta:
        model = Refund
        creatable_fields = (
            "amount",
            "shipping",
            "partial_refund",
            "order_items",
            "merchant_order",
            "memo",
            "seller_note",
        )
        editable_fields = ()
        read_only_fields = (
            "id",
            "pk",
            "resource_uri",
        )
        fields = creatable_fields + editable_fields + read_only_fields

        operator_writeable_fields = ()


class RefundViewSet(IzbergModelViewSet):
    permission_classes = [
        IsAuthenticated,
        HTTPMethodsPermissions,
        OwnersPermissions,
        ScopesPermissions,
        ActionsPermissions,
        RolesPermissions,
        WriteFieldsPermissions,
    ]

    permission_constraints = {
        OPERATOR_OWNER: {
            LIST: ALL_ROLES,
            CREATE: EDIT_ROLES,
            RETRIEVE: ALL_ROLES,
            UPDATE: EDIT_ROLES,
            PARTIAL_UPDATE: EDIT_ROLES,
            DESTROY: EDIT_ROLES,
        },
        MERCHANT_OWNER: {
            LIST: ALL_ROLES,
            CREATE: EDIT_ROLES,
            RETRIEVE: ALL_ROLES,
            UPDATE: EDIT_ROLES,
            PARTIAL_UPDATE: EDIT_ROLES,
            DESTROY: EDIT_ROLES,
        },
    }
    resource_name = "refund"

    permission_scopes = [OMS_SCOPE]

    queryset = Refund.objects.all()
    serializer_class = RefundSerializer
    http_method_names = ["get", "post"]
