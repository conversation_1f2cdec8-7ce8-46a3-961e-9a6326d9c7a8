from drf_izberg.serializers.hyper_linked_model import IzbergHyperlinkedModelSerializer
from drf_izberg.views.base import IzbergModelViewSet
from shipping2.models import ShippingProvider


class ShippingProviderSerializer(IzbergHyperlinkedModelSerializer):
    class Meta:
        model = ShippingProvider
        creatable_fields = ()
        editable_fields = ()
        read_only_fields = (
            "id",
            "pk",
            "resource_uri",
        )
        fields = creatable_fields + editable_fields + read_only_fields

        operator_writeable_fields = ()


class ShippingProviderViewSet(IzbergModelViewSet):
    queryset = ShippingProvider.objects.all()
    serializer_class = ShippingProviderSerializer
    http_method_names = ["get"]
