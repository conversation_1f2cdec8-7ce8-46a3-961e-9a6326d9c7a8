from apps.transactions.models import Transaction
from drf_izberg.serializers.hyper_linked_model import IzbergHyperlinkedModelSerializer
from drf_izberg.views.base import IzbergModelViewSet


class TransactionSerializer(IzbergHyperlinkedModelSerializer):
    class Meta:
        model = Transaction
        creatable_fields = ()
        editable_fields = ()
        read_only_fields = (
            "id",
            "resource_uri",
        )
        fields = creatable_fields + editable_fields + read_only_fields

        operator_writeable_fields = ()


class TransactionViewSet(IzbergModelViewSet):
    queryset = Transaction.objects.all()
    serializer_class = TransactionSerializer
    http_method_names = ["get"]
