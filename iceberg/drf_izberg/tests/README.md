# Tests

This folder contains some basic class and decorators for testing our API.

We have 4 testing class:
- [BaseAPITestCase](#BaseAPITestCase)
- [BaseAPITransactionTestCase](#BaseAPITransactionTestCase)
- [BasePermissionsAPITestCase](#BasePermissionsAPITestCase)
- [BaseSerializerAPITestCase](#BaseSerializerAPITestCase)

We have 2 decators but they are meant to be used together:
- [bypass_authentication_and_permissions](#bypass_authentication_and_permissions)
- [for_all_methods](#for_all_methods)



## BaseAPITestCase

Basic class for testing our API.  
Provides:
- A `self.api_client` to perform request and a `self.deserialize()` to see the response as python object.  
- Verbose assertion error like: `assertHttpCreated`, `assertHttpOK` ect

``` Python
    resp = self.api_client.get(
        "/{}/address/".format(self.API_VERSION, address.id),
        authentication=(
          "Bearer " + CUSTOMER_TOKENS["APPLICATION_1_WRITE"]["token"]
        ),
    )
    
    self.assertHttpOK(resp)
    data = self.deserialize(resp)
    self.assertEqual(data["meta"]["total_count"], 1)
```


- An easy way to check for the amount of queries performed during the request with `withAssertNumQueriesLessThan`

``` Python
    with self.withAssertNumQueriesLessThan(7, verbose=True):
        resp = self.api_client.get(
            "/{}/address/".format(self.API_VERSION),
            authentication=self.AUTH,
        )
```

- Basic fixtures 


## BaseAPITransactionTestCase

Same as above just using `TransactionTestCase` instead of `TestCase`


## BasePermissionsAPITestCase

Base class to test the permissions of a view.
Provides:
- A fake request use to provide scopes and owner_type to be checked
- A view to be tested 
- 2 helper methods to check if the permissions should pass or raise


example:
``` Python
class AddressPermissionsTestCase(BasePermissionsAPITestCase):
    def setUp(self):
        super().setUp()
        self.view = AddressViewSet()
        self.view.action = None

    def test_operator_customer_read(self):
        self.request.owner_type = OPERATOR_OWNER
        self.request.scopes = [f"{CUSTOMER_SCOPE}:{READ_ROLE}"]

        self._should_pass_permissions_with_action("list")
        self._should_pass_permissions_with_action("retrieve")
        self._should_pass_permissions_with_action("active_addresses")

        # Cannot perform creation/edition/deletion with read role.
        self._should_raise_with_action("create")
        self._should_raise_with_action("update")
```

## BaseSerializerAPITestCase

Base class to test the serializer of a view.
Provides:
- A fake request
- An application and merchant added to the request 
  > This Test class will generate an application and a merchant with the id=1
  > no need to create another application in your test.

example:
``` Python

class StateSerializerTestCase(BaseSerializerAPITestCase):
    API_VERSION = "v2"
    # This attribute controls the maximum length of diffs output by assert methods
    # that report diffs on failure.
    maxDiff = None

    def test_serializer(self):
        state = State.objects.first()
        state_country = state.country

        serializer = StateSerializer(
            instance=state,
            context={"request": self.request},
        )

        output = {
            "id": state.id,
            "pk": state.pk,
            "resource_uri": (
                f"http://testserver/{self.API_VERSION}/" f"state/{state.id}/"
            ),
            ...
            ...
            },
        }

        self.assertEqual(serializer.data, output)
```


## bypass_authentication_and_permissions

This decorator is particularly useful when you want to bypass the authentication and permissions on a view.  
This decorator will also create an application and merchant on the request with the id=1.  

The decorator takes as a parameter the View use in your test.
For more information, read the comment on the method: `bypass_authentication_and_permissions` 

Usage:
``` Python
@for_all_methods(bypass_authentication_and_permissions(AddressViewSet))
class AddressViewsTestCase(BaseAPITestCase):
    API_VERSION = "v2"

    def test_get_list(self):
        address = AddressFactory(application__id=1)
        resp = self.api_client.get("/{}/address/".format(self.API_VERSION))

        self.assertHttpOK(resp)
       

```


NB: Filters on Application and Merchant are bypass too.

A fake request is created with an application (id=1) and a merchant (id=2) 
instantiated that simulate the behavior of `IzbergJWTAuthentication` (Default authentication class).

To learn more about the setup of this fake request: `drf_izberg.tests.base_decorators.MockJWTAuthentication`

## for_all_methods

Decorator that apply another decorator on all the methods of a class.  
We use it with the decorator above `bypass_authentication_and_permissions` to apply it on each of our tests.

See usage above.


# How to test

“All code is guilty, until proven innocent.”

With DRF we have restructured our way of testing by dividing our tests files into different categories.
Each responsible to test one aspect of the API.

## Structure 

This is the structure that you should follow when testing a resource.  
Each resource must have his own folder.  

Some files migtht not be always present.  
As a good practice, these files should always be present: 
- test_api_permissions
- test_api_query_performance
- test_api_schema
- test_api_serializers

```
tests/
├─ resource_a/
│  ├─ api/
│     ├─ test_api.py
│     ├─ test_api_endpoints.py
│     ├─ test_api_filters.py
│     ├─ test_api_permissions.py
│     ├─ test_api_query_performance.py
│     ├─ test_api_schema.py
│     ├─ test_api_serializers.py
│  ├─ model/
│     ├─ test_model.py
│  ├─ action/
│     ├─ test_actions.py
├─ resource_b/
│  ├─ .
│  ├─ .
│  ├─ .
```

Extra tests might be necessary and should be added in different files and directory.
(Tasks tests ect)

**Tips & tricks**

- To simplify reading, we separate classics endpoints tests to custom endpoints.

> - Classics endpoints are List, Retrieve, Create, Update, Delete and they will be added on `test_api.py`. 
  In this file, you can find 2 Classes, 1 without auth, permissions and filters, the second is not mandatory, 
  it's to test specific behavior of classics endpoints in order to auth, permissions or filters.
> - The custom endpoints are in `test_api_endpoints.py`