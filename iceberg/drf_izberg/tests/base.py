import json

from apps.testing.factories import ApplicationFactory, MerchantFactory
from apps.user.models import InMemoryUser
from django.test import RequestFactory, TestCase, TransactionTestCase
from ims.tests.base_test_cases import (
    FIXTURES,
    AssertNumQueriesLessThan,
    ClearCacheMixin,
    ConfigurableMaxDiffMixin,
    LogExecTimeMixin,
    RandomizedSequenceMixin,
    VerboseAssersionErrorMixin,
)
from rest_framework.test import APIClient
from rest_framework.versioning import NamespaceVersioning


class APIClientMixin:
    """
    Mixin to facilitate API calls with DRF.
    Simulate the structure of tastypie get/post/put/patch/delete with authentication:
    ex:
    self.api_client.get(
        "/v1/address/",
        authentication=(
            "Bearer " + CUSTOMER_TOKENS["APPLICATION_1_READ"]["token"]
        ),
    )
    """

    class APIClientProxy:
        client = APIClient()

        def add_credentials(self, kwargs):
            # Reset credentials before every request
            self.client.credentials()

            if "authentication" in kwargs:
                self.client.credentials(HTTP_AUTHORIZATION=kwargs["authentication"])

        def get(self, *args, **kwargs):
            self.add_credentials(kwargs)
            return self.client.get(*args, **kwargs)

        def post(self, *args, **kwargs):
            self.add_credentials(kwargs)
            return self.client.post(*args, **kwargs)

        def patch(self, *args, **kwargs):
            self.add_credentials(kwargs)
            return self.client.patch(*args, **kwargs)

        def put(self, *args, **kwargs):
            self.add_credentials(kwargs)
            return self.client.put(*args, **kwargs)

        def delete(self, *args, **kwargs):
            self.add_credentials(kwargs)
            return self.client.delete(*args, **kwargs)

    api_client = APIClientProxy()

    def deserialize(self, response):
        return json.loads(response.content)


class BaseAPITestCase(
    RandomizedSequenceMixin,
    ConfigurableMaxDiffMixin,
    VerboseAssersionErrorMixin,
    LogExecTimeMixin,
    ClearCacheMixin,
    AssertNumQueriesLessThan,
    APIClientMixin,
    TestCase,
):
    """
    Base class to inherit when testing DRF api
    """

    fixtures = FIXTURES
    databases = "__all__"


class BaseAPITransactionTestCase(
    RandomizedSequenceMixin,
    ConfigurableMaxDiffMixin,
    VerboseAssersionErrorMixin,
    LogExecTimeMixin,
    ClearCacheMixin,
    AssertNumQueriesLessThan,
    APIClientMixin,
    TransactionTestCase,
):
    """
    Base class to inherit when testing DRF api (using Transaction TransactionTestCase)
    """

    fixtures = FIXTURES
    databases = "__all__"


class BasePermissionsAPITestCase(TestCase):
    """
    base class to test permissions of a view.
    Has 2 methods use to check if for a given action the permissions
    should pass or raise.
    """

    def setUp(self):
        self.request = RequestFactory()
        self.request.user = InMemoryUser(username="test")
        # The request method doesn't matter much when testings permissions
        self.request.method = "GET"
        # WriteFieldsPermissions needs to read the data in the request.
        # We need to define it empty to avoid "Request object has no attribute 'data'"
        self.request.data = {}
        # In case of raising error, the value must exist on the request.
        self.raw_scope = "fake"

        self.view = None

    def _replace_permissions_for_custom_endpoints(self, action):
        """
        When adding custom endpoints on our views, the decorator @action() offer the
        possibility to override the permissions.
        To handle this scenario we need to override the permissions of our view with
        the one from this action.
        Of course, normal create/update/destroy methods ect are not affected.

        This method will go through all extra actions set on the view and override the
        permissions if the following is true:
        - Our action is found in the list of get_extra_actions
        - This extra action has overridden the permissions, therefore permission_classes
          key exists on extra_action.kwargs

        """
        for extra_action in self.view.get_extra_actions():
            if (
                extra_action.__name__ == action
                and "permission_classes" in extra_action.kwargs
            ):
                self.view.permission_classes = extra_action.kwargs["permission_classes"]
                return

    def _replace_scopes_for_custom_endpoints(self, action):
        """
        When adding custom endpoints on our views, the decorator @action() offer the
        possibility to override the permission_scopes.
        To handle this scenario we need to override the permission_scopes of our view
        with the one from this action.

        This method will go through all extra actions set on the view and override the
        permissions if the following is true:
        - Our action is found in the list of get_extra_actions
        - This extra action has overridden the scopes, therefore permission_scopes
          key exists on extra_action.kwargs

        """
        for extra_action in self.view.get_extra_actions():
            if (
                extra_action.__name__ == action
                and "permission_scopes" in extra_action.kwargs
            ):
                self.view.permission_scopes = extra_action.kwargs["permission_scopes"]
                return

    def _should_pass_permissions_with_action(self, action):
        self.view.action = action

        # Handle custom action by replacing the current permissions and scope on the
        # view with the one from the custom action
        self._replace_permissions_for_custom_endpoints(action)
        self._replace_scopes_for_custom_endpoints(action)

        try:
            self.view.check_permissions(self.request)
        except Exception:
            assert False, "Permissions is denied, see traceback"

    def _should_raise_with_action(self, action, type=Exception):
        self.view.action = action

        # Handle custom action by replacing the current permissions and scope on the
        # view with the one from the custom action
        self._replace_permissions_for_custom_endpoints(action)
        self._replace_scopes_for_custom_endpoints(action)

        with self.assertRaises(type):
            self.view.check_permissions(self.request)


class BaseSerializerAPITestCase(TestCase):
    """
    base class to test resourse serializer
    """

    fixtures = FIXTURES
    databases = "__all__"

    def setUp(self):
        self.request = RequestFactory().get("/")

        # These 2 lines are super important because when testing the serializer, it
        # needs the request with some information that are normally added in the flow of
        # request -> view -> action -> serializer
        # But we are mocking the request.
        # So these 2 attribute "fix" any view_name use when generating the resource_uri
        # of related foreign key on the instance.
        # Otherwise, django reverse will not find the url inside his namespace.
        # ex with address and his user foreign key
        # DRF will check for the url name "user-detail" but before the call of django
        # reverse, it will call NamespaceVersioning and use request.version to
        # prepend the url name with it.
        # django reverse will be called with "v2:user-detail"
        self.request.version = "v2"
        self.request.versioning_scheme = NamespaceVersioning()

        # In these tests, we need application and merchant already setup on the request
        # in case the serializer needs the value when performing to_representation
        # see AddressSerializer for an example.
        self.request.application = ApplicationFactory(id=1)
        self.request.application_id = 1
        self.request.merchant = MerchantFactory.create_for_application(
            self.request.application, id=1
        )
        self.request.merchant_id = 1
