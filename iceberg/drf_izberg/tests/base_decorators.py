from unittest.mock import patch

from apps.ice_applications.models import Application
from apps.stores.models import Merchant
from apps.testing.factories import ApplicationFactory, MerchantFactory
from apps.user.models import InMemoryUser
from django.conf import settings
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.authentication import BaseAuthentication
from rest_framework.filters import OrderingFilter


class MockJWTAuthentication(BaseAuthentication):
    """
    Auhentification class to be use *ONLY* in tests.
    Add application and merchant to the token.
    We need this because some of ours serializers use the application in the request
    to set the application on the new created instances (see address)
    ex:
    data["application"] = self.context["request"].application

    Note:
    See below in 'bypass_authentication_and_permissions' for an example.
    """

    def __init__(self):
        assert settings.TEST_IS_RUNNING, "You should use this class in test only!"

    def authenticate(self, request):
        application = Application.objects.filter(id=1).first()
        if not application:
            application = ApplicationFactory(id=1)

        merchant = Merchant.objects.filter(id=1).first()
        if not merchant:
            merchant = MerchantFactory.create_for_application(application, id=1)

        request.application_id = application.id
        request.application = application
        request.merchant_id = merchant.id
        request.merchant = merchant
        request.owner_type = "operator"
        request.scopes = ["admin"]
        request.user = InMemoryUser(username="test")
        request.author_uri = "test"
        request.author_name = "test"
        request.author_type = "test"


def bypass_authentication_and_permissions(view):
    """
    Decorator that apply a patch on the decorated method.
    - Removes the need of authentification when calling endpoints from the view.
      ( MockJWTAuthentication will add application/merchant/user ect on the request)
    - Removes all permissions set on the view
    - Remove specific filters and keep only djangoFilter + orderingFilter

    @Params:
        view: View class to patch

    """

    def decorator_bypass(func):
        import functools

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            with patch.multiple(
                view,
                permission_classes=[],
                authentication_classes=[MockJWTAuthentication],
                filter_backends=[DjangoFilterBackend, OrderingFilter],
            ):
                return func(*args, **kwargs)

        return wrapper

    return decorator_bypass


def for_all_methods(decorator):
    """
    Decorator that apply another decorator on ALL methods of a class.
    Must be used on a Class.
    """

    def decorate(cls):
        for attr in cls.__dict__:
            if callable(getattr(cls, attr)):
                setattr(cls, attr, decorator(getattr(cls, attr)))
        return cls

    return decorate
