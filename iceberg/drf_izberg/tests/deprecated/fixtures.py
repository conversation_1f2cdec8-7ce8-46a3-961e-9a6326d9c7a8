from apps.ice_applications.models import Application
from django_filters import rest_framework as filters
from drf_izberg.deprecation.custom_endpoints_mixin import DeprecatedCustomEndpointsMixin
from drf_izberg.deprecation.filter_attributes_mixin import (
    DeprecatedFilterAttributesMixin,
)
from drf_izberg.deprecation.ordering_attributes_mixin import (
    DeprecatedOrderingAttributesMixin,
)
from drf_izberg.deprecation.schema_mixin import DeprecatedSchemaMixin
from drf_izberg.deprecation.serializer_attributes_mixin import (
    DeprecatedSerializerAttributesMixin,
)
from drf_izberg.serializers.hyper_linked_model import IzbergHyperlinkedModelSerializer
from drf_izberg.views.schema_mixins import IzbergSchemaMixin
from rest_framework import serializers
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import GenericViewSet


class FakeApplicationSerializerWithDeprecated(
    DeprecatedSerializerAttributesMixin,
    IzbergHyperlinkedModelSerializer,
):
    class Meta:
        # Using application to avoid the need of creating a fake model.
        # Application will probably still exist in 10 years :p
        model = Application
        creatable_fields = ()
        editable_fields = (
            "namespace",
            "name",
        )
        operator_writeable_fields = ()
        fields = creatable_fields + editable_fields

    namespace = serializers.CharField(
        required=False,
    )


class FakeApplicationFilterWithDeprecated(
    DeprecatedFilterAttributesMixin,
    filters.FilterSet,
):
    """
    Use with a view that implement `deprecation` dict like so:
    deprecation = {
        "fields": {
            "website": DeprecatedFeatureFactory().key,
        },
    }
    """

    namespace = filters.CharFilter(
        field_name="namespace",
        lookup_expr="iexact",
    )

    class Meta:
        model = Application
        fields = [
            "name",
            "namespace",
        ]


class FakeApplicationViewSetWithDeprecated(
    DeprecatedSchemaMixin,
    DeprecatedCustomEndpointsMixin,
    DeprecatedOrderingAttributesMixin,
    IzbergSchemaMixin,
    GenericViewSet,
):
    permission_classes = {}
    serializer_class = FakeApplicationSerializerWithDeprecated
    resource_name = "fake_view"
    filterset_class = FakeApplicationFilterWithDeprecated
    legacy_schema_extra_info = {
        "filters": {
            "namespace": "iexact",
        },
        "allowed_detail_http_methods": [
            "get",
        ],
        "allowed_list_http_methods": [
            "get",
        ],
    }
    ordering_fields = [
        "namespace",
    ]

    @action(
        methods=["get"],
        detail=False,
        url_path="fake",
        name="fake_custom_endpoint",
        permission_classes={},
    )
    def fake_custom_endpoint(self, request):
        return Response("ok")
