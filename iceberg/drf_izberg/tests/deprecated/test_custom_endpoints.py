from datetime import timedelta

from apps.deprecation.models import DeprecatedFeature
from apps.testing.factories import DeprecatedFeatureFactory
from django.http import HttpRequest
from django.utils import timezone
from drf_izberg.tests.base import BaseAPITestCase
from drf_izberg.tests.deprecated.fixtures import FakeApplicationViewSetWithDeprecated


class DeprecatedCustomEndpointTestCase(BaseAPITestCase):
    def test_deprecated_endpoint(self):
        """
        Given:
            - a fake request
            - a fake view set with deprecation set
            - a deprecated feature with decommission_after in the past
        When calling the class dispatch
        Then the custom endpoint return a code 404
        """

        deprecated_feature = DeprecatedFeatureFactory(
            key=DeprecatedFeature.KEY_CHOICES[0][0],
            decommission_after=timezone.now() - timedelta(days=1),
        )

        view = FakeApplicationViewSetWithDeprecated.as_view(
            {"get": "fake_custom_endpoint"}
        )
        view.cls.deprecation = {
            "urls": {
                "fake_custom_endpoint": deprecated_feature.key,
            },
        }
        view.cls.headers = {}
        request = HttpRequest()
        request.method = "GET"
        response = view(request)
        response.render()

        self.assertHttpNotFound(response)

    def test_deprecated_endpoint__decommission_date_in_future(self):
        """
        Given:
            - a fake request
            - a fake view set with deprecation set
            - a deprecated feature with decommission_after in the future
        When calling the class dispatch
        Then the custom endpoint return a code 200
        """

        deprecated_feature = DeprecatedFeatureFactory(
            key=DeprecatedFeature.KEY_CHOICES[0][0],
            decommission_after=timezone.now() + timedelta(days=1),
        )

        view = FakeApplicationViewSetWithDeprecated.as_view(
            {"get": "fake_custom_endpoint"}
        )
        view.cls.deprecation = {
            "urls": {
                "fake_custom_endpoint": deprecated_feature.key,
            },
        }
        view.cls.headers = {}
        request = HttpRequest()
        request.method = "GET"
        response = view(request)
        response.render()

        self.assertHttpOK(response)
