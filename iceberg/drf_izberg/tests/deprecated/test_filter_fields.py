from datetime import timedelta

from apps.deprecation.models import DeprecatedFeature
from apps.ice_applications.models import Application
from apps.testing.factories import ApplicationFactory, DeprecatedFeatureFactory
from django.test import RequestFactory
from django.utils import timezone
from django_filters.rest_framework import DjangoFilterBackend
from drf_izberg.tests.base import BaseAPITestCase
from drf_izberg.tests.deprecated.fixtures import (
    FakeApplicationFilterWithDeprecated,
    FakeApplicationViewSetWithDeprecated,
)


class DeprecatedFieldFilteringTestCase(BaseAPITestCase):
    def test_deprecated_fields__filter(self):
        """
        Given:
            - a fake request
            - a fake view set with deprecation set
            - a deprecated feature with decommission_after in the past
            - a fake filterclass
        When calling the filtering
        Then the queryset is NOT filtered
        """

        view = FakeApplicationViewSetWithDeprecated()
        deprecated_feature = DeprecatedFeatureFactory(
            key=DeprecatedFeature.KEY_CHOICES[0][0],
            decommission_after=timezone.now() - timedelta(days=1),
        )
        view.deprecation = {
            "fields": {
                "namespace": deprecated_feature.key,
            },
        }
        view.filter_backends = [
            DjangoFilterBackend,
        ]
        view.filterset_class = FakeApplicationFilterWithDeprecated

        request = RequestFactory()
        # Simulate what DRF would add on the request
        request.query_params = {
            "namespace__iexact": "batcave",
        }
        request.parser_context = {"view": view}
        view.request = request

        # 2 apps with one having the same value use in the query_params
        ApplicationFactory(namespace="batcave")
        ApplicationFactory()

        queryset = Application.objects.all()
        filtered_queryset = view.filter_queryset(queryset)

        self.assertEqual(filtered_queryset.count(), 2)
        # application = filtered_queryset.get()
        # self.assertEqual(application.name, "batman")

    def test_deprecated_fields__filter__decommission_date_in_future(self):
        """
        Given:
            - a fake request
            - a fake view set with deprecation set
            - a deprecated feature with decommission_after in the future
            - a fake filterclass
        When calling the filtering
        Then the queryset is NOT filtered
        """

        view = FakeApplicationViewSetWithDeprecated()
        deprecated_feature = DeprecatedFeatureFactory(
            key=DeprecatedFeature.KEY_CHOICES[0][0],
            decommission_after=timezone.now() + timedelta(days=1),
        )
        view.deprecation = {
            "fields": {
                "namespace": deprecated_feature.key,
            },
        }
        view.filter_backends = [
            DjangoFilterBackend,
        ]
        view.filterset_class = FakeApplicationFilterWithDeprecated

        request = RequestFactory()
        # Simulate what DRF would add on the request
        request.query_params = {
            "namespace": "batcave",
        }
        request.parser_context = {"view": view}
        view.request = request

        # 2 apps with one having the same value use in the query_params
        ApplicationFactory(id=1)
        ApplicationFactory(id=2, namespace="batcave")

        queryset = Application.objects.all()
        filtered_queryset = view.filter_queryset(queryset)

        self.assertEqual(filtered_queryset.count(), 1)
        application = filtered_queryset.get()
        self.assertEqual(application.namespace, "batcave")
