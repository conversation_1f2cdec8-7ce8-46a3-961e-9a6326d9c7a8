from datetime import timedelta

from apps.deprecation.models import DeprecatedFeature
from apps.ice_applications.models import Application
from apps.testing.factories import ApplicationFactory, DeprecatedFeatureFactory
from apps.user.models import InMemoryUser
from django.test import RequestFactory
from django.utils import timezone
from drf_izberg.tests.base import BaseAPITestCase
from drf_izberg.tests.deprecated.fixtures import FakeApplicationViewSetWithDeprecated
from rest_framework.filters import OrderingFilter


class DeprecatedFieldOrderingTestCase(BaseAPITestCase):
    def test_deprecated_fields__ordering(self):
        """
        Given:
            - a fake request
            - a fake view set with deprecation set and ordering set
            - a deprecated feature with decommission_after in the past
        When calling the filtering
        Then the queryset is NOT ordered
        """
        view = FakeApplicationViewSetWithDeprecated()
        deprecated_feature = DeprecatedFeatureFactory(
            key=DeprecatedFeature.KEY_CHOICES[0][0],
            decommission_after=timezone.now() - timedelta(days=1),
        )
        view.deprecation = {
            "fields": {
                "namespace": deprecated_feature.key,
            },
        }
        view.filter_backends = [
            OrderingFilter,
        ]
        view.ordering_fields = {
            "namespace",
            "name",
        }

        request = RequestFactory()
        request.META = {}
        request.user = InMemoryUser(username="test")
        # Simulate what DRF would add on the request
        request.query_params = {
            "order_by": "-namespace",
        }
        view.request = request
        view.initial(request)

        # 2 apps with one having the same value use in the query_params
        aa = ApplicationFactory(id=1, namespace="aaaa")
        zz = ApplicationFactory(id=2, namespace="zzzz")

        queryset = Application.objects.all()
        filtered_queryset = view.filter_queryset(queryset)

        self.assertEqual(filtered_queryset[0], aa)
        self.assertEqual(filtered_queryset[1], zz)

    def test_deprecated_fields__ordering__decommission_date_in_future(self):
        """
        Given:
            - a fake request
            - a fake view set with deprecation set and ordering set
            - a deprecated feature with decommission_after in the future
        When calling the filtering
        Then the queryset IS ordered
        """
        view = FakeApplicationViewSetWithDeprecated()
        deprecated_feature = DeprecatedFeatureFactory(
            key=DeprecatedFeature.KEY_CHOICES[0][0],
            decommission_after=timezone.now() + timedelta(days=1),
        )
        view.deprecation = {
            "fields": {
                "namespace": deprecated_feature.key,
            },
        }
        view.filter_backends = [
            OrderingFilter,
        ]
        view.ordering_fields = {
            "namespace",
            "name",
        }

        request = RequestFactory()
        # Simulate what DRF would add on the request
        request.query_params = {
            "order_by": "-namespace",
        }
        view.request = request

        # 2 apps with one having the same value use in the query_params
        aa = ApplicationFactory(id=1, namespace="aaaa")
        zz = ApplicationFactory(id=2, namespace="zzzz")

        queryset = Application.objects.all()
        filtered_queryset = view.filter_queryset(queryset)

        self.assertEqual(filtered_queryset[0], zz)
        self.assertEqual(filtered_queryset[1], aa)
