import types
from datetime import timedelta

from apps.deprecation.models import DeprecatedFeature
from apps.testing.factories import DeprecatedFeatureFactory
from django.http import HttpRequest
from django.utils import timezone
from drf_izberg.tests.base import BaseAPITestCase
from drf_izberg.tests.deprecated.fixtures import FakeApplicationViewSetWithDeprecated
from freezegun import freeze_time
from mock import patch


class DeprecatedSchemaTestCase(BaseAPITestCase):
    @patch("rest_framework.viewsets.ViewSetMixin.reverse_action")
    def test_deprecated_schema(self, _mock):
        """
        Given:
            - a fake request
            - a fake view set with deprecation set
            - a deprecated feature with decommission_after in the past
        When calling schema
        Then the schema doesn't have the field, filtering and ordering listed in
        deprecation
        """

        # This fake view is not registered in our urls.py. Therefore, we need to mock
        # the reverse_action
        _mock.return_value = "/v2/fake_view/fake/"

        deprecated_feature = DeprecatedFeatureFactory(
            key=DeprecatedFeature.KEY_CHOICES[0][0],
            decommission_after=timezone.now() - timedelta(days=1),
        )

        view = FakeApplicationViewSetWithDeprecated.as_view({"get": "schema"})
        view.cls.deprecation = {
            "fields": {
                "namespace": deprecated_feature.key,
            },
            "urls": {
                "fake_custom_endpoint": deprecated_feature.key,
            },
        }
        view.cls.headers = {}
        request = HttpRequest()
        request.method = "GET"
        request.resolver_match = types.SimpleNamespace()
        request.resolver_match.namespace = "v2"
        response = view(request)
        response.render()

        data = self.deserialize(response)

        self.assertEqual(
            data,
            {
                "allowed_detail_http_methods": ["get"],
                "allowed_list_http_methods": ["get"],
                "default_format": "application/json",
                "default_limit": 20,
                "doc": "",
                "fields": {
                    "name": {
                        "blank": False,
                        "creatable": True,
                        "default": "",
                        "editable": True,
                        "help_text": "",
                        "max_length": 255,
                        "name": "Name",
                        "nullable": False,
                        "readonly": False,
                        "required": False,
                        "type": "string",
                        "unique": False,
                    }
                },
                "filtering": {},
                "methods": [],
                "ordering": [],
            },
        )

    @freeze_time("2022-08-10 10:00:00")
    @patch("rest_framework.viewsets.ViewSetMixin.reverse_action")
    def test_deprecated_schema__decommission_date_in_future(self, _mock):
        """
        Given:
            - a fake request
            - a fake view set with deprecation set
            - a deprecated feature with decommission_after in the past
        When calling schema
        Then the schema has the field and method marked as deprecated.
        """

        # This fake view is not registered in our urls.py. Therefore, we need to mock
        # the reverse_action
        _mock.return_value = "/v2/fake_view/fake/"

        deprecated_feature = DeprecatedFeatureFactory(
            key=DeprecatedFeature.KEY_CHOICES[0][0],
            decommission_after=timezone.now() + timedelta(days=1),
        )

        view = FakeApplicationViewSetWithDeprecated.as_view({"get": "schema"})
        view.cls.deprecation = {
            "fields": {
                "namespace": deprecated_feature.key,
            },
            "urls": {
                "fake_custom_endpoint": deprecated_feature.key,
            },
        }
        view.cls.headers = {}
        request = HttpRequest()
        request.method = "GET"
        request.resolver_match = types.SimpleNamespace()
        request.resolver_match.namespace = "v2"
        response = view(request)
        response.render()

        data = self.deserialize(response)

        self.assertEqual(
            data,
            {
                "allowed_detail_http_methods": ["get"],
                "allowed_list_http_methods": ["get"],
                "default_format": "application/json",
                "default_limit": 20,
                "doc": "",
                "fields": {
                    "name": {
                        "blank": False,
                        "creatable": True,
                        "default": "",
                        "editable": True,
                        "help_text": "",
                        "max_length": 255,
                        "name": "Name",
                        "nullable": False,
                        "readonly": False,
                        "required": False,
                        "type": "string",
                        "unique": False,
                    },
                    "namespace": {
                        "information": "This field is deprecated",
                        "decommission_date": "2022-08-11T10:00:00Z",
                    },
                },
                "filtering": {"namespace": "iexact"},
                "methods": [
                    {
                        "regex": "/v1/fake_view/fake/",
                        "doc_text": "",
                        "doc_html": "",
                        "allowed_http_methods": ["get"],
                        "information": "This endpoint is deprecated",
                        "decommission_date": "2022-08-11T10:00:00Z",
                    }
                ],
                "ordering": ["namespace"],
            },
        )
