from datetime import timedelta

from apps.deprecation.models import DeprecatedFeature
from apps.testing.factories import ApplicationFactory, DeprecatedFeatureFactory
from django.utils import timezone
from drf_izberg.tests.base import BaseSerializerAPITestCase
from drf_izberg.tests.deprecated.fixtures import (
    FakeApplicationSerializerWithDeprecated,
    FakeApplicationViewSetWithDeprecated,
)


class DeprecatedFieldSerializerTestCase(BaseSerializerAPITestCase):
    def test_deprecated_fields__get(self):
        """
        Given:
            - a fake request
            - a fake view set with deprecation set
            - a deprecated feature with decommission_after in the past
            - a fake serializer
        When calling the serializer
        Then the deprecated fields are NOT in the data
        """

        view = FakeApplicationViewSetWithDeprecated()
        deprecated_feature = DeprecatedFeatureFactory(
            key=DeprecatedFeature.KEY_CHOICES[0][0],
            decommission_after=timezone.now() - timedelta(days=1),
        )
        view.deprecation = {
            "fields": {
                "namespace": deprecated_feature.key,
            },
        }
        application = ApplicationFactory(id=1)
        serializer = FakeApplicationSerializerWithDeprecated(
            instance=application,
            context={
                "request": self.request,
                "view": view,
            },
        )

        self.assertNotIn("namespace", serializer.data)
        self.assertEqual(serializer.data["name"], application.name)

    def test_deprecated_fields__get__decommission_date_in_future(self):
        """
        Given:
            - a fake request
            - a fake view set with deprecation set
            - a deprecated feature with decommission_after in the future
            - a fake serializer
        When calling the serializer
        Then the deprecated fields are IN the data
        """

        view = FakeApplicationViewSetWithDeprecated()
        deprecated_feature = DeprecatedFeatureFactory(
            key=DeprecatedFeature.KEY_CHOICES[0][0],
            decommission_after=timezone.now() + timedelta(days=1),
        )
        view.deprecation = {
            "fields": {
                "namespace": deprecated_feature.key,
            },
        }
        application = ApplicationFactory(id=1)
        serializer = FakeApplicationSerializerWithDeprecated(
            instance=application,
            context={
                "request": self.request,
                "view": view,
            },
        )

        self.assertEqual(serializer.data["namespace"], application.namespace)
        self.assertEqual(serializer.data["name"], application.name)

    def test_deprecated_fields__post(self):
        """
        Given:
            - a fake request
            - a fake view set with deprecation set
            - a deprecated feature with decommission_after in the past
            - a fake serializer
        When calling the serializer with a payload
        Then the deprecated field are NOT in the data
        """

        view = FakeApplicationViewSetWithDeprecated()
        deprecated_feature = DeprecatedFeatureFactory(
            key=DeprecatedFeature.KEY_CHOICES[0][0],
            decommission_after=timezone.now() - timedelta(days=1),
        )
        view.deprecation = {
            "fields": {
                "namespace": deprecated_feature.key,
            },
        }

        payload = {
            "name": "Test application created",
            "namespace": "dc comics",
        }
        serializer = FakeApplicationSerializerWithDeprecated(
            data=payload,
            context={
                "request": self.request,
                "view": view,
            },
        )
        serializer.is_valid(raise_exception=True)
        self.assertNotIn("namespace", serializer.data)
        self.assertEqual(serializer.data["name"], payload["name"])

    def test_deprecated_fields__post__decommission_date_in_future(self):
        """
        Given:
            - a fake request
            - a fake view set with deprecation set
            - a deprecated feature with decommission_after in the future
            - a fake serializer
        When calling the serializer with a payload
        Then the deprecated field are NOT in the data
        """

        view = FakeApplicationViewSetWithDeprecated()
        deprecated_feature = DeprecatedFeatureFactory(
            key=DeprecatedFeature.KEY_CHOICES[0][0],
            decommission_after=timezone.now() + timedelta(days=1),
        )
        view.deprecation = {
            "fields": {
                "namespace": deprecated_feature.key,
            },
        }

        payload = {
            "name": "Test application created",
            "namespace": "dc comics",
        }
        serializer = FakeApplicationSerializerWithDeprecated(
            data=payload,
            context={
                "request": self.request,
                "view": view,
            },
        )
        serializer.is_valid(raise_exception=True)
        self.assertEqual(serializer.data["name"], payload["name"])
        self.assertEqual(serializer.data["namespace"], payload["namespace"])
