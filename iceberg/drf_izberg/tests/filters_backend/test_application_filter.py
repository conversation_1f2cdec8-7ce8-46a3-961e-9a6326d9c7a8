from apps.orders.models import Merchant<PERSON>rder, Order
from apps.testing.factories import MerchantOrderFactory, OrderFactory
from django.test import RequestFactory
from drf_izberg.filters.backend.application_filter import ApplicationFilterBackend
from drf_izberg.permissions.constants.owner import (
    INTERNAL_OWNER,
    MERCHANT_OWNER,
    NO_OWNER_RESTRICTION,
)
from drf_izberg.tests.base import BaseAPITestCase
from drf_izberg.tests.fixtures import OrderFakeViewSet


class ApplicationFilterTestCase(BaseAPITestCase):
    """
    Note: Application filter depends on a modal with a foreign key to Application
    It is easier to reuse one of our factory boy model than faking it
    """

    def test_filter(self):
        """
        Given:
            - a request with owner type Merchant and application_id=1
            - an order attached to application 1
            - a fake view with attribut application_filter_relation
        When calling filter_queryset
        Then:
            - the queryset is filtered
            - the order is still accessible
        """
        order = OrderFactory(application__id=1)

        queryset = Order.objects.all()
        self.assertEqual(queryset.count(), 1)

        request = RequestFactory()
        request.owner_type = MERCHANT_OWNER
        request.application_id = order.application_id

        order_view = OrderFakeViewSet()

        filter = ApplicationFilterBackend()
        filtered_queryset = filter.filter_queryset(request, queryset, order_view)

        self.assertEqual(filtered_queryset.count(), 1)

    def test_filter__request_application_differs(self):
        """
        Given:
            - a request with owner type Merchant and application_id=999
            - an order attached to application 1
            - a fake view with attribut application_filter_relation
        When calling filter_queryset
        Then:
            - the queryset is filtered
            - the order is NOT accessible
        """
        OrderFactory(application__id=1)

        queryset = Order.objects.all()
        self.assertEqual(queryset.count(), 1)

        request = RequestFactory()
        request.owner_type = MERCHANT_OWNER
        request.application_id = 999

        order_view = OrderFakeViewSet()

        filter = ApplicationFilterBackend()
        filtered_queryset = filter.filter_queryset(request, queryset, order_view)

        self.assertEqual(filtered_queryset.count(), 0)

    def test_filter__no_owner_restriction(self):
        """
        Given:
            - a request with owner type Merchant and application_id=999
            - an order attached to application 1
            - a fake view with attribut application_filter_relation set to
              NO_OWNER_RESTRICTION
        When calling filter_queryset
        Then:
            - the queryset is filtered
            - the order is still accessible
        """
        order = OrderFactory(application__id=1)

        queryset = Order.objects.all()
        self.assertEqual(queryset.count(), 1)

        request = RequestFactory()
        request.application_id = 999
        request.owner_type = MERCHANT_OWNER
        request.application_id = order.application_id

        order_view = OrderFakeViewSet()
        order_view.application_filter_relation = NO_OWNER_RESTRICTION

        filter = ApplicationFilterBackend()
        filtered_queryset = filter.filter_queryset(request, queryset, order_view)

        self.assertEqual(filtered_queryset.count(), 1)

    def test_filter__owner_internal(self):
        """
        Given:
            - a request with owner type Merchant and application_id=999
            - an order attached to application 1
            - a fake view with attribut application_filter_relation set to
              NO_OWNER_RESTRICTION
        When calling filter_queryset
        Then:
            - the queryset is filtered
            - the order is still accessible
        """
        OrderFactory(application__id=1)

        queryset = Order.objects.all()
        self.assertEqual(queryset.count(), 1)

        request = RequestFactory()
        request.application_id = 999
        request.owner_type = INTERNAL_OWNER

        order_view = OrderFakeViewSet()
        order_view.application_filter_relation = NO_OWNER_RESTRICTION

        filter = ApplicationFilterBackend()
        filtered_queryset = filter.filter_queryset(request, queryset, order_view)

        self.assertEqual(filtered_queryset.count(), 1)

    def test_filter__deep_application_relation(self):
        """
        Given:
            - a request with owner type Merchant and application_id=1
            - a merchant order attached to an order with application 1
            - a fake view with attribut application_filter_relation
        When calling filter_queryset
        Then:
            - the queryset is filtered
            - the merchant order is accessible
        """
        MerchantOrderFactory(application__id=1)

        queryset = MerchantOrder.objects.all()
        self.assertEqual(queryset.count(), 1)

        request = RequestFactory()
        request.application_id = 1
        request.owner_type = MERCHANT_OWNER

        order_view = OrderFakeViewSet()
        order_view.application_filter_relation = "order__application_id"

        filter = ApplicationFilterBackend()
        filtered_queryset = filter.filter_queryset(request, queryset, order_view)

        self.assertEqual(filtered_queryset.count(), 1)

    def test_filter__deep_application_relation_filtered(self):
        """
        Given:
            - a request with owner type Merchant and application_id=999
            - a merchant order attached to an order with application 1
            - a fake view with attribut application_filter_relation
        When calling filter_queryset
        Then:
            - the queryset is filtered
            - the merchant order is NOT accessible
        """
        MerchantOrderFactory(application__id=1)

        queryset = MerchantOrder.objects.all()
        self.assertEqual(queryset.count(), 1)

        request = RequestFactory()
        request.application_id = 999
        request.owner_type = MERCHANT_OWNER

        order_view = OrderFakeViewSet()
        order_view.application_filter_relation = "order__application_id"

        filter = ApplicationFilterBackend()
        filtered_queryset = filter.filter_queryset(request, queryset, order_view)

        self.assertEqual(filtered_queryset.count(), 0)

    def test_filter__application_filter_relation_not_set(self):
        """
        Given:
            - a request with owner type Merchant and application_id=1
            - an order attached to application 1
            - a fake view with no attribut application_filter_relation
        When calling filter_queryset
        Then:
            - an exception is raised
        """
        order = OrderFactory(application__id=1)

        queryset = Order.objects.all()
        self.assertEqual(queryset.count(), 1)

        request = RequestFactory()
        request.owner_type = MERCHANT_OWNER
        request.application_id = order.application_id

        # no attribut merchant_filter_relation
        class FakeViewSet:
            pass

        fake_view = FakeViewSet()

        filter = ApplicationFilterBackend()

        with self.assertRaises(Exception) as error:
            filter.filter_queryset(request, queryset, fake_view)

        self.assertEqual(
            str(error.exception),
            (
                "You must declare in the view the following "
                "attribute: application_filter_relation"
            ),
        )

    def test_filter__log(self):
        """
        Given:
            - a request with owner type Merchant and application_id=1
            - an order attached to application 1
            - a fake view with attribut application_filter_relation
        When calling filter_queryset
        Then:
            - the queryset is filtered
            - the request.authorization_filters got populated
        """
        order = OrderFactory(application__id=1)

        queryset = Order.objects.all()
        self.assertEqual(queryset.count(), 1)

        request = RequestFactory()
        request.owner_type = MERCHANT_OWNER
        request.application_id = order.application_id

        order_view = OrderFakeViewSet()

        filter = ApplicationFilterBackend()
        filtered_queryset = filter.filter_queryset(request, queryset, order_view)
        self.assertEqual(filtered_queryset.count(), 1)

        self.assertEqual(
            request.authorization_filters, {"application_filter": {"application_id": 1}}
        )
