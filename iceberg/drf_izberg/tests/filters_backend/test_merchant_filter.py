from apps.orders.models import MerchantOrder, OrderItem
from apps.testing.factories import MerchantOrderFactory, ProductOfferOrderItemFactory
from django.test import RequestFactory
from drf_izberg.filters.backend.merchant_filter import MerchantFilterBackend
from drf_izberg.permissions.constants.owner import (
    MERCHANT_OWNER,
    NO_OWNER_RESTRICTION,
    OPERATOR_OWNER,
)
from drf_izberg.tests.base import BaseAPITestCase
from drf_izberg.tests.fixtures import MerchantOrderFakeViewSet


class MerchantFilterTestCase(BaseAPITestCase):
    """
    Note: Merchant filter depends on a modal with a foreign key to Application
    It is easier to reuse one of our factory boy model than faking it
    """

    def test_filter(self):
        """
        Given:
            - a request with owner type Merchant and merchant_id=1
            - a merchantOrder attached to merchant 1
            - a fake view with attribut merchant_filter_relation
        When calling filter_queryset
        Then:
            - the queryset is filtered
            - the merchantOrder is still accessible
        """
        merchant_order = MerchantOrderFactory(merchant__id=1)

        queryset = MerchantOrder.objects.all()
        self.assertEqual(queryset.count(), 1)

        request = RequestFactory()
        request.owner_type = MERCHANT_OWNER
        request.merchant_id = merchant_order.merchant_id

        merchant_order_view = MerchantOrderFakeViewSet()

        filter = MerchantFilterBackend()
        filtered_queryset = filter.filter_queryset(
            request, queryset, merchant_order_view
        )

        self.assertEqual(filtered_queryset.count(), 1)

    def test_filter__request_merchant_differs(self):
        """
        Given:
            - a request with owner type Merchant and merchant_id=999
            - a merchantOrder attached to merchant 1
            - a fake view with attribut merchant_filter_relation
        When calling filter_queryset
        Then:
            - the queryset is filtered
            - the merchantOrder is still accessible
        """
        MerchantOrderFactory(merchant__id=1)

        queryset = MerchantOrder.objects.all()
        self.assertEqual(queryset.count(), 1)

        request = RequestFactory()
        request.owner_type = MERCHANT_OWNER
        request.merchant_id = 999

        merchant_order_view = MerchantOrderFakeViewSet()

        filter = MerchantFilterBackend()
        filtered_queryset = filter.filter_queryset(
            request, queryset, merchant_order_view
        )

        self.assertEqual(filtered_queryset.count(), 0)

    def test_filter__merchant_filter_relation_not_set(self):
        """
        Given:
            - a request with owner type Merchant and merchant_id=999
            - a merchantOrder attached to merchant 1
            - a fake view with NO attribut merchant_filter_relation
        When calling filter_queryset
        Then:
            - an exception is raised
        """
        MerchantOrderFactory(merchant__id=1)

        queryset = MerchantOrder.objects.all()
        self.assertEqual(queryset.count(), 1)

        request = RequestFactory()
        request.owner_type = MERCHANT_OWNER
        request.merchant_id = 999

        # no attribut merchant_filter_relation
        class FakeViewSet:
            pass

        fake_view = FakeViewSet()

        filter = MerchantFilterBackend()

        with self.assertRaises(Exception) as error:
            filter.filter_queryset(request, queryset, fake_view)

        self.assertEqual(
            str(error.exception),
            (
                "You must declare in the view the following "
                "attribute: merchant_filter_relation"
            ),
        )

    def test_filter__no_owner_restriction(self):
        """
        Given:
            - a request with owner type Merchant and merchant_id=999
            - a merchantOrder attached to merchant 1
            - a fake view with attribut merchant_filter_relation set to
              NO_OWNER_RESTRICTION
        When calling filter_queryset
        Then:
            - the queryset is filtered
            - the merchantOrder is still accessible
        """
        MerchantOrderFactory(merchant__id=1)

        queryset = MerchantOrder.objects.all()
        self.assertEqual(queryset.count(), 1)

        request = RequestFactory()
        request.owner_type = MERCHANT_OWNER
        request.merchant_id = 999

        merchant_order_view = MerchantOrderFakeViewSet()
        merchant_order_view.merchant_filter_relation = NO_OWNER_RESTRICTION

        filter = MerchantFilterBackend()
        filtered_queryset = filter.filter_queryset(
            request, queryset, merchant_order_view
        )

        self.assertEqual(filtered_queryset.count(), 1)

    def test_filter__owner_operator(self):
        """
        Given:
            - a request with owner type Operator and merchant_id=999
            - a merchantOrder attached to merchant 1
            - a fake view with attribut merchant_filter_relation
        When calling filter_queryset
        Then:
            - the queryset is filtered
            - the merchantOrder is still accessible
        """
        MerchantOrderFactory(merchant__id=999)

        queryset = MerchantOrder.objects.all()
        self.assertEqual(queryset.count(), 1)

        request = RequestFactory()
        request.owner_type = OPERATOR_OWNER
        request.merchant_id = 1

        merchant_order_view = MerchantOrderFakeViewSet()

        filter = MerchantFilterBackend()
        filtered_queryset = filter.filter_queryset(
            request, queryset, merchant_order_view
        )

        self.assertEqual(filtered_queryset.count(), 1)

    def test_filter__deep_merchant_relation(self):
        """
        Given:
            - a request with owner type Merchant and merchant_id=1
            - an order item attached to a merchant order with merchant 1
            - a fake view with attribut merchant_filter_relation
        When calling filter_queryset
        Then:
            - the queryset is filtered
            - the merchant order is accessible
        """
        ProductOfferOrderItemFactory(
            merchant_order__merchant__id=1,
            merchant__id=1,
        )

        queryset = OrderItem.objects.all()
        self.assertEqual(queryset.count(), 1)

        request = RequestFactory()
        request.merchant_id = 1
        request.owner_type = MERCHANT_OWNER

        merchant_order_view = MerchantOrderFakeViewSet()
        merchant_order_view.merchant_filter_relation = "merchant_order__merchant_id"

        filter = MerchantFilterBackend()
        filtered_queryset = filter.filter_queryset(
            request, queryset, merchant_order_view
        )

        self.assertEqual(filtered_queryset.count(), 1)

    def test_filter__deep_merchant_relation_fileted(self):
        """
        Given:
            - a request with owner type Merchant and merchant_id=999
            - an order item attached to a merchant order with merchant 1
            - a fake view with attribut merchant_filter_relation
        When calling filter_queryset
        Then:
            - the queryset is filtered
            - the merchant order is NOT accessible
        """
        ProductOfferOrderItemFactory(
            merchant_order__merchant__id=1,
            merchant__id=1,
        )

        queryset = OrderItem.objects.all()
        self.assertEqual(queryset.count(), 1)

        request = RequestFactory()
        request.merchant_id = 999
        request.owner_type = MERCHANT_OWNER

        merchant_order_view = MerchantOrderFakeViewSet()
        merchant_order_view.merchant_filter_relation = "merchant_order__merchant_id"

        filter = MerchantFilterBackend()
        filtered_queryset = filter.filter_queryset(
            request, queryset, merchant_order_view
        )

        self.assertEqual(filtered_queryset.count(), 0)

    def test_filter__log(self):
        """
        Given:
            - a request with owner type Merchant and merchant_id=1
            - a merchantOrder attached to merchant 1
            - a fake view with attribut merchant_filter_relation
        When calling filter_queryset
        Then:
            - the queryset is filtered
            - the request.authorization_filters got populated
        """
        merchant_order = MerchantOrderFactory(merchant__id=1)

        queryset = MerchantOrder.objects.all()
        self.assertEqual(queryset.count(), 1)

        request = RequestFactory()
        request.owner_type = MERCHANT_OWNER
        request.merchant_id = merchant_order.merchant_id
        request.application_id = 1

        merchant_order_view = MerchantOrderFakeViewSet()
        merchant_order_view.application_filter_relation = "application_id"

        filter = MerchantFilterBackend()
        filtered_queryset = filter.filter_queryset(
            request, queryset, merchant_order_view
        )
        self.assertEqual(filtered_queryset.count(), 1)

        self.assertEqual(
            request.authorization_filters, {"merchant_filter": {"merchant_id": 1}}
        )
