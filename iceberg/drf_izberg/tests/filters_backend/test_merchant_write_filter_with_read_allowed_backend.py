from apps.orders.models import MerchantOrder, OrderItem
from apps.testing.factories import MerchantOrderFactory, ProductOfferOrderItemFactory
from django.test import RequestFactory
from drf_izberg.filters.backend.merchant_write_filter_with_read_allowed_backend import (
    MerchantWriteFilterWithReadAllowedBackend,
)
from drf_izberg.permissions.constants.owner import (
    MERCHANT_OWNER,
    NO_OWNER_RESTRICTION,
    OPERATOR_OWNER,
)
from drf_izberg.tests.base import BaseAPITestCase
from drf_izberg.tests.fixtures import MerchantOrderFakeViewSet


class MerchantWriteFilterWithReadAllowedBackendTestCase(BaseAPITestCase):
    def test_filter__owner_type_merchant(self):
        """
        Given:
            - a request with owner type Merchant and merchant_id=1
            - a merchantOrder attached to merchant 1
            - a fake view with attribut merchant_filter_relation set to merchant_id
        When calling filter_queryset
        Then:
            - the queryset is filtered
            - the merchantOrder is accessible for all HTTP methods
        """
        filter = MerchantWriteFilterWithReadAllowedBackend()
        MerchantOrderFactory(merchant__id=1)

        queryset = MerchantOrder.objects.all()
        self.assertEqual(queryset.count(), 1)

        request = RequestFactory()
        request.method = "GET"
        request.owner_type = MERCHANT_OWNER
        request.merchant_id = 1

        merchant_order_view = MerchantOrderFakeViewSet()
        merchant_order_view.merchant_filter_relation = NO_OWNER_RESTRICTION

        filtered_queryset = filter.filter_queryset(
            request, queryset, merchant_order_view
        )
        self.assertEqual(filtered_queryset.count(), 1)

        request.method = "DELETE"
        filtered_queryset = filter.filter_queryset(
            request, queryset, merchant_order_view
        )
        self.assertEqual(filtered_queryset.count(), 1)

    def test_filter__owner_type_merchant__no_restriction(self):
        """
        Given:
            - a request with owner type Merchant and merchant_id=999
            - a merchantOrder attached to merchant 1
            - a fake view with attribut merchant_filter_relation set
        When calling filter_queryset
        Then:
            - the queryset is filtered
            - the merchantOrder is accessible for all HTTP methods
        """
        filter = MerchantWriteFilterWithReadAllowedBackend()
        MerchantOrderFactory(merchant__id=1)

        queryset = MerchantOrder.objects.all()
        self.assertEqual(queryset.count(), 1)

        request = RequestFactory()
        request.method = "GET"
        request.owner_type = MERCHANT_OWNER
        request.merchant_id = 999

        merchant_order_view = MerchantOrderFakeViewSet()
        merchant_order_view.merchant_filter_relation = NO_OWNER_RESTRICTION

        filtered_queryset = filter.filter_queryset(
            request, queryset, merchant_order_view
        )
        self.assertEqual(filtered_queryset.count(), 1)

        request.method = "DELETE"
        filtered_queryset = filter.filter_queryset(
            request, queryset, merchant_order_view
        )
        self.assertEqual(filtered_queryset.count(), 1)

    def test_filter__owner_type_merchant__read_only__deep_one(self):
        """
        Given:
            - a request with owner type Merchant and merchant_id=1
            - a merchantOrder attached to merchant 2
            - a fake view with attribut merchant_filter_relation
        When calling filter_queryset
        Then:
            - the queryset is filtered
            - the merchantOrder is accessible for the HTTP GET only
        """
        filter = MerchantWriteFilterWithReadAllowedBackend()
        MerchantOrderFactory(merchant__id=2)

        queryset = MerchantOrder.objects.all()
        self.assertEqual(queryset.count(), 1)

        request = RequestFactory()
        request.method = "GET"
        request.owner_type = MERCHANT_OWNER
        request.merchant_id = 1

        merchant_order_view = MerchantOrderFakeViewSet()
        merchant_order_view.merchant_filter_relation = "merchant_id"

        filtered_queryset = filter.filter_queryset(
            request, queryset, merchant_order_view
        )
        self.assertEqual(filtered_queryset.count(), 1)

        request.method = "DELETE"
        filtered_queryset = filter.filter_queryset(
            request, queryset, merchant_order_view
        )
        self.assertEqual(filtered_queryset.count(), 0)

    def test_filter__owner_type_merchant__read_only__deep_two(self):
        """
        Given:
            - a request with owner type Merchant and merchant_id=2
            - an order item attached to a merchant order with merchant 1
            - a fake view with attribut merchant_filter_relation
        When calling filter_queryset
        Then:
            - the queryset is filtered
            - the merchant order is accessible for the HTTP GET method only
        """
        filter = MerchantWriteFilterWithReadAllowedBackend()
        ProductOfferOrderItemFactory(
            merchant_order__merchant__id=1,
            merchant__id=1,
        )

        queryset = OrderItem.objects.all()
        self.assertEqual(queryset.count(), 1)

        request = RequestFactory()
        request.method = "GET"
        request.merchant_id = 2
        request.owner_type = MERCHANT_OWNER

        merchant_order_view = MerchantOrderFakeViewSet()
        merchant_order_view.merchant_filter_relation = "merchant_order__merchant_id"

        filtered_queryset = filter.filter_queryset(
            request, queryset, merchant_order_view
        )
        self.assertEqual(filtered_queryset.count(), 1)

        request.method = "DELETE"
        filtered_queryset = filter.filter_queryset(
            request, queryset, merchant_order_view
        )
        self.assertEqual(filtered_queryset.count(), 0)

    def test_filter__owner_type_operator__no_restriction(self):
        """
        Given:
            - a request with owner type Operator
            - a merchantOrder attached to merchant 1
            - a fake view with attribut merchant_filter_relation set to
            NO_OWNER_RESTRICTION
        When calling filter_queryset
        Then:
            - the queryset is filtered
            - the merchantOrder is accessible for all HTTP methods
        """
        filter = MerchantWriteFilterWithReadAllowedBackend()
        MerchantOrderFactory(merchant__id=1)

        queryset = MerchantOrder.objects.all()
        self.assertEqual(queryset.count(), 1)

        request = RequestFactory()
        request.method = "GET"
        request.owner_type = OPERATOR_OWNER

        merchant_order_view = MerchantOrderFakeViewSet()
        merchant_order_view.merchant_filter_relation = NO_OWNER_RESTRICTION

        filtered_queryset = filter.filter_queryset(
            request, queryset, merchant_order_view
        )
        self.assertEqual(filtered_queryset.count(), 1)

        request.method = "DELETE"
        filtered_queryset = filter.filter_queryset(
            request, queryset, merchant_order_view
        )
        self.assertEqual(filtered_queryset.count(), 1)

    def test_filter__owner_type_operator__read_only__deep_one(self):
        """
        Given:
            - a request with owner type Operator
            - a merchantOrder attached to merchant 2
            - a fake view with attribut merchant_filter_relation set to merchant_id
        When calling filter_queryset
        Then:
            - the queryset is filtered
            - the merchantOrder is accessible for all HTTP methods
        """
        filter = MerchantWriteFilterWithReadAllowedBackend()
        MerchantOrderFactory(merchant__id=2)

        queryset = MerchantOrder.objects.all()
        self.assertEqual(queryset.count(), 1)

        request = RequestFactory()
        request.method = "GET"
        request.owner_type = OPERATOR_OWNER

        merchant_order_view = MerchantOrderFakeViewSet()
        merchant_order_view.merchant_filter_relation = "merchant_id"

        filtered_queryset = filter.filter_queryset(
            request, queryset, merchant_order_view
        )
        self.assertEqual(filtered_queryset.count(), 1)

        request.method = "DELETE"
        filtered_queryset = filter.filter_queryset(
            request, queryset, merchant_order_view
        )
        self.assertEqual(filtered_queryset.count(), 1)

    def test_filter__owner_type_operator__read_only__deep_two(self):
        """
        Given:
            - a request with owner type Operator
            - an order item attached to a merchant order with merchant 1
            - a fake view with attribut merchant_filter_relation
        When calling filter_queryset
        Then:
            - the queryset is filtered
            - the merchant order is accessible for all HTTP methods
        """
        filter = MerchantWriteFilterWithReadAllowedBackend()
        ProductOfferOrderItemFactory(
            merchant_order__merchant__id=1,
            merchant__id=1,
        )

        queryset = OrderItem.objects.all()
        self.assertEqual(queryset.count(), 1)

        request = RequestFactory()
        request.method = "GET"
        request.owner_type = OPERATOR_OWNER

        merchant_order_view = MerchantOrderFakeViewSet()
        merchant_order_view.merchant_filter_relation = "merchant_order__merchant_id"

        filtered_queryset = filter.filter_queryset(
            request, queryset, merchant_order_view
        )
        self.assertEqual(filtered_queryset.count(), 1)

        request.method = "DELETE"
        filtered_queryset = filter.filter_queryset(
            request, queryset, merchant_order_view
        )
        self.assertEqual(filtered_queryset.count(), 1)
