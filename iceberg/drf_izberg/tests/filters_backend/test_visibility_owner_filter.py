from apps.ice_applications.models import ApplicationSetting
from apps.testing.factories import ApplicationSettingFactory
from django.test import RequestFactory
from drf_izberg.filters.backend.visilibity_owner_filter import (
    VisibilityOwnerFilterBackend,
)
from drf_izberg.permissions.constants.actions import CREATE, LIST, RET<PERSON>EVE
from drf_izberg.permissions.constants.owner import INTERNAL_OWNER, MERCHANT_OWNER
from drf_izberg.tests.fixtures import OperatorOwnerFakeViewSet
from ims.api.const import OPERATOR_OWNER
from ims.tests import BaseTestCase
from lib.models import FieldLessVisibility


class VisibilityOwnerFilterTestCase(BaseTestCase):
    """
    ### MERCHANT ###
    """

    def test_filter__merchant__read(self):
        """
        Given:
            - a request with owner type Merchant
            - a ApplicationSetting with read_permission
            - a fake view with action list (read)
        When calling filter_queryset
        Then:
            - the queryset is filtered
            - the ApplicationSetting is still accessible
        """
        ApplicationSettingFactory(
            application__id=1,
            application__in_memory_setting_manager__drf=True,
            read_permission=FieldLessVisibility.APPLICATION_MERCHANT,
            write_permission=FieldLessVisibility.IZBERG_STAFF,
        )

        queryset = ApplicationSetting.objects.all()
        self.assertEqual(queryset.count(), 1)

        request = RequestFactory()
        request.owner_type = MERCHANT_OWNER
        # request.scopes = ["application:read"]

        fake_view = OperatorOwnerFakeViewSet()
        fake_view.action = LIST

        filter = VisibilityOwnerFilterBackend()
        filtered_queryset = filter.filter_queryset(request, queryset, fake_view)

        self.assertEqual(filtered_queryset.count(), 1)

    def test_filter__merchant__read__visibility_staff(self):
        """
        Given:
            - a request with owner type Merchant
            - a ApplicationSetting with read_permission
            - a fake view with action retrieve (read)
        When calling filter_queryset
        Then:
            - the queryset is filtered
            - the ApplicationSetting is NOT accessible
        """
        ApplicationSettingFactory(
            application__id=1,
            application__in_memory_setting_manager__drf=True,
            read_permission=FieldLessVisibility.APPLICATION_STAFF,
            write_permission=FieldLessVisibility.IZBERG_STAFF,
        )

        queryset = ApplicationSetting.objects.all()
        self.assertEqual(queryset.count(), 1)

        request = RequestFactory()
        request.owner_type = MERCHANT_OWNER

        fake_view = OperatorOwnerFakeViewSet()
        fake_view.action = RETRIEVE

        filter = VisibilityOwnerFilterBackend()
        filtered_queryset = filter.filter_queryset(request, queryset, fake_view)

        self.assertEqual(filtered_queryset.count(), 0)

    """
    ### INTERNAL ###
    """

    def test_filter__internal__read(self):
        """
        Given:
            - a request with owner type Internal
            - a ApplicationSetting with read_permission
            - a fake view with action list (read)
        When calling filter_queryset
        Then:
            - the queryset is filtered
            - the ApplicationSetting is still accessible

        NOTE: Internal owner has the highest read permissions
        """
        ApplicationSettingFactory(
            application__id=1,
            application__in_memory_setting_manager__drf=True,
            read_permission=FieldLessVisibility.IZBERG_STAFF,
            write_permission=FieldLessVisibility.IZBERG_STAFF,
        )

        queryset = ApplicationSetting.objects.all()
        self.assertEqual(queryset.count(), 1)

        request = RequestFactory()
        request.owner_type = INTERNAL_OWNER

        fake_view = OperatorOwnerFakeViewSet()
        fake_view.action = LIST

        filter = VisibilityOwnerFilterBackend()
        filtered_queryset = filter.filter_queryset(request, queryset, fake_view)

        self.assertEqual(filtered_queryset.count(), 1)

    """
    ### OPERATOR ###
    """

    def test_filter__operator__read(self):
        """
        Given:
            - a request with owner type Operator and scopes
            - a ApplicationSetting with read_permission
            - a fake view with action list (read)
        When calling filter_queryset
        Then:
            - the queryset is filtered
            - the ApplicationSetting is still accessible
        """
        ApplicationSettingFactory(
            application__id=1,
            application__in_memory_setting_manager__drf=True,
            read_permission=FieldLessVisibility.APPLICATION_STAFF,
            write_permission=FieldLessVisibility.IZBERG_STAFF,
        )

        queryset = ApplicationSetting.objects.all()
        self.assertEqual(queryset.count(), 1)

        request = RequestFactory()
        request.owner_type = OPERATOR_OWNER
        request.scopes = ["application:read"]

        fake_view = OperatorOwnerFakeViewSet()
        fake_view.action = LIST

        filter = VisibilityOwnerFilterBackend()
        filtered_queryset = filter.filter_queryset(request, queryset, fake_view)

        self.assertEqual(filtered_queryset.count(), 1)

    def test_filter__operator__write(self):
        """
        Given:
            - a request with owner type Operator and scopes
            - a ApplicationSetting with write_permission
            - a fake view with action create (write)
        When calling filter_queryset
        Then:
            - the queryset is filtered
            - the ApplicationSetting is still accessible
        """
        ApplicationSettingFactory(
            application__id=1,
            application__in_memory_setting_manager__drf=True,
            read_permission=FieldLessVisibility.APPLICATION_STAFF,
            write_permission=FieldLessVisibility.APPLICATION_STAFF,
        )

        queryset = ApplicationSetting.objects.all()
        self.assertEqual(queryset.count(), 1)

        request = RequestFactory()
        request.owner_type = OPERATOR_OWNER
        request.scopes = ["application:write"]

        fake_view = OperatorOwnerFakeViewSet()
        fake_view.action = CREATE

        filter = VisibilityOwnerFilterBackend()
        filtered_queryset = filter.filter_queryset(request, queryset, fake_view)

        self.assertEqual(filtered_queryset.count(), 1)

    def test_filter__operator__read__scope_admin(self):
        """
        Given:
            - a request with owner type Operator and scopes
            - a ApplicationSetting with read_permission
            - a fake view with action list (read)
        When calling filter_queryset
        Then:
            - the queryset is filtered
            - the ApplicationSetting is still accessible
        """
        ApplicationSettingFactory(
            application__id=1,
            application__in_memory_setting_manager__drf=True,
            read_permission=FieldLessVisibility.APPLICATION_ADMIN,
            write_permission=FieldLessVisibility.APPLICATION_ADMIN,
        )

        queryset = ApplicationSetting.objects.all()
        self.assertEqual(queryset.count(), 1)

        request = RequestFactory()
        request.owner_type = OPERATOR_OWNER
        request.scopes = ["admin"]

        fake_view = OperatorOwnerFakeViewSet()
        fake_view.action = LIST

        filter = VisibilityOwnerFilterBackend()
        filtered_queryset = filter.filter_queryset(request, queryset, fake_view)

        self.assertEqual(filtered_queryset.count(), 1)

    def test_filter__operator__write__scope_admin(self):
        """
        Given:
            - a request with owner type Operator and scopes
            - a ApplicationSetting with write_permission
            - a fake view with action create (write)
        When calling filter_queryset
        Then:
            - the queryset is filtered
            - the ApplicationSetting is still accessible
        """
        ApplicationSettingFactory(
            application__id=1,
            application__in_memory_setting_manager__drf=True,
            read_permission=FieldLessVisibility.APPLICATION_ADMIN,
            write_permission=FieldLessVisibility.APPLICATION_ADMIN,
        )

        queryset = ApplicationSetting.objects.all()
        self.assertEqual(queryset.count(), 1)

        request = RequestFactory()
        request.owner_type = OPERATOR_OWNER
        request.scopes = ["admin"]

        fake_view = OperatorOwnerFakeViewSet()
        fake_view.action = CREATE

        filter = VisibilityOwnerFilterBackend()
        filtered_queryset = filter.filter_queryset(request, queryset, fake_view)

        self.assertEqual(filtered_queryset.count(), 1)

    def test_filter__operator__read__lower_scope(self):
        """
        Given:
            - a request with owner type Operator and scopes with too low permissions
            - a ApplicationSetting with read_permission
            - a fake view with action list (read)
        When calling filter_queryset
        Then:
            - the queryset is filtered
            - the ApplicationSetting is NOT accessible
        """
        ApplicationSettingFactory(
            application__id=1,
            application__in_memory_setting_manager__drf=True,
            read_permission=FieldLessVisibility.APPLICATION_ADMIN,
            write_permission=FieldLessVisibility.APPLICATION_ADMIN,
        )

        queryset = ApplicationSetting.objects.all()
        self.assertEqual(queryset.count(), 1)

        request = RequestFactory()
        request.owner_type = OPERATOR_OWNER
        request.scopes = ["application:write"]

        fake_view = OperatorOwnerFakeViewSet()
        fake_view.action = LIST

        filter = VisibilityOwnerFilterBackend()
        filtered_queryset = filter.filter_queryset(request, queryset, fake_view)

        self.assertEqual(filtered_queryset.count(), 0)
