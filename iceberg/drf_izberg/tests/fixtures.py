from drf_izberg.permissions.constants.actions import (
    CREATE,
    DESTROY,
    LIST,
    PARTIAL_UPDATE,
    RET<PERSON>EVE,
    UPDATE,
)
from drf_izberg.permissions.constants.roles import ADMIN_ROLE, ALL_ROLES, EDIT_ROLES
from drf_izberg.permissions.constants.scopes import C<PERSON><PERSON>MER_SCOPE, OMS_SCOPE
from ims.api.const import MERCHANT_OWNER, OPERATOR_OWNER
from rest_framework.viewsets import GenericViewSet


class OperatorOwnerFakeViewSet(GenericViewSet):
    permission_constraints = {
        OPERATOR_OWNER: {
            LIST: ALL_ROLES,
            CREATE: EDIT_ROLES,
            RETRIEVE: ALL_ROLES,
            UPDATE: EDIT_ROLES,
            PARTIAL_UPDATE: EDIT_ROLES,
            DESTROY: ADMIN_ROLE,
            "custom_action": ALL_ROLES,
        },
    }
    permission_scopes = [CUSTOMER_SCOPE]
    resource_name = "fake"

    http_method_names = ["get", "post", "put", "patch", "delete"]


class MerchantOwnerFakeViewSet(GenericViewSet):
    permission_constraints = {
        MERCHANT_OWNER: {
            LIST: ALL_ROLES,
            RETRIEVE: ALL_ROLES,
        },
    }
    permission_scopes = [OMS_SCOPE]
    resource_name = "fake"

    http_method_names = ["get"]


class OrderFakeViewSet(GenericViewSet):
    application_filter_relation = "application_id"


class MerchantOrderFakeViewSet(GenericViewSet):
    merchant_filter_relation = "merchant_id"


class FakeSerializerWithCreateAndEdit:
    class Meta:
        creatable_fields = ("field_a_create_only",)
        editable_fields = ("field_b_edit",)
        operator_writeable_fields = ()


class FakeSerializerWithEdit:
    class Meta:
        creatable_fields = ()
        editable_fields = ("field_b_edit",)
        operator_writeable_fields = ()


class FakeSerializerWithEditAndApplicationWritable:
    class Meta:
        creatable_fields = ()
        editable_fields = ("field_b_edit",)
        operator_writeable_fields = ("field_c_application_writeable",)
