from apps.testing.live_setting_override import live_setting_override
from django.conf import settings
from django.test import RequestFactory, override_settings
from drf_<PERSON>zberg.middlewares import DRFUrlConfMiddleware
from ims.tests import BaseTestCase


# We are overriding the value of TEST_IS_RUNNING in our settings just for our middleware
# Otherwise it would always use the urlconf: ROOT_URLCONF_DRF_V2
@override_settings(TEST_IS_RUNNING=False)
class MiddlewaresTestCase(BaseTestCase):
    header_identity_token = {"Authorization": "Bearer XX.YY.ZZ-eee"}
    header_legacy_token = {"Authorization": "Bearer batman-aa:bb"}

    def setUp(self):
        self.request = RequestFactory()
        self.middleware = DRFUrlConfMiddleware(lambda x: x)

    @live_setting_override("ENABLE_DJANGO_REST_FRAMEWORK", False)
    def test_drf_disabled(self):
        """
        When calling the middleware
        With:
            - identity token set in the header
            - a path with version v1 (resource migrated to DRF)
            - live setting ENABLE_DJANGO_REST_FRAMEWORK set to False
        Then:
            - request does not have urlconf set -> default behavior with tastypie
        """
        self.request.headers = self.header_identity_token
        self.request.path = "/v1/application/"

        self.middleware(self.request)

        self.assertEqual(hasattr(self.request, "urlconf"), False)

    @live_setting_override("ENABLE_DJANGO_REST_FRAMEWORK", True)
    def test_token_legacy(self):
        """
        When calling the middleware
        With:
            - legacy token set in the header
            - a path with version v1 (resource migrated to DRF)
            - live setting ENABLE_DJANGO_REST_FRAMEWORK set to True
        Then:
            - request does not have urlconf set -> default behavior with tastypie
        """
        self.request.headers = self.header_legacy_token
        self.request.path = "/v1/application/"

        self.middleware(self.request)

        self.assertEqual(hasattr(self.request, "urlconf"), False)

    @live_setting_override("ENABLE_DJANGO_REST_FRAMEWORK", True)
    def test_wrong_path(self):
        """
        When calling the middleware
        With:
            - identity token set in the header
            - a path with version v1 (resource not migrated to DRF)
            - live setting ENABLE_DJANGO_REST_FRAMEWORK set to True
        Then:
            - request does not have urlconf set -> default behavior with tastypie
        """
        self.request.headers = self.header_identity_token
        self.request.path = "/v1/this_is_a_test/action/"

        self.middleware(self.request)

        self.assertEqual(hasattr(self.request, "urlconf"), False)

    @live_setting_override("ENABLE_DJANGO_REST_FRAMEWORK", True)
    @live_setting_override("DJANGO_REST_FRAMEWORK_API_VERSION", "v2")
    def test_api_version_v2(self):
        """
        When calling the middleware
        With:
            - identity token set in the header
            - a path with version v1 (resource migrated to DRF)
            - live setting ENABLE_DJANGO_REST_FRAMEWORK set to True
            - live setting DJANGO_REST_FRAMEWORK_API_VERSION set to v2
        Then:
            - request have urlconf set
            - urlconf is ROOT_URLCONF_DRF_V2
              Meaning:
              - v1 -> tastypie
              - v2 -> drf
        """
        self.request.headers = self.header_identity_token
        self.request.path = "/v1/application/"

        self.middleware(self.request)

        self.assertEqual(hasattr(self.request, "urlconf"), True)
        self.assertEqual(settings.ROOT_URLCONF_DRF_V2, self.request.urlconf)

    @live_setting_override("ENABLE_DJANGO_REST_FRAMEWORK", True)
    @live_setting_override("DJANGO_REST_FRAMEWORK_API_VERSION", "v1")
    def test_api_version_v1(self):
        """
        When calling the middleware
        With:
            - identity token set in the header
            - a path with version v1 (resource migrated to DRF)
            - live setting ENABLE_DJANGO_REST_FRAMEWORK set to True
            - live setting DJANGO_REST_FRAMEWORK_API_VERSION set to v1
        Then:
            - request have urlconf set
            - urlconf is ROOT_URLCONF_DRF_V1
              Meaning:
              - v1 -> drf if resource migrated
        """
        self.request.headers = self.header_identity_token
        self.request.path = "/v1/application/"

        self.middleware(self.request)

        self.assertEqual(hasattr(self.request, "urlconf"), True)
        self.assertEqual(settings.ROOT_URLCONF_DRF_V1, self.request.urlconf)

    @live_setting_override("ENABLE_DJANGO_REST_FRAMEWORK", True)
    @live_setting_override("DJANGO_REST_FRAMEWORK_API_VERSION", "v1")
    def test_api_resource_not_migrated_to_drf(self):
        """
        When calling the middleware
        With:
            - identity token set in the header
            - a path with version v1 (resource NOT YET migrated to DRF)
            - live setting ENABLE_DJANGO_REST_FRAMEWORK set to True
            - live setting DJANGO_REST_FRAMEWORK_API_VERSION set to v1
        Then:
            - request does not have urlconf set -> default behavior with tastypie
        """
        self.request.headers = self.header_identity_token
        self.request.path = "/v1/order/"

        self.middleware(self.request)

        self.assertEqual(hasattr(self.request, "urlconf"), False)

    @live_setting_override("ENABLE_DJANGO_REST_FRAMEWORK", True)
    @live_setting_override("DJANGO_REST_FRAMEWORK_API_VERSION", "v1")
    @live_setting_override(
        "DJANGO_REST_FRAMEWORK_DISABLED_RESOURCES", "address,country,application"
    )
    def test_disabled_resources(self):
        """
        When calling the middleware
        With:
            - identity token set in the header
            - a path with version v1 (resource migrated to DRF)
            - live setting ENABLE_DJANGO_REST_FRAMEWORK set to True
            - live setting DJANGO_REST_FRAMEWORK_API_VERSION set to v1
            - live setting DJANGO_REST_FRAMEWORK_DISABLED_RESOURCES set to
              "address,country,application"
        Then:
            - request does not have urlconf set -> default behavior with tastypie
        """
        self.request.headers = self.header_identity_token
        self.request.path = "/v1/application/"

        self.middleware(self.request)

        self.assertEqual(hasattr(self.request, "urlconf"), False)

    @live_setting_override("ENABLE_DJANGO_REST_FRAMEWORK", True)
    @live_setting_override("DJANGO_REST_FRAMEWORK_API_VERSION", "v1")
    @live_setting_override(
        "DJANGO_REST_FRAMEWORK_DISABLED_RESOURCES", " address,application ,country"
    )
    def test_disabled_resources_with_strip(self):
        """
        When calling the middleware
        With:
            - identity token set in the header
            - a path with version v1 (resource migrated to DRF)
            - live setting ENABLE_DJANGO_REST_FRAMEWORK set to True
            - live setting DJANGO_REST_FRAMEWORK_API_VERSION set to v1
            - live setting DJANGO_REST_FRAMEWORK_DISABLED_RESOURCES set to
              " address,application ,country"
        Then:
            - request does not have urlconf set -> default behavior with tastypie
        """
        self.request.headers = self.header_identity_token
        self.request.path = "/v1/application/"

        self.middleware(self.request)

        self.assertEqual(hasattr(self.request, "urlconf"), False)
