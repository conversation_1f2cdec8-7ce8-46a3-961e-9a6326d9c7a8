from apps.products.models import Product
from apps.testing.factories import ProductFactory
from django.core.exceptions import ValidationError
from drf_izberg.paginations.aggregate_count import IzbergAggregateCountPagination
from drf_izberg.tests.base import BaseSerializerAPITestCase
from rest_framework.response import Response


class SampleAggregateCountPagination(IzbergAggregateCountPagination):
    max_aggregate_count = 2
    allowed_fields = ["gtin"]


class AggregateCountPaginationTestCase(BaseSerializerAPITestCase):
    def test_pagination_no_aggregate_asked(self):
        self.request.query_params = {}
        ProductFactory()
        paginator = IzbergAggregateCountPagination()

        result = paginator.get_paginated_response(
            paginator.paginate_queryset(Product.objects.all(), request=self.request)
        )

        self.assertTrue(isinstance(result, Response))
        data = result.data
        self.assertIn("meta", data)
        self.assertEqual(
            dict(data["meta"]),  # it's an Ordereddict
            {
                "limit": 20,
                "offset": 0,
                "next": None,
                "previous": None,
                "total_count": 1,
            },
        )

    def test_pagination(self):
        self.request.query_params = {"aggregate_count_on": "gtin"}
        paginator = IzbergAggregateCountPagination()

        expected_agg_count = {}
        for product in ProductFactory.create_batch(size=5):
            if product.gtin not in expected_agg_count:
                expected_agg_count[product.gtin] = {
                    "gtin": product.gtin,
                    "count": 0,
                }
            expected_agg_count[product.gtin]["count"] += 1

        result = paginator.get_paginated_response(
            paginator.paginate_queryset(Product.objects.all(), request=self.request)
        )

        self.assertTrue(isinstance(result, Response))
        data = result.data
        self.assertIn("meta", data)
        self.assertIn("aggregate_count", data["meta"])
        # Sorted 2 dict because can't sort the expected dict as the paginator sorting
        self.assertEqual(
            sorted(data["meta"]["aggregate_count"], key=lambda d: d["gtin"]),
            sorted(list(expected_agg_count.values()), key=lambda d: d["gtin"]),
        )

    def test_pagination_on_not_allowed_field(self):
        self.request.query_params = {"aggregate_count_on": "status"}
        paginator = SampleAggregateCountPagination()
        ProductFactory.create_batch(size=5)

        with self.assertRaises(ValidationError) as exc:
            paginator.paginate_queryset(Product.objects.all(), request=self.request)

        self.assertEqual(
            exc.exception.args[0],
            {
                "aggregate_count_on": (
                    'Aggregate count on field "status" is not available'
                )
            },
        )

    def test_pagination_max(self):
        self.request.query_params = {"aggregate_count_on": "gtin"}
        paginator = SampleAggregateCountPagination()
        ProductFactory.create_batch(size=5)

        result = paginator.get_paginated_response(
            paginator.paginate_queryset(Product.objects.all(), request=self.request)
        )

        self.assertTrue(isinstance(result, Response))
        data = result.data
        self.assertIn("meta", data)
        self.assertIn("aggregate_count", data["meta"])
        self.assertEqual(len(data["meta"]["aggregate_count"]), 2)

    def test_pagination_on_not_indexed_field(self):
        self.request.query_params = {"aggregate_count_on": "manufacturer"}
        paginator = SampleAggregateCountPagination()
        ProductFactory.create_batch(size=5)

        with self.assertRaises(ValidationError) as exc:
            paginator.paginate_queryset(Product.objects.all(), request=self.request)

        self.assertEqual(
            exc.exception.args[0],
            {
                "aggregate_count_on": (
                    'Aggregate count on field "manufacturer" is not available'
                )
            },
        )
