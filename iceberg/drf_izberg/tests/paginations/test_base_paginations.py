from apps.products.models import Product
from apps.testing.factories import ProductFactory
from drf_izberg.paginations.base import IzbergPagination
from drf_izberg.tests.base import BaseSerializerAPITestCase
from rest_framework.response import Response


class SamplePagination(IzbergPagination):
    max_limit = 3
    default_limit = 2


class BasePaginationTestCase(BaseSerializerAPITestCase):
    def test_pagination(self):
        self.request.query_params = {}
        ProductFactory()
        paginator = IzbergPagination()

        result = paginator.get_paginated_response(
            paginator.paginate_queryset(Product.objects.all(), request=self.request)
        )

        self.assertTrue(isinstance(result, Response))
        data = result.data
        self.assertIn("meta", data)
        self.assertEqual(
            dict(data["meta"]),  # it's an Ordereddict
            {
                "limit": 20,
                "offset": 0,
                "next": None,
                "previous": None,
                "total_count": 1,
            },
        )

    def test_pagination_next_link(self):
        self.request.query_params = {}
        ProductFactory.create_batch(size=5)
        paginator = SamplePagination()

        result = paginator.get_paginated_response(
            paginator.paginate_queryset(Product.objects.all(), request=self.request)
        )

        self.assertTrue(isinstance(result, Response))
        data = result.data
        self.assertIn("meta", data)
        self.assertEqual(
            dict(data["meta"]),
            {
                "limit": 2,
                "offset": 0,
                "next": "http://testserver/?limit=2&offset=2",
                "previous": None,
                "total_count": 5,
            },
        )

    def test_pagination_previous_link(self):
        self.request.query_params = {"limit": 2, "offset": 2}
        ProductFactory.create_batch(size=5)
        paginator = SamplePagination()

        result = paginator.get_paginated_response(
            paginator.paginate_queryset(Product.objects.all(), request=self.request)
        )

        self.assertTrue(isinstance(result, Response))
        data = result.data
        self.assertIn("meta", data)
        self.assertEqual(
            dict(data["meta"]),
            {
                "limit": 2,
                "offset": 2,
                "next": "http://testserver/?limit=2&offset=4",
                "previous": "http://testserver/?limit=2",
                "total_count": 5,
            },
        )

    def test_pagination_max_limit(self):
        self.request.query_params = {"limit": 5}
        ProductFactory.create_batch(size=5)
        paginator = SamplePagination()

        result = paginator.get_paginated_response(
            paginator.paginate_queryset(Product.objects.all(), request=self.request)
        )

        self.assertTrue(isinstance(result, Response))
        data = result.data
        self.assertIn("meta", data)
        self.assertNotEqual(data["meta"]["limit"], 5)
        self.assertEqual(data["meta"]["limit"], paginator.max_limit)

    def test_pagination_meta_only(self):
        self.request.query_params = {"meta_only": True}
        ProductFactory()
        paginator = IzbergPagination()

        result = paginator.get_paginated_response(
            paginator.paginate_queryset(Product.objects.all(), request=self.request)
        )

        self.assertTrue(isinstance(result, Response))
        data = result.data
        self.assertIn("meta", data)
        self.assertEqual(len(data["objects"]), 0)
