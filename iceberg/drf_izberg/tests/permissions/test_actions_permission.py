from django.test import RequestFactory
from drf_izberg.permissions.actions import ActionsPermissions
from drf_izberg.permissions.constants.actions import DESTROY, LIST
from drf_izberg.permissions.constants.owner import MERCHANT_OWNER, OPERATOR_OWNER
from drf_izberg.tests.base import BasePermissionsAPITestCase
from drf_izberg.tests.fixtures import MerchantOwnerFakeViewSet, OperatorOwnerFakeViewSet
from ims.api.exceptions import InvalidToken


class ActionsPermissionsTestCase(BasePermissionsAPITestCase):
    def test_permission(self):
        """
        Given:
            - a request with owner type OPERATOR
            - a fake view set with operator permission constraints
            - an action LIST
        When calling has_permission
        Then the request has permission
        """
        request = RequestFactory()
        request.owner_type = OPERATOR_OWNER

        fake_view = OperatorOwnerFakeViewSet()
        fake_view.action = LIST

        permission = ActionsPermissions()
        has_permission = permission.has_permission(request, fake_view)

        self.assertEqual(has_permission, True)

    def test_permission__wrong_action(self):
        """
        Given:
            - a request with owner type MERCHANT
            - a fake view set with merchant permission constraints
            - an action DESTROY
        When calling has_permission
        Then has_permission raise InvalidOwnerType
        """
        request = RequestFactory()
        request.owner_type = MERCHANT_OWNER
        # Just use in payload by InvalidToken
        request.raw_scope = "fake:fake"
        request.application_id = "1"
        request.merchant_id = "1"

        fake_view = MerchantOwnerFakeViewSet()
        fake_view.action = DESTROY

        permission = ActionsPermissions()

        with self.assertRaises(InvalidToken):
            permission.has_permission(request, fake_view)

    def test_permission__custom_action(self):
        """
        Given:
            - a request with owner type OPERATOR
            - a fake view set with operator permission constraints
            - an action custom: "custom_action"
        When calling has_permission
        Then the request has permission
        """
        request = RequestFactory()
        request.owner_type = OPERATOR_OWNER

        fake_view = OperatorOwnerFakeViewSet()
        fake_view.action = "custom_action"

        permission = ActionsPermissions()
        has_permission = permission.has_permission(request, fake_view)

        self.assertEqual(has_permission, True)
