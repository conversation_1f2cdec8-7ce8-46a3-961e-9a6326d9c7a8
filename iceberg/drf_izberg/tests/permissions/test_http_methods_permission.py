from django.test import RequestFactory
from drf_izberg.permissions.http_methods import HTTPMethodsPermissions
from drf_izberg.tests.base import BasePermissionsAPITestCase
from drf_izberg.tests.fixtures import MerchantOwnerFakeViewSet, OperatorOwnerFakeViewSet
from rest_framework.exceptions import MethodNotAllowed


class HTTPMethodsPermissionsTestCase(BasePermissionsAPITestCase):
    def test_permission(self):
        """
        Given:
            - a request with a method http GET
            - a fake view set with http_method_names containing "get"
        When calling has_permission
        Then the request has permission
        """
        request = RequestFactory()
        request.method = "GET"

        fake_view = OperatorOwnerFakeViewSet()

        permission = HTTPMethodsPermissions()
        has_permission = permission.has_permission(request, fake_view)

        self.assertEqual(has_permission, True)

    def test_permission__wrong_method(self):
        """
        Given:
            - a request with a method http POST
            - a fake view set with http_method_names containing only "get"
        When calling has_permission
        Then has_permission raise InvalidOwnerType
        """
        request = RequestFactory()
        request.method = "POST"

        fake_view = MerchantOwnerFakeViewSet()
        permission = HTTPMethodsPermissions()

        with self.assertRaises(MethodNotAllowed):
            permission.has_permission(request, fake_view)
