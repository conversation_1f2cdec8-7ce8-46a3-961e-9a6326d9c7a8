from django.test import RequestFactory
from drf_izberg.permissions.constants.actions import LIST
from drf_izberg.permissions.constants.owner import MERCHANT_OWNER, OPERATOR_OWNER
from drf_izberg.permissions.owners import OwnersPermissions
from drf_izberg.tests.base import BasePermissionsAPITestCase
from drf_izberg.tests.fixtures import OperatorOwnerFakeViewSet
from ims.api.exceptions import InvalidOwnerType


class OwnersPermissionsTestCase(BasePermissionsAPITestCase):
    def test_permission(self):
        """
        Given:
            - a request with owner type OPERATOR
            - a fake view set with operator permission constraints
        When calling has_permission
        Then the request has permission
        """
        request = RequestFactory()
        request.owner_type = OPERATOR_OWNER

        fake_view = OperatorOwnerFakeViewSet()

        permission = OwnersPermissions()
        has_permission = permission.has_permission(request, fake_view)

        self.assertEqual(has_permission, True)

    def test_permission__wrong_owner(self):
        """
        Given:
            - a request with owner type MERCHANT
            - a fake view set with operator permission constraints and an action
        When calling has_permission
        Then has_permission raise InvalidOwnerType
        """
        request = RequestFactory()
        request.owner_type = MERCHANT_OWNER

        fake_view = OperatorOwnerFakeViewSet()
        fake_view.action = LIST

        permission = OwnersPermissions()

        with self.assertRaises(InvalidOwnerType):
            permission.has_permission(request, fake_view)
