from django.test import RequestFactory
from drf_izberg.permissions.constants.actions import DESTROY, LIST, RETRIEVE, UPDATE
from drf_izberg.permissions.constants.owner import OPERATOR_OWNER
from drf_izberg.permissions.constants.scopes import ANY_SCOPE
from drf_izberg.permissions.roles import RolesPermissions
from drf_izberg.tests.base import BasePermissionsAPITestCase
from drf_izberg.tests.fixtures import OperatorOwnerFakeViewSet
from ims.api.exceptions import InsufficientScope


class RolesPermissionsTestCase(BasePermissionsAPITestCase):
    def test_permission(self):
        """
        Given:
            - a request with owner type OPERATOR and
              a scope customer:read
            - a fake view set with operator permission constraints
            - an action LIST
        When calling has_permission
        Then the request has permission
        """
        request = RequestFactory()
        request.owner_type = OPERATOR_OWNER
        request.scopes = ["customer:read"]

        fake_view = OperatorOwnerFakeViewSet()
        fake_view.action = LIST

        permission = RolesPermissions()
        has_permission = permission.has_permission(request, fake_view)

        self.assertEqual(has_permission, True)

    def test_permission__wrong_role_for_action(self):
        """
        Given:
            - a request with owner type OPERATOR and
              a scope customer:read
            - a fake view set with operator permission constraints
            - an action UPDATE
        When calling has_permission
        Then has_permission raise InsufficientRole
        """
        request = RequestFactory()
        request.owner_type = OPERATOR_OWNER
        request.scopes = ["customer:read"]
        # Just use in payload by InvalidToken
        request.raw_scope = "fake:fake"
        request.application_id = "1"
        request.merchant_id = "1"

        fake_view = OperatorOwnerFakeViewSet()
        fake_view.action = UPDATE

        permission = RolesPermissions()

        with self.assertRaises(InsufficientScope) as e:
            permission.has_permission(request, fake_view)

        self.assertEqual(
            e.exception.error_context["accepted_scopes"],
            [
                "customer:write",
                "customer:admin",
                "write",
                "admin",
            ],
        )

    def test_permission__multiple_scopes(self):
        """
        Given:
            - a request with owner type OPERATOR and
              a scope multiple scope
            - a fake view set with operator permission constraints
            - an action RETRIEVE
        When calling has_permission
        Then the request has permission
        """
        request = RequestFactory()
        request.owner_type = OPERATOR_OWNER
        request.scopes = ["customer:read", "customer:write"]

        fake_view = OperatorOwnerFakeViewSet()
        fake_view.action = RETRIEVE

        permission = RolesPermissions()
        has_permission = permission.has_permission(request, fake_view)

        self.assertEqual(has_permission, True)

    def test_permission__scopes_wildcard__admin(self):
        """
        Given:
            - a request with owner type OPERATOR and
              a scope "*"
            - a fake view set with operator permission constraints
            - an action UPDATE
        When calling has_permission
        Then the request has permission
        """
        request = RequestFactory()
        request.owner_type = OPERATOR_OWNER
        request.scopes = ["profile", "yolo", "admin"]

        fake_view = OperatorOwnerFakeViewSet()
        fake_view.action = UPDATE

        permission = RolesPermissions()
        has_permission = permission.has_permission(request, fake_view)

        self.assertEqual(has_permission, True)

    def test_permission__wrong_scope_with_role(self):
        """
        Given:
            - a request with owner type OPERATOR and
              a scope "oms:admin", "customer:read"
            - a fake view set with operator permission constraints
            - an action UPDATE
        When calling has_permission
        Then has_permission raise InsufficientRole
        """
        request = RequestFactory()
        request.owner_type = OPERATOR_OWNER
        request.scopes = ["oms:admin", "customer:read"]
        # Just use in payload by InvalidToken
        request.raw_scope = "fake:fake"
        request.application_id = "1"
        request.merchant_id = "1"

        fake_view = OperatorOwnerFakeViewSet()
        fake_view.action = UPDATE

        permission = RolesPermissions()

        with self.assertRaises(InsufficientScope) as e:
            permission.has_permission(request, fake_view)

        self.assertEqual(
            e.exception.error_context["accepted_scopes"],
            [
                "customer:write",
                "customer:admin",
                "write",
                "admin",
            ],
        )

    def test_permission__view_wildcard(self):
        """
        Given:
            - a request with owner type OPERATOR and
              a scope "ims"
            - a fake view set with operator permission constraints
              and permission_scopes set to ANY_SCOPE
            - an action DESTROY
        When calling has_permission
        Then the request has permission
        """
        request = RequestFactory()
        request.owner_type = OPERATOR_OWNER
        request.scopes = ["ims:admin"]

        fake_view = OperatorOwnerFakeViewSet()
        fake_view.action = DESTROY
        fake_view.permission_scopes = [ANY_SCOPE]

        permission = RolesPermissions()
        has_permission = permission.has_permission(request, fake_view)

        self.assertEqual(has_permission, True)
