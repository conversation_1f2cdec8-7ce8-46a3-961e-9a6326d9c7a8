from django.test import RequestFactory
from drf_izberg.permissions.constants.actions import LIST, UPDATE
from drf_izberg.permissions.constants.owner import OPERATOR_OWNER
from drf_izberg.permissions.constants.scopes import ANY_SCOPE
from drf_izberg.permissions.scopes import ScopesPermissions
from drf_izberg.tests.base import BasePermissionsAPITestCase
from drf_izberg.tests.fixtures import OperatorOwnerFakeViewSet
from ims.api.exceptions import InsufficientScope


class ScopesPermissionsTestCase(BasePermissionsAPITestCase):
    def test_permission(self):
        """
        Given:
            - a request with scopes customer read/write
            - a fake view set with scope customer
        When calling has_permission
        Then the request has permission
        """
        request = RequestFactory()
        request.scopes = ["customer:read", "customer:write"]

        fake_view = OperatorOwnerFakeViewSet()

        permission = ScopesPermissions()
        has_permission = permission.has_permission(request, fake_view)

        self.assertEqual(has_permission, True)

    def test_permission__wrong_scope(self):
        """
        Given:
            - a request with scopes oms read/write
            - a fake view set with scope customer
        When calling has_permission
        Then has_permission raise InsufficientScope
        """
        request = RequestFactory()
        request.raw_scope = "oms:read oms:write"
        request.scopes = ["oms:read", "oms:write"]
        request.owner_type = OPERATOR_OWNER

        fake_view = OperatorOwnerFakeViewSet()
        fake_view.action = LIST

        permission = ScopesPermissions()

        with self.assertRaises(InsufficientScope) as e:
            permission.has_permission(request, fake_view)

        self.assertEqual(
            e.exception.error_context["accepted_scopes"],
            [
                "customer:read",
                "customer:write",
                "customer:admin",
                "read",
                "write",
                "admin",
            ],
        )

    def test_permission__no_scope_given(self):
        """
        Given:
            - a request with no scopes
            - a fake view set with scope customer
        When calling has_permission
        Then has_permission raise InsufficientScope
        """
        request = RequestFactory()
        request.raw_scope = ""
        request.scopes = [""]
        request.owner_type = OPERATOR_OWNER

        fake_view = OperatorOwnerFakeViewSet()
        fake_view.action = LIST

        permission = ScopesPermissions()

        with self.assertRaises(InsufficientScope) as e:
            permission.has_permission(request, fake_view)

        self.assertEqual(
            e.exception.error_context["accepted_scopes"],
            [
                "customer:read",
                "customer:write",
                "customer:admin",
                "read",
                "write",
                "admin",
            ],
        )

    def test_permission__no_scope_given__update(self):
        """
        Given:
            - a request with no scopes and an owner type
            - a fake view set with scope customer
        When calling has_permission
        Then has_permission raise InsufficientScope
        and accepted_scopes is correct
        """
        request = RequestFactory()
        request.raw_scope = ""
        request.scopes = [""]
        request.owner_type = OPERATOR_OWNER

        fake_view = OperatorOwnerFakeViewSet()
        fake_view.action = UPDATE

        permission = ScopesPermissions()

        with self.assertRaises(InsufficientScope) as e:
            permission.has_permission(request, fake_view)

        self.assertEqual(
            e.exception.error_context["accepted_scopes"],
            [
                "customer:write",
                "customer:admin",
                "write",
                "admin",
            ],
        )

    def test_permission__request_scope_wildcard(self):
        """
        Given:
            - a request with wildcard scope
            - a fake view set with scope customer
        When calling has_permission
        Then the request has permission
        """
        request = RequestFactory()

        fake_view = OperatorOwnerFakeViewSet()

        permission = ScopesPermissions()

        request.scopes = ["admin"]
        has_permission = permission.has_permission(request, fake_view)
        self.assertEqual(has_permission, True)

        request.scopes = ["write"]
        has_permission = permission.has_permission(request, fake_view)
        self.assertEqual(has_permission, True)

        request.scopes = ["read"]
        has_permission = permission.has_permission(request, fake_view)
        self.assertEqual(has_permission, True)

    def test_permission__view_wildcard(self):
        """
        Given:
            - a request with a scope
            - a fake view set with ANY_SCOPE
        When calling has_permission
        Then the request has permission
        """
        request = RequestFactory()
        request.scopes = ["fake:admin"]

        fake_view = OperatorOwnerFakeViewSet()
        # the view accept any scopes
        fake_view.permission_scopes = [ANY_SCOPE]

        permission = ScopesPermissions()
        has_permission = permission.has_permission(request, fake_view)

        self.assertEqual(has_permission, True)
