from django.http import QueryDict
from django.test import RequestFactory, override_settings
from drf_izberg.permissions.constants.owner import MERCHANT_OWNER, OPERATOR_OWNER
from drf_izberg.permissions.write_fields import WriteFieldsPermissions
from drf_izberg.tests.base import BasePermissionsAPITestCase
from drf_izberg.tests.fixtures import (
    FakeSerializerWithCreateAndEdit,
    FakeSerializerWithEdit,
    FakeSerializerWithEditAndApplicationWritable,
    OperatorOwnerFakeViewSet,
)
from rest_framework.exceptions import ValidationError


class WriteFieldsPermissionsTestCase(BasePermissionsAPITestCase):
    def test_permission__get(self):
        """
        Given:
            - a request with method get
            - a fake view
            - a fake serializer with no Meta
        When calling has_permission
        Then the request has permission
        """
        request = RequestFactory()
        request.method = "GET"

        fake_view = OperatorOwnerFakeViewSet()
        fake_view.serializer_class = FakeSerializerWithCreateAndEdit()

        permission = WriteFieldsPermissions()
        has_permission = permission.has_permission(request, fake_view)

        self.assertEqual(has_permission, True)

    def test_permission__post(self):
        """
        Given:
            - a request with method POST
            - an owner type Merchant
            - a data payload with some fields
            - a fake view
            - a fake serializer with creatable_fields and editable_fields
        When calling has_permission
        Then the request has permission
        """
        request = RequestFactory()
        request.data = {
            "field_a_create_only": "test",
            "field_b_edit": "test",
        }
        request.owner_type = MERCHANT_OWNER
        request.method = "POST"

        fake_view = OperatorOwnerFakeViewSet()
        fake_view.serializer_class = FakeSerializerWithCreateAndEdit()

        permission = WriteFieldsPermissions()
        has_permission = permission.has_permission(request, fake_view)

        self.assertEqual(has_permission, True)

    def test_permission__post__disallow_create_field(self):
        """
        Given:
            - a request with method POST
            - an owner type Merchant
            - a data payload with a field not in creatable_fields or editable_fields
            - a fake view
            - a fake serializer with only editable_fields
        When calling has_permission
        Then has_permission raise ValidationError
        """
        request = RequestFactory()
        request.owner_type = MERCHANT_OWNER
        request.data = {
            "field_a_create": "test",
            "field_b_edit": "test",
        }
        request.method = "POST"

        fake_view = OperatorOwnerFakeViewSet()
        fake_view.serializer_class = FakeSerializerWithEdit()

        permission = WriteFieldsPermissions()

        with self.assertRaises(ValidationError):
            permission.has_permission(request, fake_view)

    def test_permission__post__operator_writeable_fields__allow_for_operator(self):
        """
        Given:
            - a request with method POST
            - an owner type OPERATOR
            - a data payload with some fields
            - a fake view
            - a fake serializer with editable_fields and operator_writeable_fields
        When calling has_permission
        Then the request has permission
        """
        request = RequestFactory()
        request.owner_type = OPERATOR_OWNER
        request.data = {
            "field_b_edit": "test",
            "field_c_application_writeable": "test",
        }
        request.method = "POST"

        fake_view = OperatorOwnerFakeViewSet()
        fake_view.serializer_class = FakeSerializerWithEditAndApplicationWritable()

        permission = WriteFieldsPermissions()
        has_permission = permission.has_permission(request, fake_view)

        self.assertEqual(has_permission, True)

    def test_permission__post__operator_writeable_fields__disallow_for_merchant(
        self,
    ):
        """
        Given:
            - a request with method POST
            - an owner type MERCHANT
            - a data payload with some fields
            - a fake view
            - a fake serializer with editable_fields and operator_writeable_fields
        When calling has_permission
        Then has_permission raise a ValidationError
        """
        request = RequestFactory()
        request.owner_type = MERCHANT_OWNER
        request.data = {
            "field_a_create": "test",
            "field_b_edit": "test",
        }
        request.method = "POST"

        fake_view = OperatorOwnerFakeViewSet()
        fake_view.serializer_class = FakeSerializerWithEditAndApplicationWritable()

        permission = WriteFieldsPermissions()

        with self.assertRaises(ValidationError):
            permission.has_permission(request, fake_view)

    def test_permission__patch(self):
        """
        Given:
            - a request with method PATCH
            - an owner type Merchant
            - a data payload with some fields
            - a fake view
            - a fake serializer with editable_fields
        When calling has_permission
        Then the request has permission
        """
        request = RequestFactory()
        request.data = {
            "field_b_edit": "test",
        }
        request.owner_type = MERCHANT_OWNER
        request.method = "PATCH"

        fake_view = OperatorOwnerFakeViewSet()
        fake_view.serializer_class = FakeSerializerWithEdit()

        permission = WriteFieldsPermissions()
        has_permission = permission.has_permission(request, fake_view)

        self.assertEqual(has_permission, True)

    def test_permission__patch__disallow_field(self):
        """
        Given:
            - a request with method PATCH
            - an owner type Merchant
            - a data payload with a field not in editable_fields
            - a fake view
            - a fake serializer with only editable_fields
        When calling has_permission
        Then has_permission raise ValidationError
        """
        request = RequestFactory()
        request.owner_type = MERCHANT_OWNER
        request.data = {
            "field_a_create": "test",
        }
        request.method = "PATCH"

        fake_view = OperatorOwnerFakeViewSet()
        fake_view.serializer_class = FakeSerializerWithEdit()

        permission = WriteFieldsPermissions()

        with self.assertRaises(ValidationError):
            permission.has_permission(request, fake_view)

    def test_permission__patch__operator_writeable_fields__allow_for_operator(self):
        """
        Given:
            - a request with method PATCH
            - an owner type OPERATOR
            - a data payload with some fields
            - a fake view
            - a fake serializer with  operator_writeable_fields
        When calling has_permission
        Then the request has permission
        """
        request = RequestFactory()
        request.owner_type = OPERATOR_OWNER
        request.data = {
            "field_b_edit": "test",
            "field_c_application_writeable": "test",
        }
        request.method = "PATCH"

        fake_view = OperatorOwnerFakeViewSet()
        fake_view.serializer_class = FakeSerializerWithEditAndApplicationWritable()

        permission = WriteFieldsPermissions()
        has_permission = permission.has_permission(request, fake_view)

        self.assertEqual(has_permission, True)

    def test_permission__patch__operator_writeable_fields__disallow_for_merchant(
        self,
    ):
        """
        Given:
            - a request with method PATCH
            - an owner type OPERATOR
            - a data payload with a field
            - a fake view
            - a fake serializer with only operator_writeable_fields
        When calling has_permission
        Then has_permission raise ValidationError
        """
        request = RequestFactory()
        request.owner_type = MERCHANT_OWNER
        request.data = {
            "field_b_edit": "test",
            "field_c_application_writeable": "test",
        }
        request.method = "PATCH"

        fake_view = OperatorOwnerFakeViewSet()
        fake_view.serializer_class = FakeSerializerWithEditAndApplicationWritable()
        permission = WriteFieldsPermissions()

        with self.assertRaises(ValidationError):
            permission.has_permission(request, fake_view)

    @override_settings(RAISE_SILENT_ERRORS=False)
    def test_permission__patch__pop_disallowed_fields(self):
        """
        Given:
            - a request with method PATCH
            - an owner type OPERATOR
            - a data payload with some fields (QueryDict as a real `request.data`, since
              in write_fields.py we will mutate it.
            - a fake view
            - a fake serializer with  operator_writeable_fields
        When calling has_permission
        Then the request has permission
        and `request.data` has only `field_b_edit`, the other value has been pop
        """
        request = RequestFactory()
        request.owner_type = MERCHANT_OWNER
        request.data = QueryDict("field_b_edit=test&field_c_application_writeable=test")
        request.method = "PATCH"

        fake_view = OperatorOwnerFakeViewSet()
        fake_view.serializer_class = FakeSerializerWithEditAndApplicationWritable()

        permission = WriteFieldsPermissions()
        has_permission = permission.has_permission(request, fake_view)

        self.assertEqual(has_permission, True)
        self.assertEqual(request.data.dict(), {"field_b_edit": "test"})

    @override_settings(RAISE_SILENT_ERRORS=False)
    def test_permission__patch__pop_disallowed_fields__data_as_dict(self):
        """
        Given:
            - a request with method PATCH
            - an owner type OPERATOR
            - a data payload with some fields (Dict as a real `request.data`, since
              in write_fields.py we will mutate it.
            - a fake view
            - a fake serializer with  operator_writeable_fields
        When calling has_permission
        Then the request has permission
        and `request.data` has only `field_b_edit`, the other value has been pop
        """
        request = RequestFactory()
        request.owner_type = MERCHANT_OWNER

        request.data = {
            "field_b_edit": "test",
            "field_c_application_writeable": "test",
        }
        request.method = "PATCH"

        fake_view = OperatorOwnerFakeViewSet()
        fake_view.serializer_class = FakeSerializerWithEditAndApplicationWritable()

        permission = WriteFieldsPermissions()
        has_permission = permission.has_permission(request, fake_view)

        self.assertEqual(has_permission, True)
        self.assertEqual(request.data, {"field_b_edit": "test"})
