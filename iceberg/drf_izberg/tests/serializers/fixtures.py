from apps.address.api.country.serializers import CountrySerializer
from apps.address.models import Country
from apps.ice_applications.models import Application
from django_filters import rest_framework as filters
from django_filters.rest_framework import DjangoFilterBackend
from drf_izberg.filters.filterset.number_in_filter import NumberInFilter
from drf_izberg.serializers.hyper_linked_model import IzbergHyperlinkedModelSerializer
from drf_izberg.serializers.hyper_linked_related_field import (
    IzbergHyperlinkedRelatedField,
)
from drf_izberg.serializers.private_file_field import PrivateFileField
from drf_izberg.views.base import IzbergModelViewSet
from rest_framework import serializers


class FakeApplicationSerializer(
    IzbergHyperlinkedModelSerializer,
):
    class Meta:
        # Using application to avoid the need of creating a fake model.
        # Application will probably still exist in 10 years :p
        model = Application
        creatable_fields = ()
        editable_fields = (
            "namespace",
            "name",
        )
        read_only_fields = ("country",)
        operator_writeable_fields = ()
        fields = creatable_fields + editable_fields + read_only_fields

    namespace = serializers.CharField(
        required=False,
    )
    # Same here, using country to avoid the need of creating a fake model
    # Should still exist in 100 years :p
    country = IzbergHyperlinkedRelatedField(
        view_name="country-detail",
        many=False,
        read_only=True,
        full_representation={
            "queryset": Country.objects,
            "serializer": CountrySerializer,
        },
    )


class FakeApplicationFilter(
    filters.FilterSet,
):
    namespace = filters.CharFilter(
        field_name="namespace",
        lookup_expr="iexact",
    )
    country__in = NumberInFilter(field_name="country_id", lookup_expr="in")

    name = filters.CharFilter(
        field_name="name",
        lookup_expr="iexact",
    )

    class Meta:
        model = Application
        fields = ["name", "namespace", "country"]


class FakeApplicationView(
    IzbergModelViewSet,
):
    permission_classes = {}
    serializer_class = FakeApplicationSerializer
    resource_name = "fake_view"
    filterset_class = FakeApplicationFilter

    filter_backends = [
        DjangoFilterBackend,
    ]


class FakeFileModel:
    class Meta:
        verbose_name = "Fake File"

    _meta = Meta


class FakeFileSerializer(serializers.Serializer):
    class Meta:
        model = FakeFileModel
        depth = 0  # same as full = 0
        creatable_fields = ("file", "file_short_expire")
        editable_fields = ()
        read_only_fields = ()
        fields = creatable_fields + editable_fields + read_only_fields
        operator_writeable_fields = ()
        extra_kwargs = {}

    file = PrivateFileField(required=False)
    file_short_expire = PrivateFileField(expires_in=1, default=None, required=False)


FAKE_IDENTITY_DATA = {
    "domain_id": "DOMAIN1234",
    "author_id": "aaaaaaaa-1234-12c3-45b6-a1234567890a",
}

FAKE_PRESIGNED_POST_DATA = {
    "fields": {
        "AWSAccessKeyId": "XXXXXX",
        "Content-Type": "text/plain",
        "acl": "private",
        "key": (
            f"{FAKE_IDENTITY_DATA['domain_id']}/"
            f"{FAKE_IDENTITY_DATA['author_id']}/"
            "2022/06/12/test_8777bc17-8624-4839-92b8-6cf51fffb660.txt"
        ),
        "policy": "XXXXXXX=",
        "signature": "YYYYYY=",
    },
    "url": "https://xxx.s3.amazonaws.com/",
}

FAKE_PRESIGNED_RESOURCE_URL = (
    "https://xxx.s3.amazonaws.com/"
    f"{FAKE_IDENTITY_DATA['domain_id']}/"
    f"{FAKE_IDENTITY_DATA['author_id']}/"
    "2022/06/12/test.txt"
    "?signature=XXX"
)


FAKE_FILE_METADATA = {
    "content_type": "text/csv",
    "extension": ".csv",
    "content_length": 100,
}
