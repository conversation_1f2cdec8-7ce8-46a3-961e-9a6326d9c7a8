from datetime import datetime
from decimal import Decimal
from typing import Optional
from uuid import UUID

from drf_izberg.fields import remove_exponent


def format_datetime(value: Optional[datetime]) -> Optional[str]:
    if value is None:
        return None
    return value.astimezone().isoformat()


def format_decimal(value: Optional[Decimal]) -> Optional[str]:
    if value is None:
        return None
    return str(remove_exponent(value))


def format_uuid(value: Optional[UUID]) -> Optional[str]:
    if value is None:
        return None
    return str(value)
