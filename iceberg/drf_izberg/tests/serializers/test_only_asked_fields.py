from apps.ice_applications.models import Application
from apps.testing.factories import ApplicationFactory
from drf_izberg.tests.base import BaseSerializerAPITestCase
from drf_izberg.tests.serializers.fixtures import (
    FakeApplicationSerializer,
    FakeApplicationView,
)


class OnlyAskedFieldsTestCase(BaseSerializerAPITestCase):
    def test_ask_one_field(self):
        """
        Given:
            - a fake request with query_params
            - "only" parameter set in query_params with 1 field
            - a fake view
            - a fake serializer
        When calling the serializer
        Then the data has only the field listed in "only" list
        """
        self.request.query_params = {"only": "name"}

        view = FakeApplicationView()
        application = ApplicationFactory(id=1)
        serializer = FakeApplicationSerializer(
            instance=application,
            context={
                "request": self.request,
                "view": view,
            },
        )

        self.assertEqual(len(serializer.data), 1)
        self.assertEqual(serializer.data["name"], application.name)

    def test_ask_multiple_field(self):
        """
        Given:
            - a fake request with query_params
            - "only" parameter set in query_params with 2 fields
            - a fake view
            - a fake serializer
        When calling the serializer
        Then the data has only the fields listed in "only" list
        """
        self.request.query_params = {"only": "name,namespace"}

        view = FakeApplicationView()
        application = ApplicationFactory(id=1)
        serializer = FakeApplicationSerializer(
            instance=application,
            context={
                "request": self.request,
                "view": view,
            },
        )

        self.assertEqual(len(serializer.data), 2)
        self.assertEqual(serializer.data["name"], application.name)
        self.assertEqual(serializer.data["namespace"], application.namespace)

    def test_ask_one_field__with_filtering(self):
        """
        Given:
            - a fake request with query_params
            - "only" parameter set in query_params with 1 field
            - filter on name in query_params
            - a fake view
            - a fake serializer
        When calling the serializer
        Then the data has only the fields listed in "only" list
        """
        application_name = "marvel"
        self.request.query_params = {
            "only": "name",
            "name": application_name,
        }

        view = FakeApplicationView()
        view.request = self.request

        ApplicationFactory(id=1, name=application_name)
        ApplicationFactory(id=2)

        queryset_filtered = view.filter_queryset(Application.objects.all())

        serializer = FakeApplicationSerializer(
            queryset_filtered,
            context={
                "request": self.request,
                "view": view,
            },
            many=True,
        )

        self.assertEqual(len(serializer.data), 1)
        self.assertEqual(len(serializer.data[0]), 1)
        self.assertEqual(serializer.data[0]["name"], application_name)

    def test_ask_multiple_field__with_full(self):
        """
        Given:
            - a fake request with query_params
            - "only" parameter set in query_params with 2 fields
            - "full_country=1" in query_params
            - a fake view
            - a fake serializer
        When calling the serializer
        Then the data has only the fields listed in "only" list and the resource asked
        as full is fully represented.
        """
        self.request.query_params = {
            "only": "name,country",
            "full_country": 1,
        }

        view = FakeApplicationView()
        application = ApplicationFactory(id=1)
        serializer = FakeApplicationSerializer(
            instance=application,
            context={
                "request": self.request,
                "view": view,
            },
        )

        self.assertEqual(len(serializer.data), 2)
        self.assertEqual(serializer.data["name"], application.name)
        self.assertEqual(
            list(serializer.data["country"].keys()),
            [
                "id",
                "pk",
                "resource_uri",
                "code",
                "continent",
                "name",
                "languages",
                "lat",
                "lng",
                "phone_prefix",
                "sort_order",
                "weight",
            ],
        )

    def test_ask_no_field(self):
        """
        Given:
            - a fake request with query_params
            - "only" parameter set in query_params with 0 field
            - a fake view
            - a fake serializer
        When calling the serializer
        Then data is empty
        """
        self.request.query_params = {"only": ""}

        view = FakeApplicationView()
        application = ApplicationFactory(id=1)
        serializer = FakeApplicationSerializer(
            instance=application,
            context={
                "request": self.request,
                "view": view,
            },
        )

        self.assertEqual(len(serializer.data), 0)

    def test_ask_multiple_field__with_wrong_field(self):
        """
        Given:
            - a fake request with query_params
            - "only" parameter set in query_params with 2 fields
            - a fake view
            - a fake serializer
        When calling the serializer
        Then the data has only the fields listed in "only" list
        """
        self.request.query_params = {"only": "name,namespace_yolo"}

        view = FakeApplicationView()
        application = ApplicationFactory(id=1)
        serializer = FakeApplicationSerializer(
            instance=application,
            context={
                "request": self.request,
                "view": view,
            },
        )

        self.assertEqual(len(serializer.data), 1)
        self.assertEqual(serializer.data["name"], application.name)
