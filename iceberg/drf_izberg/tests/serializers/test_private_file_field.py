from datetime import datetime

import mock
from django.core.files import File
from drf_izberg.tests.base import BaseSerializerAPITestCase
from drf_izberg.tests.serializers.fixtures import (
    FAKE_FILE_METADATA,
    FAKE_PRESIGNED_POST_DATA,
    FAKE_PRESIGNED_RESOURCE_URL,
    FakeFileSerializer,
)
from rest_framework.exceptions import ValidationError
from storages.backends.s3boto3 import S3Boto3StorageFile


class PrivateFileFieldTestCase(BaseSerializerAPITestCase):
    def setUp(self):
        super().setUp()

    @mock.patch("drf_izberg.serializers.private_file_field.PrivateFileField._copy_file")
    @mock.patch(
        "drf_izberg.serializers.private_file_field.PrivateFileField._get_file_metadata",
        return_value=FAKE_FILE_METADATA,
    )
    def test_serialize_file(self, *args):
        serializer = FakeFileSerializer(
            data={"file": "https://test.aws.com/1234/abc/test.pdf"},
            context={"request": self.request},
        )

        serializer.is_valid(raise_exception=True)
        datetime_now = datetime.now()

        # the file pattern must be :
        # app_id/model_verbose_name/year/month/day/name-uuid.ext
        valid_pattern = r"1/fake_file/{}/{}/{}/test-[a-zA-Z0-9-]+\.pdf".format(
            datetime_now.year, datetime_now.month, datetime_now.day
        )
        self.assertRegex(
            serializer.validated_data["file"],
            valid_pattern,
        )

    @mock.patch(
        "drf_izberg.serializers.private_file_field.PrivateFileField._get_file_metadata"
    )
    def test_serializer_with_invalid_content_length(self, mock_file_metadata):
        mock_file_metadata.return_value = {
            "content_type": "text/csv",
            "extension": ".csv",
            "content_length": 20000000000000000000,
        }
        serializer = FakeFileSerializer(
            data={"file": "https://test.aws.com/1234/abc/test.pdf"},
            context={"request": self.request},
        )

        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)

        self.assertEqual(
            str(context.exception),
            "{'file': {'file': ErrorDetail(string='content_length "
            "is too high', code='invalid')}}",
        )

    @mock.patch(
        "drf_izberg.serializers.private_file_field.PrivateFileField._get_file_metadata"
    )
    def test_serializer_with_invalid_extension(self, mock_file_metadata):
        mock_file_metadata.return_value = {
            "content_type": "text/csv",
            "extension": ".toto",
            "content_length": 100,
        }
        serializer = FakeFileSerializer(
            data={"file": "https://test.aws.com/1234/abc/test.pdf"},
            context={"request": self.request},
        )

        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)

        self.assertEqual(
            str(context.exception),
            "{'file': {'file': ErrorDetail(string='extension is not valid', code='invalid')}}",
        )

    @mock.patch(
        "drf_izberg.serializers.private_file_field.PrivateFileField._get_file_metadata"
    )
    def test_serializer_with_invalid_content_type(self, mock_file_metadata):
        mock_file_metadata.return_value = {
            "content_type": "toto/csv",
            "extension": ".csv",
            "content_length": 100,
        }
        serializer = FakeFileSerializer(
            data={"file": "https://test.aws.com/1234/abc/test.pdf"},
            context={"request": self.request},
        )

        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)

        self.assertEqual(
            str(context.exception),
            "{'file': {'file': ErrorDetail(string='content_type is not valid'"
            ", code='invalid')}}",
        )

    @mock.patch(
        "botocore.signers.RequestSigner.generate_presigned_url",
        return_value=FAKE_PRESIGNED_RESOURCE_URL,
    )
    @mock.patch(
        "botocore.signers.S3PostPresigner.generate_presigned_post",
        return_value=FAKE_PRESIGNED_POST_DATA,
    )
    def test_to_representation(self, *args):
        serializer = FakeFileSerializer(
            data={"file": "https://test.aws.com/1234/abc/test.pdf"},
            context={"request": self.request},
        )

        file_mock = mock.MagicMock(spec=File)
        file_mock.name = "avatar.pdf"
        s3file_mock = mock.Mock(S3Boto3StorageFile)
        s3file_mock.obj = mock.Mock(bucket_name="bucket", key="key")
        file_mock.file = s3file_mock

        return_value = serializer.fields["file"].to_representation(file_mock)

        # in real world scenario, the pattern would be different, but at least it would
        # ensure that we are calling the presigned url method.
        self.assertEqual(
            return_value,
            "https://xxx.s3.amazonaws.com/DOMAIN1234/aaaaaaaa-1234-12c3-45b6"
            "-a1234567890a/2022/06/12/test.txt?signature=XXX",
        )

    @mock.patch(
        "drf_izberg.serializers.private_file_field.PrivateFileField._get_file_metadata",
        return_value=FAKE_FILE_METADATA,
    )
    @mock.patch("drf_izberg.serializers.private_file_field.PrivateFileField._copy_file")
    @mock.patch(
        "drf_izberg.serializers.private_file_field.PrivateFileField."
        "_add_tags_for_soft_deleted_file"
    )
    def test_adding_tags_is_called(self, mock_adding_tags, *args):
        """
        When an update is done on an existing file, the file would be replaced
        by the new one. The old file would have two tags in order to identify them
        easily in the S3 bucket which would simply the future archiving work if needed.
        """

        self.request.method = "PATCH"

        serializer = FakeFileSerializer(
            data={"file": "https://test.aws.com/1234/abc/test.pdf"},
            context={"request": self.request},
        )

        serializer.is_valid(raise_exception=True)
        mock_adding_tags.assert_called_once()
