import logging
from collections import OrderedDict

from django.conf import settings
from django.conf.global_settings import LANGUAGES
from django.utils.translation.trans_real import (
    check_for_language,
    get_language_from_path,
    get_languages,
    get_supported_language_variant,
    language_code_re,
    parse_accept_lang_header,
)

logger = logging.getLogger(__name__)
LANGUAGES_DICT = OrderedDict(LANGUAGES)


def get_supported_languages():
    return get_languages()


def get_language_from_request(
    request,
    check_path=False,
    check_get_params=True,
    supported=None,
):
    """
    ---
    Base django function + check_get_params=True, supported=None and
    default_language=None parameters + return tuple instead of string
    ----
    Analyzes the request to find what language the user wants the system to
    show. Only languages listed in settings.LANGUAGES are taken into account.
    If the user requests a sublanguage where we have a main language, we send
    out the main language.
    If check_path is True, the URL path prefix will be checked for a language
    code, otherwise this is skipped for backwards compatibility.
    """

    if check_get_params:
        lang_code = (
            (request.GET.get("lang", "") or request.GET.get("language", ""))
            .lower()
            .split("_")[0]
        )
        if (
            lang_code in supported
            and lang_code is not None
            and check_for_language(lang_code)
        ):
            return lang_code, "get_param"

    if check_path:
        lang_code = get_language_from_path(request.path_info).split("_")[0]
        if lang_code is not None:
            return lang_code, "path"

    lang_code = request.COOKIES.get(settings.LANGUAGE_COOKIE_NAME, "").split("_")[0]

    try:
        return get_supported_language_variant(lang_code), "cookie"
    except LookupError:
        pass

    # put/post method >> priority to Content-Language
    if request.method in ["POST", "PUT"]:
        accept = request.headers.get(
            "Content-Language", request.headers.get("Accept-Language", "")
        ).split("_")[0]
    else:
        # get/delete method >> priority to Accept-Language
        accept = request.headers.get(
            "Accept-Language", request.headers.get("Content-Language", "")
        ).split("_")[0]

    for accept_lang, unused in parse_accept_lang_header(accept):
        if accept_lang == "*":
            break

        if not language_code_re.search(accept_lang):
            continue

        try:
            return get_supported_language_variant(accept_lang), "accept_lang"
        except LookupError:
            continue
    default_language = settings.LANGUAGE_CODE
    try:
        return (
            get_supported_language_variant(default_language),
            "default_language",
        )
    except LookupError:
        return default_language, "default_language"


def get_request_language(request, default_language="fr", check_get_params=True):
    """
    Returns the language of the request
    optionally handle GET params lang and language
    """
    try:
        language, source = get_language_from_request(
            request,
            supported=LANGUAGES_DICT,
            check_get_params=check_get_params,
        )
        logger.debug("request lang=%s (from %s)" % (language, source))
        return language
    except Exception:
        if settings.DEVELOPMENT:
            # we should fix this if it fails in DEVELOPMENT
            raise
        else:
            logger.exception("in get_request_language: ")
        return default_language
