# Views

Django rest framework offers to handle Resources (Controller) through Views using composition.  
We have defined a generic model view composed of mixins that provides all the functionality
for each of the CRUD methods, plus some additional features needed for Izberg business logic like: 

- Automatic schema generated on `<resource_name>/schema/` 


#### Tips:

- Try to not add too many features on IzbergModelViewSet.   
  This class should stay as small as possible and composed only of globally used features (IzbergSchemaMixin is a good example).  
  If you need some generic features, you can create a mixin and use it only on the resource view that needs it.

