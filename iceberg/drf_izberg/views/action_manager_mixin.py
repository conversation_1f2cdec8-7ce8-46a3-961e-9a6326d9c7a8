from drf_izberg.jwt_authentication import extract_user_tracking_info


class ActionManagerMixin:
    """
    Implements a method for performing action of a model instance action manager.
    """

    def perform_action(self, instance, request, action_name, data=None):
        """
        @param instance: Model instance
        @param request: DRF request
        @param action_name: The action name that has to be performed.
        @param data: Additional data passed to the action method
        @return: dict
        """
        if data is None:
            data = {}

        action_args = {}
        action_args.update(data)
        # enrich the action_args (ip, author ect)
        action_args.update(extract_user_tracking_info(request))

        # Retrieve action on model and call it
        action = getattr(instance.actions, action_name)
        action_result = action(**action_args)

        result = {}
        if action_result:
            result.update(action_result)

        return result
