from drf_izberg.views.base_mixins import (
    IzbergCreateModelMixin,
    IzbergDestroyModelMixin,
    IzbergUpdateModelMixin,
)
from drf_izberg.views.schema_mixins import IzbergSchemaMixin
from rest_framework import mixins
from rest_framework.viewsets import GenericViewSet


class IzbergModelViewSet(
    IzbergSchemaMixin,
    IzbergCreateModelMixin,
    mixins.RetrieveModelMixin,
    IzbergUpdateModelMixin,
    IzbergDestroyModelMixin,
    mixins.ListModelMixin,
    GenericViewSet,
):
    """
    Default model viewset that must be used anytime we develop an API  model.
    Regroup all the necessary mixins.

    Note: NEVER inherit your view directly from GenericViewSet.
    You must inherit from this class instead.
    """

    def dispatch(self, request, *args, **kwargs):
        """
        This method does:
        - Log the usage of DRF. It helps distinguish between Tastypie and DRF for a
          given resource.

        - During the dispatch, DRF will make a copy of the request by reading it first.
          Making, in the process, the request.body inaccessible...
          But our logs use the request.body (see _enrich_with_body in json_logs.py ) and
          trying to access it wil result in:
          "you cannot access body after reading from request's data stream"

          To fix this, we copy the content the drf request into the WSGI request and in
          json_logs.py we use the content of the drf_request.data instead of the body.

          - request -> WSGI request
          - self.request -> RestFramework Request
        """
        request.is_using_drf = True
        dispatch = super().dispatch(request, *args, **kwargs)
        request.drf_request = self.request
        return dispatch
