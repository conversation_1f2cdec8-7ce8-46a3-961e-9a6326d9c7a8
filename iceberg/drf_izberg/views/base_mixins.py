import logging

from django.core.exceptions import ValidationError
from django.db import IntegrityError
from django.utils.translation import gettext_lazy as _
from rest_framework import mixins, status
from rest_framework.response import Response

from .constants import FALSE_VALUES

logger = logging.getLogger(__name__)


class IzbergShouldReturnDataMixin:
    """
    Mixin that provide a method that check in the url parameters for return_data
    and return True or False based on this parameter
    - Used only for post/put/patch.
    - By default, will always return data.
    """

    def should_return_data(self, request):
        should_return_data = True
        if request:
            return_data = request.GET.get("return_data", True) in FALSE_VALUES
            if return_data:
                should_return_data = False
        return should_return_data


class IzbergCreateModelMixin(mixins.CreateModelMixin, IzbergShouldReturnDataMixin):
    def create(self, request, *args, **kwargs):
        """
        Tastypie does not follow the REST standard creation status code
        (HTTP_201_CREATED) for PATCH and PUT verbs but respond with an acknowledgment
        code (HTTP_202_ACCEPTED) which is semantically false.
        But, hey, compatibility, so we have to override.
        """
        response = super().create(request, *args, **kwargs)

        if request.method in ("patch", "put"):
            if not self.should_return_data(request):
                return Response(
                    status=status.HTTP_202_ACCEPTED, headers=response.headers
                )

            return Response(
                response.data, status=status.HTTP_202_ACCEPTED, headers=response.headers
            )
        else:
            if not self.should_return_data(request):
                return Response(
                    status=status.HTTP_201_CREATED, headers=response.headers
                )

            return response

    def perform_create(self, serializer):
        """
        We are overriding this DRF method in order to be compatible with the custom
        obj_create of Tastypie.
        Please take a look at :
        ims.api.resources.custom_auth_resource.MPCustomAuthResource.obj_create

        Since we are handling manually the IntegrityError, we need to do the same here.
        """

        try:
            super().perform_create(serializer)
        except IntegrityError:
            logger.exception("Cant save obj")
            raise ValidationError(_("Violation of a unique constraint"))


class IzbergUpdateModelMixin(mixins.UpdateModelMixin, IzbergShouldReturnDataMixin):
    def update(self, request, *args, **kwargs):
        """
        Tastypie does not follow the REST standard creation status code
        (HTTP_201_CREATED) for PATCH and PUT verbs but respond with an acknowledgment
        code (HTTP_202_ACCEPTED) which is semantically false.
        But, hey, compatibility, so we have to override.
        """
        response = super().update(request, *args, **kwargs)

        if not self.should_return_data(request):
            return Response(status=status.HTTP_202_ACCEPTED, headers=response.headers)

        return Response(
            response.data, status=status.HTTP_202_ACCEPTED, headers=response.headers
        )


class IzbergDestroyModelMixin(mixins.DestroyModelMixin):
    """
    This mixin is a security against the default behavior of deleting an instance of a
    model.
    One should implement his own logic for perform_destroy, most likely using a
    soft delete via a transition using the status (like INACTIVE/DELETED ect).
    """

    def perform_destroy(self, instance):
        raise NotImplementedError
