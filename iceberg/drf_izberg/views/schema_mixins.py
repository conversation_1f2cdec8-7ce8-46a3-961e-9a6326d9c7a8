from drf_izberg.schema.generator import IzbergSchemaGenerator
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response


class IzbergSchemaMixin:
    """
    Tastypie has a specific schema format. DRF uses openapi.
    To have the same behavior we add a schema endpoint with no permissions except
    to be authenticated.
    To generate the schema, we use our own custom schema generator
    """

    @action(detail=False, permission_classes={IsAuthenticated})
    def schema(self, request):
        serializer_class = self.get_serializer_class()
        data = self.generate_schema_data(request, serializer_class)
        return Response(data)

    def generate_schema_data(self, request, serializer_class):
        """
        Generate the data from our schema generator.
        Can be overridden to add/alter the schema data before the Response
        """
        schema = IzbergSchemaGenerator(request, self, serializer_class)
        schema_data = schema.generate()
        return schema_data
