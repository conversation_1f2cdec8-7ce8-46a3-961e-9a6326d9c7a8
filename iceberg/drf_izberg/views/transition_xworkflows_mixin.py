from django.core.exceptions import ValidationError
from drf_izberg.jwt_authentication import extract_user_tracking_info
from xworkflows import AbortTransition


class TransitionXWorkflowsMixin:
    """
    Implements methods for XWorkflows transition and a helper method to verify if we
    can perform a given transition.
    """

    def perform_xworkflows_transition(self, instance, request, transition_name):
        """

        @param instance: Model instance
        @param request: DRF request
        @param transition_name: The transition name that has to be performed.
        @return: None

        Either the transition is successfully performed or an exception is raised.
        """
        transition = getattr(instance, transition_name)
        transition_kwargs = extract_user_tracking_info(request)

        try:
            transition(**transition_kwargs)
        except AbortTransition as err:
            raise ValidationError(str(err.args[0]))

    def can_perform_xworkflows_transition(
        self, instance, transition_name="delete_action", method_name=None
    ):
        """
        @param instance: Model instance
        @param transition_name: Name of the transition to delete on this instance
        @param method_name: Name of the method that would be used instead of
                            transition_name on this instance
        @return: A dict with a key "is_available" and the value True/False

        Check for a given instance model if the transition is available.
        Will return True if the transition doesn't implement the method is_available.
        Also, when using method_name, we assume that the method is implemented on the
        model instance. No checking is performed.
        Finally, if both transition_name and method_name is defined, only the latter
        will be used.

        """
        if method_name:
            is_available = getattr(instance, method_name)()
        else:
            transition = getattr(instance, transition_name)
            if hasattr(transition, "is_available"):
                is_available = transition.is_available()
            else:
                is_available = True
        return {"is_available": is_available}
