providers = [
    {"name": "per article", "method": "per_item"},
    {"name": "weight layers", "method": "banded_weight"},
    {"name": "price layers", "method": "banded_prices"},
]

shipping_assignments = {
    "per_item": {
        "status": "initial",
        "options": {
            "collection_within_hours": 8,
            "delivery_within_hours": 12,
            "price_per_item": 5,
        },
    },
    "banded_weight": {
        "status": "initial",
        "options": {
            "infinite": 40,
            "collection_within_hours": 12,
            "limits": [{"limit": 0.5, "value": 5}, {"limit": 2, "value": 10}],
            "delivery_within_hours": 48,
        },
    },
    "banded_prices": {
        "status": "initial",
        "options": {
            "infinite": 0,
            "collection_within_hours": 24,
            "limits": [
                {"limit": 100, "value": 15},
                {"limit": 500, "value": 10},
                {"limit": 700, "value": 5},
            ],
            "delivery_within_hours": 12,
        },
    },
}

carriers = [
    {
        "integrator": True,
        "application": None,
        "name": "Chronopost",
        "url": "http://www.chronopost.fr/fr/particulier/suivez-votre-colis",
        "tracking_page": "http://www.chronopost.fr/fr/particulier/suivez-votre-colis",
        "tracking_page_code": (
            "http://www.chronopost.fr/fr/chrono_suivi_search#<TRACKINGCODE>"
        ),
        "carrier_type": "home",
        "status": "active",
        "created_on": None,
        "last_modified": None,
    },
    {
        "integrator": True,
        "application": None,
        "name": "Chrono Relais",
        "url": (
            "http://www.chronopost.fr/transport-express/livraison-colis/ChronoRelais"
        ),
        "tracking_page": None,
        "tracking_page_code": None,
        "carrier_type": "home",
        "status": "active",
        "created_on": None,
        "last_modified": None,
    },
    {
        "integrator": True,
        "application": None,
        "name": "Colissimo",
        "url": "http://www.colissimo.fr/",
        "tracking_page": "http://www.colissimo.fr/portail_colissimo/suivre.do",
        "tracking_page_code": (
            "http://www.colissimo.fr/portail_colissimo/suivre.do?colispart="
            "<TRACKINGCODE>"
        ),
        "carrier_type": "home",
        "status": "active",
        "created_on": None,
        "last_modified": None,
    },
    {
        "integrator": True,
        "application": None,
        "name": "UPS",
        "url": "http://www.ups.com/",
        "tracking_page": "http://www.ups.com/tracking/tracking.html",
        "tracking_page_code": (
            "http://wwwapps.ups.com/WebTracking/track?"
            "loc=fr_FR&track.x=Track&trackNums=<TRACKINGCODE>"
        ),
        "carrier_type": "home",
        "status": "active",
        "created_on": None,
        "last_modified": None,
    },
    {
        "integrator": True,
        "application": None,
        "name": "FedEx",
        "url": "http://www.fedex.com/",
        "tracking_page": "https://www.fedex.com/fedextrack/",
        "tracking_page_code": (
            "https://www.fedex.com/fedextrack/"
            "?tracknumbers=<TRACKINGCODE>&locale=fr_FR&cntry_code=fr&language=french"
        ),
        "carrier_type": "home",
        "status": "active",
        "created_on": None,
        "last_modified": None,
    },
    {
        "integrator": True,
        "application": None,
        "name": "DHL",
        "url": "http://www.dhl.com/",
        "tracking_page": "http://webtrack.dhlglobalmail.com/",
        "tracking_page_code": (
            "http://webtrack.dhlglobalmail.com/" "?trackingnumber=<TRACKINGCODE>"
        ),
        "carrier_type": "home",
        "status": "active",
        "created_on": None,
        "last_modified": None,
    },
    {
        "integrator": True,
        "application": None,
        "name": "USPS",
        "url": "https://www.usps.com/",
        "tracking_page": "https://tools.usps.com/go/TrackConfirmAction!input.action",
        "tracking_page_code": (
            "https://tools.usps.com/go/TrackConfirmAction.action"
            "?tRef=fullpage&tLc=1&tLabels=<TRACKINGCODE>"
        ),
        "carrier_type": "home",
        "status": "active",
        "created_on": None,
        "last_modified": None,
    },
    {
        "integrator": True,
        "application": None,
        "name": "TNT",
        "url": "http://www.tnt.com/",
        "tracking_page": "http://www.tnt.com/webtracker/tracker.do",
        "tracking_page_code": (
            "http://www.tnt.com/webtracker/tracker.do"
            "?cons=<TRACKINGCODE>&trackType=CON&saveCons=N"
        ),
        "carrier_type": "home",
        "status": "active",
        "created_on": None,
        "last_modified": None,
    },
    {
        "integrator": True,
        "application": None,
        "name": "Parcelforce",
        "url": "http://www.parcelforce.com/",
        "tracking_page": "http://www.parcelforce.com/track-trace",
        "tracking_page_code": (
            "http://www.parcelforce.com/track-trace?trackNumber=<TRACKINGCODE>"
        ),
        "carrier_type": "home",
        "status": "active",
        "created_on": None,
        "last_modified": None,
    },
    {
        "integrator": True,
        "application": None,
        "name": "EXAPAQ",
        "url": "http://www.exapaq.com/",
        "tracking_page": "http://e-trace.ils-consult.fr/exa-webtrace/webtrace.aspx",
        "tracking_page_code": (
            "http://www.parcelforce.com/track-trace?trackNumber=<TRACKINGCODE>"
        ),
        "carrier_type": "home",
        "status": "active",
        "created_on": None,
        "last_modified": None,
    },
    {
        "integrator": True,
        "application": None,
        "name": "GLS",
        "url": "https://gls-group.eu/FR/fr/home",
        "tracking_page": "https://gls-group.eu/FR/fr/suivi-colis",
        "tracking_page_code": (
            "https://gls-group.eu/FR/fr/suivi-colis?match=<TRACKINGCODE>"
        ),
        "carrier_type": "home",
        "status": "active",
        "created_on": None,
        "last_modified": None,
    },
    {
        "integrator": True,
        "application": None,
        "name": "La Poste (courrier suivi)",
        "url": "http://www.csuivi.courrier.laposte.fr/",
        "tracking_page": "http://www.csuivi.courrier.laposte.fr/",
        "tracking_page_code": (
            "http://www.csuivi.courrier.laposte.fr/suivi/index?id=<TRACKINGCODE>"
        ),
        "carrier_type": "home",
        "status": "active",
        "created_on": None,
        "last_modified": None,
    },
]
