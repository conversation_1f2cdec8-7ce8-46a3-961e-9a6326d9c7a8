# -*- coding: utf-8 -*-

import datetime
import json
import logging
import os
import random
import time
import uuid
from collections import defaultdict

import boto3
from apps.address.tests import AddressTestSetupMixin
from apps.attributes.models import (
    MerchantAttribute,
    MerchantAttributeValueChoice,
    ProductAttribute,
    ProductAttributeGroup,
    ProductAttributeGroupAssignment,
    ProductAttributeValueChoice,
)
from apps.cart.tests import CartTestSetupMixin
from apps.categories.models import AppCategoryAssignment, ApplicationCategory
from apps.deprecation.models import DeprecatedFeature
from apps.ice_applications.app_conf_settings import (
    APIAlwaysReturnData,
    AsynchMerchantOrderConfirmation,
    AsynchOrderCreation,
    AsynchPaymentAuthorize,
    AutoCollectOnConfirm,
    AutoGenerateInvoice,
    MerchantHandleInvoiceUpload,
    OrderWorkflowV2,
    PartialCollectEnabled,
)
from apps.ice_applications.models import Application
from apps.ice_applications.tests import ApplicationTestSetupMixin
from apps.kyc.models import <PERSON>yc<PERSON>ag, <PERSON>yc<PERSON>ype, KycTypeWorkflow
from apps.orders.models import MerchantOrder, Order, OrderItem
from apps.orders.models.workflows.configuration import IZBERG_V1, IZBERG_V2
from apps.payment.models import Payment, PaymentTerm
from apps.products.models import Product, ProductOffer, ProductVariation
from apps.products.tests import BrandTestSetupMixin, ProductSetupMixin
from apps.reviews.models import MerchantReview
from apps.stores.tests import TestMerchantSetupMixin
from apps.tax.models import (
    TaxRate,
    TaxRateAssignment,
    TaxRateWorkflow,
    TaxZone,
    TaxZoneWorkflow,
)
from apps.testing.factories import (
    ApplicationCompanyAddressFactory,
    ApplicationCompanyFactory,
    ApplicationCompanyImageFactory,
    CountryFactory,
    MerchantAddressFactory,
)
from commissions.models import ItemCommissionRule
from django.contrib.auth import get_user_model
from django.contrib.contenttypes.models import ContentType
from django.core.exceptions import ValidationError
from django.core.files.base import ContentFile
from django.utils import timezone
from env_setup.data.attributes import attributes, attributes_choices, attributes_types
from env_setup.data.brands import brands
from env_setup.data.shipping import carriers, providers, shipping_assignments
from faker import Faker
from invoicing.models import CustomerInvoice
from mapper.api.configuration_resource import ConfigurationUploadResource
from mapper.loaders import METHOD_FILE
from mapper.models import ConfigurationUpload
from mapper.models.configuration_models import Configuration
from merchant_groups.tests import MerchantGroupSetupMixin
from payment_backends.psp_gateway.models import PspGateway
from reference import status
from zone.models import Zone

logger = logging.getLogger(__name__)


class OrdersGenerator:
    """
    Uses same code as CreateOrderFromCart. Need refactoring to call
    directly same code. Currently not possible because of transactions context
    """

    def generate(self, carts, random_workflow=False):
        for cart in carts:
            if random_workflow:
                cart.application.set_setting(
                    OrderWorkflowV2, bool(random.randint(0, 1))
                )
            order = self._create_order_from_cart(cart)
            self._create_order_shipping_choices(cart, order)
            self._review_merchant_from_order(order)

    def _group_cart_items(self, cart):
        merchant_id_to_cart_items = defaultdict(list)
        for ci in cart.cartitems.all():
            merchant_id_to_cart_items[ci.merchant.id].append(ci)
        return merchant_id_to_cart_items

    def _create_order_items(self, cart, cart_items, merchant_order):
        for ci in cart_items:
            for quantity_index in range(ci.quantity):
                OrderItem.objects.create_from_cart_item(ci, merchant_order, 1)

    def _create_merchant_orders(self, cart, order):
        grouped_cart_items = self._group_cart_items(cart).items()
        for merchant_id, cart_items in grouped_cart_items:
            mo = MerchantOrder.objects.create(
                merchant_id=merchant_id,
                order=order,
                application=order.application,
                debug=order.debug,
            )
            self._create_order_items(cart, cart_items, mo)

    def _create_order_from_cart(self, cart):
        use_v2 = cart.application.get_setting(OrderWorkflowV2)
        order = Order.objects.create_from_cart(
            cart,
            None,
            IZBERG_V2 if use_v2 else IZBERG_V1,
        )
        self._create_merchant_orders(cart, order)
        order.update_amounts(save=True)
        self._pay_order(cart, order)
        return order

    def _review_merchant_from_order(self, order):
        for mo in order.merchant_orders.all():
            try:
                MerchantReview.objects.create(
                    merchant_order=mo,
                    score=random.randint(1, 5),
                    title=Faker().sentence(),
                    body=Faker().paragraph(),
                    display_name=(Faker().first_name() + " " + Faker().last_name()),
                    status=MerchantReview.APPROVED,
                )
            except ValidationError:
                pass

    def _pay_order(self, cart, order):
        payment_backend_name = cart.selected_payment_backend
        if cart.selected_payment_type == cart.PREPAYMENT:
            self._create_payment(cart, order)
        if payment_backend_name == "psp_gateway":
            self._pay_through_psp_gateway(cart, order)
        else:
            self._pay_through_payment_object(order)

    def _create_payment(self, cart, order):
        payment = Payment.objects.create(
            order=order,
            currency=order.currency,
            application=order.application,
            payment_backend=cart.selected_payment_backend,
            payment_method=cart.selected_payment_method,
        )
        order.first_payment_id = payment.id
        order.save(update_fields=["first_payment_id"])

    def _pay_through_payment_object(self, order):
        order.payment.authorize()

    def _pay_gateway(self, psp_gateway):
        # refresh object and all related
        psp_gateway = psp_gateway.__class__.objects.get(id=psp_gateway.id)
        psp_gateway.actions.pay()

    def _pay_through_psp_gateway(self, cart, order):
        for mo in order.merchant_orders.all():
            gateway_id = "{}-{}".format(order.id_number, mo.id)
            if cart.selected_payment_type == cart.PREPAYMENT:
                psp_gateway = PspGateway.objects.create(
                    application=order.application,
                    external_id=gateway_id,
                    gateway_type=PspGateway.PREPAYMENT_TYPE,
                    merchant_order=mo,
                )
                self._pay_gateway(psp_gateway)
            else:
                if order.status == "0":
                    order.actions.paymentless_authorization()
                mo = mo.actions.confirm()
                for oi in mo.order_items.all():
                    oi.invoiceable_quantity = oi.max_invoiceable
                    oi.save(update_fields=["invoiceable_quantity"])

                invoice = self._create_invoice_for_merchant_order(mo)
                invoice.emit()

                psp_gateway = PspGateway.objects.create(
                    application=order.application,
                    external_id=gateway_id,
                    gateway_type=PspGateway.TERM_PAYMENT_TYPE,
                    invoice=invoice,
                )
                self._pay_gateway(psp_gateway)
                for trans in invoice.transactions.filter_commission():
                    psp_gateway = PspGateway.objects.create(
                        application=order.application,
                        external_id=gateway_id + "-{}".format(trans.id),
                        gateway_type=PspGateway.COMMISSION_TYPE,
                        transaction=trans,
                    )

    def _create_order_shipping_choices(self, cart, order):
        cart.actions.shipping.create_order_choices(order)

    def _create_invoice_for_merchant_order(self, merchant_order):
        customer_address = (
            merchant_order.order.billing_address
            or merchant_order.order.shipping_address
        )
        if merchant_order.merchant.billing_address:
            issuer_billing_address = merchant_order.merchant.billing_address.to_dict()
        else:
            issuer_billing_address = None
        invoice = CustomerInvoice.objects.create(
            merchant_order=merchant_order,
            application=merchant_order.application,
            issuer=merchant_order.merchant,
            receiver=merchant_order.user,
            receiver_billing_address=customer_address.to_dict(),
            receiver_phone_number=customer_address.all_phone_numbers_to_text(),
            issuer_billing_address=issuer_billing_address,
            payment_status=CustomerInvoice.NOT_PAID,
        )

        for order_item in merchant_order.order_items.exclude_cancelled_deleted():
            invoice.lines.create(
                order_item=order_item,
                unit_price=order_item.price,
                quantity=order_item.quantity,
                price=order_item.price * order_item.quantity,
                sku=order_item.sku,
                name=order_item.name,
                tax_rate=order_item.tax_rate,
            )
            order_item.invoiceable_quantity = order_item.quantity
            order_item.invoiced_quantity = 0
            order_item.save(update_fields=["invoiceable_quantity", "invoiced_quantity"])

        invoice.id_number = "TEST-" + str(invoice.id)
        invoice.invoice_url = "test_file_" + str(invoice.id) + ".pdf"
        return invoice


class BrandGenerator:
    def __init__(self):
        self._helper = BrandTestSetupMixin()

    def generate(self, application):
        created_brands = []
        for name in brands:
            brand = self._helper.create_brand(
                name, application=application, force_create=False
            )
            created_brands.append(brand)
        return created_brands


class ProductsGenerators:
    def __init__(self):
        self._brands = BrandGenerator()
        self._helper = ProductSetupMixin()

    def generate(self, application, merchants, products_count):
        created_brands = self._brands.generate(application)
        products = []
        for merchant in merchants:
            nb_products_to_generate = random.randint(2, products_count)
            for _ in range(nb_products_to_generate):
                products.append(
                    self._helper.create_product(
                        application=application,
                        merchant_admin=merchant,
                        brand=random.choice(created_brands),
                        language="en",
                    )
                )
        return products


class ProductAttributeGroupGenerator:
    def generate(self, application):
        groups = []
        types = {
            "product": Product,
            "productoffer": ProductOffer,
            "productvariation": ProductVariation,
        }
        for t in types.keys():
            group, created = ProductAttributeGroup.objects.get_or_create(
                application=application,
                group_key="%s:all" % t,
                entity_type=ContentType.objects.get_for_model(types[t]),
            )
            if created:
                group.save()
            groups.append(group)


class ProductAttributeGenerator:
    def generate(self, merchants, attributes_count):
        p_attrs = []
        for index in range(attributes_count):
            key = attributes[index]
            merchant = random.choice(merchants)
            attr, created = ProductAttribute.objects.get_or_create(
                application=merchant.application,
                merchant=merchant,
                value_type=attributes_types[key],
                key="%s_%s_%s" % (key, index, merchant.pk),
            )
            if created:
                attr.save()
            p_attrs.append(attr)
            self.generate_choices(attr, attributes_choices.get(key, []))
        self.generate_assignements(p_attrs, "product")
        self.generate_assignements(p_attrs, "productoffer")
        return p_attrs

    def generate_choices(self, attribute, values):
        choices = []
        for value in values:
            choice, created = ProductAttributeValueChoice.objects.get_or_create(
                value_raw=value, attribute=attribute, application=attribute.application
            )
            if created:
                choice.save()
            choices.append(choice)
        return choices

    def generate_assignements(self, attributes, entity_type):
        assignments = []
        for attribute in attributes:
            assignment, created = ProductAttributeGroupAssignment.objects.get_or_create(
                attribute=attribute,
                group=ProductAttributeGroup.objects.filter(
                    application=attribute.application, group_key="%s:all" % entity_type
                ).first(),
            )
            if created:
                assignment.save()
            assignments.append(assignment)
        return assignments


class MerchantAttributeGenerator:
    def generate(self, merchants, attributes_count):
        p_attrs = []
        for index in range(attributes_count):
            key = attributes[index]
            merchant = random.choice(merchants)
            attr, created = MerchantAttribute.objects.get_or_create(
                application=merchant.application,
                merchant=merchant,
                key="%s_%s_%s" % (key, index, merchant.pk),
            )
            if created:
                attr.save()
            p_attrs.append(attr)
            self.generate_choices(attr, attributes_choices.get(key, []))
        return p_attrs

    def generate_choices(self, attribute, values):
        choices = []
        for value in values:
            choices.append(
                MerchantAttributeValueChoice.objects.create(
                    value_raw=value,
                    attribute=attribute,
                    application=attribute.application,
                )
            )
        return choices


class ProductOffersGenerator:
    BASE_NAME = "Product offer test"
    PRICE_RANGE = range(10, 500)

    def __init__(self):
        self._helper = ProductSetupMixin()

    def generate(self, application, merchants, products, offers_count=2):
        offers = []
        first_index = ProductOffer.objects.count() + 1
        index = 0
        for product in products:
            nb_offers_to_generate = random.randint(0, offers_count)
            for _ in range(nb_offers_to_generate):
                offers.append(
                    self._helper.create_product_offer(
                        product=product,
                        status=status.PRODUCT_OFFER_STATUS_ACTIVE,
                        offer_name="{}-{}".format(self.BASE_NAME, first_index + index),
                        merchant=product.merchant_admin,
                        sku="{}".format(first_index + index),
                        force_create=False,
                        price=random.choice(self.PRICE_RANGE),
                        language="en",
                    )
                )
                index += 1
        return offers


class OffersGenerator:
    def __init__(self):
        self._products = ProductOffersGenerator()

    def generate(self, application, merchants, products, offers_count=2):
        product_offers = self._products.generate(
            application, merchants, products, offers_count
        )
        return product_offers


class ShippingProvidersGenerator:
    def generate(self, application, countries=None):
        from shipping2.tests.utils import create_shipping_provider
        from zone.tests.utils import create_zone

        created_providers = []
        zones = []
        if not countries:
            zones.append(
                create_zone(
                    "country('FR')",
                    name="FR_testing_zone",
                    application=application,
                    check_zone_exists=True,
                )
            )
        else:
            for country in countries:
                zones.append(
                    create_zone(
                        f"country('{country}')",
                        name="{}_testing_zone".format(country.upper()),
                        application=application,
                        check_zone_exists=True,
                    )
                )

        for provider in providers:
            for zone in zones:
                created_provider = create_shipping_provider(
                    name=provider["name"],
                    method=provider["method"],
                    application=application,
                    zone=zone,
                    options={},
                    force=False,
                )
                created_providers.append(created_provider)

        return created_providers


class ShippingCarrierGenerator:
    def generate(self, application):
        from shipping2.tests.utils import create_shipping_carrier

        created_carriers = []
        for carrier in carriers:
            created_carriers.append(
                create_shipping_carrier(
                    integrator=carrier["integrator"],
                    application=carrier["application"],
                    name=carrier["name"],
                    url=carrier["url"],
                    tracking_page=carrier["tracking_page"],
                    tracking_page_code=carrier["tracking_page_code"],
                    carrier_type=carrier["carrier_type"],
                    status=carrier["status"],
                    force=False,
                )
            )
        return created_carriers


class ShippingProviderAssignmentGenerator:
    def __init__(self):
        self._providers = ShippingProvidersGenerator()
        self._carriers = ShippingCarrierGenerator()
        self._helper = TestMerchantSetupMixin()

    def generate(self, application, merchants, countries=None):
        self._carriers.generate(application)
        providers = self._providers.generate(application, countries)
        zones = Zone.objects.filter(application=application).all()
        for merchant in merchants:
            self._generate_merchant_assignments(application, merchant, providers, zones)

    def _generate_merchant_assignments(self, application, merchant, providers, zones):
        from shipping2.models import Carrier
        from shipping2.tests.utils import create_shipping_provider_assignment

        created_assignments = []
        for provider in providers:
            assignment = shipping_assignments.get(provider.method)

            if assignment is not None:
                for zone in zones:
                    created_assignments.append(
                        create_shipping_provider_assignment(
                            application=application,
                            provider=provider,
                            merchant=merchant,
                            status=assignment["status"],
                            options=assignment["options"],
                            carrier=Carrier.objects.first(),
                            force=False,
                            zone=zone,
                        )
                    )


class MerchantGenerator:
    BASE_NAME = "testing-merchant"

    def __init__(self):
        self._helper = TestMerchantSetupMixin()

    def generate(self, application, count, countries=None):
        from tasks.django_tasks_content import (
            create_merchant_address,
            create_merchant_bank_account,
        )

        get_default_offer_tax_rates = []
        zone_keys = set()
        tax_rate_assignments = TaxRateAssignment.objects.filter(
            tax_rate__application_id=application.id, tax_zone__status="active"
        )
        for tax_rate_assignment in tax_rate_assignments:
            if tax_rate_assignment.tax_zone.zone_key not in zone_keys:
                zone_keys.add(tax_rate_assignment.tax_zone.zone_key)

                get_default_offer_tax_rates.append(
                    {
                        "zone_key": tax_rate_assignment.tax_zone.zone_key,
                        "rate_key": tax_rate_assignment.tax_rate.rate_key,
                    }
                )

        merchants = []
        if countries:
            count = len(countries)
        for index in range(count):
            name = "app{}-{}-{}".format(application.pk, self.BASE_NAME, index + 1)
            merchant = self._helper.create_merchant(
                name=name,
                slug=name,
                application=application,
                auto_create_merchant_address=False,
                force_create=False,
                status=status.MERCHANT_STATUS_ACTIVE,
                language="en",
                languages=["en", "fr"],
            )

            merchant.tax_settings.enabled_tax_zones = list(zone_keys)
            merchant.tax_settings.shipping_tax_rates = get_default_offer_tax_rates
            merchant.tax_settings.default_offer_tax_rates = get_default_offer_tax_rates
            merchant.tax_settings.save()

            merchants.append(merchant)
            if not countries:
                create_merchant_address(merchant)
            else:
                self._create_merchant_address(merchant, countries[index])
            create_merchant_bank_account(merchant)
            self._create_user(merchant)
        return merchants

    def _create_merchant_address(self, merchant, country):
        from apps.cart.tests.test_cart_setup_mixin import CartTestSetupMixin as Helper

        _helper = Helper()
        country = CountryFactory(code=country.upper())
        address = MerchantAddressFactory.build(country=country)
        _helper.create_merchant_address(
            merchant=merchant,
            contact_email=address.contact_email,
            address=address.address,
            city=address.city,
            state=address.state,
            zipcode=address.zipcode,
            country_id=country.id,
            force_create=False,
            return_created=False,
        )

    def _create_user(self, merchant):
        self._helper.create_merchant_user(
            name=f"merchant_{merchant.id}_user",
            merchant=merchant,
        )


class MerchantGroupGenerator:
    BASE_NAME = "test-group"

    def __init__(self):
        self._helper = MerchantGroupSetupMixin()

    def generate(self, merchant, index=1):
        name = "app{}-{}-{}".format(merchant.application.pk, self.BASE_NAME, index)
        return self._helper.create_merchant_group(
            name=name,
            application=merchant.application,
            return_created=True,
            force_create=True,
        )


class CartGenerator:
    def __init__(self):
        self._helper = CartTestSetupMixin()

    def generate(
        self,
        application,
        user,
        offers,
        orders_count=1,
        payment_backend="external",
        possible_payment_types=None,
        payment_term=None,
    ):
        possible_payment_types = possible_payment_types or ["prepayment"]
        carts = []
        for _ in range(orders_count):
            payment_type = random.choice(possible_payment_types)
            if payment_type == "term_payment":
                selected_payment_term = payment_term
            else:
                selected_payment_term = None
            cart = self._helper.create_cart(
                user=user,
                application=application,
                payment_backend=payment_backend,
                payment_type=payment_type,
            )
            for offer in offers:
                self._helper.create_product_cart_item(
                    cart=cart,
                    product_offer=offer,
                    by_user=user,
                    selected_payment_term=selected_payment_term,
                )
            carts.append(cart)
        return carts


class MapperConfigurationGenerator:
    BASE_NAME = "Mapper Configuration"
    MAPPER_CONF_EXPORT = os.path.join(
        os.path.dirname(__file__), "data", "simple_mapp_conf.json"
    )
    MAPPER_FEED_FILE = os.path.join(
        os.path.dirname(__file__), "..", "fixtures", "basic_catalog.xml"
    )

    def _upload_mapper_configuration(
        self, mapper_conf, application_id, uploaded_by="42"
    ):
        """
        Simulate uploader by creating a ConfigurationUpload instance
        """
        upload = ConfigurationUploadResource._meta.object_class()
        upload.set_uploader_attributes(
            "mapper/conf/path.json",
            ContentFile(mapper_conf),
            uploaded_by,
            timezone.now(),
            application_id=application_id,
        )
        # bypass validation of ConfigurationUpload
        super(ConfigurationUpload, upload).save()

        return upload.id

    def generate(self, application, merchant):
        configuration = Configuration.objects.create(
            name=self.BASE_NAME,
            application=application,
            method=METHOD_FILE,
            options={"filepath": os.path.abspath(self.MAPPER_FEED_FILE)},
            language="fr",
            merchant=merchant,
        )
        with open(self.MAPPER_CONF_EXPORT, "rb") as fd:
            data = fd.read()
        admin_user = get_user_model().objects.filter(is_superuser=True).first()
        upload_id = self._upload_mapper_configuration(
            data, application.id, uploaded_by=admin_user.id
        )
        configuration.model_actions.import_from_upload_id(upload_id)
        configuration.save()
        return configuration


class ApplicationCategoryGenerator:
    BASE_NAME = "category-app%(app_id)s-d%(d)s-x%(x)s"

    def generate(self, application, products, number=3, depth=2):
        categories = self._create_categories(
            application=application, parent=None, number=number, depth=depth
        )
        self._create_assignments(application, products, categories)
        return categories

    def _create_categories(self, application, parent, number, depth):
        """
        Recursive creation of 'number' category on 'depth' recursion
        """
        parents = [parent] if parent else []
        categories = []
        for x in range(0, number):
            cat, _ = ApplicationCategory.objects.get_or_create(
                application=application,
                name=self.BASE_NAME % {"app_id": application.pk, "d": depth, "x": x},
            )
            cat.parents.set(parents)
            cat.save()
            categories.append(cat)
            if depth > 0:
                categories += self._create_categories(
                    application, cat, number, depth - 1
                )
        return categories

    def _create_assignments(self, application, products, categories):
        for product in products:
            AppCategoryAssignment.objects.get_or_create(
                application=application,
                product=product,
                application_category=random.choice(categories),
            )


class DeprecatedFeatureGenerator:
    def generate(self):
        DeprecatedFeature.objects.get_or_create(
            key=DeprecatedFeature.APPLICATION_NESTED_ROUTES,
            decommission_after=datetime.datetime(2123, 7, 25, 11, 00),
        )

        DeprecatedFeature.objects.get_or_create(
            key=DeprecatedFeature.SERVICE_LINKS,
            decommission_after=datetime.datetime(2123, 6, 20, 11, 00),
        )

        DeprecatedFeature.objects.get_or_create(
            key=DeprecatedFeature.CART_ADD_ITEM_SHORTCUT,
            decommission_after=datetime.datetime(2124, 6, 20, 11, 00),
        )


class ApplicationGenerator:
    BASE_NAME = "My marketplace of "
    BASE_NAMESPACE = "testing-app"

    def __init__(self):
        self._name_template = "{} - {}"
        self._namespace_template = "{}-{}-{}"
        self._offers_gen = OffersGenerator()
        self._merchant_gen = MerchantGenerator()
        self._merchant_group_gen = MerchantGroupGenerator()
        self._shipping_gen = ShippingProviderAssignmentGenerator()
        self._products_gen = ProductsGenerators()
        self._carts_gen = CartGenerator()
        self._orders_gen = OrdersGenerator()
        self._mapper_gen = MapperConfigurationGenerator()
        self._address_helper = AddressTestSetupMixin()
        self._cart_helper = CartTestSetupMixin()
        self._helper = ApplicationTestSetupMixin()
        self._merchants = []
        self._product_attribute_group_gen = ProductAttributeGroupGenerator()
        self._product_attributes_groups = []
        self._product_attribute_gen = ProductAttributeGenerator()
        self._product_attributes = []
        self._merchant_attribute = MerchantAttributeGenerator()
        self._merchant_attributes = []
        self._app_category_generator = ApplicationCategoryGenerator()
        self._deprecated_feature_gen = DeprecatedFeatureGenerator()

    def _set_payment_backend(self, application, payment_backend):
        application.payment_settings.payment_backend = payment_backend
        application.payment_settings.save()
        if payment_backend == "psp_gateway":
            payment_term = PaymentTerm.objects.create(
                name="My paÿment term",
                application=application,
            )
            payment_term.lines.create(number_of_days=14, term_type="net_eom")
            payment_term.activate()
        else:
            payment_term = None
        return payment_term

    def _set_payment_backend_new(self, application, payment_backend):
        application.payment_settings.payment_backend = payment_backend
        application.payment_settings.save()
        if payment_backend == "psp_gateway":
            payment_term = PaymentTerm.objects.create(
                name="My paÿment term",
                application=application,
            )
            term_payment_line_setting = self._get_payment_term_line_setting_from_json(
                application
            )
            if term_payment_line_setting:
                payment_term.lines.create(
                    number_of_days=term_payment_line_setting["number_of_days"],
                    term_type=term_payment_line_setting["term_type"],
                )
            else:
                payment_term.lines.create(number_of_days=14, term_type="net_eom")
            payment_term.activate()
        else:
            payment_term = None
        return payment_term

    def generate(
        self,
        applications=1,
        merchants=2,
        products_count=10,
        offers_count=2,
        orders_count=1,
        attributes_count=4,
        random_workflow=False,
        payment_backend="external",
        tax_zones_to_activate=None,
        countries=None,
        app_name=None,
    ):
        created_applications = None
        if app_name:
            created_applications = self.generate_new(
                app_name=app_name,
                merchants=merchants,
                products_count=products_count,
                offers_count=offers_count,
                orders_count=orders_count,
                attributes_count=attributes_count,
                random_workflow=random_workflow,
                payment_backend=payment_backend,
                tax_zones_to_activate=tax_zones_to_activate,
                countries=countries,
            )
        else:
            created_applications = self.generate_old(
                applications=applications,
                merchants=merchants,
                products_count=products_count,
                offers_count=offers_count,
                orders_count=orders_count,
                attributes_count=attributes_count,
                random_workflow=random_workflow,
                payment_backend=payment_backend,
            )
        return created_applications

    def generate_old(
        self,
        applications=1,
        merchants=2,
        products_count=10,
        offers_count=2,
        orders_count=1,
        attributes_count=4,
        random_workflow=False,
        payment_backend="external",
    ):
        created_applications = []
        nb_existing_app = Application.objects.count()
        for count in range(nb_existing_app + 1, nb_existing_app + applications + 1):
            application = self._create_application(count)
            self._setup_default_settings(application, payment_backend)
            self._create_default_commission_rule(application)
            payment_term = self._set_payment_backend(application, payment_backend)
            products_count = self._create_merchants(
                application, merchants, products_count
            )
            self._product_attributes_groups = (
                self._product_attribute_group_gen.generate(application)
            )
            self._product_attributes = self._product_attribute_gen.generate(
                self._merchants, attributes_count
            )
            self._merchant_attributes = self._merchant_attribute.generate(
                self._merchants, attributes_count
            )

            self._shipping_gen.generate(application, self._merchants)
            products = self._products_gen.generate(
                application, self._merchants, products_count
            )
            offers = self._offers_gen.generate(
                application, self._merchants, products, offers_count
            )
            user = self._create_user(application, count)
            self._create_company(application)
            if payment_backend == "psp_gateway":
                possible_payment_types = ["term_payment", "prepayment"]
            else:
                possible_payment_types = ["prepayment"]
            carts = self._carts_gen.generate(
                application,
                user,
                offers,
                orders_count,
                payment_backend=payment_backend,
                possible_payment_types=possible_payment_types,
                payment_term=payment_term,
            )
            self._orders_gen.generate(carts, random_workflow)
            application.actions.initialize_default_tax_data(dry_run=False)
            created_applications.append(application)
            conf = self._mapper_gen.generate(application, self._merchants[0])
            conf.run_import(async_task=False, skip_not_modified=False, gateway="live")
            TaxZone.objects.all().update(status=TaxZoneWorkflow.ACTIVE)
            TaxRate.objects.all().update(status=TaxRateWorkflow.ACTIVE)
            try:
                self._setup_domain_and_bo_access(application)
            except Exception:
                logger.exception("error while creating domain and BOv3 access: ")
        return created_applications

    def generate_new(
        self,
        app_name,
        merchants=2,
        products_count=4,
        offers_count=2,
        orders_count=1,
        attributes_count=4,
        random_workflow=False,
        payment_backend="external",
        tax_zones_to_activate=None,
        countries=None,
    ):
        application = self._create_application_new(app_name)
        self._setup_default_settings_from_json(application)
        self._setup_default_kyc_types(application)
        self._create_default_commission_rule(application)

        # must be done as soon as possible otherwise we cannot generate cart with
        # tax handler V2
        application.actions.initialize_default_tax_data(
            dry_run=False, tax_zones_to_activate=tax_zones_to_activate
        )

        if payment_backend == "hipay_tpp":
            from tasks.django_tasks_content import create_hipay_izberg_config

            create_hipay_izberg_config(application)

        payment_term = self._set_payment_backend_new(application, payment_backend)
        self._create_merchants(application, merchants, products_count, countries)
        self._product_attributes_groups = self._product_attribute_group_gen.generate(
            application
        )
        self._product_attributes = self._product_attribute_gen.generate(
            self._merchants, attributes_count
        )
        self._merchant_attributes = self._merchant_attribute.generate(
            self._merchants, attributes_count
        )

        self._shipping_gen.generate(application, self._merchants, countries)
        products = self._products_gen.generate(
            application, self._merchants, products_count
        )
        self._app_category_generator.generate(application, products)
        offers = self._offers_gen.generate(
            application, self._merchants, products, offers_count
        )
        user = self._create_user(application, app_name)
        self._create_company(application)
        if payment_backend == "psp_gateway":
            possible_payment_types = ["term_payment", "prepayment"]
        else:
            possible_payment_types = ["prepayment"]
        carts = self._carts_gen.generate(
            application,
            user,
            offers,
            orders_count,
            payment_backend=payment_backend,
            possible_payment_types=possible_payment_types,
            payment_term=payment_term,
        )
        self._orders_gen.generate(carts, random_workflow)

        self._create_customers_per_zone(application)
        self._deprecated_feature_gen.generate()
        conf = self._mapper_gen.generate(application, self._merchants[0])
        conf.run_import(async_task=False, skip_not_modified=False, gateway="live")
        try:
            self._setup_domain_and_bo_access(application, domain_name=application.name)
        except Exception:
            logger.exception("error while creating domain and BOv3 access: ")
        # we return a list in order to be compatible with the calling method and the
        # old generate_old method
        return [application]

    def _get_default_operators(self):
        """read default operator list from secretsmanager"""
        secretsmanager = boto3.session.Session().client("secretsmanager")
        return json.loads(
            secretsmanager.get_secret_value(SecretId="CUSTOM_ENV_OPERATORS")[
                "SecretString"
            ]
        )

    def _setup_domain_and_bo_access(self, application, domain_name=None):
        """
        - create domain for application
        - create operator users
        """
        domain_created = application.actions.create_domain(domain_name=domain_name)
        if domain_created:
            for operator_dict in self._get_default_operators():
                application.actions.create_identity_operator(**operator_dict)

    def _create_company(self, application):
        application.company = ApplicationCompanyFactory(
            application=application, name=application.name + " company"
        )
        application.save()
        ApplicationCompanyAddressFactory(
            country=application.country, company=application.company
        )
        ApplicationCompanyImageFactory(
            company=application.company,
            uploaded_by=application.contact_user,
        )

    @property
    def merchant(self):
        return self._merchants[0]

    def _create_merchants(self, application, merchants, products_count, countries=None):
        self._merchants = self._merchant_gen.generate(application, merchants, countries)
        # create one merchant group
        self._merchant_group_gen.generate(self._merchants[0])
        return products_count

    def _create_application(self, count):
        from apps.user.tests.user_tests_setup_mixin import UserTestsSetupMixin

        staff_user = UserTestsSetupMixin().create_izberg_user(
            username="admin-user-{}".format(count),
            staff=True,
            email="admin-{}@example.com".format(count),
            force_create=False,
        )
        name = self._name_template.format(self.BASE_NAME, count)
        namespace = self._namespace_template.format(
            self.BASE_NAMESPACE, count, int(time.time())
        )
        app = self._helper.create_application(
            name=name,
            admin=staff_user,
            force_create=False,
            namespace=namespace,
            secret_key=namespace,
            languages=["en", "fr"],
        )
        operator = UserTestsSetupMixin().create_izberg_user(
            username=f"app-{app.id}-operator-user",
            email=f"operator-{app.id}@example.com",
            force_create=False,
            application=app,
        )
        app.create_user(operator, True)
        return app

    def _create_application_new(self, name):
        from apps.user.tests.user_tests_setup_mixin import UserTestsSetupMixin

        staff_user = UserTestsSetupMixin().create_izberg_user(
            username="admin-user-{}".format(name),
            staff=True,
            email="admin-{}@example.com".format(name),
            force_create=False,
        )

        namespace = "{}-{}".format(name, int(time.time()))

        app = self._helper.create_application(
            name=name,
            admin=staff_user,
            force_create=False,
            namespace=namespace,
            secret_key=namespace,
            language="en",
            languages=["en", "fr"],
        )
        operator = UserTestsSetupMixin().create_izberg_user(
            username=f"app-{app.id}-operator-user",
            email=f"operator-{app.id}@example.com",
            force_create=False,
        )
        app.create_user(operator, True)
        return app

    def _create_user(self, application, count):
        helper = AddressTestSetupMixin()
        user = self._helper.create_izberg_user(
            application=application,
            username="application-user-{}".format(count),
            email="user-{}@test.net".format(count),
            password="password",
            force_create=False,
        )
        helper.create_user_address(user, application=application, force_create=False)
        self._helper.create_access(application, user)
        return user

    def _create_customers_per_zone(self, application):
        address_helper = AddressTestSetupMixin()
        zones = TaxZone.objects.filter(
            status=TaxZoneWorkflow.ACTIVE, application=application
        ).exclude(zone_key="www")
        for index, zone in enumerate(zones):
            user = self._helper.create_izberg_user(
                application=application,
                username="customer-user-{}".format(index),
                last_name="for zone {}".format(zone.country.code),
                email="customer-user-{}@test.net".format(index),
                password="password",
                force_create=True,
            )
            address_helper.create_user_address(
                user, application=application, country_or_id=zone.country
            )

    def _create_default_commission_rule(self, application):
        return ItemCommissionRule.objects.create(
            application=application,
            price_currency=application.default_currency,
            commission_method="rate",
            commission_rate_value=25,
            commission_rate_price_base="product_tax_included",
            external_id=str(uuid.uuid4())[:8],
            name="test name {}".format(str(uuid.uuid4())[:8]),
            description="test description {}".format(str(uuid.uuid4())[:8]),
        )

    def _setup_default_settings(self, application, payment_backend):
        application.set_setting(APIAlwaysReturnData, True)
        application.set_setting(PartialCollectEnabled, True)
        application.set_setting(AsynchMerchantOrderConfirmation, False)
        application.set_setting(AsynchPaymentAuthorize, False)
        application.set_setting(AsynchOrderCreation, False)
        if payment_backend == "psp_gateway":
            application.set_setting(AutoCollectOnConfirm, False)
            application.set_setting(AutoGenerateInvoice, False)
            application.set_setting(MerchantHandleInvoiceUpload, True)

    def _setup_default_settings_from_json(self, application):
        app_settings = self._get_default_settings_from_json(application)
        if app_settings:
            settings = app_settings["settings"]
            for setting in settings:
                application.set_setting(setting["name"], setting["value"])

    def _get_default_settings_from_json(self, application):
        import json
        import os

        module_dir = os.path.dirname(__file__)  # get current directory
        file_path = os.path.join(module_dir, "data", "app_settings.json")

        with open(file_path) as json_file:
            data = json.load(json_file)
            app = next(
                (
                    app
                    for app in data["applications"]
                    if app["name"] == application.name
                ),
                None,
            )
            return app

    def _get_payment_term_line_setting_from_json(self, application):
        import json
        import os

        module_dir = os.path.dirname(__file__)  # get current directory
        file_path = os.path.join(module_dir, "data", "app_settings.json")

        with open(file_path) as json_file:
            data = json.load(json_file)
            term_payment_line_setting = {}
            for app in data["applications"]:
                if (
                    app["name"] == application.name
                    and "term_payment_line_setting" in app
                ):
                    term_payment_line_setting = app["term_payment_line_setting"]
                    break

            return term_payment_line_setting

    def _get_default_kyc_types_from_json(self):
        import json
        import os

        module_dir = os.path.dirname(__file__)  # get current directory
        file_path = os.path.join(module_dir, "data", "kyc.json")

        with open(file_path) as json_file:
            data = json.load(json_file)
            return data["kyc_types"]

    def _setup_default_kyc_types(self, application):
        kyc_types = self._get_default_kyc_types_from_json()
        for kyc_type in kyc_types:
            kyc_type_instance = KycType.objects.create(
                name=kyc_type["name"],
                external_id=kyc_type["external_id"],
                status=KycTypeWorkflow.ACTIVE,
                application=application,
            )
            for tag in kyc_type["tags"]:
                KycTag.objects.create(
                    kyc_type=kyc_type_instance,
                    name=tag["name"],
                    help_text=tag["help_text"],
                    external_id=tag["external_id"],
                )
