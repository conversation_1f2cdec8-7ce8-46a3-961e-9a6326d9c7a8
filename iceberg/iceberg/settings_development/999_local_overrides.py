# -*- coding: utf-8 -*-
"""
Local development overrides to ensure no AWS services are used
This file is loaded last in development settings to override any AWS configurations
"""
import os

# Only apply these overrides if we're in local development mode
if os.environ.get('CUSTOM_ENV') == 'local' or os.environ.get('DISABLE_AWS_SERVICES') == 'true':
    
    # ===================
    # = FILE STORAGE =
    # ===================
    DEFAULT_FILE_STORAGE = "django.core.files.storage.FileSystemStorage"
    MEDIA_ROOT = os.path.join(BASE_DIR, "media")  # noqa
    MEDIA_URL = "/media/"
    
    # Remove all S3 configurations
    AWS_STORAGE_BUCKET_NAME = None
    AWS_S3_CUSTOM_DOMAIN = None
    AWS_GLACIER_STORAGE_BUCKET_NAME = None
    AWS_S3_UPLOADER_BUCKET_NAME = None
    AWS_QUERYSTRING_AUTH = False
    
    # ===================
    # = QUEUE SYSTEM =
    # ===================
    USE_SQS_QUEUE = False
    CELERY_BROKER_TRANSPORT = "memory"
    CELERY_BROKER_URL = "memory://"
    
    # ===================
    # = DYNAMODB CONFIG =
    # ===================
    MAPPER_DYNAMODB_HOST = os.environ.get("DYNAMODB_ENDPOINT", "http://dynamodb:8000")
    MAPPER_DYNDB_TABLE_NAME_PREFIX = "local-dev-mapper"
    MAPPER_ITEM_SPLIT_HANDLER = "local"
    MAPPER_ASYNC_ANALYSIS = False
    MAPPER_LOCAL_VALUE_ANALYSIS = True
    
    # ===================
    # = EMAIL BACKEND =
    # ===================
    EMAIL_BACKEND = "django.core.mail.backends.console.EmailBackend"
    
    # ===================
    # = SEARCH SERVICES =
    # ===================
    AUTO_ACTIVATE_ALGOLIA_OUTPUT_OF_MAIN_CHANNELS = False
    AUTO_CREATE_ALGOLIA_OUTPUT_OF_MAIN_CHANNELS = False
    AUTO_ACTIVATE_ELASTICSEARCH_OUTPUT_OF_MAIN_CHANNELS = False
    AUTO_CREATE_ELASTICSEARCH_OUTPUT_OF_MAIN_CHANNELS = False
    
    # ===================
    # = EXTERNAL APIS =
    # ===================
    WEBHOOK_ASYNC = False
    DISABLE_EXTERNAL_APIS = True
    
    # ===================
    # = BUCKET NAMES =
    # ===================
    ARCHIVING_BUCKET_NAME = None
    REJECTED_PRODUCTS_REPORT_BUCKET_NAME = None
    STATS_BUCKET_NAME = None
    
    # ===================
    # = LOGGING =
    # ===================
    # Override logging to be more verbose for local development
    LOGGING = {
        'version': 1,
        'disable_existing_loggers': False,
        'formatters': {
            'verbose': {
                'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
                'style': '{',
            },
            'simple': {
                'format': '{levelname} {message}',
                'style': '{',
            },
        },
        'handlers': {
            'console': {
                'class': 'logging.StreamHandler',
                'formatter': 'simple',
            },
        },
        'root': {
            'handlers': ['console'],
            'level': 'INFO',
        },
        'loggers': {
            'django': {
                'handlers': ['console'],
                'level': 'INFO',
                'propagate': False,
            },
            'boto3': {
                'handlers': ['console'],
                'level': 'WARNING',  # Reduce boto3 noise
                'propagate': False,
            },
            'botocore': {
                'handlers': ['console'],
                'level': 'WARNING',  # Reduce botocore noise
                'propagate': False,
            },
            'urllib3': {
                'handlers': ['console'],
                'level': 'WARNING',  # Reduce urllib3 noise
                'propagate': False,
            },
        },
    }
    
    print("🔧 Local development overrides applied - AWS services disabled")
