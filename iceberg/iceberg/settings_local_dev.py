# -*- coding: utf-8 -*-
"""
Local development settings that override AWS services with local alternatives
Use this for completely offline development after initial Docker image pull
"""
import os

# Import base development settings
from iceberg.settings_development import *  # noqa

# ===================
# = LOCAL OVERRIDES =
# ===================

# Disable AWS services completely
USE_SQS_QUEUE = False
CELERY_BROKER_TRANSPORT = "memory"

# Local file storage instead of S3
DEFAULT_FILE_STORAGE = "django.core.files.storage.FileSystemStorage"
MEDIA_ROOT = os.path.join(BASE_DIR, "media")  # noqa
MEDIA_URL = "/media/"

# Remove S3 configurations
AWS_STORAGE_BUCKET_NAME = None
AWS_S3_CUSTOM_DOMAIN = None
AWS_GLACIER_STORAGE_BUCKET_NAME = None
AWS_S3_UPLOADER_BUCKET_NAME = None

# Local DynamoDB configuration
MAPPER_DYNAMODB_HOST = "http://dynamodb:8000"
MAPPER_DYNAMODB_REGION = "eu-west-1"
MAPPER_DYNDB_TABLE_NAME_PREFIX = "local-dev-mapper"

# Fake AWS credentials for local DynamoDB
if not os.environ.get("AWS_ACCESS_KEY_ID"):
    os.environ.update({
        "AWS_ACCESS_KEY_ID": "fake",
        "AWS_SECRET_ACCESS_KEY": "fake",
        "AWS_DEFAULT_REGION": "eu-west-1",
    })

# Local email backend (console output)
EMAIL_BACKEND = "django.core.mail.backends.console.EmailBackend"

# Disable external search services
AUTO_ACTIVATE_ALGOLIA_OUTPUT_OF_MAIN_CHANNELS = False
AUTO_CREATE_ALGOLIA_OUTPUT_OF_MAIN_CHANNELS = False
AUTO_ACTIVATE_ELASTICSEARCH_OUTPUT_OF_MAIN_CHANNELS = False
AUTO_CREATE_ELASTICSEARCH_OUTPUT_OF_MAIN_CHANNELS = False

# Local cache configuration
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://redis:6379/0',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}

# Local session configuration
SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
SESSION_CACHE_ALIAS = 'default'

# Disable external API calls
WEBHOOK_ASYNC = False

# Local development URLs
BASE_DOMAIN = "api.local.core.izbgtech.com:8080"
BASE_URL = f"http://{BASE_DOMAIN}"
IDENTITY_API_ENDPOINT = "http://api.local.izberg.me:8081"
DOMAIN_URI = "http://api.local.domains.izbgtech.com:8082"

# JWT configuration for local development
JWT_CONFIG_URI = f"{IDENTITY_API_ENDPOINT}/.well-known/openid-configuration"
JWT_AUDIENCE = BASE_URL
DOMAINS_API_ENDPOINT = DOMAIN_URI
DOMAINS_API_AUDIENCE = DOMAIN_URI

# Local development client credentials
IDENTITY_CLIENT_ID = "local-dev-client"
IDENTITY_CLIENT_SECRET = "local-dev-secret"

# Disable external integrations
DISABLE_EXTERNAL_APIS = True

# Local logging configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
        'file': {
            'class': 'logging.FileHandler',
            'filename': 'debug.log',
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
        'django.db.backends': {
            'handlers': ['console'],
            'level': 'WARNING',  # Set to DEBUG to see SQL queries
            'propagate': False,
        },
    },
}

# Local development security settings
SECRET_KEY = "local-development-secret-key-not-for-production"
ALLOWED_HOSTS = [
    "localhost",
    "127.0.0.1",
    "api.local.core.izbgtech.com",
    "api.local.izberg.me",
    "api.local.domains.izbgtech.com",
]

# Disable HTTPS requirements for local development
SECURE_SSL_REDIRECT = False
SECURE_PROXY_SSL_HEADER = None
USE_TLS = False

# Local development CORS settings
CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_CREDENTIALS = True

# Disable external service health checks
HEALTH_CHECK_EXTERNAL_SERVICES = False

# Local development feature flags
FEATURES = {
    'ENABLE_EXTERNAL_APIS': False,
    'ENABLE_ELASTICSEARCH': False,
    'ENABLE_ALGOLIA': False,
    'ENABLE_AWS_SERVICES': False,
    'ENABLE_EMAIL_SENDING': False,
    'ENABLE_WEBHOOKS': False,
}

# Override any remaining AWS-dependent settings
ARCHIVING_BUCKET_NAME = None
REJECTED_PRODUCTS_REPORT_BUCKET_NAME = None
STATS_BUCKET_NAME = None

print("🚀 Local development settings loaded - running completely offline!")
print(f"📁 Media files will be stored in: {MEDIA_ROOT}")
print(f"🗄️  Database: PostgreSQL at postgres:5432")
print(f"⚡ Cache: Redis at redis:6379")
print(f"📊 DynamoDB: Local at dynamodb:8000")
print(f"📧 Email: Console backend (no external sending)")
