# -*- coding: utf-8 -*-

from logging import getLogger
from urllib.request import urlparse

logger = getLogger(__name__)


def get_id_from_resource_uri(resource_uri):
    if resource_uri is None:
        return None, None
    parsed_uri = urlparse(str(resource_uri))
    parts = parsed_uri.path.strip("/").split("/")
    try:
        _, resource_name = parts[0:2]
    except (TypeError, ValueError):
        logger.warning(
            "given value %s cannot be splitted in at least two URI path parts.",
            resource_uri,
        )
        # if it can't unpack values, then return None, None tuple
        return None, None
    try:
        pk = int(parts[2])
    except (IndexError, ValueError):
        # if 3rd item doesn't exist or is not an integer, return pk=None
        logger.warning(
            "given URI value %s cannot be split in three parts. "
            "Returning None as resource id and %s as resource_name.",
            resource_uri,
            resource_name,
        )
        pk = None
    return resource_name, pk


def get_resource_id_from_field_value(resource_field_value):
    """
    Return resource ID from given field value (which can be either a dict with
    "pk" or "resource_uri" keys, or an integer, or even a resource URI.

    Return None if could not get id from given field value.

    :param resource_field_value: value from field.
    :return: int or None for resource ID
    """
    if resource_field_value is None:
        logger.warning("Given field value is None, returning id None.")
        return None
    # shrink absolute OR relative path in resource URI and only
    # keep variation ID.
    if isinstance(resource_field_value, dict):
        # get "id" or "pk" key values
        pk = resource_field_value.get("pk", resource_field_value.get("id", None))
        if pk is not None:
            try:
                return int(pk)
            except ValueError:
                logger.warning("pk or id value %s is not an integer", pk)
        # else use uri parsing if any
        uri = resource_field_value.get("resource_uri", None)
    elif isinstance(resource_field_value, int):
        return resource_field_value
    elif isinstance(resource_field_value, str):
        try:
            return int(resource_field_value)
        except ValueError:
            # it might be a uri; pass and let the next step parse it
            uri = resource_field_value
    else:
        uri = str(resource_field_value)
    _, pk = get_id_from_resource_uri(uri)

    return pk
