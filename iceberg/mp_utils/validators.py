# -*- coding: utf-8 -*-

import decimal

import django
import jsonschema
from django.core.validators import BaseValidator
from django.core.validators import MaxValueValidator as DjMaxValueValidator
from django.core.validators import MinValueValidator as DjMinValueValidator


def convert_to_decimal(value):
    value = str(value) if isinstance(value, float) else value
    if not isinstance(value, decimal.Decimal):
        value = decimal.Decimal(value)
    return value


class ValueValidatorBase:
    def __init__(self, limit_value, **kwargs):
        limit_value = convert_to_decimal(limit_value)
        super(ValueValidatorBase, self).__init__(limit_value, **kwargs)

    def clean(self, value):
        return convert_to_decimal(value)


class MinValueValidator(ValueValidatorBase, DjMinValueValidator):
    pass


class MaxValueValidator(ValueValidatorBase, DjMaxValueValidator):
    pass


class JSONSchemaValidator(BaseValidator):
    """
    JSON Validator for models.JSONField
    """

    def compare(self, value, schema):
        try:
            jsonschema.validate(value, schema)
        except jsonschema.exceptions.ValidationError:
            raise django.core.exceptions.ValidationError(
                "%(value)s failed JSON not valid", params={"value": value}
            )
