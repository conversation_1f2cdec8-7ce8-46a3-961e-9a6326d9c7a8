# -*- coding: utf-8 -*-


"""
mixin and signal hook to easily track updated fields of an object
(used for webhooks)
NB: as signals dispatch doesnt work with deferred models
(when using .defer()/.only()), value tracking logically
doesnt work with deferred models, meaning _values_at_init
and _updated_attributes will always be empty (but wont fail)
"""
import logging
from copy import copy

from django.db import models

logger = logging.getLogger(__name__)


class ValueTrackingMixin:
    """
    The mixin your model need to inherit from
    """

    def _field_names_to_track(self, *attributes):
        """
        The field names to track that is the concrete fields
        of the model. Can be overwritten to limit them.
        NB: doesn't work with M2M fields
        """
        fields = self._meta.local_concrete_fields
        if attributes:
            return [f.attname for f in fields if f.attname in attributes]
        return [f.attname for f in fields if f.attname in self.__dict__]

    def _store_values_at_init(self):
        """stores the value in self._values_at_init_dict"""
        self._values_at_init_dict = {}
        for field_name in self._field_names_to_track():
            try:
                value = getattr(self, field_name)
                if isinstance(value, (list, dict)):
                    value = copy(value)
                self._values_at_init_dict[field_name] = value
            except (AttributeError, models.ObjectDoesNotExist):
                pass

    @property
    def _values_at_init(self):
        # NB: fail silently (no ImproperlyConfigured)
        # because post_init hook is not called on
        # deferred models and we don't want it to fail
        # on deferred models
        return getattr(self, "_values_at_init_dict", {})

    def _get_initial_value(self, field_name, *args):
        try:
            return self._values_at_init[field_name]
        except KeyError:
            if len(args) == 1:
                # default value given, returning it
                return args[0]
            raise

    @property
    def _updated_attributes(self):
        """
        compares current values and original one and returns
        the set of the updated attribute
        """
        return self._check_updated_attributes()

    def _flush_updated_attributes(self):
        """
        flush _updated_attributes by actually
        recalling _store_values_at_init
        """
        self._store_values_at_init()

    def _check_updated_attributes(self, *attributes):
        updated_attributes = set()
        for field_name in self._field_names_to_track(*attributes):
            try:
                previous_value = self._values_at_init[field_name]
                current_value = getattr(self, field_name)
                if not previous_value == current_value:
                    updated_attributes.add(field_name)
            except (AttributeError, models.ObjectDoesNotExist, KeyError):
                pass
        return updated_attributes

    def _attribute_has_changed(self, attribute):
        return attribute in self._check_updated_attributes(attribute)


def value_tracking_instance_post_init_hook(sender, instance, **kwargs):
    """
    this hook needs to be connected to the post_init signal of the
    model you want the values to be tracked.
    > from apps.products.models import Product
    > from django.db.models.signals import post_init
    > post_init.connect(
        value_tracking_instance_post_init_hook,
        sender=Product)
    """
    instance._store_values_at_init()
