# Payment backends

Payment backends allowing different kind of PSP integration

## Key concepts

- The default payment backend of an application is declared in its `ApplicationPaymentSettings.payment_backend`. It can be overridden for a cart through the `Cart.selected_payment_backend`, and is stored for all payments in the `Payment.payment_backend` field.

- A payment backend can need additional configuration, for instance `HipayAppConfiguration`

- The payment backend is called on actions such as `authorize`, `collect` or `refund` and may block the authorization/confirmation/refund process

## Future work

- Deprecate native handling of Hipay in favor of an external connector using PSP Gateway ?
