# -*- coding: utf-8 -*-

from apps.payment.backends import HIPAYTPP
from django.utils.translation import gettext_lazy as _
from payment_backends.common.exceptions import NotManagedDocumentType
from reference.status import DOCUMENT_PENDING


class KycBackendMeta:
    """
    Helpers to get information such as document types and parts
    required for current merchant.
    """

    def __init__(self, merchant):
        from apps.kyc.models import MerchantIdentityDocument

        self.merchant = merchant
        self.all_doc_types = {
            doc_type: str(name)
            for doc_type, name in MerchantIdentityDocument.DOCUMENT_TYPE_CHOICES
        }

    def get_meta_for_type(self, document_type):
        """
        Given a document type, return required parts information.
        Warning: Only used with KYC v1. Deprecated in V2.
        :rtype document_type: int (cf. MerchantIdentityDocument.document_type)
        :rtype: dict
        Format: {
            tag_name: {
                name: str,
                help_text: str
            },
            ...
        }
        """
        if document_type not in self.all_doc_types:
            raise NotManagedDocumentType()
        type_name = self.all_doc_types[document_type]
        return {
            "file": {
                "name": type_name,
                "help_text": _("File of the {file}").format(file=type_name.lower()),
            }
        }

    def get_document_types_meta(self):
        """
        return all documents types and associated parts available for current
        merchant
        Warning: Only used with KYC v1. Deprecated in V2.
        :rtype: dict
        format: {
            doc_type: dict (format of get_meta_for_type)
            ...
        }
        """
        return {
            doc_type: self.get_meta_for_type(doc_type)
            for doc_type in self.all_doc_types.keys()
        }

    def get_required_document_types_meta(self):
        """
        return all documents types and associated parts required for current
        merchant
        Warning: Only used with KYC v1. Deprecated in V2.
        :rtype: dict
        format: {
            doc_type: dict (format of get_meta_for_type)
            ...
        }
        """
        return {
            doc_type: self.get_meta_for_type(doc_type)
            for doc_type in self.doc_types.keys()
        }


class BaseKycBackend:
    def __init__(self, merchant):
        self.merchant = merchant

    @property
    def meta(self):
        return KycBackendMeta(self.merchant)

    def is_document_acceptable(self, document):
        """
        Should return False if document cannot be uploaded on backend API

        :param document: MerchantIdentityDocument or KycInformation
        :rtype: bool, [str,None]
        """
        return True, None

    def can_publish(self, document):
        """
        Should return True or False depending if User account is in a good
        state for publishing the document.

        :param document: MerchantIdentityDocument
        :rtype: bool, [str,None]
        """
        if document.kyc_backend_status != DOCUMENT_PENDING:
            return False, "Non pending document"
        return True, None

    def publish(self, document):
        """
        One should call is_document_acceptable and can_publish before running
        this method. It should call the backend API to publish the document

        :param document: MerchantIdentityDocument
        :rtype: None
        """
        return


def get_kyc_backend(merchant):
    """
    Return the KYC backend for current merchant

    :param merchant: Merchant
    :rtype: BaseKycBackend
    """
    from payment_backends.hipay_tpp.kyc import build_backend_from_merchant

    application = merchant.application
    if application.payment_settings.payment_backend == HIPAYTPP:
        return build_backend_from_merchant(merchant)
    elif application.psp_migrations.to_hipay().kyc_started().exists():
        return build_backend_from_merchant(merchant)
    else:
        return BaseKycBackend(merchant)
