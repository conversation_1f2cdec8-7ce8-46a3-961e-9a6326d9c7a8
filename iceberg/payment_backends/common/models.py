# -*- coding: utf-8 -*-

from django.core.serializers.json import DjangoJSONEncoder
from django.db import models
from django.utils.translation import gettext_lazy as _
from ims.storage import PrivateStorage
from lib.api.file_path import FilePathGenerator
from lib.models import IzbergQueryset, TimestampedNullableModelMixin
from mp_utils.models import CacheableModel


class BaseAppConfiguration(TimestampedNullableModelMixin, CacheableModel):
    class Meta:
        abstract = True

    payment_backend = None

    application = models.ForeignKey(
        "ice_applications.Application", db_index=False, on_delete=models.CASCADE
    )
    currency = models.ForeignKey(
        "currencies.Currency", default="EUR", db_index=False, on_delete=models.CASCADE
    )
    active = models.BooleanField(default=False)
    staging = models.BooleanField(
        null=True,
        blank=True,
        choices=(
            (True, _("Force to use staging")),
            (False, _("Force to use production")),
            (None, _("Let IZBERG API select the environment")),
        ),
    )
    offline_refund = models.BooleanField(
        _("Force offline refunds"),
        default=False,
        help_text=_(
            "If enabled, all refunds will be treated as bank transfers by default"
        ),
    )


class BaseRestCallLog(TimestampedNullableModelMixin, CacheableModel):
    class Meta:
        abstract = True

    payment_backend = None
    payment = models.ForeignKey(
        "payment.Payment",
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        db_index=False,
    )
    merchant = models.ForeignKey(
        "stores.Merchant",
        null=True,
        blank=True,
        related_name="+",
        on_delete=models.CASCADE,
        db_index=False,
    )
    application = models.ForeignKey(
        "ice_applications.Application",
        null=True,
        blank=True,
        related_name="+",
        on_delete=models.CASCADE,
        db_index=False,
    )

    # Request
    target_url = models.CharField(max_length=255)
    data = models.TextField(blank=True, null=True)
    method = models.CharField(max_length=10)

    headers = models.JSONField(
        blank=True,
        db_column="headers",
        default=dict,
        encoder=DjangoJSONEncoder,
    )

    # Response
    response_body = models.TextField(blank=True, null=True)
    response_file = models.FileField(
        max_length=1024,
        upload_to=FilePathGenerator(),
        blank=True,
        null=True,
        storage=PrivateStorage(),
    )
    response_file_bkp = models.FileField(
        max_length=1024,
        null=True,
        blank=True,
        default=None,
        storage=PrivateStorage(),
    )
    status_code = models.IntegerField(blank=True, null=True)
    response_time = models.DurationField(blank=True, null=True)

    objects = IzbergQueryset.as_manager()

    def clean(self):
        if not self.application_id and self.merchant_id:
            self.application_id = self.merchant.application_id
