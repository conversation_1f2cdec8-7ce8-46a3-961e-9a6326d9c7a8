# -*- coding: utf-8 -*-


from constance import config


class BaseMoneyTracker:
    FAKE = False

    def __init__(self, payment):
        self.transaction = self.get_psp_transaction(payment=payment)

    def get_psp_transaction(self, payment=None, order=None, merchant_order=None):
        raise NotImplementedError

    def is_capture_not_failed(self, merchant_order):
        """
        Check if there is capture requested for current merchant order
        """
        raise NotImplementedError

    def is_captured(self, merchant_order):
        """This action is in charge of marking sure the order has been
        captured.

        If yes, then the money is not on client bank account anymore and may be
        either on PSP collect API pipe OR already arrived onto marketplace
        API.

        :return: Money taken from client account?
        :rtype: boolean
        """
        raise NotImplementedError

    def is_on_escrow(self, merchant_order):
        """This action is in charge of making sure money has been credited onto
        marketplace API.

        If yes, then funds allocation can occur

        :return: Money distributed onto ESCROW wallet
        :rtype: bool
        """
        raise NotImplementedError

    def set_on_escrow(self, merchant_order):
        raise NotImplementedError

    def is_order_resolved_on_payment_backend(self, order):
        """Return true if all merchant-orders have been paid (i.e. if all
        merchant_orders have been captured)

        :params order: Order
        :type order: apps.orders.models.Order
        """
        # order is resolved when zero merchant-order still authorized
        # (which is equivalent to zero merchant-order neither
        # cancelled nor confirmed_or_more)
        raise NotImplementedError

    def set_on_wallet(self, merchant_order, amount):
        raise NotImplementedError

    def is_on_wallet(self, merchant_order):
        raise NotImplementedError

    def set_on_operator_wallet(self, order, amount):
        raise NotImplementedError

    def is_on_operator_wallet(self, order):
        raise NotImplementedError

    def should_refund_from_wallet(self, refund):
        raise NotImplementedError

    def can_refund(self, refund):
        raise NotImplementedError


class BaseFakeMoneyTracker(BaseMoneyTracker):
    FAKE = True

    RESOLVED_SETTING = {"on_escrow": True, "order_resolved": True}

    ON_ESCROW_SETTING = {"on_escrow": True, "order_resolved": False}

    NOT_FUNDED_SETTINGS = {
        "on_escrow": False,
        "order_resolved": False,
    }

    def __init__(self, payment):
        """
        Should set self.payment
        and self.transaction
        """
        self.payment = payment

    @property
    def conf(self):
        if config.TRACKER_CONFIG == "ON_ESCROW":
            return self.ON_ESCROW_SETTING
        elif config.TRACKER_CONFIG == "NOT_FUNDED":
            return self.NOT_FUNDED_SETTINGS
        elif config.TRACKER_CONFIG == "RESOLVED":
            return self.RESOLVED_SETTING
        else:
            raise Exception(f"Unknown TRACKER_CONFIG {config.TRACKER_CONFIG}")

    def is_on_escrow(self, merchant_order):
        return self.conf.get("on_escrow")

    def is_order_resolved_on_payment_backend(self, order):
        return self.conf.get("order_resolved")

    def can_refund(self, refund):
        if not refund.merchant_order_id:
            raise NotImplementedError(
                "MoneyTracker can only handle mechant_order based refund for now"
            )
        return self.is_on_wallet(refund.merchant_order) and self.is_on_operator_wallet(
            refund.merchant_order.order
        )
