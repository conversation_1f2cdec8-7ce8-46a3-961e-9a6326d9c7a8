# -*- coding: utf-8 -*-


import functools

from payment_backends.common.exceptions import (
    BaseApiResponseError,
    BaseConfigError,
    TransactionProcessingError,
)


def transaction_error_handler(func):
    """Decorator handling cashout API error responses so that decrated
    function's given transaction will automatically transit to
    "attention_required" state with complete details in error_message.

    Note: Given transaction is modified in the process.
    """

    @functools.wraps(func)
    def wrapped_func(instance, transaction, *args, **kwargs):
        handle_exceptions = (
            TransactionProcessingError,
            BaseApiResponseError,
            BaseConfigError,
        )
        try:
            return func(instance, transaction, *args, **kwargs)
        except handle_exceptions as e:
            transaction.warning(error_message=str(e))

    return wrapped_func
