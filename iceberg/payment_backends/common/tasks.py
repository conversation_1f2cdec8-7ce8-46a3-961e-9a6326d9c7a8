# -*- coding: utf-8 -*-


from logging import getLogger

from dateutil.relativedelta import relativedelta
from django.utils import timezone

from .models import BaseRestCallLog

logger = getLogger(__name__)


def delete_old_api_rest_log(log_cls, age_in_days, dry_run, chunk_size):
    """Generic cleaning task made for BaseRestCallLog models"""
    if not issubclass(log_cls, BaseRestCallLog):
        raise Exception(
            "Expects a BaseRestCallLog class. Received {}".format(log_cls.__name__)
        )
    due_date = timezone.now() - relativedelta(days=age_in_days)
    logs_to_delete = log_cls.objects.filter(created_on__lt=due_date)
    total = logs_to_delete.count()
    logger.info("{} old {} to delete".format(total, log_cls.__name__))
    if dry_run:
        logger.info("Dry run, old logs NOT deleted")
        return
    counter = 0
    while counter < total:
        log_ids = list(logs_to_delete[:chunk_size].values_list("id", flat=True))
        if not log_ids:
            # done
            break
        log_cls.objects.filter(id__in=list(log_ids)).delete()
        counter += len(log_ids)
        logger.info("Deleted {}/{} logs".format(counter, total))
    logger.info("Finished: {} logs deleted".format(counter))
    return counter
