# -*- coding: utf-8 -*-

from random import choice

from apps.kyc.models import MerchantIdentityDocument
from apps.testing.factories import KycInformationFactory
from django.test import override_settings
from ims.tests import BaseTestCase
from mock import Mock
from payment_backends.common import BaseKycBackend

DOCUMENT_TYPES_META = {
    1: {"file": {"help_text": "File of the identity proof", "name": "Identity Proof"}},
    2: {
        "file": {
            "help_text": "File of the registration proof",
            "name": "Registration Proof",
        }
    },
    3: {
        "file": {
            "help_text": "File of the articles of association",
            "name": "Articles Of Association",
        }
    },
    4: {
        "file": {
            "help_text": "File of the shareholder declaration",
            "name": "Shareholder Declaration",
        }
    },
    5: {"file": {"help_text": "File of the address proof", "name": "Address Proof"}},
    6: {"file": {"help_text": "File of the bank info", "name": "Bank Info"}},
    7: {
        "file": {
            "help_text": "File of the insurance certificate",
            "name": "Insurance certificate",
        }
    },
    8: {
        "file": {
            "help_text": "File of the general sales conditions",
            "name": "General Sales Conditions",
        }
    },
    9: {
        "file": {"help_text": "File of the return policies", "name": "Return Policies"}
    },
    10: {
        "file": {
            "help_text": "File of the marketplace agreement",
            "name": "Marketplace Agreement",
        }
    },
    11: {"file": {"help_text": "File of the tax status", "name": "Tax status"}},
    12: {
        "file": {
            "help_text": "File of the president of association",
            "name": "President of association",
        }
    },
    13: {
        "file": {
            "help_text": "File of the association official journal",
            "name": "Association official journal",
        }
    },
    14: {
        "file": {
            "help_text": "File of the association status",
            "name": "Association status",
        }
    },
}


@override_settings(LANGUAGE_CODE="fr-FR")
class TestKycV1Backend(BaseTestCase):
    def test_document_type_should_be_acceptable(self):
        merchant = Mock()
        document = Mock()
        document.document_type = 42
        backend = BaseKycBackend(merchant)

        acceptable, reason = backend.is_document_acceptable(document)

        self.assertTrue(acceptable)
        self.assertIsNone(reason)

    @override_settings(LANGUAGE_CODE="en-US", LANGUAGES=(("en", "English"),))
    def test_document_meta_format(self):
        merchant = Mock()
        document = Mock()
        document.document_type = choice(
            [doc_type[0] for doc_type in MerchantIdentityDocument.DOCUMENT_TYPE_CHOICES]
        )
        backend = BaseKycBackend(merchant)

        types_meta = backend.meta.get_document_types_meta()

        self.assertDictEqual(DOCUMENT_TYPES_META, types_meta)


@override_settings(LANGUAGE_CODE="fr-FR")
class TestKycV2Backend(BaseTestCase):
    def test_should_be_acceptable(self):
        kyc = KycInformationFactory.build()
        backend = BaseKycBackend(kyc.merchant)

        acceptable, reason = backend.is_document_acceptable(kyc)

        self.assertTrue(acceptable)
        self.assertIsNone(reason)
