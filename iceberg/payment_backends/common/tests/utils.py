# -*- coding: utf-8 -*-
from datetime import timedelta

from requests import Request, Response


def request(url, data, headers, method, raw_response, status_code=200):
    response = Response()
    request = Request(
        url=url,
        data=data,
        headers=headers,
        method=method,
    )
    request.body = data
    response.request = request
    response.status_code = status_code
    response.elapsed = timedelta(milliseconds=100)
    response._content = raw_response
    return response
