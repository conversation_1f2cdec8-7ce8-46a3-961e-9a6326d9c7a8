# -*- coding: utf-8 -*-


import logging
import uuid
from functools import wraps

from decorator import contextmanager
from django.conf import settings
from django.core.files.base import ContentFile
from requests import RequestException

logger = logging.getLogger(__name__)


def request_logger(func):
    """
    Decorator that log into db API calls.

    Must be used on a method that return a requests.Response instance
    and on a class that have a db_logger field of type "RestLogsBuilder"
    """

    @wraps(func)
    def wrapped(
        instance,
        url,
        post_args=None,
        method="GET",
        headers=None,
        return_content=True,
        **kwargs,
    ):
        try:
            response = func(
                instance,
                url,
                post_args=post_args,
                method=method,
                headers=headers,
                **kwargs,
            )
        except RequestException as e:
            response = e.response
            if response is not None:
                instance.db_logger.log_request(response)
            raise

        instance.db_logger.log_request(response)
        if not return_content:
            return response
        json_content_type = "application/json"
        content_type = response.headers.get("Content-Type", json_content_type)
        if json_content_type in content_type:
            return response.json()
        return response.content

    return wrapped


@contextmanager
def logging_context(
    db_logger=None,
    action_name=None,
    merchant=None,
    payment=None,
    application=None,
    log_response_as_file=False,
):
    """
    Set and clear logger context

    :param db_logger: RestLogsBuilder instance
    :param action_name: action name context
    :param merchant: merchant context
    :param application: application context
    :param payment: payment context
    :param log_response_as_file: log reponse as file instead of in a field
    """
    if db_logger is None or action_name is None:
        raise RuntimeError("db_logger and action_name are mandatory!")
    db_logger.work_on(
        action_name,
        merchant=merchant,
        payment=payment,
        application=application,
        log_response_as_file=log_response_as_file,
    )
    try:
        yield
    finally:
        db_logger.clear_work()


class RestLogsBuilder:
    """
    Use this class to log PSP APIs calls. This class have a state that can
    change given a context in which the logs are made.

    The context is defined by:
    - an action: this is a str field that must be settled before logging to
                 enhance comprehension of log's content.
    - a payment: this is the payment related with the call logged (optional)
    - a merchant: this is the merchant related with the call logged (optional)
    - an application: this is the application related with the call logged (optional)

    To change the context before logging, the method "workon" must be called
    To clear context the method "clear_work" must be called
    """

    def __init__(self, logger_model):
        """

        :param logger_model: BaseRestCallLog instance
        """
        self.logger_model = logger_model
        self.payment = None
        self.merchant = None
        self.application = None
        self.action = None
        self.log_response_as_file = False

    def log_request(self, response):
        try:
            self._do_log_request(response)
        except Exception:
            if settings.RAISE_SILENT_ERRORS:
                raise
            logger.exception("Unable to log API call:")

    def raise_for_action_not_configured(self, url):
        if not self.action:
            msg = (
                f"Request action must be configured to ease readability! "
                f"Actual request: {url}"
            )
            if settings.RAISE_SILENT_ERRORS:
                raise RuntimeError(msg)
            logger.error(msg)

    def _do_log_request(self, response):
        request = response.request
        request_content_type = request.headers.get("Content-Type", "")
        if "multipart" in request_content_type:
            data = "Unable to log Content-Type {}".format(request_content_type)
        else:
            data = request.body

        self.raise_for_action_not_configured(request.url)
        if self.log_response_as_file:
            response_body = None
        else:
            response_body = response.text
        log = self.logger_model.objects.create(
            action=self.action,
            application=self.application,
            merchant=self.merchant,
            payment=self.payment,
            target_url=request.url,
            data=data,
            method=request.method,
            headers=dict(request.headers),
            status_code=response.status_code,
            response_body=response_body,
            response_time=response.elapsed,
        )
        if self.log_response_as_file:
            log.response_file.save(str(uuid.uuid4()), ContentFile(response.content))

    def clear_work(self):
        self.payment = None
        self.merchant = None
        self.action = None
        self.application = None
        self.log_response_as_file = False

    def work_on(
        self,
        action,
        payment=None,
        merchant=None,
        application=None,
        log_response_as_file=False,
    ):
        self.action = action
        self.payment = payment
        self.merchant = merchant
        self.application = application
        self.log_response_as_file = log_response_as_file
