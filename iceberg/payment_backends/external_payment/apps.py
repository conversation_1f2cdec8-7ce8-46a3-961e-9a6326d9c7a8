# -*- coding: utf-8 -*-


from django.apps import AppConfig
from ims.core_modules import TECHNICAL_MODULE


class ExternalPaymentConfig(AppConfig):
    name = "payment_backends.external_payment"
    verbose_name = "External payment backend"
    module_name = TECHNICAL_MODULE

    def ready(self):
        from apps.payment.summarizers import register_payment_summarizer

        from .summarizer import ExternalSummarizer

        register_payment_summarizer(ExternalSummarizer)
