# Generated by Django 3.2.20 on 2023-08-24 09:28

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("currencies", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="ExternalPaymentLog",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "debug",
                    models.BooleanField(
                        default=True,
                        help_text="If True, will use the debug api handler",
                    ),
                ),
                (
                    "external_payment_backend_id",
                    models.CharField(
                        blank=True,
                        help_text="External backend payment ID as returned by used service's API",
                        max_length=255,
                        null=True,
                        verbose_name="External payment ID",
                    ),
                ),
                (
                    "external_payment_backend",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "payment_amount",
                    models.DecimalField(decimal_places=2, max_digits=20),
                ),
                ("creation_date", models.DateTimeField(auto_now_add=True)),
                (
                    "payment_currency",
                    models.ForeignKey(
                        db_index=False,
                        default="EUR",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="currencies.currency",
                    ),
                ),
            ],
        ),
    ]
