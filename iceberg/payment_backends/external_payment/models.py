# -*- coding: utf-8 -*-

from django.db import models
from django.utils.translation import gettext_lazy as _


class ExternalPaymentLogManager(models.Manager):
    def create_with_payment_and_amount(self, payment, amount):
        currency = payment.currency
        backend_name = "external"
        external_id = payment.external_id
        return self.create(
            payment_amount=amount,
            payment_currency=currency,
            external_payment_backend=backend_name,
            external_payment_backend_id=external_id,
        )

    def authorized(self, payment):
        amount = payment.to_collect_amount
        return self.create_with_payment_and_amount(payment, amount)

    def collected(self, payment):
        amount = payment.collected_amount
        return self.create_with_payment_and_amount(payment, amount)

    def refunded(self, payment):
        """
        @param payment: Same as authorized but with reverse amount
        @return:
        """
        amount = -payment.refunded_amount
        return self.create_with_payment_and_amount(payment, amount)


class ExternalPaymentLog(models.Model):
    objects = ExternalPaymentLogManager()

    debug = models.BooleanField(
        default=True, help_text=_("If True, will use the debug api handler")
    )
    external_payment_backend_id = models.CharField(
        _("External payment ID"),
        max_length=255,
        null=True,
        blank=True,
        help_text=_("External backend payment ID as returned by used service's API"),
    )
    external_payment_backend = models.CharField(null=True, blank=True, max_length=255)
    payment_amount = models.DecimalField(max_digits=20, decimal_places=2)
    payment_currency = models.ForeignKey(
        "currencies.Currency", default="EUR", db_index=False, on_delete=models.CASCADE
    )
    creation_date = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return (
            f"{self.id} - payment of {self.payment_amount} "
            f"{self.payment_currency} through {self.external_payment_backend} "
            f"({self.external_payment_backend_id})"
        )
