# -*- coding: utf-8 -*-

import logging

from apps.payment.bases.processor import BasePaymentProcessor
from django.db import transaction
from django.utils.translation import gettext_lazy as _
from payment_backends.external_payment.models import ExternalPaymentLog

logger = logging.getLogger(__name__)


class ExternalPayment(BasePaymentProcessor):
    name = "external"
    version = "1.0.0"
    verbose_name = _("External")
    handle_term_payment = False
    handle_prepayment = True

    def get_manual_refund(self, application, payment):
        """
        Return True: refunds are managed through external plugin or webservice.
        :return: is external intervention required
        """
        return True

    def get_manual_cashout(self, application):
        """
        Return True if payment backend requires a manual intervention for
        processing cashout.
        (if not overridden, returns True)
        :return: is external intervention required
        """
        return True

    def get_config(self, application, currency=None, payment_method=None):
        return None

    def authorize(self, payment, by_user, is_operator=False, extra_data=None):
        """Apply authorization on payment_object."""
        if payment.authorize.is_available():
            with transaction.atomic():
                payment.authorize(by_user=by_user, previous_status=payment.status)
            ExternalPaymentLog.objects.authorized(payment)

    # Actions
    def collect(self, payment, by_user, amount, merchant_order=None):
        """Return True to confirm capture success to the API
        (capture is considered as OK on external service since the "confirm"
        action has been called)

        Payment Obj
        amount: Decimal
        """
        ExternalPaymentLog.objects.collected(payment)
        return True

    def refund(self, payment, by_user, amount=None, refund=None):
        """Refund must also be managed on external service's website or plugin.

        Payment Obj
        amount: Decimal
        """
        ExternalPaymentLog.objects.refunded(payment)
        return True

    def cancel(self, payment, by_user=None, amount=None, merchant_order=None):
        return True

    def cash_out(
        self,
        transaction,
        merchant,
        bank_account,
        amount,
        currency,
        application,
        debug=False,
        by_user=None,
    ):
        """
        @merchant: stores.Merchant object
        @bank_account: stores.MerchantBankAccount object
        @amount: Decimal Amount
        @currency: Currency obj or ID
        @application: Application owning the merchant
        @debug: if debug the cashout is not real
        @by_user: user making the cashout
        """
        # Don't do anything. It's external man!
        return True

    def application_cash_out(
        self,
        transaction,
        application_bank_account,
        amount,
        currency,
        debug=False,
        by_user=None,
    ):
        return True
