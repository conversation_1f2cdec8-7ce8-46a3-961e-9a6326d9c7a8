# -*- coding: utf-8 -*-

from apps.payment.bases.summarizer import BaseBackendSummarizer
from apps.payment.payment_summary import PaymentSummary


class ExternalSummarizer(BaseBackendSummarizer):
    """
    Payment Backend Extractor for External Backend.

    By definition, we know nothing about external backends so
    no raw data and empty summary. This means consistency check
    will always return status as consistent.
    """

    backend_name = "external"

    def get_raw_payment_data(self):
        return []

    def get_backend_summary(self):
        return PaymentSummary()
