# -*- coding: utf-8 -*-
from apps.ice_applications.app_conf_settings import EnableKycV2
from apps.kyc.models import KycInformation, MerchantIdentityDocument
from apps.stores.models import MerchantBankAccount
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from payment_backends.hipay_tpp.references import HIPAY_BANK_ACCOUNT_DETAILS
from payment_backends.hipay_tpp.utils import (
    get_hipay_kyc_type,
    get_wallet_and_conf_from_merchant_id,
)


class BankAccountSelectionParamsParser:
    @classmethod
    def check_request_coherence(cls, wallet, identity_document):
        hipay_doctype, is_valid = get_hipay_kyc_type(wallet, identity_document)
        if not is_valid or hipay_doctype != HIPAY_BANK_ACCOUNT_DETAILS:
            raise ValidationError(
                {
                    "document_id": _(
                        "Invalid document type: Selected document does not appear "
                        "to be a bank account number copy."
                    )
                }
            )

    @classmethod
    def parse_select_form(cls, hipay_bank_account, form_data):
        return (
            cls.get_selected_account(hipay_bank_account, form_data),
            cls.get_selected_document(hipay_bank_account, form_data),
        )

    @classmethod
    def get_selected_account(cls, hipay_bank_account, form):
        try:
            bank_account = int(form["account_id"])
        except (ValueError, KeyError, TypeError):
            raise ValidationError({"account_id": _("Invalid account ID.")})

        try:
            bank_account = MerchantBankAccount.objects.get(
                merchant=hipay_bank_account.merchant_id, pk=bank_account
            )
        except MerchantBankAccount.DoesNotExist:
            raise ValidationError({"account_id": _("Bank account not found.")})
        return bank_account

    @classmethod
    def _get_v1_document(cls, wallet, hipay_bank_account, document_id):
        try:
            document_id = int(document_id)
        except (ValueError, TypeError):
            raise ValidationError({"document_id": _("Invalid document ID.")})

        try:
            document = MerchantIdentityDocument.objects.get(
                merchant=hipay_bank_account.merchant_id, pk=document_id
            )
        except MerchantIdentityDocument.DoesNotExist:
            raise ValidationError(
                {"document_id": _("Merchant identity document not found.")}
            )
        return document

    @classmethod
    def _get_v2_document(cls, wallet, hipay_bank_account, document_id):
        try:
            document = KycInformation.objects.get(
                merchant=hipay_bank_account.merchant_id, pk=document_id
            )
        except KycInformation.DoesNotExist:
            raise ValidationError({"document_id": _("Kyc information not found.")})
        return document

    @classmethod
    def get_selected_document(cls, hipay_bank_account, form):
        wallet, _ = get_wallet_and_conf_from_merchant_id(hipay_bank_account.merchant_id)
        app = hipay_bank_account.application or hipay_bank_account.merchant.application
        if "document_id" not in form:
            raise ValidationError({"document_id": _("Missing document ID.")})
        if app.get_setting(EnableKycV2):
            document = cls._get_v2_document(
                wallet, hipay_bank_account, form["document_id"]
            )
        else:
            document = cls._get_v1_document(
                wallet, hipay_bank_account, form["document_id"]
            )
        cls.check_request_coherence(wallet, document)
        return document
