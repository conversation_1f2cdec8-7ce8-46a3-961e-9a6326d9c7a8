# -*- coding: utf-8 -*-
import logging

from apps.kyc.models import KycInformation
from lib.core.base_actions_manager import BaseActionsManager
from payment_backends.hipay_tpp.references import (
    HIPAY_BANK_ACCOUNT_DETAILS,
    HIPAY_BANK_INFOS_STATUSES_INITIAL,
    HIPAY_BANK_INFOS_STATUSES_VALID,
    HIPAY_BANK_INFOS_STATUSES_WAITING,
)
from requests import HTTPError

logger = logging.getLogger(__name__)


class BankAccountActionsManager(BaseActionsManager):
    HIPAY_NO_BANK_INFO = 1
    HIPAY_VALIDATION_IN_PROGRESS = 2
    HIPAY_VALIDATED = 3

    def select(self, store_bank_account, identity_document):
        cashout_api = self.model.get_api()
        wallet = self.model.get_wallet()
        try:
            response_data = cashout_api.select_merchant_bank_account(
                store_bank_account, identity_document, wallet
            )
        except HTTPError as e:
            self._handle_error_response(e.response.json())
        else:
            self.model.store_bank_account = store_bank_account
            if isinstance(identity_document, KycInformation):  # v2
                self.model.kyc_information = identity_document
            else:  # v1
                self.model.identity_document = identity_document
            self._handle_success_response(response_data)

    def refresh(self):
        api = self.model.get_api()

        merchant = self.model.get_merchant()
        if api is None or merchant is None:
            return self.model
        wallet = self.model.get_wallet()
        try:
            response_data = api.get_bank_infos_status(wallet)
        except HTTPError as e:
            return self._handle_error_response(e.response.json())
        else:
            return self._handle_success_response(response_data)

    def _handle_success_response(self, response_data):
        code = response_data["status_code"]
        status = self.model.synchro_status
        handled_codes = [
            self.HIPAY_NO_BANK_INFO,
            self.HIPAY_VALIDATION_IN_PROGRESS,
            self.HIPAY_VALIDATED,
        ]
        if code not in handled_codes:
            raise Exception("Unhandled status_code {}".format(code))
        can_transition = (
            (
                code == self.HIPAY_NO_BANK_INFO
                and status != HIPAY_BANK_INFOS_STATUSES_INITIAL
            )
            or (
                code == self.HIPAY_VALIDATION_IN_PROGRESS
                and status != HIPAY_BANK_INFOS_STATUSES_WAITING
            )
            or (
                code == self.HIPAY_VALIDATED
                and status != HIPAY_BANK_INFOS_STATUSES_VALID
            )
        )
        if can_transition:
            transition = {
                self.HIPAY_NO_BANK_INFO: "set_initial",
                self.HIPAY_VALIDATION_IN_PROGRESS: "set_waiting",
                self.HIPAY_VALIDATED: "validate",
            }[code]
            getattr(self.model, transition)()

        self._update_kyc(code)
        return self.model

    def _update_kyc(self, code):
        doc = self.model.identity_document or self.model.kyc_information
        if not doc:
            return
        if code == self.HIPAY_VALIDATION_IN_PROGRESS:
            # filling doc external_id
            doc.external_id = HIPAY_BANK_ACCOUNT_DETAILS
            doc.save(update_fields=["external_id"])
            # setting it in under_analysis
            if doc.analyse_doc.is_available():
                doc.analyse_doc()

    def _handle_error_response(self, response_data):
        def _log_exception(response_data):
            logger.exception(
                "Unhandled hipay error. See extra data",
                extra={"response_data": response_data},
            )

        errors = response_data.get("errors")
        messages = []
        try:
            if isinstance(errors, list):
                messages = [error["message"] for error in errors]
            elif isinstance(errors, dict):
                messages = list(errors.values())
            elif "message" in response_data:
                messages = [response_data["message"]]
        except Exception:
            _log_exception(response_data)
        if not messages:
            _log_exception(response_data)
            messages = "Unknown error"
        self.model.synchro_error = {"__all__": messages}
        self.model.invalidate()
        return self.model
