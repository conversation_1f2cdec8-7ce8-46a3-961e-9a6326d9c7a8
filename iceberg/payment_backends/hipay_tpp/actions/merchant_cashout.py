# -*- coding: utf-8 -*-
import time
from datetime import datetime
from decimal import Decimal
from logging import getLogger
from random import randint
from typing import List

from apps.stores.models import BalanceTransaction
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from payment_backends.hipay_tpp.exceptions import (
    CashoutNotToBeDone,
    HipayImpossibleCashout,
    HipayWalletStillEmptyAfterTransfer,
    WithdrawableAmountMisMatch,
)
from payment_backends.hipay_tpp.hipay_api import TransferParameter
from payment_backends.hipay_tpp.models import Application, HipayAppConfiguration
from payment_backends.hipay_tpp.utils import (
    get_and_check_account_identification,
    get_and_check_bank_infos,
    get_wallet_and_conf_from_merchant_id,
)
from reference import status
from reference import transaction_codes as tc

logger = getLogger(__name__)

HIPAY_MINIMUM_TRANSFER_WAIT_TIME = 10  # second
HIPAY_MAXIMUM_WAITING_TIME = 300  # second
HIPAY_MAX_RETRY = 5


def handle_result_propagation_on_transactions(called_function):
    def do_call(overall_cashout, *args, **kwargs):
        try:
            result = called_function(overall_cashout, *args, **kwargs)
        except CashoutNotToBeDone:
            # just do nothing.... we got there BUT nothing has to be done
            logger.error("We ran over a cashout request but cashout must not be done.")
            result = None

        except HipayImpossibleCashout as e:
            logger.info(
                "Cashout operation dropped because of the following issue: %s",
                "%s" % e.args[0],
            )
            # in case of expected hipay issue, propagate real message
            warning_related_transactions(overall_cashout, e.args[0])
            result = None

        except Exception as e:
            if settings.RAISE_SILENT_ERRORS:
                raise
            logger.exception("Exception occurred while running nigthly cash out: %s", e)
            # Only feature "Internal error" message
            warning_related_transactions(overall_cashout, _("Internal error"))
            result = None

        else:
            # if success, mark transactions as succeeded
            confirm_related_transactions(overall_cashout, result)

        return result

    return do_call


def check_wallet_after_transfer_or_raises(api, wallet) -> None:
    # Default waiting time to let Hipay with some random millisecond (0 to 5s) to avoid
    # multiples task to perform the request at the same time
    time.sleep(HIPAY_MINIMUM_TRANSFER_WAIT_TIME + (randint(0, 5000) / 1000))

    retry = 0
    while retry < HIPAY_MAX_RETRY:
        if not api.check_ewallet_is_empty(wallet):
            # Wallet is not empty, we are good to go
            return

        retry += 1
        # Exponential backoff 3, 9, 27, 81, 243 with some random millisecond
        # Using min() to avoid insane waiting time In case of HIPAY_MAX_RETRY being
        # increased in the future,
        waiting_time = min((3**retry), HIPAY_MAXIMUM_WAITING_TIME) + (
            randint(0, 5000) / 1000
        )
        time.sleep(waiting_time)

    # Wallet is still empty after X retries, raise an error
    raise HipayWalletStillEmptyAfterTransfer()


def check_withdrawal_limit_is_not_reached_or_raises(api, transfer_param) -> None:
    balance, withdrawable_amount = api.get_balance_and_withdrawable_amount(
        transfer_param.wallet
    )
    if balance != withdrawable_amount:
        api.transfer_amount_back(transfer_param)
        raise WithdrawableAmountMisMatch()


@handle_result_propagation_on_transactions
def try_to_cashout(global_cashout_instruction):
    """
    Cashout step:
    - Get wallet et configuration from the merchant
    - Get bank account from merchant
    - Check bank information and account identification
    - Initiate the Hipay request transfer. Meaning transfering the given amount from
      Wallet operator to wallet merchant. (The operator wallet is defined in the headers
       of the request. See:`def enrich_headers(self, headers)`)
    - Wait for the money to be on the merchant wallet
      if the money is not on the wallet after the defined time, raise an error
    - Check the balance of the waller merchant, if the money_available_for_withdrawal is
      different from the balance, transfer the money back from merchant wallet to
      operator wallet and raise.

    - Try to withdraw the money from wallet merchant to bank account merchant.
      Some checks are performed:
      - Check if the wallet merchant is empty, if os raise.
      - Check if the amount ask for the withdraw is different
      - Initiate the Hipay request withdrawal
    - If the withdraw is a success, the method stop and return True
    - If not, we try to revert the transfer of money that was done between the wallet
      operator and the wallet merchant. But this time we transfer from wallet merchant
      to wallet operator.
      - First we check the balance of the wallet merchant. If no money is on it, we
        simply raise the exception that occur on during the withdraw.
      - If the balance is different that 0, then we initiate the transfer of money from
        wallet merchant to wallet operator. Finally we raise the exception that occur
        on during the withdraw.


    Note: Each request to Hipay can fail for multiple reason (Hipay is unavailable,
    wrong parameters, account baldy configured, wrong or mismatch amount ect)
    When one request fail, the overall state of each wallet can be wrong. The code try
    to deal with the situation but most of the time will fail miserably. The only
    concrete and working solution is to call Hipay and to ask them to reset the wallet
    merchant.


    :param global_cashout_instruction: Dict containing the following:
        {
            "merchant_id",
            "merchant_name",
            "application_id",
            "cashout_request_ids_list",
            "total_amount",
            "currency":,
        }
    """

    merchant_id = global_cashout_instruction["merchant_id"]
    # cash out transactions are negative. Hipay expects a positive amount
    amount = -Decimal(global_cashout_instruction["total_amount"])
    currency = global_cashout_instruction["currency"]

    wallet, conf = get_wallet_and_conf_from_merchant_id(merchant_id)
    selected_bank_account = get_selected_bank_account_for_merchant(merchant_id)
    merchant = wallet.merchant

    api = conf.cashout_api

    if not conf.cashout_enabled or config_env_mismatch(conf):
        logger.error(
            "A cashout operation didn't succeed while running nightly batch, "
            "the cashout feature has been disabled on app %s. "
            "Please fix the problem & re-engage the cashout feature.",
            conf.application.name,
        )
        raise CashoutNotToBeDone()

    # Hipay keeps on their side, the bank account registration
    get_and_check_bank_infos(merchant, selected_bank_account)
    get_and_check_account_identification(api, wallet)
    label_s = get_merchant_cashout_transaction_label(amount, currency, merchant)
    transfer_param = TransferParameter(amount, currency, label_s, wallet)

    api.transfer_amount(transfer_param)

    check_wallet_after_transfer_or_raises(api, wallet)
    check_withdrawal_limit_is_not_reached_or_raises(api, transfer_param)

    try:
        api.withdraw_without_commissions(transfer_param)
    except Exception as e:
        logger.exception(e)
        current_balance = api.get_balance(transfer_param.wallet)["balance"]
        logger.error(
            f"Withdrawal failed {transfer_param}: {e} "
            f"- current_balance: {current_balance}"
        )
        if Decimal(current_balance) != Decimal("0"):
            logger.info(
                f"[failure] Balance not null, transferring amount {amount} back onto "
                f"technical account ({current_balance} on balance)",
            )
            api.transfer_amount_back(transfer_param)
            logger.info("Safety transfer successfully achieved")
        raise

    logger.info("Withdrawal succeeded")
    return True


def get_related_cashout_transactions(aggregated_cashout_information):
    """Get the list of all cash out transactions for a given aggregated cash
    out instruction dict.

    The list of transactions will then be used to update them accordingly.
    """
    ids_list = aggregated_cashout_information["cashout_request_ids_list"]
    return BalanceTransaction.objects.filter(id__in=ids_list)


def confirm_related_transactions(aggregated_cashout_information, hipay_result):
    """Mark all cash out transactions as succeeded"""
    pending_cashout_transactions = get_related_cashout_transactions(
        aggregated_cashout_information
    )

    transaction_public_id = getattr(hipay_result, "transaction_public_id", "")
    for transaction in pending_cashout_transactions:
        transaction.async_confirmation(extra_infos=transaction_public_id)


def warning_related_transactions(aggregated_cashout_information, error_message):
    """Mark all cash out transactions as failed and set given error_message on
    all of them.
    """
    pending_cashout_transactions = get_related_cashout_transactions(
        aggregated_cashout_information
    )

    for transaction in pending_cashout_transactions:
        transaction.warning(error_message=error_message)


def get_selected_bank_account_for_merchant(merchant_id):
    from apps.stores.models import MerchantBankAccount

    try:
        return MerchantBankAccount.objects.get(merchant_id=merchant_id, _default=True)
    except MerchantBankAccount.DoesNotExist:
        raise HipayImpossibleCashout("No default bank account selected in settings")


def get_pending_cashouts(application_id: int, limit_date: datetime) -> List[dict]:
    """Iterate on all merchant's pending cashout requests for given application

    Return list of dict with keys:
     - merchant_id: requesting merchant-id
     - merchant_name: name of the merchant
     - total_amount: sum of all cashout requests,
     - currency: cash out currency supposed constant amongst cash out
                requests of a single merchant
     - cashout_request_ids_list: list of all cash out transactions

    """
    # get all cashout
    # in status pending/processing/warning
    # and created before task execution date
    # limited to hipay-powered-apps
    transactions = BalanceTransaction.objects.filter(
        transaction__transaction_type=tc.TRANSACTION_CASHOUT,
        status__in=[
            status.TRANSACTION_STATUS_PENDING,
            status.TRANSACTION_STATUS_PROCESSING,
        ],
        timestamp__lt=limit_date,
        application_id=application_id,
        # Add the order-by clause to virtually create a 'group-by' behaviour
        # so I can yield every-time the merchant changes
    ).order_by("merchant_id")

    if transactions.count() == 0:
        return []

    aggregated_cashouts = []

    _current_cashout = None
    _last_merchant_id = None
    for t in transactions:
        if t.merchant_id != _last_merchant_id:
            # first round or done with _last_merchant_id
            if _current_cashout:
                aggregated_cashouts.append(_current_cashout)
            _current_cashout = init_aggregation_from_transaction(t)
            _last_merchant_id = t.merchant_id
        add_transaction(_current_cashout, t)
    aggregated_cashouts.append(_current_cashout)

    return aggregated_cashouts


def add_transaction(aggregated_cashout, t):
    aggregated_cashout["total_amount"] = str(
        Decimal(aggregated_cashout["total_amount"]) + t.amount
    )
    aggregated_cashout["cashout_request_ids_list"].append(t.id)


def init_aggregation_from_transaction(t):
    return {
        "merchant_id": t.merchant_id,
        "merchant_name": t.merchant.name,
        "application_id": t.merchant.application_id,
        "cashout_request_ids_list": [],
        "total_amount": "0",
        "currency": t.currency.code,
    }


def get_merchant_cashout_transaction_label(amount, currency, merchant):
    return "Withdrawal of %s %s from %s's account to bank account" % (
        amount,
        currency,
        merchant,
    )


def config_env_mismatch(conf):
    """Return True is config target env mismatches application's environment

    :type conf: payment.backends.hipay_tpp.models.HipayAppConfiguration

    :rtype: bool
    """
    if conf.staging is None:
        # no problem, staging is taken from app, so there is no mismatch
        return False
    app = conf.application
    # staging should be true if app is not Prod, else should be false
    expected_staging_value = app.environment != app.PRODUCTION
    # return "config mismatched env" response
    return expected_staging_value != conf.staging


def disengage_and_mail_cashout_module(app_id, err):
    """Log error and then shut all configs' cashout_enabled down"""
    application = Application.objects.get(id=app_id)
    logger.error(
        "Transaction status propagation failed on application %s (%s) - %s",
        application.name,
        app_id,
        err,
    )

    # don't care it config is active or not / staging or not...
    # just shut the cashout off
    HipayAppConfiguration.objects.filter(application_id=app_id).update(
        cashout_enabled=False
    )
