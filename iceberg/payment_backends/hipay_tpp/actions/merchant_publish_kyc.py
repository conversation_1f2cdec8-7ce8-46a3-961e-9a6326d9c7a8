# -*- coding: utf-8 -*-
from datetime import <PERSON><PERSON><PERSON>
from logging import getLogger

from apps.kyc.models import KycInformation, MerchantIdentityDocument
from django.db.transaction import atomic
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from payment_backends.hipay_tpp.references import HIPAY_BANK_ACCOUNT_DETAILS
from reference.status import DOCUMENT_ACCEPTED, DOCUMENT_EXPIRED, DOCUMENT_REFUSED

from ..references import (
    HIPAY_EXISTING_POTENTIALLY_VALID_DOC_TYPE_CODES,
    HIPAY_KYC_MESSAGES,
)
from ..utils import (
    get_hipay_kyc_type,
    humanify_kyc_errors,
    send_internal_error_mail,
    unpack_dict,
)

logger = getLogger(__name__)


def get_message(result):
    """Return a formatted error string OR a success message"""
    errors_list = result.get("errors", [])
    code = result.get("code", 0)
    if code == 0:
        return _("Document successfully submitted for validation.")
    if code in HIPAY_KYC_MESSAGES:
        return HIPAY_KYC_MESSAGES[code]
    else:
        error_message = humanify_kyc_errors(errors_list)
    return error_message


def send_error_mail(document, code, error_message):
    error_str = _("KYC upload failed : %(code)s - %(reason)s") % {
        "code": code,
        "reason": error_message,
    }

    logger.error(error_str)
    send_internal_error_mail(
        to_application=document.merchant.application,
        from_seller=document.merchant,
        concerning_document=document,
        error_detail=error_message,
    )


def send_kyc(config, merchant_wallet, document):
    hipay_doctype, is_valid = get_hipay_kyc_type(merchant_wallet, document)

    # ignore documents that are not matched to a type on hipay
    if mark_document_as_not_required(
        document, is_valid and hipay_doctype != HIPAY_BANK_ACCOUNT_DETAILS
    ):
        return "not_required_doc"

    add_default_validity_date(document)

    result = config.kyc_api.send_kyc(merchant_wallet, document)

    code, message = unpack_dict(result, "code", "message")
    backend_message = message or get_message(result)
    if code == 0:
        doc_action = "analyse_doc"
    elif code in HIPAY_EXISTING_POTENTIALLY_VALID_DOC_TYPE_CODES:
        doc_action = "not_required_doc"
    else:
        doc_action = "refuse_doc"

    if doc_action != "not_required_doc":
        # if there is no existing doc validated/under validation
        # we can expire all previous doc of same type to keep new one
        expire_other_documents(
            hipay_doctype,
            document,
            # expire previously succeeded document if new document is uploaded
            # with success because the new one's replacing it (else keep it if any)
            #  -- equivalent to `True if upload_success else False`
            include_succeeded=(doc_action == "analyse_doc"),
        )

    update_upload_status(document, doc_action, hipay_doctype, backend_message)

    if doc_action == "refuse_doc":
        try:
            send_error_mail(document, code, backend_message)
        except Exception:
            logger.exception("Couldn't send mail to marketplace")

    return doc_action


def _get_other_merchant_identity_documents(
    hipay_doctype, uploaded_doc, include_succeeded
):
    merchant_id = uploaded_doc.merchant_id
    other_docs = (
        MerchantIdentityDocument.objects.filter(
            merchant_id=merchant_id, external_id=hipay_doctype
        )
        .exclude_deleted()
        .exclude(kyc_backend_status__in=[DOCUMENT_EXPIRED, DOCUMENT_REFUSED])
    )

    if isinstance(uploaded_doc, MerchantIdentityDocument):
        other_docs.exclude(pk=uploaded_doc.pk)
    if not include_succeeded:
        other_docs = other_docs.exclude(kyc_backend_status=DOCUMENT_ACCEPTED)
    return list(other_docs)


def _get_other_kyc_informations(hipay_doctype, uploaded_doc, include_succeeded):
    merchant_id = uploaded_doc.merchant_id
    other_docs = (
        KycInformation.objects.filter(
            merchant_id=merchant_id, external_id=hipay_doctype
        )
        .exclude_deleted()
        .exclude(kyc_backend_status__in=[DOCUMENT_EXPIRED, DOCUMENT_REFUSED])
    )

    if isinstance(uploaded_doc, KycInformation):
        other_docs.exclude(pk=uploaded_doc.pk)
    if not include_succeeded:
        other_docs = other_docs.exclude(kyc_backend_status=DOCUMENT_ACCEPTED)
    return list(other_docs)


@atomic
def expire_other_documents(hipay_doctype, uploaded_doc, include_succeeded):
    """find any existing previous uploaded document of same type
    if any doc found and mark them as expired (merchant is free to remove
    them if he wishes to)
    Warning: atomic operation

    - ignores refused docs
    - ignores already expired docs
    - ignores current document
    - only applies to current merchant's docs of the same hipay-type


    :param hipay_doctype: Hipay document type
    :type hipay_doctype: int

    :param uploaded_doc: MerchantIdentityDocument being uploaded
    :type uploaded_doc: apps.stores.models.MerchantIdentityDocument

    :param include_succeeded: Does expiration applies on accepted documents?
    :type include_succeeded: bool
    """
    other_docs = _get_other_merchant_identity_documents(
        hipay_doctype, uploaded_doc, include_succeeded
    ) + _get_other_kyc_informations(hipay_doctype, uploaded_doc, include_succeeded)
    for doc in other_docs:
        doc.kyc_backend_message = _("Old document replaced by the uploaded one")
        doc.expire_doc()


@atomic
def update_upload_status(document, transition, doctype, message=""):
    document.external_id = doctype
    document.kyc_backend_message = message
    getattr(document, transition)()


def mark_document_as_not_required(document, doc_type_is_valid):
    """Changes document state to "not-required" if possible plus adds a
    user-friendly message telling the document is not sent to PSP
    """
    if doc_type_is_valid:
        return False

    if document.not_required_doc.is_available():
        document.not_required_doc()

    document.kyc_backend_message = _("Document not transmitted for validation")
    document.save(update_fields=["kyc_backend_message"])

    return True


def add_default_validity_date(document):
    """Set default validity date to `now + 3 years` if not validity
    date is set.

    It's mandatory for hipay-doctypes 1,3 & 7 BUT we'll set-it always
    """
    if document.validity_date is not None:
        return
    today = timezone.now()
    validity_date = today + timedelta(days=3 * 365)
    document.validity_date = validity_date
    document.save(update_fields=["validity_date"])
