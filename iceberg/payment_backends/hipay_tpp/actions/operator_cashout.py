# -*- coding: utf-8 -*-

import logging
import time
from decimal import Decimal

from apps.ice_applications.models import ApplicationBankAccount
from django.core.exceptions import ValidationError
from django.db import transaction
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from payment_backends.hipay_tpp.hipay_api import TransferParameter
from payment_backends.hipay_tpp.references import CASHOUT_LIMIT
from payment_backends.hipay_tpp.utils import get_conf_for_application

logger = logging.getLogger(__name__)


def _format_result(result):
    return {
        "code": result["code"],
        "description": result["message"],
        "transaction_public_id": getattr(result, "transaction_public_id", ""),
    }


class HipayOperatorCashout:
    def __init__(self, wallet, requested_amount, user):
        self.operator_wallet = wallet
        self.application = wallet.application
        self.requested_amount = Decimal(requested_amount)
        self.currency = wallet.application.default_currency
        self.user = user

        self.hipay_conf = get_conf_for_application(self.application)
        self.current_balance = self.application.actions.get_current_balance(
            self.currency
        )
        self.bank_account = self._retrieve_bank_account()
        self.label = "cashout operator {app_name} - {date}".format(
            app_name=str(self.application.name), date=timezone.now().strftime("%Y%m%d")
        )

        self.actual_cashout_amount = None
        self.hipay_fees = None
        self.credit_result = {}
        self.withwraw_result = {}
        self.cashout_transaction = None

    def _retrieve_bank_account(self):
        try:
            return ApplicationBankAccount.objects.get(
                _default=True, application=self.application
            )
        except ApplicationBankAccount.DoesNotExist:
            raise ValidationError(_("Please add a bank account"))

    def _setup_hipay_fees(self):
        self.hipay_fees = self.operator_wallet.current_balance
        if self.hipay_fees > 0:
            raise ValidationError(
                _(
                    "Current amount on your wallet is positive. "
                    "This may be caused by an error during your previous "
                    "cashout attempt. "
                    "Please contact IZBERG support."
                )
            )
        self.hipay_fees = abs(self.hipay_fees)

    def _initial_checks(self):
        if self.operator_wallet.merchant is not None:
            raise ValidationError(
                _("This action is only available on commission wallet.")
            )
        if self.requested_amount <= 0:
            raise ValidationError(_("Cannot cashout negative amount."))
        if self.requested_amount > CASHOUT_LIMIT:
            raise ValidationError(
                _(
                    "Hipay will not allow a cashout request "
                    "with an amount above {amount}{currency}. Try doing several "
                    "cashout below this amount."
                ).format(amount=CASHOUT_LIMIT, currency=self.currency)
            )
        if (
            self.requested_amount - self.hipay_fees
            > self.current_balance.current_balance
        ):
            raise ValidationError(_("Can't ask for more than current balance"))
        self.current_balance.fail_for_rolling_reserve(self.requested_amount)
        if self.requested_amount <= self.hipay_fees:
            raise ValidationError(
                _(
                    "The requested amount of {requested_amount}{currency} "
                    "does not cover Hipay fees of {hipay_fees}{currency}. "
                    "Cashout aborted."
                ).format(
                    requested_amount=self.requested_amount,
                    currency=self.currency,
                    hipay_fees=self.hipay_fees,
                )
            )

    def _credit_wallet(self):
        tp = TransferParameter(
            wallet=self.operator_wallet,
            amount=abs(self.requested_amount),
            currency=self.currency.code,
            label_str=self.label,
        )
        result = self.hipay_conf.cashout_api.transfer_amount(tp)
        self.credit_result = _format_result(result)
        logger.debug("Transfer done. Waiting 10 seconds before withdrawal")
        time.sleep(10)
        self.actual_cashout_amount = self.operator_wallet.withdrawable_amount

    def _withdraw_wallet(self):
        tp = TransferParameter(
            wallet=self.operator_wallet,
            amount=self.actual_cashout_amount,
            currency=self.currency.code,
            label_str=self.label,
        )
        result = self.hipay_conf.cashout_api.withdraw(tp)
        self.withwraw_result = _format_result(result)

    @transaction.atomic
    def _update_balance(self):
        self.cashout_transaction = (
            self.current_balance.actions.process_application_cash_out(
                sender=self.current_balance,
                amount=self.requested_amount,
                application_bank_account=self.bank_account,
                by_user=self.user,
                debug=False,
                payment_backend=self.application.payment_settings.payment_backend,
                application=self.application,
            )
        )

    def _generate_report(self):
        return {
            "requested_amount": self.requested_amount,
            "hipay_fees": self.hipay_fees,
            "actual_cashout_amount": self.actual_cashout_amount,
            "wallet_credit_hipay_result": self.credit_result,
            "wallet_withdraw_hipay_result": self.withwraw_result,
            "cashout_transaction": self.cashout_transaction,
            "used_label": self.label,
        }

    def run(self):
        self._setup_hipay_fees()
        self._initial_checks()
        self._credit_wallet()
        self._withdraw_wallet()
        self._update_balance()
        return self._generate_report()
