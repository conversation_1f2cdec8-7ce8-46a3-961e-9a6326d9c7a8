# -*- coding: utf-8 -*-
import logging
from base64 import b64encode
from decimal import Decimal

import payment_backends.hipay_tpp.references as hipay_refs
import requests
from payment_backends.common.utils import (
    RestLogsBuilder,
    logging_context,
    request_logger,
)
from payment_backends.hipay_tpp.models import HipayApiCallLog, HipayTransaction
from payment_backends.hipay_tpp.money_tracker import HipayMoneyTracker
from reference.status.transaction import TRANSACTION_STATUS_ATTENTION_REQUIRED
from reference.transaction_codes import TRANSACTION_OPERATOR_SALE, TRANSACTION_SALE

logger = logging.getLogger(__name__)

TIMEOUT = 120


class HipaySettlementAPI:
    """Minimal implem of the Settlement API to handle DB logged request."""

    def __init__(self):
        self.db_logger = RestLogsBuilder(HipayApiCallLog)

    @request_logger
    def request(
        self, url, method="GET", headers=None, params=None, timeout=60, **kwargs
    ):
        with requests.request(
            url=url, method=method, timeout=timeout, headers=headers, params=params
        ) as response:
            return response


hipay_settlement_api = HipaySettlementAPI()


def _retrieve_settlement_list_of_id(base_url, auth, date_from, date_to, max_page=100):
    """
    Yield a list of hipay settlement ids filtered between
    date_from and date_to. Dates are cut at 00:00AM
    """
    date_fmt = "%Y-%m-%d"
    url = base_url + "settlement"
    for page in range(1, max_page + 1):
        response = hipay_settlement_api.request(
            url=url,
            params={
                "date_from": date_from.strftime(date_fmt),
                "date_to": date_to.strftime(date_fmt),
                "page": page,
            },
            headers={"Authorization": auth, "cache-control": "no-cache"},
            timeout=TIMEOUT,
            return_content=False,
        )
        response.raise_for_status()

        settlements = response.json().get("settlements", [])
        if not settlements:
            break
        for settlement in settlements:
            if settlement.get("settlementid") is not None:
                yield settlement["settlementid"]
    if page == max_page:
        logger.warning("Reached max page {}, might be more results".format(max_page))


def _retrieve_settlement_operations(
    base_url, settlement_id, auth, allowed_operations=None
):
    """
    Yield a list of operations as dict from an Hipay settlement id
    """
    if allowed_operations:
        allowed_operations = [value.lower() for value in allowed_operations]
    operations = hipay_settlement_api.request(
        f"{base_url}settlement/{settlement_id}/raw.json",
        headers={"Authorization": auth, "cache-control": "no-cache"},
        timeout=TIMEOUT,
    )
    for operation in operations:
        should_yield_operation = (
            not allowed_operations
            or operation.get("operation", "").lower() in allowed_operations
        )
        if should_yield_operation:
            yield operation


def reschedule_outdated_sell_transactions(hipay_transaction):
    """reschedule all failed sell transactions linked to the order"""
    sell_transactions = hipay_transaction.payment.order.transactions.filter(
        transaction_type__in=[TRANSACTION_SALE, TRANSACTION_OPERATOR_SALE],
        status=TRANSACTION_STATUS_ATTENTION_REQUIRED,
    )
    for transaction in sell_transactions:
        transaction.reschedule()


def process_sale_operation(operation, transaction, tracker):
    order = transaction.payment.order
    settled_amount = Decimal(operation["settlement_amount"])
    settlement_date = operation["date_value"]

    if order.total_amount == settled_amount:
        # single settlement for the whole order
        transaction.update_settled_amounts("full", settled_amount)
        for mo in order.merchant_orders.exclude_canceled_deleted():
            mo.actions.settle_sale_transaction(
                settlement_date, ignore_already_settled=True
            )
            tracker.set_on_escrow(mo)
        return

    # partial settlement
    operation_id = operation.get("merchant_operation_reference")
    if operation_id is None:
        return

    transaction.update_settled_amounts(operation_id, settled_amount)
    if order.total_amount == transaction.settled_amount:
        # partial settlements cover the whole order
        for mo in order.merchant_orders.exclude_canceled_deleted():
            mo.actions.settle_sale_transaction(
                settlement_date, ignore_already_settled=True
            )
            tracker.set_on_escrow(mo)
        return

    # identified partial on specific merchant order
    mo_ids = transaction.capture_operations.get(operation_id)
    if mo_ids is None:
        logger.exception(
            "Inconsistent state: expected operation ID "
            "on Hipay transaction not found"
        )
        return

    mo_ids = [int(x) for x in mo_ids]
    merchant_orders = order.merchant_orders.exclude_canceled_deleted()
    merchant_orders = merchant_orders.filter(pk__in=mo_ids)
    if merchant_orders.count() != len(mo_ids):
        logger.error(
            "Operation id refers to unexisting or deleted/canceled merchant orders"
        )
    for mo in merchant_orders:
        mo.actions.settle_sale_transaction(settlement_date, ignore_already_settled=True)
        tracker.set_on_escrow(mo)


def process_settlement_operation(operation):
    try:
        transaction = HipayTransaction.objects.get(
            transaction_reference=operation["trxid"]
        )
        tracker = HipayMoneyTracker(transaction.payment)
        if operation["operation"].lower() == "sale":
            process_sale_operation(operation, transaction, tracker)
            reschedule_outdated_sell_transactions(transaction)

    except HipayTransaction.DoesNotExist:
        logger.error(
            "Could not find the HipayTransaction for operation {}".format(operation)
        )


def process_app_settlements(conf, date_from, date_to):
    """
    Retrieve the list of settlement for the given
    application auth between the specified dates
    and process them
    """
    base_url = conf.RestApiFinanceUrl
    auth = _generate_basic_auth(conf.RestApiLogin, conf.RestApiPassword)
    proccessed_count = 0
    errors = []
    with logging_context(
        db_logger=hipay_settlement_api.db_logger,
        action_name=hipay_refs.HIPAY_ACTION_GET_SETTLEMENT,
        application=conf.application,
        log_response_as_file=True,
    ):
        for set_id in _retrieve_settlement_list_of_id(
            base_url, auth, date_from, date_to
        ):
            operations = _retrieve_settlement_operations(
                base_url, set_id, auth, allowed_operations=["sale"]
            )
            for i, operation in enumerate(operations):
                try:
                    process_settlement_operation(operation)
                except Exception as err:
                    logger.exception("ignored failure on process_settlement_operation")
                    errors.append({"index": i, "error": repr(err)})
                else:
                    proccessed_count += 1
    return proccessed_count, errors


def _generate_basic_auth(user, passwd):
    """
    Generate a Basic Base64 auth token
    from user and passwd.
    """
    token = "{}:{}".format(user, passwd)
    token = b64encode(token.encode("utf-8")).decode("ascii")
    return "Basic {}".format(token)
