# -*- coding: utf-8 -*-
import logging

from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from lib.core.base_actions_manager import BaseActionsManager
from payment_backends.hipay_tpp.actions.operator_cashout import Hipay<PERSON>peratorCashout
from payment_backends.hipay_tpp.models import HipayWalletAccount
from payment_backends.hipay_tpp.utils import get_conf_for_application

from .wallet_account import WalletAccountValidator

logger = logging.getLogger(__name__)


class WalletAccountActionsManager(BaseActionsManager):
    def _get_user_api_endpoint(self):
        conf = self.model.application_configuration
        return conf.UserApiEndpoint if conf else None

    def validate_wallet_infos(self):
        return WalletAccountValidator.validate(
            self._get_user_api_endpoint(),
            self.model.api_login,
            self.model.api_password,
            self.model.user_account_id,
            self.model.user_space_id,
        )

    @classmethod
    def validate_creation(cls, merchant, application, account=None, check_psp=False):
        checker = MerchantWalletAccountChecker(merchant, application)
        checker.check_izberg_account_unicity(account=account)
        if check_psp:
            config = get_conf_for_application(application)
            checker.check_hipay_account_unicity(config.cashout_api)

    def refresh_salt(self):
        self.model.callback_salt = self.get_hipay_info()["callback_salt"]
        self.model.save(update_fields=["callback_salt"])

    def get_hipay_info(self):
        config = self.model.hipay_config
        return config.cashout_api.get_merchant_account_infos(self.model)

    def cashout(self, amount, user):
        if not self.lock(expiration_time=60, action_name="operator_cashout"):
            raise ValidationError(
                _(
                    "A cashout request is already processing on this wallet"
                    " account. Please try again later."
                )
            )
        try:
            report = HipayOperatorCashout(self.model, amount, user).run()
        except Exception:
            self.unlock(action_name="operator_cashout")
            raise
        self.unlock(action_name="operator_cashout")
        return report


class MerchantWalletAccountChecker:
    def __init__(self, merchant, application):
        self.merchant = merchant
        self.application = application

    def check_izberg_account_unicity(self, account=None):
        accounts = HipayWalletAccount.objects.filter(
            merchant_id=self.merchant.id if self.merchant else None,
            application_id=self.application.id if self.application else None,
        )
        if account is not None:
            accounts = accounts.exclude(id=account.id)
        if accounts.exists():
            if self.merchant:
                error = {"merchant": _("Merchant cannot have two Hipay accounts.")}
            else:
                error = {
                    "application": _("Application cannot have two Hipay accounts.")
                }
            raise ValidationError(error)

    def check_hipay_account_unicity(self, cashout_api):
        logger.debug("Creating account for merchant %s", self.merchant)

        first_address = self.merchant.address.exclude_hidden().first()
        if not first_address:
            msg = _(
                "Merchant {id} has no address. Please add one and try again"
            ).format(id=self.merchant.id)
            raise ValidationError(msg)

        email = first_address.contact_email
        if not email:
            msg = _(
                "Merchant address contact_email is not set. Please add one "
                "and try again"
            ).format(id=self.merchant.id)
            raise ValidationError(msg)

        r, available = cashout_api.is_email_available(email)
        if r["code"]:
            msg = _("Failed to get email availability: %(error)s") % {
                "error": r["message"]
            }
            raise ValidationError(msg)
        elif not available:
            raise ValidationError(
                _("email already used for another merchant '%(email)s'")
                % {"email": email}
            )
