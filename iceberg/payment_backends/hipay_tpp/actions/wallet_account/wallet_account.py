# -*- coding: utf-8 -*-
import logging

from apps.kyc.api.resources import MerchantIdentityDocumentResource
from apps.kyc.models import MerchantIdentityDocument, get_required_tags_for_merchant
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from payment_backends.hipay_tpp.models import HipayBankAccount
from payment_backends.hipay_tpp.references import (
    HIPAY_BUSINESS_ACCOUNT_TYPE,
    TITLES_CONSTANTS,
)
from payment_backends.hipay_tpp.utils import get_ip_address
from unidecode import unidecode

logger = logging.getLogger(__name__)


class WalletAccountValidator:
    def __init__(self, conf):
        self.conf = conf

    def validate(self, user_account_id, user_space_id):
        data = self.conf.cashout_api.get_hipay_wallet(user_account_id)
        if int(data["user_account_id"]) != user_account_id:
            return False
        if int(data["user_space_id"]) != user_space_id:
            return False
        return True


class HipayAccountCreator:
    def __init__(self, account_api, builder=None):
        """
        Create a wallet account on Hipay API

        :param account_api: Hipay account API
        """
        self.account_api = account_api
        self.builder = (
            builder
            if builder is not None
            else HipayCreationParamsBuilder(self.account_api)
        )

    def create_account(self, merchant_account):
        """
        :type merchant_account: HipayWalletAccount
        :raises: validationError
        :return: Updated merchant account
        :rtype: HipayWalletAccount
        """
        merchant = merchant_account.merchant
        if merchant is None:
            raise ValidationError("Cannot create operator wallets")

        params = self.builder.build_creation_params(merchant_account)
        response = self.account_api.create_wallet_account(params, merchant)
        updater = HipayAccountUpdater(merchant_account)
        return updater.update_from_hipay_response(params, response)


class HipayCreationParamsBuilder:
    def __init__(self, cashout_api):
        self.cashout_api = cashout_api

    def build_creation_params(self, merchant_account):
        params = self._get_account_details(merchant_account)
        params["credential[wslogin]"] = self.cashout_api.config.EWalletApiLogin
        params["credential[wspassword]"] = self.cashout_api.config.EWalletApiPassword
        return params

    def _get_account_details(self, merchant_account):
        merchant = merchant_account.merchant
        country_code = (
            merchant.company.country.code if merchant.company.country else None
        ) or merchant.application.country.code
        locale = self.cashout_api.get_locale_from_lang_code(
            country_code.lower() if country_code else None
        )
        address = self.cashout_api.get_last_active_merchant_address(merchant)
        title = address.contact_social_reason
        return {
            "email": merchant_account.email or address.contact_email,
            "civility": TITLES_CONSTANTS[title.lower()],
            "ip_address": get_ip_address(),
            "firstname": unidecode(address.contact_first_name),
            "lastname": unidecode(address.contact_last_name),
            "currency": merchant.default_currency.code,
            "locale": locale,
            "entity_code": self.cashout_api.config.EWalletApiEntity,
            "controle_type": "CREDENTIALS",
            "account_type": HIPAY_BUSINESS_ACCOUNT_TYPE,
            "pro_type": merchant_account.account_type,
            "company_name": unidecode(merchant.company.name),
            "address[address]": " ".join(
                (unidecode(address.address), unidecode(address.address2))
            ),
            "address[zipcode]": address.zipcode,
            "address[city]": unidecode(address.city),
            "address[country]": country_code,
            "timezone": merchant.timezone,
            "vat_number": merchant.company.vat_number,
        }


class HipayWalletAccountHelper:
    def __init__(self, account):
        self.account = account

    @staticmethod
    def can_create(merchant):
        has_account = merchant.hipaywalletaccount_set.exists()
        if has_account:
            return {
                "can_create": False,
                "reason": _(
                    "Merchant already has an account, "
                    "it is not possible to create a second account!"
                ),
            }
        has_address = merchant.address.exists()
        reason = (
            ""
            if has_address
            else _(
                "Merchant does not have an address which "
                "is mandatory for wallet creation!"
            )
        )
        return {"can_create": has_address, "reason": reason}

    def is_ready(self):
        merchant = self.account.merchant
        actual_docs = merchant.merchantidentitydocument_set.filter(
            status=MerchantIdentityDocument.ACTIVE
        ).values_list("document_type", flat=True)
        required_docs = [
            doc_type for doc_type in get_required_tags_for_merchant(merchant)
        ]
        missing_docs = list(set(required_docs).difference(actual_docs))
        has_bank_account = HipayBankAccount.objects.filter(
            merchant_id=merchant.id, store_bank_account__isnull=False
        ).exists()
        choices = MerchantIdentityDocumentResource().get_choices(
            MerchantIdentityDocument._meta.get_field("document_type"), missing_docs
        )
        return {
            "ready": not missing_docs and has_bank_account,
            "missing": {
                "document_types": choices,
                "hipay_bank_account": not has_bank_account,
            },
        }


class HipayAccountUpdater:
    def __init__(self, merchant_account):
        self.merchant_account = merchant_account

    def update_from_hipay_response(self, params, response):
        self.merchant_account.api_login = response["wslogin"]
        self.merchant_account.api_password = response["wspassword"]
        self.merchant_account.user_account_id = response["account_id"]
        self.merchant_account.user_space_id = response["user_space_id"]
        self.merchant_account.email = params["email"]

        return self.merchant_account
