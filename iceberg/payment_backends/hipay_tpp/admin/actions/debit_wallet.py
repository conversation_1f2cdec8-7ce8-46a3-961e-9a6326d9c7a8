# -*- coding: utf-8 -*-
from django.contrib import messages
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from payment_backends.hipay_tpp.admin.actions.transfer_amount import make_form
from payment_backends.hipay_tpp.forms.admin.debit_wallet import DebitWalletForm


def debit_wallet(modeladmin, request, queryset):
    if "do_action" not in request.POST:
        form = DebitWalletForm()
        return make_form(request, form, queryset, "debit")

    form = DebitWalletForm(request.POST)

    if not form.is_valid():
        return make_form(request, form, queryset, "debit")

    amount = form.cleaned_data["amount"]
    if amount <= 0:
        messages.error(request, "Negative amount, use the credit action.")
        return make_form(request, form, queryset, "debit")

    currency = form.cleaned_data["currency"]

    label = form.cleaned_data["label"] or (
        "Debit wallet with %s%s" % (amount, currency)
    )
    try:
        form.run_with(queryset, amount, currency, label)
    except ValidationError as e:
        messages.error(request, e.args[0])
        return make_form(request, form, queryset, "credit")

    messages.success(request, "%s transfers scheduled" % queryset.count())
    return


debit_wallet.short_description = _("Debit selected wallet(s)")
