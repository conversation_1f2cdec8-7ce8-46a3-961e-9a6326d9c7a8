# -*- coding: utf-8 -*-
from django.contrib import messages
from django.core.exceptions import ValidationError
from django.shortcuts import redirect, render
from django.urls import reverse
from django.utils.translation import gettext_lazy as _
from payment_backends.hipay_tpp.forms.admin.wallet_creation import (
    CreateWalletForm,
    SelectTargetForm,
)


def make_form(request, form):
    return render(
        request,
        "hipay_tpp/admin/manually_create_wallet.html",
        {"title": "Create a new merchant wallet", "form": form},
    )


def step1(request):
    if "do_action" not in request.POST:
        form = SelectTargetForm()
        return make_form(request, form)
    form = SelectTargetForm(request.POST)
    if not form.is_valid():
        messages.error(request, form.errors)
        return make_form(request, form)

    return


def step2(request):
    form = CreateWalletForm(request.POST)

    if not form.is_valid():
        return make_form(request, form)

    try:
        form.save()
    except ValidationError as e:
        messages.error(request, e.args[0])
        return make_form(request, form)

    messages.success(request, "Wallet created")

    return redirect(reverse("admin:index"))


def manually_create_wallet(request):
    ret = step1(request)
    if ret is not None:
        return ret

    return step2(request)


manually_create_wallet.short_description = _("Create a new wallet (manually)")
