# -*- coding: utf-8 -*-
from django.shortcuts import render
from payment_backends.hipay_tpp.hipay_api import TransferParameter
from payment_backends.hipay_tpp.models import HipayWalletAccount
from payment_backends.hipay_tpp.utils import get_conf_for_application


def transfer_money(wallet_id, amount, currency_code, lbl):
    wallet = HipayWalletAccount.objects.get(id=wallet_id)

    tp = TransferParameter(
        wallet=wallet, amount=abs(amount), currency=currency_code, label_str=lbl
    )

    conf = get_conf_for_application(wallet.application or wallet.merchant.application)

    if amount > 0:
        conf.cashout_api.transfer_amount(tp)
    elif amount < 0:
        conf.cashout_api.transfer_amount_back(tp)


def make_form(request, form, queryset, action_name):
    return render(
        request,
        "hipay_tpp/admin/transfer_action.html",
        {
            "title": "Choose the amount to %s" % action_name,
            "objects": queryset,
            "form": form,
        },
    )
