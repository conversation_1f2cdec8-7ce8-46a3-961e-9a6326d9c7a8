# -*- coding: utf-8 -*-
from django.shortcuts import render
from payment_backends.hipay_tpp.forms.admin.read_hipay_status import DisplayHipayStatus


def make_form(request, form, objects):
    return render(
        request,
        "hipay_tpp/admin/view_data.html",
        {
            "title": "Here are all information we can fetch from <PERSON><PERSON>",
            "objects": objects,
            "form": form,
        },
    )


def view_wallet_data(modeladmin, request, wallet):
    data = wallet.actions.get_hipay_info()
    if "do_action" not in request.POST:
        form = DisplayHipayStatus(
            {
                "is_identified": data.get("identified") == 1,
                "current_balance": wallet.current_balance,
                "withdrawable_amount": wallet.withdrawable_amount,
                "bank_account_selected": wallet.bank_account_selected,
                "is_cashout_possible": wallet.is_cashout_possible,
                "callback_salt": data.get("callback_salt"),
                "callback_url": data.get("callback_url"),
                "raw_data": data,
            }
        )
        return make_form(request, form, [wallet])

    return
