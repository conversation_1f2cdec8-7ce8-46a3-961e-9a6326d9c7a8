# -*- coding: utf-8 -*-
import time

from django.contrib import messages
from django.core.exceptions import ValidationError
from django.shortcuts import render
from django.utils.translation import gettext_lazy as _
from payment_backends.hipay_tpp.forms.admin.withdraw_wallet import WithdrawWalletForm
from payment_backends.hipay_tpp.hipay_api import TransferParameter
from payment_backends.hipay_tpp.models import HipayWalletAccount
from payment_backends.hipay_tpp.utils import get_conf_for_application


def withdraw_money(wallet_id, amount, currency_code, lbl):
    wallet = HipayWalletAccount.objects.get(id=wallet_id)

    tp = TransferParameter(
        wallet=wallet, amount=abs(amount), currency=currency_code, label_str=lbl
    )

    conf = get_conf_for_application(wallet.application or wallet.merchant.application)

    if amount > 0:
        return conf.cashout_api.withdraw(tp)


def make_form(request, form, queryset):
    return render(
        request,
        "hipay_tpp/admin/withdraw_action.html",
        {"title": "Choose the amount to withdraw", "objects": queryset, "form": form},
    )


def withdraw_wallet(modeladmin, request, queryset):
    if "do_action" not in request.POST:
        form = WithdrawWalletForm()
        return make_form(request, form, queryset)

    form = WithdrawWalletForm(request.POST)

    if queryset.count() > 1:
        messages.error("Action unavailable for multiple wallets.")
        return make_form(request, form, queryset)

    if not form.is_valid():
        return make_form(request, form, queryset)

    amount = form.cleaned_data["amount"]
    if amount <= 0:
        messages.error(request, "Negative amount impossible for action 'Withdraw'.")
        return make_form(request, form, queryset)

    currency = form.cleaned_data["currency"]

    wallet = queryset.first()
    label = form.cleaned_data["label"] or (
        "Withdrawal of {amount}{currency} from [{merchant_id}] "
        "{merchant_name}'s account to bank account".format(
            amount=amount,
            currency=currency,
            merchant_id=wallet.merchant_id,
            merchant_name=wallet.owner_display_name,
        )
    )

    try:
        result = form.run_with(queryset, amount, currency, label)
    except ValidationError as e:
        messages.error(request, "%s (timestamp %s)" % (e.args[0], time.time()))
        return make_form(request, form, queryset)

    messages.success(
        request,
        "withdrawal asked (code %(code)s): %(description)s - "
        "transaction public id: %(transaction_public_id)s"
        % {
            "code": result["code"],
            "description": result["message"],
            "transaction_public_id": getattr(result, "transaction_public_id", ""),
        },
    )
    return


withdraw_wallet.short_description = _("Withdraw from selected wallet")
