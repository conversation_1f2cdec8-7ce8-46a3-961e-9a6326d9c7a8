# -*- coding: utf-8 -*-
from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from mp_utils.admin import RawIDModelAdmin
from payment_backends.hipay_tpp.models.application_configuration import (
    HipayAppConfiguration,
)


class HipayAppConfigurationAdmin(RawIDModelAdmin):
    search_fields = (
        "application__id",
        "application__name",
        "application__namespace",
        "configuration_description",
    )
    list_display = (
        "id",
        "application",
        "active",
        "staging",
        "cashin_mode",
        "cashout_enabled",
        "eci",
        "Enable3ds",
        "payment_form_template",
    )
    list_filter = ("active",)

    fieldsets = [
        (
            _("Environment configuration"),
            {
                "fields": [
                    "application",
                    "configuration_description",
                    "active",
                    "staging",
                    "currency",
                    "country",
                    "payment_methods",
                    "offline_refund",
                ]
            },
        ),
        (
            _("Cash-in method configuration"),
            {
                "fields": [
                    "cashin_mode",
                    "RestApiLogin",
                    "RestApiPassword",
                    "RestApiPassphrase",
                    "eci",
                    "Enable3ds",
                    "single_payment",
                ]
            },
        ),
        (
            _("Cash-out method configuration"),
            {
                "fields": [
                    "cashout_enabled",
                    "EWalletApiEntity",
                    "EWalletApiLogin",
                    "EWalletApiPassword",
                    "account_id",
                ]
            },
        ),
        (
            _("Payment page customization"),
            {
                # TODO: payment_products_categories and marketplace_payment_css_url
                #  deprecated with Hosted page V2
                "fields": [
                    "payment_form_template",
                    "display_payment_method_selector",
                    "payment_products",
                    "payment_products_categories",
                    "marketplace_payment_css_url",
                    "payment_page_css",
                    "merchant_display_name",
                ]
            },
        ),
        (
            _("Post payment redirections"),
            {
                "fields": [
                    "accept_url",
                    "decline_url",
                    "pending_url",
                    "exception_url",
                    "cancel_url",
                ]
            },
        ),
    ]
    # TODO : removing marketplace_payment_css_url after migration of hosted page V2
    readonly_fields = ("marketplace_payment_css_url", "merchant_display_name")


admin.site.register(HipayAppConfiguration, HipayAppConfigurationAdmin)
