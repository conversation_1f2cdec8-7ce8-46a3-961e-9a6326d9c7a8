# -*- coding: utf-8 -*-
from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django_object_actions import DjangoObjectActions
from mp_utils.admin import RawIDModelAdmin
from payment_backends.hipay_tpp.forms.bank_account_creation import (
    HipayBankAccountCreationForm,
)
from payment_backends.hipay_tpp.models.bank_account import HipayBankAccount


class HipayBankAccountAdmin(DjangoObjectActions, RawIDModelAdmin):
    list_display = (
        "id",
        "created_on",
        "last_update_on",
        "synchro_status",
        "store_bank_account",
        "merchant",
        "app_bank_account",
        "application",
        "country",
        "iban",
        "swift",
        "owner",
        "active",
    )
    add_form = HipayBankAccountCreationForm
    add_fieldsets = (
        (
            None,
            {
                "fields": (
                    "application",
                    "merchant",
                    "identity_document",
                    "kyc_information",
                    "store_bank_account",
                    "app_bank_account",
                ),
            },
        ),
    )
    change_actions = ("refresh_submission_status",)

    def get_form(self, request, obj=None, **kwargs):
        """
        Use special form during user creation
        """
        defaults = {}
        if obj is None:
            defaults["form"] = self.add_form
        defaults.update(kwargs)
        return super(HipayBankAccountAdmin, self).get_form(request, obj, **defaults)

    def refresh_submission_status(self, request, obj):
        obj.actions.refresh()

    refresh_submission_status.label = _("Resync status")
    refresh_submission_status.short_description = _(
        "Refresh bank account submission synchronization status."
    )


admin.site.register(HipayBankAccount, HipayBankAccountAdmin)
