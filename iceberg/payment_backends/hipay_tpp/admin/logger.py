# -*- coding: utf-8 -*-

from django.contrib import admin
from django.utils.html import format_html
from ims.storage import create_s3_presigned_url
from mp_utils.admin import RawIDModelAdmin
from payment_backends.hipay_tpp.models import HipayApiCallLog
from storages.backends.s3boto3 import S3Boto3StorageFile


class HipayRestLogsAdmin(RawIDModelAdmin):
    model = HipayApiCallLog
    list_display = (
        "action",
        "created_on",
        "application",
        "merchant",
        "payment",
        "order_id",
        "status_code",
    )
    list_filter = (
        "action",
        "status_code",
        "created_on",
    )
    search_fields = (
        "payment__id",
        "payment__order__id",
        "merchant__id",
        "application__id",
        "application__name",
        "application__namespace",
        "target_url",
    )
    readonly_fields = (
        "order_id",
        "created_on",
        "response_file_signed_url",
    )

    def order_id(self, obj):
        if obj.payment is not None and obj.payment.order is not None:
            return obj.payment.order.id_number
        return None

    def application_name(self, obj):
        if obj.payment:
            return obj.payment.application
        elif obj.merchant:
            return obj.merchant.application

    def response_file_signed_url(self, obj):
        if not obj.response_file.name:
            return ""

        if isinstance(obj.response_file.file, S3Boto3StorageFile):
            s3_obj = obj.response_file.file.obj
            url = create_s3_presigned_url(
                s3_obj.bucket_name, s3_obj.key, 600  # 10 minutes
            )
        else:
            url = obj.response_file.url
        return format_html(f'<a href="{url}" target=_blank>Download response file</a>')

    response_file_signed_url.short_description = "Response file signed url"

    fieldsets = (
        (
            None,
            {
                "fields": (
                    "action",
                    "payment",
                    "application",
                    "merchant",
                    "created_on",
                ),
            },
        ),
        (
            "Request",
            {
                "fields": (
                    "target_url",
                    "method",
                    "headers",
                    "data",
                ),
            },
        ),
        (
            "Response",
            {
                "fields": (
                    "response_body",
                    "response_file",
                    "response_file_signed_url",
                    "status_code",
                    "response_time",
                ),
            },
        ),
    )


admin.site.register(HipayApiCallLog, HipayRestLogsAdmin)
