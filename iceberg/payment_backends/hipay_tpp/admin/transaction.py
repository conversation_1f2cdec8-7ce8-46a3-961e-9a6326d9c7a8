# -*- coding: utf-8 -*-
from django.contrib import admin
from mp_utils.admin import RawIDModelAdmin
from payment_backends.hipay_tpp.models.transaction import HipayTransaction


class HipayTransactionAdmin(RawIDModelAdmin):
    list_display = (
        "id",
        "dirty_capture",
        "created_on",
        "last_update_on",
        "payment",
        "transaction_reference",
        "error_code",
        "message",
        "reason",
        "state",
        "status",
        "test",
        "hipay_orderid",
        "forward_url",
        "eci_level",
        "authorized_amount",
        "captured_amount",
        "attempt_id",
        "ip_address",
        "payment_product",
        "payment_method_brand",
        "payment_method_expiry",
        "payment_method_token",
        "payment_method_card_holder",
        "payment_method_country",
        "payment_method_issuer_bank",
        "payment_method_pan",
    )
    list_filter = ("dirty_capture",)
    search_fields = (
        "order__id",
        "payment__id",
        "order__application__id",
        "order__application__name",
        "order__application__namespace",
        "payment__application__id",
        "payment__application__name",
        "payment__application__namespace",
    )


admin.site.register(HipayTransaction, HipayTransactionAdmin)
