# -*- coding: utf-8 -*-

from django.contrib import admin, messages
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django_object_actions import DjangoObjectActions
from mp_utils.admin import RawIDModelAdmin
from payment_backends.hipay_tpp.models.wallet_account import HipayWalletAccount
from payment_backends.hipay_tpp.references import ACCOUNT_CREATION_FAILED
from payment_backends.hipay_tpp.tasks import HipayPostCreationTasks
from requests.exceptions import HTTPError, RequestException

from .actions import credit_wallet, debit_wallet, view_wallet_data, withdraw_wallet


class HipayWalletAccountAdmin(DjangoObjectActions, RawIDModelAdmin):
    search_fields = (
        "id",
        "merchant__id",
        "merchant__name",
        "application__id",
        "application__name",
        "application__namespace",
        "email",
        "user_account_id",
    )

    readonly_fields = (
        "email",
        "synchro_status",
        "synchro_error",
    )
    list_display = (
        "id",
        "owner_display_name",
        "user_account_id",
        "merchant",
        "application",
        "email",
    )
    readonly_fields = ("callback_salt",)

    actions = [credit_wallet, debit_wallet]
    change_actions = (
        "retry_creation",
        "credit_single_wallet",
        "debit_single_wallet",
        "withdraw_single_wallet",
        "view_wallet_data",
        "refresh_salt",
    )

    fieldsets = [
        (
            _("IZBERG relations"),
            {
                "fields": [
                    "application",
                    "merchant",
                    "email",
                ]
            },
        ),
        (
            _("Hipay creation"),
            {
                "fields": [
                    "synchro_status",
                    "synchro_error",
                ]
            },
        ),
        (
            _("Hipay API infos"),
            {
                "fields": [
                    "account_type",
                    "user_account_id",
                    "user_space_id",
                    "websiteIds",
                    "api_login",
                    "api_password",
                    "callback_salt",
                ]
            },
        ),
    ]

    def retry_creation(self, request, obj):
        if obj.synchro_status != ACCOUNT_CREATION_FAILED:
            messages.error(request, "Can only retry if synchro status is failed")
            return
        try:
            obj.retry()
            HipayPostCreationTasks(obj).create_hipay_account()
        except ValidationError as e:
            messages.error(request, str(e))

    retry_creation.label = _("Retry Hipay creation")
    retry_creation.short_description = _(
        "Call again Hipay API to create merchant's account"
    )

    def debit_single_wallet(self, request, obj):
        queryset = HipayWalletAccount.objects.filter(id=obj.id)
        return debit_wallet(self, request, queryset)

    debit_single_wallet.label = _("Debit wallet")
    debit_single_wallet.short_description = _(
        "Take money from wallet and move it to the technical account."
    )

    def credit_single_wallet(self, request, obj):
        queryset = HipayWalletAccount.objects.filter(id=obj.id)
        return credit_wallet(self, request, queryset)

    credit_single_wallet.label = _("Credit wallet")
    credit_single_wallet.short_description = _(
        "Take money from technical account and move it to the wallet."
    )

    def withdraw_single_wallet(self, request, obj):
        queryset = HipayWalletAccount.objects.filter(id=obj.id)
        wallet = queryset.first()
        if wallet is None or wallet.withdrawable_amount == 0:
            messages.error(
                request, "No you have no money. You're doing things the wrong way man."
            )
            return
        return withdraw_wallet(self, request, queryset)

    withdraw_single_wallet.label = _("Withdraw from wallet (can't be undone)")
    withdraw_single_wallet.short_description = _(
        "Take money from wallet account and move it to the bank account."
    )

    def view_wallet_data(self, request, obj):
        try:
            return view_wallet_data(self, request, obj)
        except HTTPError as err:
            messages.error(
                request,
                f"Could not retreive hipay data. The following error "
                f"occured: {err}",
            )

    view_wallet_data.label = _("View Hipay data")
    view_wallet_data.short_description = _(
        "Connect and display all information we can fetch from Hipay API."
    )

    def refresh_salt(self, request, obj):
        try:
            obj.actions.refresh_salt()
        except RequestException as e:
            messages.add_message(
                request, messages.ERROR, "Unable to refresh wallet salt: {}".format(e)
            )
        else:
            messages.add_message(
                request, messages.INFO, "Wallet salt refreshed successfully"
            )

    refresh_salt.label = "Refresh salt"
    refresh_salt.short_description = "Fetch the wallet salt from Hipay"


admin.site.register(HipayWalletAccount, HipayWalletAccountAdmin)
