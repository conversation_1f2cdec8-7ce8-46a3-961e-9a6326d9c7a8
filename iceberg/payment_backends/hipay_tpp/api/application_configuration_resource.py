# -*- coding: utf-8 -*-
from urllib.parse import urlparse

from apps.currencies.api import CurrencyResource
from apps.currencies.models import Currency
from apps.tasking.models import ProcessHipaySettlementAction
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from ims.api.decorators import api_view
from ims.api.fields import LegacyForeignKey, LegacyManyToManyField
from ims.api.resources import MPModelResource
from lib.api.auth.hipay_auths import HipayApplicationConfigurationResourceAuthorization
from payment_backends.hipay_tpp.models.application_configuration import (
    HipayAppConfiguration,
)
from payment_backends.hipay_tpp.utils import obfuscate
from tastypie import fields
from tastypie.constants import ALL


class HipayAppConfigurationResource(MPModelResource):
    staging = fields.CharField(attribute="staging", null=True, blank=True)

    application = LegacyForeignKey(
        "apps.ice_applications.api.ApplicationResource",
        "application",
        backbone_friendly=True,
    )

    # TODO : to be removed after migration of hosted page V2
    marketplace_payment_css = fields.CharField(
        attribute="marketplace_payment_css_url", readonly=True, null=True
    )

    country = LegacyForeignKey(
        "apps.address.api.resources.CountryResource",
        attribute="country",
        backbone_friendly=True,
    )
    currency = LegacyForeignKey(
        "apps.currencies.api.resources.CurrencyResource",
        attribute="currency",
        backbone_friendly=True,
    )

    payment_methods = LegacyManyToManyField(
        "apps.payment.api.resources.PaymentMethodResource",
        "payment_methods",
        backbone_friendly=True,
        null=True,
        blank=True,
    )

    # Set date fields with null explicitly to avoid issue
    # https://github.com/django-tastypie/django-tastypie/issues/1199
    created_on = fields.DateTimeField("created_on", readonly=True, null=True)
    last_modified = fields.DateTimeField("last_modified", readonly=True, null=True)

    class Meta:
        queryset = HipayAppConfiguration.objects.all()
        authorization = HipayApplicationConfigurationResourceAuthorization()
        resource_name = "hipay_application_configuration"
        always_return_data = True
        filtering = {
            "id": ALL,
            "application": "exact",
        }

        editable_fields = [
            "configuration_description",
            "RestApiLogin",
            "RestApiPassword",
            "RestApiPassphrase",
            "EWalletApiEntity",
            "EWalletApiLogin",
            "EWalletApiPassword",
            "account_id",
            "authentication_indicator",
            "display_payment_method_selector",
            "cashout_enabled",
            "payment_form_template",
            "single_payment",
            "accept_url",
            "decline_url",
            "pending_url",
            "exception_url",
            "cancel_url",
            "cashin_mode",
            "staging",
            "country",
            "currency",
            "payment_methods",
        ]
        creatable_fields = ["application"] + editable_fields

        doc = {
            "show": True,
            "order": 1,
            "doc": (
                "Hipay application configuration model : stores config "
                "for a given application-environment couple."
            ),
        }

    def prepend_urls(self):
        return [
            self.make_pk_url(
                "retrigger-settlements",
                "retrigger_settlements",
                "hipay_application_configuration_retrigger_settlements",
            ),
        ]

    def dehydrate_RestApiPassword(self, bundle, *args, **kwargs):  # noqa
        return obfuscate(bundle.obj.RestApiPassword)

    def dehydrate_RestApiPassphrase(self, bundle, *args, **kwargs):  # noqa
        return obfuscate(bundle.obj.RestApiPassphrase)

    def dehydrate_EWalletApiPassword(self, bundle, *args, **kwargs):  # noqa
        return obfuscate(bundle.obj.EWalletApiPassword)

    def hydrate_obfuscated_field(self, field_attr_name, bundle, *args, **kwargs):
        """
        Dehydrate happens on outputting data while hydrate occurs on input

        Dehydrate takes the real password & replaces it with stars

        When front-end guy send my obfuscated field back, I get an obfuscated
        password on my input. Comparing obfuscated password with a clear
        password on DB would say: different data --> replace

        Because I get obfuscated data on input, I obfuscate DB data to see
        if both obfuscated data match. If they do, client didn't change anything
        and i assume the form is being returned as-is.

        In this specific case I want to act as noting ever happened so I remove
        obfuscated password from input to avoid updating my model.
        """
        if field_attr_name not in bundle.data:
            return bundle

        previous = obfuscate(getattr(bundle.obj, field_attr_name))
        if bundle.data[field_attr_name] == previous:
            del bundle.data[field_attr_name]

        return bundle

    def hydrate_RestApiPassword(self, bundle, *args, **kwargs):
        return self.hydrate_obfuscated_field("RestApiPassword", bundle)

    def hydrate_RestApiPassphrase(self, bundle, *args, **kwargs):
        return self.hydrate_obfuscated_field("RestApiPassphrase", bundle)

    def hydrate_EWalletApiPassword(self, bundle, *args, **kwargs):
        return self.hydrate_obfuscated_field("EWalletApiPassword", bundle)

    def hydrate_currency(self, bundle):
        """
        Convert currency name or resource_uri to Currency object
        """

        if "currency" not in bundle.data:
            return bundle

        currency = bundle.data["currency"]

        if not currency:
            raise ValidationError({"currency": "This field cannot be null."})

        uri = CurrencyResource.get_resource_uri_prefix()

        try:
            # use parse.path to be able to handle absolute resource_uri
            # values. If you get the raw currency code, url-parse will also put
            # it in the .path attribute. That's a super cool line ^^
            path = urlparse(currency).path

            if path.startswith(uri):
                currency = Currency.get_object_from_uri(currency)
            else:
                currency = Currency.objects.get(code=currency)

        except Currency.DoesNotExist:
            raise ValidationError(
                {
                    "currency": _("Reference not found '{currency}'.").format(
                        currency=currency
                    ),
                }
            )

        except (ValueError, AttributeError):
            raise ValidationError(
                {
                    "currency": _(
                        "Parsing error: expected resource_uri or currency-code."
                    ),
                }
            )

        else:
            bundle.data["currency"] = currency

        return bundle

    def hydrate_staging(self, bundle, *args, **kwargs):
        if "staging" not in bundle.data:
            return bundle

        staging_data = str(bundle.data["staging"]).lower()
        if staging_data in ("true", "1"):
            staging_data = True
        elif staging_data in ("false", "0"):
            staging_data = False
        elif staging_data in ("null", "none"):
            staging_data = None

        bundle.data["staging"] = staging_data

        return bundle

    @api_view(method_allowed=["post"], only_for_izberg_staff=True)
    def retrigger_settlements(self, request, **kwargs):
        """
        [Doc]
        Retrigger settlements processing on a specific period
        """
        bundle = self.get_cached_object_or_raise(request, **kwargs)

        from_date = self.read_datetime_in_param(
            request, "from_date", raise_on_missing=True
        )
        to_date = self.read_datetime_in_param(request, "to_date", raise_on_missing=True)
        if not from_date < to_date:
            raise ValidationError(
                {
                    "from_date": _("Must be prior to 'to_date'"),
                }
            )
        if not to_date < timezone.now():
            raise ValidationError({"to_date": _("Must be in the past")})

        async_action = ProcessHipaySettlementAction(
            application_id=bundle.obj.application.id,
        )
        async_action.initialize(bundle.obj.id, from_date, to_date)
        return async_action.run_async_task(return_response=True)
