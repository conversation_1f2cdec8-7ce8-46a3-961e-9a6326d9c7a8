# -*- coding: utf-8 -*-
import logging

from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from ims.api.decorators import api_view
from ims.api.fields import LegacyForeignKey
from ims.api.resources import MPModelResource
from lib.api.auth.hipay_auths import HipayBankAccountResourceAuthorization
from payment_backends.hipay_tpp.actions import BankAccountSelectionParamsParser
from payment_backends.hipay_tpp.models import HipayBankAccount
from tastypie import fields
from tastypie.constants import ALL

from ..tasks import select_bank_account

logger = logging.getLogger(__name__)


class HipayBankAccountResource(MPModelResource):
    state_display = fields.CharField(
        attribute="get_synchro_status_display",
        help_text="Deprecated. Use synchro_status_display instead.",
        readonly=True,
    )
    synchro_status_display = fields.CharField(
        attribute="get_synchro_status_display", readonly=True
    )
    # rib fields can be null in case the HipayWalletAccount is brand new and
    # is not linked to any MerchantBankAccount.
    country = fields.CharField(attribute="country", readonly=True, null=True)
    owner = fields.CharField(attribute="owner", readonly=True, null=True)
    iban = fields.CharField(attribute="iban", readonly=True, null=True)
    swift = fields.CharField(attribute="swift", readonly=True, null=True)
    bank_account = LegacyForeignKey(
        "apps.stores.api.resources.MerchantBankAccountResource",
        "store_bank_account",
        null=True,
        readonly=True,  # updatable with select_bank_account route
        backbone_friendly=True,
    )
    merchant = LegacyForeignKey(
        "apps.stores.api.resources.MerchantResource", "merchant", backbone_friendly=True
    )
    identity_document = LegacyForeignKey(
        "apps.kyc.api.resources.MerchantIdentityDocumentResource",
        "identity_document",
        null=True,
        backbone_friendly=True,
    )
    kyc_information = LegacyForeignKey(
        "apps.kyc.api.resources.KycInformationResource",
        "kyc_information",
        null=True,
        backbone_friendly=True,
    )

    synchro_error = fields.DictField(
        attribute="synchro_error", readonly=True, null=True
    )

    class Meta:
        queryset = HipayBankAccount.objects.all()
        resource_name = "hipay_bank_account"
        authorization = HipayBankAccountResourceAuthorization()
        always_return_data = True

        doc = {"show": True, "order": 51, "doc": "Hipay bank account binding"}

        filtering = {
            "id": ALL,
            "bank_account": "exact",
            "state": ALL,
            "application": ALL,
            "created_on": ALL,
            "last_update_on": ALL,
        }
        detail_allowed_methods = ["get", "put", "patch"]
        editable_fields = HipayBankAccount.EDITABLE_FIELDS
        creatable_fields = HipayBankAccount.CREATABLE_FIELDS
        excludes = []

    def prepend_urls(self):
        return [
            self.make_url("getOrCreate", "get_or_create", "api_hipay_bank_account_get"),
            self.make_pk_url(
                "refresh_status",
                "refresh_account_status",
                "api_hipay_bank_account_refresh",
            ),
            self.make_pk_url(
                "select_bank_account",
                "select_bank_account",
                "api_hipay_bank_account_select",
            ),
        ]

    @api_view(method_allowed=["post"])
    def refresh_account_status(self, request, **kwargs):
        """
        [Doc] Refresh bank account status by connecting to hipay server and
        asking for bank info status.
        """
        bundle = self.get_cached_object_or_raise(request, **kwargs)
        # get linked object and call refresh on it
        hipay_bank_account = bundle.obj
        hipay_bank_account.actions.refresh()
        # generated update data dict & return JSON content for it.
        bank_account_dict = self.full_dehydrate(bundle)
        return self.create_response(request, bank_account_dict)

    @api_view(method_allowed=["post"])
    def select_bank_account(self, request, **kwargs):
        """
        [Doc]
        Select bank account to bind with Hipay wallet. Selected bank account
        will be used to cash merchant-earnings out.
        """
        bundle = self.get_cached_object_or_raise(request, **kwargs)
        form = self.deserialize_form(request)
        hipay_bank_account = bundle.obj
        account, document = BankAccountSelectionParamsParser.parse_select_form(
            hipay_bank_account, form
        )
        select_bank_account.delay(
            hipay_bank_account.id,
            account.id,
            str(document.id),  # cast to str needed to handle UUIDs
            hipay_bank_account.application_id,
        )
        return self.create_response(request, "OK")

    @api_view(method_allowed=["get"])
    def get_or_create(self, request, **kwargs):
        """
        [Doc]
        Get or create bank account.
        Return bank account related to requested merchant id.
        """
        form = self.deserialize_form(request)
        try:
            merchant_id = int(form["merchant_id"])
        except (KeyError, ValueError, TypeError):
            raise ValidationError(
                {"merchant_id": _("Invalid or missing GET parameter")}
            )

        obj = HipayBankAccount.objects.get_for_merchant(merchant_id=merchant_id)

        bundle = self.build_bundle(obj=obj, request=request)
        bundle = self.full_dehydrate(bundle)
        bundle = self.alter_detail_data_to_serialize(request, bundle)
        return self.create_response(request, bundle)
