# -*- coding: utf-8 -*-
import logging

from apps.stores.models.store_models import Merchant
from apps.transactions.api.resources import TransactionResource
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from ims.api.decorators import api_view
from ims.api.fields import LegacyForeignKey
from ims.api.resources import MPModelResource
from lib.api.auth.hipay_auths import HipayWalletAccountResourceAuthorization
from payment_backends.hipay_tpp.actions.wallet_account.wallet_account import (
    HipayWalletAccountHelper,
)
from payment_backends.hipay_tpp.models.wallet_account import HipayWalletAccount
from payment_backends.hipay_tpp.references import ACCOUNT_CREATION_FAILED
from payment_backends.hipay_tpp.tasks import (
    create_hipay_account,
    validate_hipay_account,
)
from reference.generic_error_codes import MISSING_REQUIRED_PARAMETER
from tastypie import fields
from tastypie.constants import ALL, ALL_WITH_RELATIONS
from tastypie.exceptions import ImmediateHttpResponse, NotFound
from tastypie.http import HttpAccepted, HttpBadRequest, HttpCreated

logger = logging.getLogger(__name__)


class HipayWalletAccountResource(MPModelResource):
    email = fields.CharField(attribute="email")

    is_identified = fields.BooleanField(
        attribute="is_identified", use_in="detail", null=True, readonly=True
    )

    merchant = LegacyForeignKey(
        "apps.stores.api.resources.MerchantResource",
        attribute="merchant",
        backbone_friendly=True,
        null=True,
    )

    application = LegacyForeignKey(
        "apps.ice_applications.api.ApplicationResource",
        attribute="application",
        backbone_friendly=True,
        null=True,
    )

    owner_display_name = fields.CharField(
        attribute="owner_display_name", readonly=True, null=True
    )

    selected_bank_account = LegacyForeignKey(
        "payment_backends.hipay_tpp.api.resources.HipayBankAccountResource",
        attribute="bank_account_selected",
        readonly=True,
        null=True,
        use_in="detail",
        backbone_friendly=True,
        full=True,
    )

    synchro_error = fields.DictField(
        attribute="synchro_error", readonly=True, null=True
    )

    synchro_status_display = fields.CharField(
        attribute="get_synchro_status_display", readonly=True
    )

    class Meta:
        queryset = HipayWalletAccount.objects.all()
        authorization = HipayWalletAccountResourceAuthorization()
        resource_name = "hipay_merchant_account"
        always_return_data = True
        filtering = {
            "id": ALL,
            "email": "exact",
            "merchant": ALL_WITH_RELATIONS,
            "application": ALL_WITH_RELATIONS,
            "user_account_id": ALL,
        }

        creatable_fields = [
            "application",
            "merchant",
            "account_type",
            "email",
        ]
        fields = [
            "id",
            "created_on",
            "last_update_on",
            "api_login",
            "api_password",
            "user_account_id",
            "user_space_id",
            "websiteIds",
            "synchro_status",
            "synchro_error",
        ] + creatable_fields
        excludes = []
        editable_fields = [
            "api_login",
            "api_password",
            "user_account_id",
            "user_space_id",
            "websiteIds",
        ]

    def prepend_urls(self):
        return [
            self.make_pk_url(
                "retry_hipay_creation", "retry_creation_view", "retry_creation_url"
            ),
            self.make_pk_url("is_ready", "is_ready_view", "is_ready_url"),
            self.make_url(
                "can_create", "can_create_view", "hipay_merchant_account_can_create"
            ),
            self.make_url(
                "is_set",
                "is_set_view",
                "hipay_merchant_account_creation_existence_check",
            ),
            self.make_url(
                "create_from_existing",
                "create_from_existing_view",
                "create_from_existing_url",
            ),
            self.make_pk_url(
                "cashout", "operator_cashout_view", "hipay_operator_cashout_view"
            ),
            self.make_pk_url(
                "current-balance",
                "get_current_balance_view",
                "hipay_get_current_balance_view",
            ),
        ]

    def _get_merchant_from_qs(self, request):
        merchant_id = request.GET.get("merchant_id") or request.GET.get("merchant")
        if merchant_id is None:
            raise ValidationError(
                _("missing get parameter 'merchant_id'"),
                code=MISSING_REQUIRED_PARAMETER,
            )
        try:
            merchant = Merchant.objects.get(id=merchant_id)
        except Merchant.DoesNotExist:
            raise NotFound(_("Merchant not found for id %s") % merchant_id)
        return merchant

    def obj_update(self, bundle, skip_errors=False, **kwargs):
        bundle = super(HipayWalletAccountResource, self).obj_update(
            bundle, skip_errors, **kwargs
        )
        validate_hipay_account.delay(bundle.obj.id, bundle.obj.application_id)
        return bundle

    @api_view(method_allowed=["post"])
    def create_from_existing_view(self, request, **kwargs):
        """
        [Doc]
        Allow creating wallet resource from existing Hipay account data:
        - user_account_id
        - user_space_id
        - api_login
        - api_password
        - email
        and Izberg data:
        - merchant and/or application
        """
        data = self.deserialize_form(request)
        bundle = self.build_bundle(data=data, request=request)
        bundle = self.obj_create(bundle)

        bundle = self.full_dehydrate(bundle)
        bundle = self.alter_detail_data_to_serialize(request, bundle)
        return self.create_response(request, bundle, response_class=HttpCreated)

    @api_view(method_allowed=["post"])
    def retry_creation_view(self, request, **kwargs):
        """
        [Doc]
        If synchronization failed (cf. synchro_status field), it allows
        retrying synchronization with Hipay API.
        """
        bundle = self.get_cached_object_or_raise(request, **kwargs)
        if bundle.obj.synchro_status != ACCOUNT_CREATION_FAILED:
            msg = _(
                "Cannot retry synchronization if synchronization status "
                "is not {synchro_status}, "
                "current status is {current_status}"
            ).format(
                synchro_status=ACCOUNT_CREATION_FAILED,
                current_status=bundle.obj.synchro_status,
            )
            raise ImmediateHttpResponse(HttpBadRequest(msg))
        bundle.obj.retry()
        create_hipay_account.delay(bundle.obj.id, bundle.obj.application_id)

        body = {"message": _("Creation in progress!")}
        return self.create_response(request, body, response_class=HttpAccepted)

    @api_view(method_allowed=["get"])
    def is_ready_view(self, request, **kwargs):
        """
        [Doc]
        Check if account is ready for validation in Hipay.
        (cf query parameter merchant_id).
        """
        bundle = self.get_cached_object_or_raise(request, **kwargs)

        data = HipayWalletAccountHelper(bundle.obj).is_ready()
        return self.create_response(request, data)

    @api_view(method_allowed=["get"])
    def can_create_view(self, request, **kwargs):
        """
        [Doc]
        Check if a given merchant (cf. query parameter "merchant_id")
        can create a new account.
        """
        merchant = self._get_merchant_from_qs(request)
        data = HipayWalletAccountHelper.can_create(merchant)
        return self.create_response(request, data)

    @api_view(method_allowed=["get"])
    def is_set_view(self, request, **kwargs):
        """
        [Doc]
        Helper to check if a given merchant (cf query parameter merchant_id),
        already as an Hipay account.
        """
        merchant = self._get_merchant_from_qs(request)
        try:
            found = HipayWalletAccount.objects.get(merchant=merchant)
        except HipayWalletAccount.DoesNotExist:
            found = object()
        # make parameters dict
        existence_d = {
            "id": getattr(found, "id", None),
            "isSet": hasattr(found, "id"),
        }

        return self.create_response(request, existence_d)

    def _get_uri(self, resource, request, instance):
        bundle = resource.build_bundle(obj=instance, request=request)
        return resource.get_resource_uri(bundle)

    @api_view(method_allowed=["post"], requested_action="operator_cashout")
    def operator_cashout_view(self, request, **kwargs):
        """
        [Doc]
        Helper to allow operator to process a cashout on their wallet
        """
        bundle = self.get_cached_object_or_raise(request, **kwargs)
        data = self.deserialize_form(request)
        amount = data.get("amount")
        user = request.user if hasattr(request, "user") else None
        body = bundle.obj.actions.cashout(amount, user)
        if body.get("cashout_transaction"):
            body["cashout_transaction"] = self._get_uri(
                TransactionResource(), request, body.get("cashout_transaction")
            )
        return self.create_response(request, body)

    @api_view(method_allowed=["get"], requested_action="read_detail")
    def get_current_balance_view(self, request, **kwargs):
        bundle = self.get_cached_object_or_raise(request, **kwargs)
        body = {"current_balance": bundle.obj.current_balance}
        return self.create_response(request, body)
