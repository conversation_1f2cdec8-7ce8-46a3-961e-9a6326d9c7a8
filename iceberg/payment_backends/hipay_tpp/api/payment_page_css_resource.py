# -*- coding: utf-8 -*-
from ims.api.resources import MPModelResource
from lib.api.auth.hipay_auths import HipayPaymentPageCSSResourceAuthorization
from payment_backends.hipay_tpp.models.payment_page_css import HipayPaymentPageCSS
from tastypie.resources import ALL


class HipayPaymentPageCSSResource(MPModelResource):
    class Meta:
        queryset = HipayPaymentPageCSS.objects.all()
        authorization = HipayPaymentPageCSSResourceAuthorization()
        resource_name = "hipay_payment_page_css"
        filtering = {
            "config_id": ALL,
            "id": ALL,
            "uploaded_by": ALL,
        }
        creatable_fields = [
            "config_id",
        ]
        editable_fields = []
        fields = [
            "id",
            "config_id",
            "css_source_file",
            "uploaded_by",
        ] + creatable_fields
        excludes = ["css_source_file_bkp"]
