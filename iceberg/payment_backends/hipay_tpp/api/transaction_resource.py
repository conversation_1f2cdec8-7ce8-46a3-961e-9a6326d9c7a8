# -*- coding: utf-8 -*-
import logging

from apps.payment.models import Payment
from django.conf import settings
from django.core.cache import cache
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from ims.api.decorators import api_view
from ims.api.resources import MPModelResource
from lib.api.auth.hipay_auths import HipayResourceAuthorization
from payment_backends.hipay_tpp.models.transaction import HipayTransaction
from payment_backends.hipay_tpp.processor import generate_operation_ids
from payment_backends.hipay_tpp.references import REALTIME_BANKING_PRODUCTS
from payment_backends.hipay_tpp.utils import get_payment_from_hipay_unique_id
from redis import OutOfMemoryError
from reference.generic_error_codes import MISSING_REQUIRED_PARAMETER
from tastypie import fields
from tastypie.resources import ALL, ALL_WITH_RELATIONS

logger = logging.getLogger(__name__)


ONE_HOUR = 60 * 60


class HipayTransactionResource(MPModelResource):
    escrow_delivery_settlement = fields.DictField(
        "escrow_delivery_settlement", blank=True
    )
    payment = fields.ForeignKey(
        "apps.payment.api.payment_resources.PaymentResource", "payment", null=True
    )
    order = fields.ForeignKey(
        "apps.orders.api.resources.OrderResource", "order", null=True
    )

    class Meta:
        queryset = HipayTransaction.objects.all()
        authorization = HipayResourceAuthorization()
        resource_name = "hipay_transaction"
        always_return_data = True
        filtering = {
            "id": ALL,
            "payment": ALL_WITH_RELATIONS,
            "order": ALL_WITH_RELATIONS,
            "status": ALL,
            "dirty_capture": ALL,
            "transaction_reference": ALL,
            "hipay_orderid": ALL,
            "created_on": ALL,
            "last_update_on": ALL,
        }

        creatable_fields = ["payment", "order_id", "created_on", "last_update_on"]
        editable_fields = ["escrow_delivery_settlement"]
        fields = [
            "id",
            "transaction_reference",
            "hipay_orderid",
            "dirty_capture",
            "error_code",
            "message",
            "reason",
            "state",
            "status",
            "test",
            "order_id",
            "authorized_amount",
            "captured_amount",
            "refunded_amount",
            "attempt_id",
            "ip_address",
            "payment_product",
            "payment_method_brand",
            "payment_method_expiry",
            "payment_method_token",
            "payment_method_card_holder",
            "payment_method_country",
            "payment_method_issuer_bank",
            "payment_method_pan",
            "escrow_delivery_settlement",
        ] + creatable_fields
        excludes = []

    def prepend_urls(self):
        return [
            self.make_pk_url(
                "refresh_status",
                "refresh_transaction_status",
                "api_hipay_transaction_refresh",
            ),
            self.make_url(
                "validation",
                "cashin_validation",
                "api_hipay_transaction_validation_notif_url",
            ),
            self.make_url(
                "updateOrderStatus",
                "update_order_status",
                "api_hipay_update_order_status",
            ),
        ]

    @api_view(method_allowed=["get"])
    def refresh_transaction_status(self, request, **kwargs):
        """
        Refresh transaction status by connecting to hipay server and asking for
        transaction status.
        """
        bundle = self.get_cached_object_or_raise(request, **kwargs)

        # get linked object and call refresh on it
        hipay_transaction = bundle.obj
        self.refresh_hipay_status(hipay_transaction)

        # generated update data dict & return JSON content for it.
        transaction_dict = self.full_dehydrate(bundle)
        return self.create_response(request, transaction_dict)

    def cashin_validation(self, request, **kwargs):
        """
        Refresh order & transaction status when contacted by hipay
        server-to-server notification system.
        """

        self.method_check(request, allowed=["post"])
        self.is_authenticated(request)
        self.throttle_check(request)
        self.log_throttled_access(request)

        logger.info("HIPAY payment module was notified >> %s", request.read())

        form = self.deserialize_form(request)

        transaction = self.get_hipay_transaction_from_form(form)

        cache_key = self.get_notification_cache_key(transaction, form)
        if cache.get(cache_key, False):
            return self.create_response(request, "[OK]")

        logger.info("updating transaction status for order : %s", transaction.order)

        self.update_order_from_notification(transaction, form)

        try:
            cache.set(cache_key, True, ONE_HOUR)
        except OutOfMemoryError as e:
            logger.error(
                "Out of memory error while trying to set {} cache key: {}".format(
                    cache_key, e
                )
            )
        return self.create_response(request, "[OK]")

    def update_order_from_notification(self, transaction, form):
        transaction.update_from_hipay_notif(form)
        payment_product = transaction.payment_product
        if payment_product in REALTIME_BANKING_PRODUCTS:
            mo_ids, operation_id = generate_operation_ids(
                transaction.payment.order.merchant_orders.all(), "CAPTURE"
            )
            transaction.capture_operations[operation_id] = mo_ids
        transaction.save()

        if transaction.order:
            payment_obj = transaction.order.payment
            user = transaction.order.user
        else:
            payment_obj = transaction.payment
            user = None
        previous_status = payment_obj.status
        hipay_processor = payment_obj.payment_backend_obj

        try:
            tref = self.get_tref(form)
        except ValidationError:
            tref = ""
        hipay_processor.update_payment_from_hipay_state(
            payment_obj, self.get_state(form), tref, user, lock=True
        )
        payment_obj.save()

        if previous_status == payment_obj.status:
            return
        if payment_obj.order:
            payment_obj.order.actions.try_to_emit_order_webhooks()

    def get_hipay_transaction_from_form(self, form):
        transaction = self.get_from_order_id(form) or self.get_from_tref(form)
        if transaction is not None:
            return transaction
        # last hope : getting the order by reversing the hipay unique id
        # If we get there, we'll need to create a transaction model because
        # we've been missing it until now.
        payment = self.reverse_hipay_unique_id_to_get_payment(form)
        transaction = self.create_transaction_from_form_and_payment(form, payment)
        if payment.order:
            transaction.order = payment.order
            transaction.save()

        return transaction

    def get_from_order_id(self, form):
        try:
            hipay_id = self.get_order_id(form)
        except ValidationError:
            return

        try:
            return HipayTransaction.objects.get(hipay_orderid=hipay_id)
        except HipayTransaction.DoesNotExist:
            logger.warning(
                "An expected : unknown order_id %s, trying with "
                "transaction reference.",
                hipay_id,
            )

    def get_from_tref(self, form):
        transaction_reference = self.get_tref(form)
        try:
            return HipayTransaction.objects.get(
                transaction_reference=transaction_reference
            )
        except HipayTransaction.DoesNotExist:
            logger.warning("Unknown transaction reference %s", transaction_reference)

    def reverse_hipay_unique_id_to_get_payment(self, form):
        hipay_unique_id = self.get_order_id(form)
        try:
            return get_payment_from_hipay_unique_id(hipay_unique_id)
        except Payment.DoesNotExist:
            logger.error(
                "Got notified for unknown payment-id %s",
                hipay_unique_id,
                extra={"form": form},
            )
            HIPAY_ORDERID_PREFIX = getattr(settings, "HIPAY_ORDERID_PREFIX", "")
            raise ValidationError(
                {
                    "order[id]": "Not found. Format must be {}[-payment-]"
                    "<order_id_number>".format(HIPAY_ORDERID_PREFIX)
                }
            )

    def get_single_value(self, form, key):
        value = form[key]
        if isinstance(value, list):
            value = value[0]
        return value

    def get_tref(self, form):
        try:
            return self.get_single_value(form, "transaction_reference")
        except KeyError:
            raise ValidationError(_("Missing transaction_reference field."))

    def get_order_id(self, form):
        try:
            return self.get_single_value(form, "order[id]")
        except KeyError:
            raise ValidationError({"order[id]": _("Missing mandatory field.")})

    def get_state(self, form):
        try:
            return self.get_single_value(form, "state")
        except KeyError:
            raise ValidationError({"state": _("Missing field.")})

    def get_notification_cache_key(self, transaction, form):
        status = form.get("status", "unknown")
        return "hipay-notification-%(transaction_id)s-%(status)s" % {
            "transaction_id": transaction.id,
            "status": status,
        }

    def create_transaction_from_form_and_payment(self, form, payment):
        tref = self.get_tref(form)
        transaction = HipayTransaction.objects.create(
            order_id=self.get_order_id(form),
            transaction_reference=tref,
            payment=payment,
        )
        if tref and payment.external_id is None:
            payment.external_id = tref
            payment.save()
        return transaction

    def refresh_hipay_status(self, hipay_transaction):
        hipay_transaction.refresh_status()

    def update_order_status(self, request, **kwargs):
        """
        [Doc]
        Update an order's authorization status

        @params:
            - order_id: Integer. Id of the order object to refresh (Mandatory)

        @Allowed Methods: [GET]
        @Return: Error dict
        """
        self.method_check(request, allowed=["get"])
        self.is_authenticated(request)
        self.throttle_check(request)
        self.log_throttled_access(request)
        deserialized = request.GET

        hipay_order_id = deserialized.get("order_id", None)
        if hipay_order_id is None:
            raise ValidationError(
                _("missing get parameter 'order_id'"), code=MISSING_REQUIRED_PARAMETER
            )

        transaction = HipayTransaction.objects.get(hipay_orderid=hipay_order_id)
        # if transaction.payment is not set, update it.
        if transaction.payment is None:
            # /!\ do not remove this block!
            transaction.payment = transaction.order.payment

        # this refresh method will save so it is not needed to save on previous
        # call
        self.refresh_hipay_status(transaction)
        # ask hipay backend to update order state.
        payment_backend = transaction.payment.payment_backend_obj
        payment_backend.update_payment_from_hipay_state(
            transaction.payment,
            transaction.state,
            transaction.transaction_reference,
            request.user,
            lock=True,
        )
        # That's so pessimistic! :-X
        return self.create_response(request, {"error": ""})
