# -*- coding: utf-8 -*-

from django.apps import AppConfig
from ims.core_modules import TECHNICAL_MODULE


class HipayTppConfig(AppConfig):
    name = "payment_backends.hipay_tpp"
    verbose_name = "Hipay payment backend"
    module_name = TECHNICAL_MODULE

    def ready(self):
        from apps.payment.summarizers import register_payment_summarizer

        from .summarizer import HipayTppSummarizer

        register_payment_summarizer(HipayTppSummarizer)

        from . import hooks  # noqa: F401:  ignore=unused-import
