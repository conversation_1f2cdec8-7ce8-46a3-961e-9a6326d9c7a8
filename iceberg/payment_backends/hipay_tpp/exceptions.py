# -*- coding: utf-8 -*-

from typing import Optional

from apps.ice_applications.app_conf_settings import EnableKycV2
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from ims.api.exceptions import BaseResponseException
from payment_backends.hipay_tpp.codes import HIPAY_KYC_ERROR_BASIS

from .references import (
    AMOUNT_LIMIT_EXCEEDED,
    AUTHORIZATION_ALREADY_COMPLETED,
    BANK_ACCOUNT_MISMATCH,
    BANK_ACCOUNT_NOT_SELECTED,
    BANK_ACCOUNT_NOT_VALIDATED,
    HIPAY_NOT_CONFIGURED,
    INVALID_ORDER_ID,
    MERCHANT_WALLET_MISSING,
    NO_TRANSACTION_FOUND,
    OPERATION_NOT_PERMITTED_ALREADY_CLOSED,
    PREVIOUS_CASHOUT_WAITING,
    UNAPROVISIONNED_WITHDRAWAL,
    WALLET_ACCOUNT_NOT_IDENTIFIED,
    WALLET_STILL_EMPTY_AFTER_TRANSFER,
    WITHDRAWABLE_AMOUNT_INVALID,
)


class PostCreationTaskError(Exception):
    pass


class HipayAccountCreationError(Exception):
    def __init__(self, response):
        self.response = response


class HipayImpossibleCashout(ValidationError):
    pass


class WithdrawableAmountMisMatch(HipayImpossibleCashout):
    def __init__(self):
        super(WithdrawableAmountMisMatch, self).__init__(
            _(
                "Some fees may be applied to your cashout transaction. The "
                "money is safe. Please contact the marketplace for more "
                "information."
            ),
            WITHDRAWABLE_AMOUNT_INVALID,
        )


class HipayWalletStillEmptyAfterTransfer(HipayImpossibleCashout):
    def __init__(self):
        super().__init__(
            _(
                "The Hipay wallet is still empty after transferring the money."
                "Please contact the marketplace for more information."
            ),
            WALLET_STILL_EMPTY_AFTER_TRANSFER,
        )


class HipayBankAccountNotSelected(HipayImpossibleCashout):
    def __init__(self):
        super(HipayBankAccountNotSelected, self).__init__(
            _("Bank account not selected onto hipay services."),
            BANK_ACCOUNT_NOT_SELECTED,
        )


class HipayWrongBankAccount(HipayImpossibleCashout):
    def __init__(self):
        super(HipayWrongBankAccount, self).__init__(
            _("Bank account selected mismatches the one selected on hipay."),
            BANK_ACCOUNT_MISMATCH,
        )


class HipayBankAccountNotValidated(HipayImpossibleCashout):
    def __init__(self):
        super(HipayBankAccountNotValidated, self).__init__(
            _("Bank account not validated onto hipay services yet."),
            BANK_ACCOUNT_NOT_VALIDATED,
        )


class HipayMerchantWalletNotFound(HipayImpossibleCashout):
    def __init__(self):
        super(HipayMerchantWalletNotFound, self).__init__(
            _("Merchant account not created onto hipay services."),
            MERCHANT_WALLET_MISSING,
        )


class HipayNotConfigured(ValidationError):
    def __init__(self):
        super(HipayNotConfigured, self).__init__(
            _("Hipay service is not configured on this environment."),
            HIPAY_NOT_CONFIGURED,
        )


class HipayInvalidCashoutCredentials(ValidationError):
    def __init__(self):
        super(HipayInvalidCashoutCredentials, self).__init__(
            _("Hipay cashout credentials not well configured")
        )


class NoTransactionError(ValidationError):
    def __init__(self):
        super(NoTransactionError, self).__init__(
            _("No transaction for order"), NO_TRANSACTION_FOUND
        )


class InvalidOrderId(ValidationError):
    def __init__(self):
        super(InvalidOrderId, self).__init__(
            _("Order ID not valid OR misspelled"), INVALID_ORDER_ID
        )


class AuthorizationAlreadyCompletedException(ValidationError):
    def __init__(self, message=None):
        super().__init__(
            message or _("Authorization already completed"),
            AUTHORIZATION_ALREADY_COMPLETED,
        )


class TransactionAlreadyClosedException(ValidationError):
    def __init__(self, message=None):
        super().__init__(
            message or _("Operation not permitted: transaction already closed"),
            OPERATION_NOT_PERMITTED_ALREADY_CLOSED,
        )


class CapturingTooMuchException(ValidationError):
    def __init__(self):
        super(CapturingTooMuchException, self).__init__(
            _("Capturing too much money for authorization"), AMOUNT_LIMIT_EXCEEDED
        )


class HipayPreviousCashoutOngoing(HipayImpossibleCashout):
    def __init__(self):
        super(HipayPreviousCashoutOngoing, self).__init__(
            _("Previous cashout request not terminated."), PREVIOUS_CASHOUT_WAITING
        )


class HipayUnaprovisionnedWithdrawal(HipayImpossibleCashout):
    def __init__(self):
        super(HipayUnaprovisionnedWithdrawal, self).__init__(
            _("Merchant has nothing to withdraw."), UNAPROVISIONNED_WITHDRAWAL
        )


class HipayFundsTransferError(HipayImpossibleCashout):
    pass


class HipayWithdrawalFailure(HipayImpossibleCashout):
    pass


class MerchantAccountNotIdentified(HipayImpossibleCashout):
    def __init__(self, wallet):
        message = _(
            "Account number %d not identified on hipay. Please send mail "
            "containing RIB scan + Kbis extract + ID proof to address "
            "<EMAIL> with account id %d in mail subject."
        ) % (wallet.user_account_id, wallet.user_account_id)
        super(MerchantAccountNotIdentified, self).__init__(
            message, WALLET_ACCOUNT_NOT_IDENTIFIED
        )


class HipayDocumentTypeNotNeeded(KeyError):
    def __init__(self, doc_type):
        message = _(
            "Document type #%s not needed for account identification. "
            "KYC file not sent.",
            doc_type,
        )
        super(HipayDocumentTypeNotNeeded, self).__init__(message)


class CashoutNotToBeDone(ValueError):
    def __init__(self):
        msg = _("Cashout must not be done")
        super(CashoutNotToBeDone, self).__init__(msg)


class HipayInternalError(ValueError):
    def __init__(self, message=None, code=0):
        message = message or _("Can't fetch hipay KYC statuses")
        super(HipayInternalError, self).__init__(message)
        self.code = HIPAY_KYC_ERROR_BASIS + code


class InvalidXMLError(ValidationError):
    def __init__(self, message=None):
        message = message or _("Invalid xml data received")
        super(InvalidXMLError, self).__init__(message=message)


class InvalidContentType(ValidationError):
    def __init__(self, content_type):
        message = "Invalid content-type received: {}".format(content_type)
        super(InvalidContentType, self).__init__(message=message)


class MissingMandatoryKey(ValidationError):
    def __init__(self, key):
        message = _("Missing mandatory key '{key}' in XML".format(key=key))
        super(MissingMandatoryKey, self).__init__(message=message)


class InvalidChecksumError(ValidationError):
    def __init__(self, message=None):
        message = message or _("Invalid request signature")
        super(InvalidChecksumError, self).__init__(message=message)


class UnhandledOperationError(ValidationError):
    def __init__(self, operation):
        message = _("Unhandled operation {operation}").format(operation=operation)
        super(UnhandledOperationError, self).__init__(message=message)


class WalletNotFound(ValidationError):
    def __init__(self, account_id):
        message = _("No wallet exists with account_id {account_id}").format(
            account_id=account_id
        )
        super(WalletNotFound, self).__init__(message=message)


class InvalidNotificationStatus(ValidationError):
    def __init__(self, status):
        message = _('Unhandled validation status "{status}"').format(status=status)
        super(InvalidNotificationStatus, self).__init__(message=message)


class InvalidUrlEncodedBody(ValidationError):
    def __init__(self):
        message = _("Request body is not a valid url encoded form")
        super(InvalidUrlEncodedBody, self).__init__(message=message)


class HipayMissingKYCError(ValidationError):
    def __init__(self, merchant):
        self.extra_data = {"merchant": merchant}
        if merchant.application.get_setting(EnableKycV2):
            field = "kyc_information"
        else:
            field = "identity_document"
        super(HipayMissingKYCError, self).__init__(
            {field: _("Cannot set bank account until an IBAN proof got uploaded.")}
        )


class UnexpectedHipayError(BaseResponseException):
    status = 400
    error_code = "HIPAY.UNEXPECTED_ERROR"

    def __init__(
        self,
        hipay_status: Optional[str] = None,
        hipay_message: Optional[str] = None,
        **error_context,
    ):
        self.hipay_status = hipay_status
        self.hipay_message = hipay_message
        error_context["hipay_status"] = hipay_status
        error_context["hipay_message"] = hipay_message
        hipay_status = f"[{hipay_status}] " if hipay_status else ""
        hipay_message = hipay_message or "Unknown error"
        super().__init__(
            msg=f"Unexpected Hipay Error: {hipay_status}{hipay_message}",
            **error_context,
        )


class TransactionReferenceMismatch(BaseResponseException):
    status = 400
    error_code = "HIPAY.TRANSACTION_REFERENCE_MISMATCH"

    def __init__(self, msg: Optional[str] = None, **error_context):
        msg = msg or _("Transaction reference mismatch")
        super().__init__(msg=msg, **error_context)
