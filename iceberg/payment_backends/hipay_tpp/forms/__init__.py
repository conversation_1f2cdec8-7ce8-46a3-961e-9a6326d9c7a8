from apps.ice_applications.models import Application
from payment_backends.hipay_tpp.utils import get_conf_for_application


class APIRelyingFormMixin:
    data = {}

    def init_api(self):
        self.application = self.get_application_from_form_data()
        if self.application:
            self.cashout_api = self.get_cashout_api()

    def get_cashout_api(self):
        return get_conf_for_application(self.application).cashout_api

    def get_data_application_id_value(self, data):
        if data and isinstance(data, str):
            return int(data)
        if isinstance(data, list):
            return self.get_data_application_id_value(data[0])
        return data

    def get_application_from_form_data(self):
        app_field_value = self.data.get("application")
        if isinstance(app_field_value, Application):
            return app_field_value
        application_id = self.get_data_application_id_value(app_field_value)
        if application_id:
            return Application.objects.get(id=application_id)
        return None
