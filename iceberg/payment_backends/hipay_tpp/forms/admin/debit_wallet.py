# -*- coding: utf-8 -*-
from apps.currencies.models import Currency
from django import forms
from payment_backends.hipay_tpp.admin.actions.transfer_amount import transfer_money


class DebitWalletForm(forms.Form):
    action_name = "debit_wallet"
    label = forms.CharField(max_length=100, required=False)
    amount = forms.DecimalField()
    currency = forms.ModelChoiceField(Currency.objects, initial="EUR")

    def run_with(self, wallets, amount, currency, label):
        for wallet in wallets.values_list("id", flat=True):
            transfer_money(wallet, -amount, currency.code, label)
