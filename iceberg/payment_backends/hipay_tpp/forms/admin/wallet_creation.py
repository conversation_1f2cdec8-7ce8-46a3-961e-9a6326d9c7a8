# -*- coding: utf-8 -*-
import pytz
from apps.address.models import Country
from apps.currencies.models import Currency
from apps.ice_applications.models import Application
from apps.stores.models import Merchant
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from payment_backends.hipay_tpp.models import HipayWalletAccount
from payment_backends.hipay_tpp.references import (
    BUSINESS_ACCOUNT,
    HIPAY_ACCOUNT_TYPE_CHOICES,
    HIPAY_BUSINESS_LINE_OTHERS,
    SUPPORTED_LOCALES,
    TITLES_CONSTANTS,
    TITLES_CONSTANTS_CHOICES,
)
from payment_backends.hipay_tpp.utils import get_conf_for_application, get_ip_address
from unidecode import unidecode


class SelectTargetForm(forms.Form):
    action_name = "manually_create_wallet"
    merchant = forms.ModelChoiceField(
        required=False,
        queryset=Merchant.objects.all(),
        label=_("Merchant "),
        empty_label="No merchant",
        widget=forms.TextInput,
        help_text=_("leave blank for app commission account"),
    )
    application = forms.ModelChoiceField(
        queryset=Application.objects.filter_active(),
        widget=forms.TextInput,
        label=_("Application"),
    )


class CreateWalletForm(forms.ModelForm):
    """
    A form that creates a wallet, and sends creation request to hipay
    """

    action_name = "manually_create_wallet"

    email = forms.EmailField(
        label=_("Contact email"),
        max_length=250,
        help_text=_("Required. Must be unique."),
        error_messages={
            "duplicate_email_address": _(
                "This value is already used by another merchant."
            )
        },
    )
    application = forms.ModelChoiceField(
        Application.objects.filter_active(),
        required=True,
        widget=forms.HiddenInput,
    )
    merchant = forms.ModelChoiceField(
        Merchant.objects.all(),
        required=False,
        widget=forms.HiddenInput,
    )

    company_name = forms.CharField(
        max_length=70,
        label=_("Company name (defaults to target's name if not set)"),
        required=False,
    )

    title = forms.ChoiceField(
        label=_("Title"), choices=TITLES_CONSTANTS_CHOICES, required=True
    )
    firstname = forms.CharField(label=_("First name"), required=True)
    lastname = forms.CharField(label=_("Last name"), required=True)

    currency = forms.ModelChoiceField(
        Currency.objects.all(),
        label=_("account main currency"),
        initial="EUR",
        required=True,
    )

    locale = forms.ChoiceField(
        choices=SUPPORTED_LOCALES,
        label=_("Merchant/application language"),
        required=True,
    )

    address = forms.CharField(label=_("Address"), required=True)
    zip_code = forms.CharField(label=_("Zipcode"), required=True)
    city = forms.CharField(label=_("City"), required=True)
    country = forms.ModelChoiceField(
        queryset=Country.objects.all(),
        label=_("Country"),
        required=True,
    )

    phone_number = forms.CharField(label=_("Phone"), required=True)

    vat_number = forms.CharField(label=_("European VAT Number"), required=True)

    error_messages = {
        "duplicate_email_address": _("A wallet with that email already exists."),
        "application_only": _(
            "A wallet must be given either only an app or a merchant"
        ),
        "hipay_api_issue": _(
            "There has been an error with HipayAPI. Check logs & tracebacks."
        ),
    }

    timezone = forms.ChoiceField(
        choices=[(x, x) for x in pytz.common_timezones],
        label=_("Timezone"),
        initial="Europe/Paris",
        required=True,
    )

    account_type = forms.ChoiceField(
        choices=HIPAY_ACCOUNT_TYPE_CHOICES,
        label=_("Account type"),
        required=True,
    )

    def save_m2m(self):
        # override this for we have no related models to save.
        pass

    def save(self, commit=True):
        if not commit:
            raise NotImplementedError("commit=False option not handled by form")
        return self.create_account()

    def __init__(self, *args, **kwargs):
        if len(args) and "application" in args[0]:
            self.init_api(args[0]["application"])
        self.application_name = self._application.name
        super(CreateWalletForm, self).__init__(*args, **kwargs)

    def init_api(self, application_id):
        self._application = Application.objects.get(id=int(application_id))
        config = get_conf_for_application(self._application)
        self.cashout_api = config.cashout_api

    def get_basic_dict(self):
        # added all these str(...) to make sure any forgotten non) ascii
        # string vould raise an exception when being converted.
        return {
            "email": str(self.cleaned_data["email"]),
            "title": str(self.cleaned_data["title"]),
            "ipAddress": get_ip_address(),
            "firstname": self.cleaned_data["firstname"],
            "lastname": self.cleaned_data["lastname"],
            "currency": str(self.cleaned_data["currency"].code),
            "locale": str(self.cleaned_data["locale"]),
            "entity": str(self.cashout_api.config.EWalletApiEntity),
        }

    def get_account_details_dict(self):
        merchant = self.cleaned_data.get("merchant")
        application = self.cleaned_data.get("application")
        company_name = self.cleaned_data.get("company_name")
        if company_name:
            name = company_name
        elif merchant:
            name = merchant.name
        elif application:
            name = application.name
        else:
            raise ValidationError(_("Application or merchant must be set."))

        return {
            "legalStatus": BUSINESS_ACCOUNT,
            # company
            "companyName": unidecode(name),
            "directorRole": "CEO",
            # contact info
            "address": self.cleaned_data["address"],
            "zipCode": self.cleaned_data["zip_code"],
            "city": self.cleaned_data["city"],
            "country": str(self.cleaned_data["country"].code),
            "timeZone": "Europe/Paris",
            "contactEmail": str(self.cleaned_data["email"]),
            "phoneNumber": str(self.cleaned_data["phone_number"]),
            # business info
            "europeanVATNumber": str(self.cleaned_data["vat_number"]),
            # 'businessId': company.siret,
            "businessLineId": HIPAY_BUSINESS_LINE_OTHERS,
            # terms agreement
            "termsAgreed": 1,
        }

    def create_account(self):
        parameters_d = {
            "userAccountBasic": self.get_basic_dict(),
            "userAccountDetails": self.get_account_details_dict(),
            # optional merchant data are never filled.
            "merchantDatas": [],
        }
        return self.cashout_api.create_wallet_account(
            parameters_d, self.merchant_property, self.application_property
        )

    @property
    def merchant_property(self):
        return self.cleaned_data.get("merchant", None)

    @property
    def application_property(self):
        return self.cleaned_data.get("application", None)

    class Meta:
        model = HipayWalletAccount
        fields = ("merchant", "application", "email")

    def clean_email(self):
        # Since User.username is unique, this check is redundant,
        # but it sets a nicer error message than the ORM. See #13147.
        email = self.cleaned_data["email"]
        r, available = self.cashout_api.is_email_available(email)
        if r["code"]:
            raise forms.ValidationError(
                self.error_messages["hipay_api_issue"],
                code="hipay_api_issue",
            )
        if not available:
            raise forms.ValidationError(
                self.error_messages["duplicate_email_address"],
                code="duplicate_email_address",
            )
        return email

    def clean_locale(self):
        real_locale = self.cleaned_data["locale"]
        hipay_given_locale = self.cashout_api.get_locale_from_lang_code(real_locale)
        self.cleaned_data["locale"] = hipay_given_locale
        return hipay_given_locale

    def clean_firstname(self):
        return self.unidecode_cleaner("firstname")

    def clean_lastname(self):
        return self.unidecode_cleaner("lastname")

    def clean_address(self):
        return self.unidecode_cleaner("address")

    def clean_city(self):
        return self.unidecode_cleaner("city")

    def clean_zip_code(self):
        return self.unidecode_cleaner("zip_code")

    def unidecode_cleaner(self, field_name):
        field_value = self.cleaned_data[field_name]
        field_value = unidecode(field_value)
        self.cleaned_data[field_name] = field_value
        return field_value

    def clean_title(self):
        title = self.cleaned_data["title"]
        title = TITLES_CONSTANTS[title.lower()]
        self.cleaned_data["title"] = title
        return title
