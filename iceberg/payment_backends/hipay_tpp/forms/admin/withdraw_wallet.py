# -*- coding: utf-8 -*-
from apps.currencies.models import Currency
from django import forms


class WithdrawWalletForm(forms.Form):
    action_name = "withdraw_wallet"
    label = forms.CharField(
        max_length=100,
        required=False,
        help_text="""
    Cashout engine uses
    "Withdrawal of xxx.xx CUR from [{merchant_id}] {merchant_name}'s account
    to bank account"
    """,
    )
    amount = forms.DecimalField(required=True)
    currency = forms.ModelChoiceField(Currency.objects, initial="EUR")

    def run_with(self, wallets, amount, currency, label):
        if len(wallets) > 1:

            class TooManyObjFakeResult:
                code = "Too many objects"
                description = "Can't withdraw on more than 1 wallet"

            return TooManyObjFakeResult()
        wallet = getattr(wallets.first(), "id", None)
        if wallet is None:

            class NoWalletFakeResult:
                code = "No wallet found"
                description = "Can't withdraw on a wallet not found"

            return NoWalletFakeResult()
        from payment_backends.hipay_tpp.admin.actions import withdraw_money

        return withdraw_money(wallet, amount, currency.code, label)
