# -*- coding: utf-8 -*-
from apps.ice_applications.models import Application, ApplicationBankAccount
from apps.kyc.models import KycInformation, MerchantIdentityDocument
from apps.stores.models import Merchant, MerchantBankAccount
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from payment_backends.hipay_tpp.exceptions import HipayMissingKYCError
from payment_backends.hipay_tpp.forms import APIRelyingFormMixin
from payment_backends.hipay_tpp.models import HipayBankAccount, HipayWalletAccount


class HipayBankAccountCreationForm(forms.ModelForm, APIRelyingFormMixin):
    """
    A form that creates a bank account, and sends RIB infos request to hipay
    """

    error_messages = {
        "missing_either_app_or_store": _("You must select either an APP or a store."),
        "missing_bank_account": _("You must select One bank account."),
    }

    def save_m2m(self):
        # override this for we have no related models to save.
        pass

    def save(self, commit=True):
        return self.create_infos_and_set_rib()

    def _clean_fields(self, *args, **kwargs):
        """Do some shit before actually cleaning the form"""
        APIRelyingFormMixin.init_api(self)
        return super(HipayBankAccountCreationForm, self)._clean_fields(*args, **kwargs)

    def create_infos_and_set_rib(self):
        hipay_bank_account = self.create_bank_account()
        if self.merchant_property:
            bank_account = hipay_bank_account.store_bank_account
            identity_document = (
                hipay_bank_account.identity_document
                or hipay_bank_account.kyc_information
            )
            if identity_document is None:
                raise HipayMissingKYCError(self.merchant_property)
            wallet = self.cashout_api.get_wallet(
                merchant=hipay_bank_account.get_merchant(),
                application=hipay_bank_account.get_application(),
            )
            response = self.cashout_api.select_merchant_bank_account(
                bank_account, identity_document, wallet
            )
            if response.code != 0:
                raise ValidationError(
                    _("set-bank infos failed : %(error)s")
                    % {"error": response.description}
                )
        return hipay_bank_account

    def create_bank_account(self):
        if self.merchant_property:
            params = {
                "application": self.merchant_property.application,
                "merchant": self.merchant_property,
                "store_bank_account": self.store_bank_account_property,
                "identity_document": self.identity_document_property,
                "kyc_information": self.kyc_information_property,
            }
        else:
            params = {
                "application": self.application_property,
                "app_bank_account": self.app_bank_account_property,
            }
        return HipayBankAccount.objects.create(**params)

    @property
    def merchant_property(self):
        return self.cleaned_data.get("merchant", None)

    @property
    def application_property(self):
        return self.cleaned_data.get("application", None)

    @property
    def store_bank_account_property(self):
        return self.cleaned_data.get("store_bank_account", None)

    @property
    def app_bank_account_property(self):
        return self.cleaned_data.get("app_bank_account", None)

    @property
    def identity_document_property(self):
        return self.cleaned_data.get("identity_document", None)

    @property
    def kyc_information_property(self):
        return self.cleaned_data.get("kyc_information", None)

    merchant = forms.models.ModelChoiceField(
        required=False,
        queryset=Merchant.objects.all(),
        label=_("Merchant"),
        empty_label="No merchant",
        help_text=_("Leave blank for app commission account"),
        widget=forms.TextInput,
    )

    application = forms.models.ModelChoiceField(
        required=True,
        queryset=Application.objects.filter_active(),
        label=_("Application"),
        empty_label="No application",
        help_text=_("Leave blank for merchant commission account"),
        widget=forms.TextInput,
    )

    store_bank_account = forms.models.ModelChoiceField(
        required=False,
        queryset=MerchantBankAccount.objects.all(),
        label=_("Merchant bank account"),
        empty_label="No merchant",
        help_text=_("leave blank for app commission account"),
        widget=forms.TextInput,
    )

    app_bank_account = forms.models.ModelChoiceField(
        required=False,
        queryset=ApplicationBankAccount.objects.all(),
        label=_("Application bank account"),
        empty_label="No application bank account",
        help_text=_("leave blank for merchant commission account"),
        widget=forms.TextInput,
    )

    identity_document = forms.models.ModelChoiceField(
        required=False,
        queryset=MerchantIdentityDocument.objects.exclude_deleted(),
        empty_label="No document",
        help_text=_("Bank account proof document (KYC v1)"),
        widget=forms.TextInput,
    )
    kyc_information = forms.models.ModelChoiceField(
        required=False,
        queryset=KycInformation.objects.exclude_deleted(),
        empty_label="No document",
        help_text=_("Bank account proof document (KYC v2)"),
        widget=forms.TextInput,
    )

    class Meta:
        model = HipayWalletAccount
        fields = (
            "application",
            "merchant",
            "identity_document",
            "kyc_information",
            "store_bank_account",
            "app_bank_account",
        )
