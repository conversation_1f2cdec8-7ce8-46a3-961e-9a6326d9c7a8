# -*- coding: utf-8 -*-
import logging
from decimal import Decimal

import payment_backends.hipay_tpp.references as hipay_refs
from payment_backends.common.utils import logging_context
from payment_backends.hipay_tpp.exceptions import (
    NoTransactionError,
    TransactionAlreadyClosedException,
)
from payment_backends.hipay_tpp.models import HipayTransaction

logger = logging.getLogger(__name__)


class CaptureAction:
    def __init__(self, api):
        self.api = api
        self.config = api.config

    def run(self, payment, amount, merchant_orders, operation_id):
        if self.config.cashin_mode == self.config.CASHIN_MODE_UNHANDLED:
            return False

        currency = payment.currency
        ids = ",".join(str(mo.merchant_id) for mo in merchant_orders)
        logger.info("Hipay will capture %f%s for merchant(s) %s", amount, currency, ids)

        transaction = self.get_transaction_for_payment(payment)

        transaction.refresh_status()
        logger.info(
            "Refreshed transaction %s: status=%s, authorized=%.2f, captured=%.2f, refunded=%.2f",
            transaction.transaction_reference,
            transaction.status,
            transaction.authorized_amount,
            transaction.captured_amount,
            transaction.refunded_amount,
        )

        if transaction.status == int(hipay_refs.TRANSACTION_CAPTURE_REQUESTED):
            logger.warning(
                "Transaction %s is already in capture requested state, "
                "ignoring capture request",
                transaction.transaction_reference,
            )
            return False

        tref = self.get_and_propagate_transaction_reference(transaction, payment)

        context = {
            "db_logger": self.api.db_logger,
            "action_name": hipay_refs.HIPAY_ACTION_CAPTURE,
            "payment": payment,
        }
        with logging_context(**context):
            try:
                self.api.capture(tref, operation_id, Decimal(amount))
            except TransactionAlreadyClosedException as err:
                # transaction is already closed, meaning it may already be
                # captured
                transaction.refresh_status()
                if not transaction.captured_amount:
                    raise
                already_captured = (
                    transaction.captured_amount
                    == transaction.authorized_amount
                    == Decimal(amount)
                )
                if already_captured:
                    logger.warning(
                        "Operation %s of ref %s for amount %s seems already "
                        "captured, ignoring error %s",
                        operation_id,
                        tref,
                        amount,
                        err,
                    )
                else:
                    # amount mismatch, reraising
                    raise

            # resync transaction to update "captured_amount" property.
            # Note: remind refresh_status only update denormalized hipay
            # API fields. Other attributes in HipayTransaction are left
            # untouched.
        transaction.refresh_status()
        return True

    def get_and_propagate_transaction_reference(self, transaction, payment):
        """As IZBERG API not receiving all information, we need to
        constantly resync our models to feature Hipay's transaction reference
        in payment model's external_id field.
        """
        transaction_reference = transaction.transaction_reference

        if transaction_reference is None:
            transaction.refresh_status()

            if transaction.transaction_reference == payment.external_id:
                return transaction.transaction_reference

            transaction_reference = transaction.transaction_reference = (
                payment.external_id
            )

        elif not payment.external_id:
            payment.external_id = transaction_reference
            payment.save()

        return transaction_reference

    def get_transaction_for_payment(self, payment):
        """Returns a HipayTransaction model bound to payment.

        This model was generated at the authorization step
        (even if we're in Presta Shop mode)
        """
        try:
            return HipayTransaction.objects.get(payment=payment)
        except HipayTransaction.DoesNotExist:
            # no transaction found for order linked to this payment model object
            raise NoTransactionError()
