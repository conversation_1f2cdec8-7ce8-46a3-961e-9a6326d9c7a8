# -*- coding: utf-8 -*-
import logging
from decimal import Decimal
from typing import List, Optional

from constance import config as live_settings
from django.utils.translation import get_language
from requests.exceptions import HTTPError

from ..exceptions import NoTransactionError, UnexpectedHipayError
from ..models import HipayTransaction
from ..references import (
    HIPAY_ECI_LEVEL_END_USER,
    HIPAY_ECI_LEVEL_END_USER_RECUR,
    HIPAY_ECI_LEVEL_OPERATOR,
    TRANSACTION_CANCELLED,
    TRANSACTION_CAPTURED,
    TRANSACTION_PARTIALLY_REFUNDED,
    TRANSACTION_REFUND_REQUESTED,
    TRANSACTION_REFUNDED,
)
from .rest_api_interface import RestAPIInterface

logger = logging.getLogger(__name__)


class HipayCashinApi(RestAPIInterface):
    SUPPORTED_LANGUAGES = {
        "fr": "fr_FR",
        "nl": "nl_NL",
        "en": "en_GB",
        "es": "es_ES",
        "pt": "pt_PT",
        "de": "de_DE",
    }
    HANDLED_SECURITY_LEVELS = (
        HIPAY_ECI_LEVEL_END_USER,
        HIPAY_ECI_LEVEL_END_USER_RECUR,
        HIPAY_ECI_LEVEL_OPERATOR,
    )

    # customer's ip address
    ipaddr = ""
    # customer's browser "Accept" header as received by the marketplace
    http_accept = ""
    # customer's browser "User-Agent" header as receved by the marketplace
    http_user_agent = ""
    # language of the user (changes language of the payment page)
    language = ""

    def __init__(self, config):
        super(HipayCashinApi, self).__init__()
        self.config = config

    def get_credentials_tuple(self):
        return self.config.RestApiCredentialsTuple

    def get_application(self):
        return self.config.application

    def get_url(self, path):
        """
        RestApiV2Endpoint is used only for hosted page V2 and request containing
        /v1/hpayment
        For /v1/transaction and /v1/maintenance we always use the RestApiEndpoint
        """
        if live_settings.ENABLE_HIPAY_HOSTED_PAGE_V2 and "hpayment" in path:
            return self.config.RestApiV2Endpoint + path
        return self.config.RestApiEndpoint + path

    def get_url_v3(self, path):
        """
        Get URL for HiPay TPP API v3 endpoints
        """
        return self.config.RestApiV3Endpoint + path

    def get_request_prams_for_order(self, order_data):
        # WONTFIX : I should use the django request information like requester's
        # IP but it sounds too complicated just to log requester's IP on
        # hipay servers. If you are needing such, fell free to fill the
        # commented fields in request_params_d
        request_params_d = {
            # customer's ip address
            # "ipaddr": self.ipaddr,
            # customer's browser "Accept" header as received by the marketplace
            # "http_accept": self.http_accept,
            # customer's browser "User-Agent" header as received by the marketplace
            # "http_user_agent": self.http_user_agent,
            # language of the environment (changes language of the payment page)
            # use this instead of django's `to_locale` function since hipay
            # does not support all locales.
            "language": self.get_locale_for_language(get_language()),
        }
        # add configuration parameters
        request_params_d.update(self.config.apiConfigDict)
        return request_params_d

    def add_order_id_to_urls(self, config_d, order_id):
        """Facility going through all urls set by config and adding the
        order_id=`order_id` parameter.
        :param config_d: configuration generated dictionnary
        :type config_d: dict

        :param order_id: hipay_order_id for current order
        :type order_id: int
        """
        transaction = self.get_transaction_from_hipay_id(order_id)
        order = transaction.order
        payment = transaction.payment
        keys = [
            "accept_url",
            "decline_url",
            "pending_url",
            "exception_url",
            "cancel_url",
        ]

        for key in keys:
            if key not in config_d:
                url = getattr(self.config, key, "")
            else:
                url = config_d[key]
            if order is not None:
                url = url.replace("{hipay_id}", order_id)
                url = url.replace("{id_number}", order.id_number)
                url = url.replace("{id}", str(order.id))
            if payment is not None:
                url = url.replace("{payment_id}", str(payment.id))
                url = url.replace("{payment_dated_id}", payment.dated_id)
            config_d[key] = url
        return config_d

    def get_transaction_from_hipay_id(self, hipay_unique_id):
        try:
            transaction = HipayTransaction.objects.get(hipay_orderid=hipay_unique_id)
        except HipayTransaction.DoesNotExist:
            raise NoTransactionError()
        return transaction

    def authorize(self, order_data_d, security_level=None, override=None, token=None):
        # operation can override default value set
        # on MerchantInterface
        if override is None:
            override = {}
        return self.validate(
            order_data_d, "Authorization", security_level, override, token
        )

    def sell(self, order_data_d, security_level=None, override=None, token=None):
        # operation can override default value set
        # on MerchantInterface
        if override is None:
            override = {}
        return self.validate(order_data_d, "Sale", security_level, override, token)

    def validate(self, order_data_d, operation, security_level, override, token):
        """Validate a transaction by authorizing it.
        You can specify whether you want a Sale (automatic
        capture) or an Authorization (manual capture).

        :param order_data_d: data to transmit in request
        :type order_data_d: dict<str, str>

        :param operation: operation to do (amongst capture,
            refund, cancel)
        :type operation: str

        :param security_level: Defines eci security level amongst Hipay
        references' constants named HIPAY_ECI_LEVEL_* if given.
        :default security_level: None
        :type security_level: str

        :param override: Dict used to override module's generated parameters.
        :type override: dict

        :return: payment form url
        :rtype: str

        :raise HTTPError: when request encounters an HTTP error.
        """
        args = self.create_data_dict(operation, order_data_d, security_level)
        # using a token means we're making recurring payment
        if token:
            args = self.customize_args_for_recurring_payment(args, token)
        if override:
            args.update(override)
        url = self.get_authorization_method_url(token)

        logger.info("Hipay: %s call", operation)

        try:
            contents = self.request(method="POST", url=url, post_args=args)
        except HTTPError:
            # unused e but it is needed so we can at least read response message
            # when hipay raises a 500 error response.
            raise RuntimeError("Unexpected error while getting auth page URL")

        logger.info(
            "Hipay: Authorization call for operation %s returned contents %s",
            operation,
            contents,
        )

        return contents

    def get_status(self, payment):
        """Contacts hipay server to get transaction status.

        Only one ID is used, provide exactly one of the expected
        ID parameters.

        If authorization form is not filled yet, it can cause a 500 http error.
        :param payment: Payment model object
        :type payment: apps.payment.models.Payment

        :return : transaction status information in a dict
        :rtype : dict

        :raise TypeError: No ID provided / wrong params / too much params
        :raise HTTPError: Error returned from payment server.
        """
        url = self.get_url("transaction")

        if payment.external_id:
            url += "/%s" % payment.external_id
            args = {}
        else:
            from payment_backends.hipay_tpp.utils import hipay_get_unique_id

            args = {"orderid": hipay_get_unique_id(payment)}

        logger.info(
            "[HiPay] get_status call | Payment ID: %s | External ID: %s | URL: %s | args: %s",
            payment.id,
            payment.external_id,
            url,
            args,
        )
        # send it (it's a GET request because we have no data)
        try:
            contents = self.request(url=url, args=args)
            logger.info(
                "[HiPay] get_status call | Payment ID: %s | Response: %s",
                payment.id,
                contents,
            )
        except HTTPError as e:
            if getattr(e.response, "status_code", None) in (404, 500):
                return {}
            logger.error("Error occured while getting hipay transaction status %s", e)
            raise RuntimeError("Unexpected HTTP Error on hipay get_status call: %s" % e)
        return contents

    def get_status_v3(self, payment):
        """Contacts hipay server to get transaction status using API v3.

        :param payment: Payment model object
        :type payment: apps.payment.models.Payment

        :return : transaction status information in a dict
        :rtype : dict

        :raise TypeError: No ID provided / wrong params / too much params
        :raise HTTPError: Error returned from payment server.
        """
        if payment.external_id:
            transaction_id = payment.external_id
        else:
            from payment_backends.hipay_tpp.utils import hipay_get_unique_id

            transaction_id = hipay_get_unique_id(payment)

        url = self.get_url_v3(f"transaction/{transaction_id}")
        args = {}

        logger.info(
            "[HiPay v3] get_status call | Payment ID: %s | External ID: %s | URL: %s | args: %s",
            payment.id,
            payment.external_id,
            url,
            args,
        )

        try:
            contents = self.request(url=url, args=args)
            logger.info(
                "[HiPay v3] get_status call | Payment ID: %s | Response: %s",
                payment.id,
                contents,
            )
        except HTTPError as e:
            if getattr(e.response, "status_code", None) in (404, 500):
                return {}
            logger.error(
                "Error occured while getting hipay v3 transaction status %s", e
            )
            raise RuntimeError(
                "Unexpected HTTP Error on hipay v3 get_status call: %s" % e
            )

        return contents

    def capture(
        self, transaction_ref: str, operation_id: str, amount: Optional[Decimal] = None
    ) -> dict:
        """
        Capture authorized transacrion, i.e. actually accept transaction.
        """
        return self._make_maintenance_request(
            transaction_ref,
            "capture",
            (TRANSACTION_CAPTURED,),
            amount=amount,
            operation_id=operation_id,
        )

    def refund(
        self, transaction_ref: str, operation_id: str, amount: Optional[Decimal] = None
    ):
        """
        Refund captured transaction.
        """
        return self._make_maintenance_request(
            transaction_ref,
            "refund",
            (
                TRANSACTION_REFUND_REQUESTED,
                TRANSACTION_REFUNDED,
                TRANSACTION_PARTIALLY_REFUNDED,
            ),
            amount=amount,
            operation_id=operation_id,
        )

    def cancel(self, transaction_ref: str, amount: Optional[Decimal] = None):
        """
        Cancel authorized transaction. Doesn't work on captured  transactions
        (use refund for that).
        """
        return self._make_maintenance_request(
            transaction_ref,
            "cancel",
            (TRANSACTION_CANCELLED,),
            amount=amount,
        )

    def _make_maintenance_request(
        self,
        transaction_ref: str,
        operation_name: str,
        valid_statuses: List[str],
        amount: Optional[Decimal] = None,
        operation_id: Optional[str] = None,
    ) -> dict:
        """
        Alter a transaction by capturing, refunding or canceling it. You can
        specify the amount for partial refund.
        """
        url = self.get_maintenance_method_url(transaction_ref)
        args = self.get_maintenance_parameters_d(amount, operation_name, operation_id)
        try:
            response = self.request(
                method="POST", url=url, post_args=args, return_content=False
            )
        except HTTPError as exc:
            if not self._raise_on_invalid_status_code(exc):
                raise
        if response.status_code != 200:
            raise Exception(f"Unexpected status code received: {response.status_code}")
        data = response.json()
        self._raise_on_invalid_status_in_response(data, valid_statuses)
        return data

    def get_locale_for_language(self, language):
        """Converts a language to hipay's expected locale code (i.e.
        <language>_<COUNTRY>)

        defaults to en_GB if language not supported by hipay

        @param language: language code from user's language
        @return: locale code
        """
        # use this instead of django's `to_locale` function since hipay
        # does not support all locales.
        return self.SUPPORTED_LANGUAGES.get(language, "en_GB")

    def get_authorization_method_url(self, token):
        return self.get_url("order" if token else "hpayment")

    def create_data_dict(self, operation, order_data, security_level):
        # create data dict agregating config value and user order data.
        args = {}
        args.update(self.get_request_prams_for_order(order_data))
        args = self.add_order_id_to_urls(args, order_data["orderid"])
        args = self.apply_security_level_to_call_args(args, security_level)
        args = self.apply_moto_parameters(args, security_level)
        # config values will be overridden by values given in case of value name
        # conflict. I made it on purpose.
        args.update(order_data)
        # add operation field to request params
        args["operation"] = operation
        return args

    def customize_args_for_recurring_payment(self, args, token):
        args["payment_product"] = "visa"
        args["cardtoken"] = token
        # force eci_level to recur mode
        args["eci"] = HIPAY_ECI_LEVEL_END_USER_RECUR
        args["multi_use"] = 1
        return args

    def apply_security_level_to_call_args(self, args, security_level):
        """Override config's eci level using param's security level

        :param args: dictionary for arguments we'll use in request
        :type args: dict

        :param security_level: Level of security (hipay defined constants)
        :type security_level: int

        :return: updated args dictionary
        :rtype: dict
        """

        if security_level not in self.HANDLED_SECURITY_LEVELS:
            logger.warning(
                "security level invalid : %s. Keeping default value %s set in "
                "application config.",
                security_level,
                args["eci"],
            )
            return args

        args["eci"] = security_level

        return args

    def apply_moto_parameters(self, args, security_level):
        """If order is paid as a call center, then disable 3DS"""
        if security_level == HIPAY_ECI_LEVEL_OPERATOR:
            args["authentication_indicator"] = self.config._3DS_LEVELS["no"]
        return args

    def get_maintenance_method_url(self, transaction):
        path = "/".join(
            (
                "maintenance",
                "transaction",
                transaction,  # received transaction reference
            )
        )
        return self.get_url(path)

    def get_maintenance_parameters_d(self, amount, operation, operation_id=None):
        args = {
            "operation": operation,
        }
        if amount is not None:
            args["amount"] = Decimal(amount)
        if operation_id is not None:
            args["operation_id"] = operation_id
        return args

    def _raise_on_invalid_status_code(self, exc: HTTPError) -> None:
        unhandled = exc.response.status_code >= 500 or exc.response.status_code in (
            401,
            403,
        )
        if unhandled:
            return False
        data = exc.response.json()
        raise UnexpectedHipayError(
            hipay_status=data.get("code"),
            hipay_message=data.get("description") or data.get("message"),
        )

    def _raise_on_invalid_status_in_response(
        self, data: dict, valid_statuses: List[str]
    ) -> None:
        status = data.get("status")
        if status in valid_statuses:
            return
        raise UnexpectedHipayError(
            hipay_status=status,
            hipay_message=data.get("message"),
        )
