# -*- coding: utf-8 -*-
import datetime
import logging
import time
from decimal import Decimal

import payment_backends.hipay_tpp.references as hipay_refs
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from payment_backends.common.utils import logging_context
from payment_backends.hipay_tpp.codes import HIPAY_ERROR_ACCOUNT_SETUP_NOT_COMPLETE
from payment_backends.hipay_tpp.exceptions import (
    HipayAccountCreationError,
    HipayFundsTransferError,
    HipayInvalidCashoutCredentials,
    HipayMerchantWalletNotFound,
    HipayPreviousCashoutOngoing,
    HipayUnaprovisionnedWithdrawal,
    HipayWithdrawalFailure,
    WithdrawableAmountMisMatch,
)
from payment_backends.hipay_tpp.hipay_api import RestAPIInterface
from payment_backends.hipay_tpp.models import HipayWalletAccount
from payment_backends.hipay_tpp.utils import get_currency_code
from reference import status
from requests import HTTPError
from unidecode import unidecode

logger = logging.getLogger(__name__)

GET_BALANCE_THROTTLE_TIME = 5  # seconds
GET_BALANCE_MAX_RETRY = 3


class TransferParameter:
    """Transfer parameter aggregates transfer's
    - amount
    - currency
    - label
    - destination wallet
    That way you only pass 1 parameter and it always has all data available.
    """

    def __init__(self, amount, currency, label_str, wallet):
        self.amount = amount
        self.currency = currency
        self.wallet = wallet
        self.label_str = str(label_str)

    @property
    def is_application_cashout(self):
        return self.wallet.merchant is None

    @property
    def private_label(self):
        return unidecode("[PRIVATE] " + self.label_str)

    @property
    def public_label(self):
        return unidecode(self.label_str)

    @property
    def currency_code(self):
        return get_currency_code(self.currency)

    @property
    def withdraw_label(self):
        return "Withdrawal of %(amount)f - on %(date_string)s" % {
            "amount": self.amount,
            "date_string": datetime.datetime.today(),
        }

    def __str__(self):
        return repr(self)

    def __repr__(self):
        return "Transfer of %s%s in favour of %s (hipay account id %s)" % (
            self.amount,
            self.currency_code,
            unidecode(self.wallet.owner_display_name),
            self.wallet.user_account_id,
        )


class HipayCashoutApi(RestAPIInterface):
    def __init__(self, config):
        super(HipayCashoutApi, self).__init__()
        self.config = config

    def get_application(self):
        return self.config.application

    def get_credentials_tuple(self):
        credentials = (self.config.EWalletApiLogin, self.config.EWalletApiPassword)
        return credentials

    ####
    # Bank infos
    ####

    def get_bank_infos_status(self, wallet):
        """get bank infos & bank infos registration status
        :param wallet: returns ewallet's bank infos
        :return: bank infos status
        """
        context = {
            "db_logger": self.config.cashout_api.db_logger,
            "action_name": hipay_refs.HIPAY_ACTION_GET_ACCOUNT,
            "application": self.get_application(),
        }
        with logging_context(**context):
            return self.request(
                url=self.config.BankInfoApiEndpoint,
                method="GET",
                headers={"php-auth-subaccount-id": str(wallet.user_account_id)},
                mute_ssl_warnings=True,
            )

    def select_merchant_bank_account(
        self, merchant_bank_account, identity_document, wallet
    ):
        """Set bank infos like IBAN, SWIFT and so on.
        :param merchant_bank_account: Merchant bank information object.
        :type merchant_bank_account: apps.stores.models.MerchantBankAccount
        :param identity_document: Merchant Identity document containing IBAN ref
        :type identity_document: apps.stores.models.MerchantIdentityDocument
        :param wallet: hipay wallet account to use
        :type wallet: hipay_tpp.models.wallet_account.HipayWalletAccount
        """
        post_args = {
            "bank_country": merchant_bank_account.account_bank_country_code,
            "iban": merchant_bank_account.account_IBAN,
            "swift": merchant_bank_account.account_BIC,
        }
        logger.info(
            "Setting bank infos for %s : %s", wallet.owner_display_name, post_args
        )
        context = {
            "db_logger": self.config.cashout_api.db_logger,
            "action_name": hipay_refs.HIPAY_ACTION_SET_BANK_ACCOUT,
            "merchant": merchant_bank_account.merchant,
            "application": self.get_application(),
        }
        with logging_context(**context):
            return self.request(
                url=self.config.BankInfoApiEndpoint,
                method="POST",
                post_args=post_args,
                files={"file": identity_document.parts.first().part},
                headers={"php-auth-subaccount-id": str(wallet.user_account_id)},
                mute_ssl_warnings=True,
            )

    ####
    # Create account
    ####
    def create_wallet_account(self, args, merchant=None, application=None):
        context = {
            "action_name": hipay_refs.HIPAY_ACTION_CREATE_WALLET,
            "db_logger": self.db_logger,
            "merchant": merchant,
            "application": self.get_application(),
        }
        with logging_context(**context):
            try:
                response = self.request(
                    self.config.UserApiEndpoint,
                    post_args=args,
                    method="POST",
                    mute_ssl_warnings=True,
                    headers={
                        "Content-Type": "application/x-www-form-urlencoded",
                    },
                )
            except HTTPError as err:
                if 400 <= err.response.status_code < 500:
                    raise HipayAccountCreationError(err.response.json())
                raise
        if response.get("code", -1):
            raise HipayAccountCreationError(response)
        return response

    ###
    # Wallet retrieval
    ###
    def get_hipay_wallet(self, account_id):
        context = {
            "action_name": hipay_refs.HIPAY_ACTION_GET_WALLET,
            "db_logger": self.db_logger,
            "application": self.get_application(),
        }
        with logging_context(**context):
            try:
                return self.request(
                    self.config.UserApiEndpoint,
                    method="GET",
                    mute_ssl_warnings=True,
                    headers={"php-auth-subaccount-id": str(account_id)},
                )
            except HTTPError as err:
                if err.response.status_code == 401:
                    raise HipayInvalidCashoutCredentials()
                raise

    def get_wallet_or_none(self, merchant=None, application=None):
        """Return HipayWalletAccount for given merchant.
        :param merchant: Merchant (or None for Application comission wallet)
        :return: HipayWalletAccount or None
        """
        try:
            return self.get_wallet(merchant, application)
        except ValidationError:
            logger.warning(
                "No hipay merchant account set for merchant %s (%d)",
                merchant,
                merchant.id,
            )
            return None

    def get_wallet(self, merchant=None, application=None):
        """Get HipayWalletAccount linked to given core's Merchant item.
        if merchant given : returns hipay wallet for merchant
        if application given : returns hipay wallet for commission
        if none of them given : return technical account wallet
        """
        if merchant is None and application is None:
            return self.config
        try:
            if merchant is not None:
                return HipayWalletAccount.objects.get(merchant=merchant)

            elif application is not None:
                return HipayWalletAccount.objects.get(
                    application=application, merchant__isnull=True
                )

        except HipayWalletAccount.DoesNotExist:
            raise HipayMerchantWalletNotFound()

    ###
    # Wallet account infos
    ###

    def get_merchant_account_infos(self, wallet):
        self.current_wallet = wallet
        context = {
            "action_name": hipay_refs.HIPAY_ACTION_GET_WALLET_INFO,
            "db_logger": self.db_logger,
            "merchant": wallet.merchant,
            "application": self.get_application(),
        }
        with logging_context(**context):
            return self.request(
                url=self.config.UserApiEndpoint,
                method="GET",
                headers={"php-auth-subaccount-id": str(wallet.user_account_id)},
                mute_ssl_warnings=True,
            )

    def raise_merchant_account_infos_fetching_errors(self, wallet, response):
        description = response.description
        code = response.code

        if code == 0:
            return
        logger.error(
            "Failed to get wallet account infos for %s : %s - %s",
            wallet.owner_display_name,
            code,
            description,
        )
        raise ValidationError(_("Wallet account info not found: %s") % description)

    def is_identified(self, wallet):
        info = self.get_merchant_account_infos(wallet)
        return info["identified"] == 1

    ###
    # money transfers
    ###

    def get_transfer_amount_back_parameters(self, transfer_parameter):
        args = {
            "amount": transfer_parameter.amount,
            "recipient_account_id": self.config.account_id,
            "public_label": "[CANCEL]" + transfer_parameter.public_label,
            "private_label": "[CANCEL]" + transfer_parameter.private_label,
        }
        return args

    def transfer_amount_back(self, transfer_parameter):
        """
        Transfer funds back from an account to another account
        :param transfer_parameter: parameters
        :type dict
        :return: response
        :type: dict
        """
        params = self.get_transfer_amount_back_parameters(transfer_parameter)
        context = {
            "db_logger": self.config.cashout_api.db_logger,
            "action_name": hipay_refs.HIPAY_ACTION_ASK_TRANSFER,
            "application": self.get_application(),
        }
        with logging_context(**context):
            response = self.request(
                url=self.config.TransferApiEndpoint,
                post_args=params,
                method="POST",
                mute_ssl_warnings=True,
                headers={
                    "php-auth-subaccount-id": str(
                        transfer_parameter.wallet.user_account_id
                    )
                },
            )

        code, message = response["code"], response["message"]
        if code != 0:
            logger.error(
                "Failed to cancel cashout %s%s for %s infos : %s - %s",
                transfer_parameter.amount,
                transfer_parameter.currency_code,
                transfer_parameter.wallet.owner_display_name,
                code,
                message,
            )
            raise HipayFundsTransferError(message)

        return response

    def get_transfer_amount_parameters(self, transfer_parameter):
        args = {
            "amount": transfer_parameter.amount,
            "recipient_account_id": transfer_parameter.wallet.user_account_id,
            "public_label": transfer_parameter.public_label,
            "private_label": transfer_parameter.private_label,
        }
        return args

    def transfer_amount(self, transfer_parameter):
        """
        Transfer funds from an account to another account
        :param transfer_parameter: parameters
        :type dict
        :return: response
        :type: dict
        """
        params = self.get_transfer_amount_parameters(transfer_parameter)
        context = {
            "db_logger": self.config.cashout_api.db_logger,
            "action_name": hipay_refs.HIPAY_ACTION_ASK_TRANSFER,
            "application": self.get_application(),
        }
        with logging_context(**context):
            response = self.request(
                url=self.config.TransferApiEndpoint,
                post_args=params,
                method="POST",
                mute_ssl_warnings=True,
            )

        code, message = response["code"], response["message"]
        if code != 0:
            logger.error(
                "Failed to transfer %s%s for %s infos :  %s - %s",
                transfer_parameter.amount,
                transfer_parameter.currency_code,
                transfer_parameter.wallet.owner_display_name,
                code,
                message,
            )
            raise HipayFundsTransferError("{} (error {})".format(message, code))

        return response

    def transfer_amount_safe(self, transfer_parameter):
        """Transfer ``amount`` of money from merchant ``src_merchant``'s
        account to ``dst_merchant`` merchant account with public label set
        to ``transaction_message`` using selected ``currency``.
        :param transfer_parameter: Transfer object containing amount and
         target information
        :type transfer_parameter: TransferParameter
        :return: transaction request response
        :rtype: object
        """
        if not self.check_ewallet_is_empty(transfer_parameter.wallet):
            raise HipayPreviousCashoutOngoing()
        self.transfer_amount(transfer_parameter)

    def withdraw_without_commissions(self, transfer_parameter):
        """Withdraw money from a e-wallet to real bank account.
        :param transfer_parameter: Transfer parameter also used on
        transfer_amount actions.
        :return: withdrawal request response
        :rtype: dict
        """
        return self.withdraw_safe(transfer_parameter)

    def withdraw_safe(self, transfer_parameter):
        wallet = transfer_parameter.wallet
        if self.check_ewallet_is_empty(wallet):
            raise HipayUnaprovisionnedWithdrawal()

        __, withdrawable_amount = self.get_balance_and_withdrawable_amount(wallet)

        if withdrawable_amount != transfer_parameter.amount:
            raise WithdrawableAmountMisMatch()

        return self.withdraw(transfer_parameter)

    def withdraw(self, transfer_parameter):
        params = self.get_withdrawal_parameters(transfer_parameter)

        context = {
            "db_logger": self.config.cashout_api.db_logger,
            "action_name": hipay_refs.HIPAY_ACTION_ASK_WITHDRAW,
            "application": self.get_application(),
        }
        with logging_context(**context):
            response = self.request(
                url=self.config.WithdrawalApiEndpoint,
                post_args=params,
                method="POST",
                mute_ssl_warnings=True,
                headers={
                    "php-auth-subaccount-id": str(
                        transfer_parameter.wallet.user_account_id
                    )
                },
            )

        code, message = response["code"], response["message"]
        logger.info("Withdrawal request response : %s", message)
        if code != 0:
            logger.error(
                "Failed to withdraw amount %d :  %s - %s",
                params["amount"],
                code,
                message,
            )

            raise HipayWithdrawalFailure(message)

        return response

    def get_withdrawal_parameters(self, transfer_parameter):
        params = {
            "amount": transfer_parameter.amount,
            "label": unidecode(transfer_parameter.withdraw_label),
        }
        return params

    def get_balance(self, wallet):
        """Get account current balance for given wallet
        :param wallet: Wallet you want balance from.
        :type wallet: hipay_tpp.models.hipay_wallet.HipayWalletAccount or None
        :return: wallet balance
        :type: dict
        """

        def _wrapper_get_balance_wrapper():
            context = {
                "db_logger": self.config.cashout_api.db_logger,
                "action_name": hipay_refs.HIPAY_ACTION_GET_USER_BALANCE,
                "application": self.get_application(),
            }
            with logging_context(**context):
                return self.request(
                    url=self.config.UserBalanceApiEndpoint,
                    method="GET",
                    headers={"php-auth-subaccount-id": str(wallet.user_account_id)},
                    mute_ssl_warnings=True,
                )

        # We are experiencing some 429 due to too many requests on `get balance`.it
        # appears that Hipay is sensitive to excessive requests. Unfortunately, caching
        # the result isn't possible as the balance of this wallet may change during the
        # life of this cashout. We need fresh and accurate balance when calling this
        # method.
        retry = 0
        response = None
        while retry < GET_BALANCE_MAX_RETRY:
            should_retry = False
            # When a 429 is return, self.request will raise an HTTPError instead of
            # the response as json.
            try:
                response = _wrapper_get_balance_wrapper()
            except HTTPError as e:
                if "429" in e.args[0]:
                    should_retry = True
                    retry += 1
                    time.sleep(GET_BALANCE_THROTTLE_TIME)
                else:
                    # Any other type of HTTPError, we simply re-raise it.
                    raise

            if not should_retry:
                break

        code, message = response["code"], response["message"]
        if code != 0:
            logger.error("Failed to get account balance :  %s - %s", code, message)
            raise ValidationError(
                _("Failed to synchronize balance amounts : %s") % message
            )

        return response["balances"][0]

    def get_balance_and_withdrawable_amount(self, wallet):
        """Get wallet's current balance and withdrawable amount
        :param wallet: Wallet you want balance from.
        :type wallet: hipay_tpp.models.hipay_wallet.HipayWalletAccount or None
        :return: balance
        :type: Decimal
        """
        wallet_balance = self.get_balance(wallet)

        return (
            Decimal(wallet_balance["balance"]).quantize(Decimal("0.01")),
            Decimal(wallet_balance["money_available_for_withdrawal"]).quantize(
                Decimal("0.01")
            ),
        )

    def check_ewallet_is_empty(self, wallet):
        """Check wallet is empty for given entity
        :param wallet: Wallet instance
        :type wallet: payment_backends.hipay_tpp.models.HipayWalletAccount
        :return: bool
        """
        wallet_balance = self.get_balance(wallet)
        return Decimal(wallet_balance["balance"]) == Decimal("0")

    def get_locale_from_lang_code(self, country_code):
        """returns closer ISO 639-1 language plus ISO 3166-1 country code
        to given region code as set in settings.LANGUAGE. defaults to
        fr_FR if not found.
        :param country_code: source country code used to get locale in
        that country
        :type country_code: str
        :return: ISO 639-1 language code (ex. fr_FR)
        :rtype: str
        :defaults: 'en_US'
        """

        context = {
            "db_logger": self.config.cashout_api.db_logger,
            "action_name": hipay_refs.HIPAY_ACTION_GET_LOCALE_CODES,
            "application": self.get_application(),
        }
        with logging_context(**context):
            response = self.request(
                url=self.config.LocalCodesApiEndpoint,
                method="GET",
                mute_ssl_warnings=True,
            )

        code, message = response["code"], response["message"]
        if code != 0:
            logger.error("Error while getting locales : %s", message)
            raise RuntimeError("Unexpected hipay remote server error %s" % message)
        elif not len(response["locales"]):
            logger.error("Error while getting locales : %s", message)
            raise ValueError("Unexpected hipay remote server error %s" % message)

        available_locales = response["locales"]
        for locale in available_locales:
            if country_code.lower() in locale["code"].lower():
                return locale["code"]
        return "fr_FR"

    def is_email_available(self, email):
        """Will check for given email existence in config's entity namespace
        and return whether email is free (not already taken) or not.
        :param email: email address to check
        :type email: str
        :return: availability (True if available, False if not)
        :rtype: bool
        """
        entity = self.config.EWalletApiEntity
        context = {
            "db_logger": self.config.cashout_api.db_logger,
            "action_name": hipay_refs.HIPAY_ACTION_GET_USER_EMAIL_AVAILABILITY,
            "application": self.get_application(),
        }
        with logging_context(**context):
            response = self.request(
                url=self.config.UserEmailIsAvailableApiEndpoint,
                post_args={
                    "user_email": email,
                    "entity": entity,
                },
                method="POST",
                mute_ssl_warnings=True,
            )

        code, message = response["code"], response["message"]

        # if request failed log an error and return 'Not available' result.
        if code != 0:
            logger.error(
                "Failed to check availability for email %s in "
                "entity %s. API Returned following error : %d - %s"
                "Email considered as unavailable.",
                email,
                entity,
                code,
                message,
            )
            return response, False
        # return availability from request.
        available = response["is_available"]
        logger.debug(
            "Availability for email %s in entity %s : %s",
            email,
            self.config.EWalletApiEntity,
            "available" if available else "taken",
        )
        # stop if email already taken
        return response, available

    @staticmethod
    def get_last_active_merchant_address(merchant):
        address = merchant.address.filter(
            _billing_address=True, status=status.ADDRESS_STATUS_ACTIVE
        ).last()

        if address is None:
            raise ValidationError(
                _(
                    "Billing address information not filled-in yet. Review the "
                    "'Company information' section to check what's missing."
                ),
                code=HIPAY_ERROR_ACCOUNT_SETUP_NOT_COMPLETE,
            )
        return address
