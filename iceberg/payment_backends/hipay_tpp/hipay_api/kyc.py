# -*- coding: utf-8 -*-

from logging import getLogger

import payment_backends.hipay_tpp.references as hipay_refs
from apps.kyc.models import KycInformation
from apps.kyc.models import MerchantIdentityDocument
from apps.kyc.models import MerchantIdentityDocument as DocsClass
from django.utils.translation import gettext_lazy as _
from payment_backends.common.utils import logging_context
from payment_backends.hipay_tpp.exceptions import (
    HipayDocumentTypeNotNeeded,
    HipayInternalError,
)
from payment_backends.hipay_tpp.hipay_api import RestAPIInterface
from payment_backends.hipay_tpp.references import (
    FILE_TAGS_MAPPING,
    HIPAY_ACCOUNT_TYPE_ASSOCIATION,
    HIPAY_ACCOUNT_TYPE_CORPORATION,
    HIPAY_ACCOUNT_TYPE_PERSON,
    HIPAY_ARTICLES_OF_ASSOCIATION,
    HIPAY_ASSOCIATION_OFFICIAL_JOURNAL,
    HIPAY_ASSOCIATION_PRESIDENT,
    HIPAY_ASSOCIATION_STATUS,
    HIPAY_BANK_ACCOUNT_DETAILS,
    HIPAY_ID_DOCUMENT,
    HIPAY_KBIS_EXTRACT,
    HIPAY_KYC_MESSAGES,
    HIPAY_PHYSICAL_KBIS_EXTRACT,
)
from payment_backends.hipay_tpp.utils import get_hipay_kyc_type
from reference.status import documents as izberg_doc_statuses

logger = getLogger(__name__)


class HipayKycApi(RestAPIInterface):
    def __init__(self, config):
        # intentionally skipping the "super().__init__" call because it'd have
        # called the "get_credentials_tuple" function before the wallet is set
        # but that raises exceptions. Further, the super __init__ function does
        # nothing interesting in ourcase.
        super(HipayKycApi, self).__init__()
        self.config = config
        self.current_wallet = None

    def get_application(self):
        return self.config.application

    def get_credentials_tuple(self):
        if self.current_wallet is None:
            raise RuntimeError("Current wallet not set before calling request")
        credentials = (self.config.EWalletApiLogin, self.config.EWalletApiPassword)
        self.current_wallet = None
        return credentials

    def send_kyc(self, wallet, document):
        """Upload a KYC file

        :param wallet: Hipay wallet to upload document-for.
        :type wallet: payment_backends.hipay_tpp.models.HipayWalletAccount

        :param document: MerchantIdentityDocument from izberg documents
        :type document: apps.stores.models.MerchantIdentityDocument
        :return: response dict with `code` and `description` keys
        :rtype: dict
        """
        self.current_wallet = wallet
        try:
            post_args = self.get_upload_parameters(wallet, document)
        except HipayDocumentTypeNotNeeded as e:
            return self.known_internal_exception_response(e)
        if isinstance(document, MerchantIdentityDocument):  # kyc v1
            files = {
                FILE_TAGS_MAPPING.get(part.tag, part.tag): part.part
                for part in document.parts.all()
            }
        else:  # kyc v2
            files = {
                FILE_TAGS_MAPPING.get(
                    part.tag.external_id, part.tag.external_id
                ): part.part
                for part in document.parts.all()
            }
        context = {
            "action_name": hipay_refs.HIPAY_ACTION_SEND_KYC,
            "db_logger": self.db_logger,
            "merchant": document.merchant,
        }
        with logging_context(**context):
            return self.request(
                url=self.config.KycApiEndpoint,
                method="POST",
                post_args=post_args,
                files=files,
                headers={"php-auth-subaccount-id": str(wallet.user_account_id)},
                mute_ssl_warnings=True,
                silent=True,
            )

    def get_kyc_status(self, wallet, document):
        if not document.external_id:
            return document.kyc_backend_status, document.kyc_backend_message

        response = self.get_all_statuses(wallet)
        NO_INFO = izberg_doc_statuses.DOCUMENT_PENDING, _("Unknown status")
        if response["code"] != 0:
            logger.warning("Hipay docs statuses fetching returned: %s", response)
            return NO_INFO

        for doc in response["documents"]:
            if doc["type"] == int(document.external_id):
                return self.convert_hipay_doc_dict_to_status_and_message(doc)

        return NO_INFO

    def resync_doc_statuses(self):
        # import here to break looped imports
        from payment_backends.hipay_tpp.models import HipayWalletAccount

        wallets = HipayWalletAccount.objects.filter(
            application_id=self.config.application_id
        )

        for wallet in wallets:
            if wallet.is_identified:
                continue
            merchant_id = wallet.merchant_id
            if not merchant_id:
                continue

            self.refresh_all_docs_at_once(wallet, merchant_id)

    @staticmethod
    def _get_linked_documents(hipay_doc_d, merchant_id):
        return list(
            MerchantIdentityDocument.objects.filter(
                merchant_id=merchant_id,
                external_id=hipay_doc_d["type"],
            )
            .exclude(
                status=MerchantIdentityDocument.ACTIVE,
            )
            .exclude(
                kyc_backend_status__in=[
                    izberg_doc_statuses.DOCUMENT_ACCEPTED,
                    izberg_doc_statuses.DOCUMENT_REFUSED,
                    izberg_doc_statuses.DOCUMENT_EXPIRED,
                    izberg_doc_statuses.DOCUMENT_NOT_NEEDED,
                ]
            )
        ) + list(
            KycInformation.objects.filter(
                merchant_id=merchant_id,
                external_id=hipay_doc_d["type"],
            )
            .exclude_active()
            .exclude(
                kyc_backend_status__in=[
                    izberg_doc_statuses.DOCUMENT_ACCEPTED,
                    izberg_doc_statuses.DOCUMENT_REFUSED,
                    izberg_doc_statuses.DOCUMENT_EXPIRED,
                    izberg_doc_statuses.DOCUMENT_NOT_NEEDED,
                ]
            )
        )

    def get_all_statuses(self, wallet):
        self.current_wallet = wallet
        context = {
            "action_name": hipay_refs.HIPAY_ACTION_UPDATE_KYC,
            "db_logger": self.db_logger,
            "merchant": wallet.merchant,
        }
        with logging_context(**context):
            return self.request(
                url=self.config.KycApiEndpoint,
                headers={
                    "php-auth-subaccount-id": str(wallet.user_account_id),
                },
                mute_ssl_warnings=True,
                silent=True,
            )

    @staticmethod
    def convert_hipay_status_to_izberg(hipay_status_code):
        return {
            -1: izberg_doc_statuses.DOCUMENT_PENDING,
            0: izberg_doc_statuses.DOCUMENT_UPLOADED,
            1: izberg_doc_statuses.DOCUMENT_UNDER_ANALYSIS,
            2: izberg_doc_statuses.DOCUMENT_ACCEPTED,
            3: izberg_doc_statuses.DOCUMENT_REFUSED,
            5: izberg_doc_statuses.DOCUMENT_UNDER_ANALYSIS,
            8: izberg_doc_statuses.DOCUMENT_REFUSED,
            9: izberg_doc_statuses.DOCUMENT_UNDER_ANALYSIS,
        }.get(
            hipay_status_code,
            # status unknown. Let's say it's being analysed to calm users down
            izberg_doc_statuses.DOCUMENT_UNDER_ANALYSIS,
        )

    def get_upload_parameters(self, wallet, document):
        """Create api parameters for KYC upload

        :param wallet: Hipay wallet to upload document-for.
        :type wallet: payment_backends.hipay_tpp.models.HipayWalletAccount

        # v1 (deprecated):
        :param document: MerchantIdentityDocument from izberg documents
        :type document: apps.kyc.models.MerchantIdentityDocument
        # v2:
        :param document: KycInformation from izberg documents
        :type document: apps.kyc.models.KycInformation
        """
        hipay_doctype, is_valid = get_hipay_kyc_type(wallet, document)
        if is_valid is False:
            raise HipayDocumentTypeNotNeeded(hipay_doctype)

        params = {"type": hipay_doctype}
        if hipay_doctype == HIPAY_ID_DOCUMENT:
            params["validity_date"] = document.validity_date
        return params

    @staticmethod
    def get_account_type_for_wallet(wallet):
        """All wallets are created under PROFESSIONAL_STATUS for now.

        :param wallet: wallet from which we want account type.
        :type wallet: payment_backends.hipay_tpp.models.HipayWalletAccount

        :rtype: int
        """
        return wallet.account_type

    @staticmethod
    def known_internal_exception_response(exception=None):
        """Generate response dict from exception
        :param exception: Exception handled
        :type exception: Exception

        :return: response dict
        :rtype: dict
        """
        return {
            "code": getattr(exception, "code", 0),
            "description": getattr(exception, "message", ""),
        }

    def refresh_document_status_from_hipay_state(self, wallet, document):
        status, message = self.get_kyc_status(wallet=wallet, document=document)
        self.update_doc_using_status_and_message(document, status, message)

    @staticmethod
    def update_doc_using_status_and_message(document, status, message):
        has_news = (
            status != document.kyc_backend_status
            or message != document.kyc_backend_message
        )

        if not has_news:
            # avoid updating document if it has no news since saving documents
            # triggers webhooks
            return

        document.kyc_backend_message = message

        if status == izberg_doc_statuses.DOCUMENT_ACCEPTED:
            document.accept_doc()
        elif status == izberg_doc_statuses.DOCUMENT_REFUSED:
            document.refuse_doc()
        elif status == izberg_doc_statuses.DOCUMENT_UNDER_ANALYSIS:
            document.analyse_doc()

        document.save(update_fields=["kyc_backend_message"])

    def convert_hipay_doc_dict_to_status_and_message(self, doc):
        """Take the hipay document dict & returns `status + message` as a tuple"""
        status_code = self.convert_hipay_status_to_izberg(doc["status_code"])
        status_message = HIPAY_KYC_MESSAGES.get(doc["status_code"], doc["status_label"])
        if doc.get("message"):
            # more details about the status, we want that
            status_message += " ({}: {})".format(doc["status_label"], doc["message"])
        return (status_code, status_message)

    def refresh_all_docs_at_once(self, wallet, merchant_id):
        """Loop on all hipay documents from a raw hipay call on
        GET `/api/identification.json`

        then update documents
        """
        all_statuses_response = self.get_all_statuses(wallet)

        if all_statuses_response["code"] != 0:
            raise HipayInternalError(code=all_statuses_response["code"])

        for hipay_dict in all_statuses_response["documents"]:
            self.refresh_all_docs_from_dict(hipay_dict, merchant_id)

    def refresh_all_docs_from_dict(self, hipay_dict, merchant_id):
        """Given a hipay document status dict (with a document type, status &
        message) - take care of updating all active documents related
        to the hipay document type (from dict data)
        """
        docs = self._get_linked_documents(hipay_dict, merchant_id)
        for doc in docs:
            info = self.convert_hipay_doc_dict_to_status_and_message(hipay_dict)
            self.update_doc_using_status_and_message(doc, *info)


# Hipay documentation:
# https://developer.hipay.com/getting-started/platform-hipay-marketplace/
# upload_kyc_doc/#how-to-upload-kyckyb-documents-with-this-specific-rest-
# api-types-of-kyckyb-documents
KYC_DOCUMENT_CLASSIFICATION_MATCHING = {
    # Hipay account type : IZBERG document type : hipay document class
    HIPAY_ACCOUNT_TYPE_PERSON: {  # 2
        # 1: 1
        DocsClass.IDENTITY_PROOF: HIPAY_ID_DOCUMENT,
        # 2: 8
        DocsClass.REGISTRATION_PROOF: HIPAY_PHYSICAL_KBIS_EXTRACT,
        # 6: 6
        DocsClass.BANK_INFOS_COPY: HIPAY_BANK_ACCOUNT_DETAILS,
    },
    HIPAY_ACCOUNT_TYPE_CORPORATION: {  # 1
        # 1: 1
        DocsClass.IDENTITY_PROOF: HIPAY_ID_DOCUMENT,
        # 2: 4
        DocsClass.REGISTRATION_PROOF: HIPAY_KBIS_EXTRACT,
        # 3: 5
        DocsClass.ARTICLES_OF_ASSOCIATION: HIPAY_ARTICLES_OF_ASSOCIATION,
        # 6: 6
        DocsClass.BANK_INFOS_COPY: HIPAY_BANK_ACCOUNT_DETAILS,
    },
    HIPAY_ACCOUNT_TYPE_ASSOCIATION: {  # 3
        # 1: 1
        DocsClass.IDENTITY_PROOF: HIPAY_ID_DOCUMENT,
        # 12: 11
        DocsClass.PRESIDENT_OF_ASSOCIATION: HIPAY_ASSOCIATION_PRESIDENT,
        # 13: 12
        DocsClass.ASSOCIATION_OFFICIAL_JOURNAL: HIPAY_ASSOCIATION_OFFICIAL_JOURNAL,
        # 14: 13
        DocsClass.ASSOCIATION_STATUS: HIPAY_ASSOCIATION_STATUS,
        # 6: 6
        DocsClass.BANK_INFOS_COPY: HIPAY_BANK_ACCOUNT_DETAILS,
    },
}
