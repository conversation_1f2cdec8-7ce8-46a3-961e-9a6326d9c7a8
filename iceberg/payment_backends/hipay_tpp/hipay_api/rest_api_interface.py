# -*- coding: utf-8 -*-
import base64
import logging

# import requests for API calls
from ssl import PROTOCOL_TLSv1_2

import six
from django.conf import settings
from payment_backends.common.utils import RestLogsBuilder, request_logger
from payment_backends.hipay_tpp.models import HipayApi<PERSON>allLog
from payment_backends.hipay_tpp.utils import (
    get_ip_address,
    raise_known_operation_exception,
)
from requests import sessions
from requests.adapters import HTTPAdapter
from requests.exceptions import HTTPError
from requests.models import Response
from requests.packages import urllib3
from urllib3.exceptions import SSLError
from urllib3.poolmanager import PoolManager

logger = logging.getLogger(__name__)


class RestAPIInterface:
    def __init__(self):
        self.db_logger = RestLogsBuilder(HipayApiCallLog)

    def get_credentials_tuple(self):
        raise NotImplementedError("This method must be implemented")

    def get_application(self):
        raise NotImplementedError("This method must be implemented")

    def get_auth_header(self):
        raw = ("%s:%s" % self.get_credentials_tuple()).strip()
        return {
            "Authorization": "Basic {}".format(
                six.ensure_text(base64.b64encode(six.ensure_binary(raw)))
            )
        }

    def _log_api_call_error_during_tests(self, url):
        """
        Mock me if you know what you're doing and you're sure you won't do
        any API call to hipay
        """
        if settings.TEST_IS_RUNNING:
            raise Exception(
                f"Making PSP request during tests, please mock "
                f"{self.__class__.__name__}.request()\n[url={url}]"
            )

    @request_logger
    def request(
        self,
        url,
        args=None,
        post_args=None,
        files=None,
        method="GET",
        headers=None,
        silent=False,
        mute_ssl_warnings=False,
    ):
        headers = self.enrich_headers(headers)
        self._log_api_call_error_during_tests(url)
        response = logged_tls_request(
            method=method,
            url=url,
            timeout=60,
            params=args,
            data=post_args,
            files=files,
            headers=headers,
            mute_ssl_warnings=mute_ssl_warnings,
        )
        if not silent:
            self.raise_for_error_response(response)
        return response

    def enrich_headers(self, headers):
        base_headers = {
            # accept JSON to use JSON serialized data
            "Accept": "application/json",
        }
        base_headers.update(self.get_auth_header())
        if headers is not None:
            base_headers.update(headers)
        return base_headers

    def raise_for_error_response(self, response):
        if response.status_code == 400:
            # raise exceptions according to hipay service error codes if any
            raise_known_operation_exception(response.text)

        self.log_and_raise_response_errors_for_status(response)
        # raise HTTP error statuses (4xx, 5xx ect...)
        response.raise_for_status()

    def log_and_raise_response_errors_for_status(self, response):
        """call logger to inform Izberg staff of some known issue with
        Hipay API
        @param response: hipay request's response to exploit
        """
        try:
            return response.raise_for_status()
        except HTTPError as e:
            if "403" in e.args[0]:
                logger.error(
                    "Application %s has misconfigured its hipay "
                    "security account for cash-in process : it looks "
                    "like Izberg server IP %s is not in hipay-"
                    "fullservice security whitelist.",
                    self.get_application(),
                    get_ip_address(),
                )
            elif "401" in e.args[0]:
                logger.error(
                    "Application %s has misconfigured its izberg "
                    "payment settings : it looks like the credentials"
                    " acknowledged are wrong",
                    self.get_application(),
                )
            raise


class ForceTLSAdapter(HTTPAdapter):
    """
    Force TLSv1.2 for the request lib connection
    """

    def init_poolmanager(self, connections, maxsize, block=False, **connexion_pool_kw):
        # This method gets called when there's no proxy.
        self.poolmanager = PoolManager(
            num_pools=connections,
            maxsize=maxsize,
            block=block,
            ssl_version=PROTOCOL_TLSv1_2,
            **connexion_pool_kw,
        )

    def proxy_manager_for(self, proxy, **proxy_kwargs):
        # This method is called when there is a proxy.
        proxy_kwargs["ssl_version"] = PROTOCOL_TLSv1_2
        return super(ForceTLSAdapter, self).proxy_manager_for(proxy, **proxy_kwargs)


def tls_request(method: str, url: str, **kwargs) -> Response:
    kwargs = dict(kwargs)
    mute_ssl_warnings = kwargs.pop("mute_ssl_warnings", False)
    with sessions.Session() as session:
        session.mount("https://", ForceTLSAdapter())
        try:
            return session.request(method=method, url=url, **kwargs)
        except SSLError as e:
            if not mute_ssl_warnings:
                raise
            logger.error(
                "A SSL verification error occured but mute_ssl_warning "
                "parameter is set so we're replaying the same request and "
                "ignoring the verification issue : %s",
                repr(e),
            )
            urllib3.disable_warnings()
            return tls_request(method, url, **kwargs)


def logged_tls_request(*args, **kwargs):
    def compute_elapsed_time(response):
        elapsed = response.elapsed
        try:
            return elapsed.total_seconds()
        except Exception:
            return (elapsed.days * 1440 + elapsed.seconds // 60) * 60

    logger.debug(
        "REQUEST %s - %s - %s - GET PARAMS: %s - POST PARAMS: %s - files: %s",
        kwargs.get("method"),
        kwargs.get("url"),
        kwargs.get("headers"),
        kwargs.get("params"),
        kwargs.get("data"),
        kwargs.get("files"),
    )

    response = tls_request(*args, **kwargs)

    elapsed = compute_elapsed_time(response)
    logger.debug(
        "RESPONSE - Status: %s - Response Time (s): %s - %s",
        response.status_code,
        elapsed,
        response.text,
    )
    return response
