# -*- coding: utf-8 -*-

import logging

from django.conf import settings
from django.db.models.signals import post_save
from django.dispatch.dispatcher import receiver
from payment_backends.hipay_tpp.models import HipayWalletAccount

from .tasks import sync_all_kyc

logger = logging.getLogger(__name__)


@receiver(post_save, sender=HipayWalletAccount)
def sync_all_existing_documents(sender, instance, created, **kwargs):
    """Hook that sends all pending documents when user creates his wallet.

    Later on, documents will be sent at upload time, but here we flush the
    pending documents list.
    """

    wallet = instance
    # skip on wallet "updates" since docs are sent on their upload when wallet
    # is already created.
    if not created:
        return

    # Note 1: identification check is made in the task as it's an external
    # connector check. I want to avoid loosing too-much time on wallet-creation
    # call.

    # Note 2: Even if you might think a wallet creation implies an unidentified
    # wallet, keep in mind you can create wallets directly from existing
    # credentials

    # skip if application commission wallet
    if wallet.merchant_id is None:
        return

    try:
        if settings.DEBUG:
            sync_all_kyc(wallet.merchant_id, wallet.merchant.application_id)
        else:
            sync_all_kyc.delay(wallet.merchant_id, wallet.merchant.application_id)

    except Exception:
        # failing here must not raise a 500 on API since we can at anytime,
        # update our pending documents to trigger a new synchronization
        # even if it's a SQS related error.
        # However, we may ping the operator so that he assists his merchant or
        # knows abount some un-sent docs.
        logger.exception("Failed to synchronize existing documents with hipay")
