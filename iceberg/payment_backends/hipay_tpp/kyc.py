# -*- coding: utf-8 -*-
import logging

from apps.kyc.models import KycInformation, MerchantIdentityDocument
from django.utils.translation import gettext_lazy as _
from payment_backends.common import BaseKycBackend, KycBackendMeta
from payment_backends.hipay_tpp.processor import HipayTppPayment

from .actions.merchant_publish_kyc import send_kyc
from .exceptions import HipayMerchantWalletNotFound
from .hipay_api.kyc import KYC_DOCUMENT_CLASSIFICATION_MATCHING
from .models import HipayBankAccount, HipayWalletAccount
from .references import ACCOUNT_CREATION_SYNCHRONIZED, HIPAY_BANK_ACCOUNT_DETAILS
from .utils import get_hipay_kyc_type

logger = logging.getLogger(__name__)


class KycDocumentBackendMeta(KycBackendMeta):
    def __init__(self, merchant):
        super(KycDocumentBackendMeta, self).__init__(merchant)
        try:
            merchant_account = HipayWalletAccount.objects.get(merchant=merchant)
        except HipayWalletAccount.DoesNotExist:
            accepted_types = {
                doc_type
                for mapping in KYC_DOCUMENT_CLASSIFICATION_MATCHING.values()
                for doc_type in mapping.keys()
            }
        else:
            account_type = merchant_account.account_type
            mapping = KYC_DOCUMENT_CLASSIFICATION_MATCHING[account_type]
            accepted_types = {doc_type for doc_type in mapping.keys()}
        self.doc_types = {
            key: value
            for key, value in self.all_doc_types.items()
            if key in accepted_types
        }

    def get_meta_for_type(self, doc_type):
        if doc_type == MerchantIdentityDocument.IDENTITY_PROOF:
            type_name = self.doc_types[doc_type]
            return {
                "front": {
                    "name": type_name,
                    "help_text": _("Front of the {front}").format(
                        front=type_name.lower()
                    ),
                },
                "back": {
                    "name": type_name,
                    "help_text": _("Back of the {back}").format(back=type_name.lower()),
                },
            }
        else:
            return super(KycDocumentBackendMeta, self).get_meta_for_type(doc_type)


class HipayKycBackend(BaseKycBackend):
    def __init__(self, merchant, config):
        super(HipayKycBackend, self).__init__(merchant)
        self.config = config
        self.merchant_wallet = self._get_wallet(merchant)
        self.bank_account = self._get_bank_account(merchant)

    @property
    def meta(self):
        return KycDocumentBackendMeta(self.merchant)

    def is_document_acceptable(self, document):
        # If no wallet, don't reject any document.
        if self.merchant_wallet is None:
            return False, "Can't check KYC without a hipay wallet account."
        _, is_valid = get_hipay_kyc_type(self.merchant_wallet, document)

        if is_valid is False:
            template = "Document type {} not acceptable for merchant {}"
            return False, template.format(
                self._get_doc_type(document), self.merchant.name
            )
        return True, None

    def _get_doc_type(self, document):
        """Helper returning the doc type from v1 or v2 document"""
        if isinstance(document, KycInformation):  # v2
            return document.kyc_type.external_id
        else:  # v1
            return document.document_type

    def _is_document_inactive(self, document):
        """Helper needed to check if v1 and v2 document is inactive"""
        if isinstance(document, KycInformation):  # v2
            return not document.status.is_active
        else:  # v1
            return document.status != document.ACTIVE

    def _is_bank_info(self, document):
        """Helper needed to check if v1 and v2 document is a bank info"""
        if isinstance(document, KycInformation):  # v2
            return document.kyc_type.external_id == str(HIPAY_BANK_ACCOUNT_DETAILS)
        else:  # v1
            return document.document_type == document.BANK_INFOS_COPY

    def can_publish(self, document):
        can_publish, reason = super().can_publish(document)
        if can_publish is False:
            return can_publish, reason
        invalid_wallet = self.merchant_wallet is None or (
            self.merchant_wallet.synchro_status != ACCOUNT_CREATION_SYNCHRONIZED
        )
        if invalid_wallet:
            return False, "Missing or unsynced wallet"
        if self._is_document_inactive(document):
            return False, "Inactive document"

        if self._is_bank_info(document):
            # this hook is not in charge of sending bank info anymore.
            # Bank info copy document is sent through bank account selection
            return False, "Bank info are ignored"

        if self.config.cashout_api.is_identified(self.merchant_wallet):
            return False, "Merchant already identified"
        return True, None

    def publish(self, document):
        return send_kyc(self.config, self.merchant_wallet, document)

    def _get_wallet(self, merchant):
        try:
            return self.config.cashout_api.get_wallet(merchant=merchant)
        except HipayMerchantWalletNotFound:
            logger.info(
                "Merchant account not created yet. documents will be "
                "uploaded after bank account selection."
            )
            return None

    def _get_bank_account(self, merchant):
        try:
            return HipayBankAccount.objects.get(merchant=merchant)
        except HipayBankAccount.DoesNotExist:
            logger.info("Merchant bank account not selected for hipay cashout.")
            return None


def build_backend_from_merchant(merchant):
    config = HipayTppPayment().get_config(merchant.application)
    return HipayKycBackend(merchant, config)
