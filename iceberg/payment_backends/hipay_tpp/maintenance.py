# -*- coding=utf-8 -*-
import json
import logging
from decimal import Decimal

from defusedcsv import csv
from django.db.models import Q
from payment_backends.hipay_tpp.actions.settlement import (
    reschedule_outdated_sell_transactions,
)
from payment_backends.hipay_tpp.models import (
    HipayApiCallLog,
    HipayBankAccount,
    HipayTransaction,
    HipayWalletAccount,
)
from payment_backends.hipay_tpp.money_tracker import HipayMoneyTracker

logger = logging.getLogger(__name__)


class excel_fr(csv.Dialect):
    """Describe the usual properties of French Excel-generated CSV files."""

    delimiter = ";"
    quotechar = '"'
    doublequote = True
    skipinitialspace = False
    lineterminator = "\r\n"
    quoting = csv.QUOTE_MINIMAL


csv.register_dialect("excel-fr", excel_fr)


def get_transactions(filename):
    """Read transaction file given from hipay"""
    with open(filename, "r", encoding="utf-8-sig") as f:
        return list(csv.DictReader(f, dialect="excel-fr"))


def declare_hipay_transactions_as_settled(application_id, transactions, dry_run=True):
    """declare all related MOs as settled + reschedule failed izberg transactions"""
    trans_count = len(transactions)
    print("{} transactions to process".format(trans_count))
    result = {"valid": [], "skipped": []}
    for index, trans_dict in enumerate(transactions):
        hipay_trans = HipayTransaction.objects.get(
            payment__application=application_id,
            transaction_reference=trans_dict["Transaction ID"],
        )
        authorized_amount = Decimal(trans_dict["Authorized"].replace(",", ""))
        collected_amount = Decimal(
            trans_dict["Captured"].replace("€", "").replace(",", "")
        )
        print(
            "[{}/{}] triggering hipay trans {} - ref {} - payment {} - authorized {}"
            " - captured {}".format(
                index + 1,
                trans_count,
                hipay_trans.id,
                hipay_trans.transaction_reference,
                hipay_trans.payment_id,
                authorized_amount,
                collected_amount,
            )
        )
        if hipay_trans.authorized_amount != authorized_amount:
            print(
                "Authorized amount mismatch : {} != {}, skipping transaction".format(
                    hipay_trans.authorized_amount, authorized_amount
                )
            )
            result["skipped"].append(trans_dict["Transaction ID"])
            continue
        if hipay_trans.payment.collected_amount != collected_amount:
            print(
                "Captured amount mismatch : {} != {}, skipping transaction".format(
                    hipay_trans.payment.collected_amount, collected_amount
                )
            )
            result["skipped"].append(trans_dict["Transaction ID"])
            continue
        operations = (
            trans_dict.get("Operation merchant ID")
            or trans_dict["Payment operation MID"]
        )
        if "[" in operations:
            merchant_order_ids = set(
                [
                    int(op.replace("CAPTURE:", ""))
                    for op in json.loads(operations)
                    if "CAPTURE:" in op
                ]
            )
        else:
            merchant_order_ids = [int(operations.replace("CAPTURE:", ""))]
        print(f"merchant_order_ids={merchant_order_ids}")
        tracker = HipayMoneyTracker(hipay_trans.payment)
        merchant_orders = list(
            hipay_trans.payment.order.merchant_orders.exclude_canceled_deleted().filter(
                id__in=merchant_order_ids
            )
        )
        if len(merchant_orders) != len(merchant_order_ids):
            mos = hipay_trans.payment.order.merchant_orders.exclude_canceled_deleted()
            mo_ids = list(mos.values_list("id", flat=True))
            print(
                f"Merchant order ids mismatch : {merchant_order_ids} not in {mo_ids}, "
                f"skipping transaction"
            )
            result["skipped"].append(trans_dict["Transaction ID"])
            continue
        result["valid"].append(trans_dict["Transaction ID"])
        if dry_run:
            print("ALL OK, but dry_run >> skipping")
            continue
        for mo in merchant_orders:
            print("setting {} on escrow: {}".format(mo, tracker.set_on_escrow(mo)))
        reschedule_outdated_sell_transactions(hipay_trans)
    return result


def display_progress(model_name, total_count, subset_count):
    if total_count > 0:
        percent = (subset_count / total_count) * 100
        logger.info({model_name + " progression %": percent})


def split_by_ids_loop_migration_exec(func, start_at=0, increment=200000):
    from_id = start_at
    to_id = from_id + increment
    remaining_raw, total_count = func(from_id, to_id, 0, 0)
    while remaining_raw > 0:
        from_id = from_id + increment
        to_id = from_id + increment
        remaining_raw, total_count = func(from_id, to_id, remaining_raw, total_count)
    logger.info("Done")


def migration_copy_HipayTransaction_new_fields_in_temp(
    from_id, to_id, total_raw=0, total_count=0
):
    logger.info(
        "Start copy HipayTransaction field legacy into new"
        + str(from_id)
        + " to id "
        + str(to_id)
    )

    done_count = 0
    not_modified = 0
    empty_value = 0

    list_fields = [
        "escrow_delivery_settlement",
        "capture_operations",
        "refund_operations",
    ]

    if total_raw == 0:
        total_raw = total_count = HipayTransaction.objects.filter(
            Q(escrow_delivery_settlement_temp__isnull=True)
            | Q(capture_operations_temp__isnull=True)
            | Q(refund_operations__isnull=True)
        ).count()
    all_hipay_transactions = HipayTransaction.objects.filter(
        pk__gte=from_id, pk__lt=to_id
    ).filter(
        Q(escrow_delivery_settlement_temp__isnull=True)
        | Q(capture_operations_temp__isnull=True)
        | Q(refund_operations__isnull=True)
    )
    subset_count = all_hipay_transactions.count()
    logger.info({"subset_count": subset_count})
    for hipay_transaction in all_hipay_transactions.page_chunk_iterate():
        elem_updated = {}
        for elem in list_fields:
            elem_temp = f"{elem}_temp"
            field = getattr(hipay_transaction, elem)
            temp_field = getattr(hipay_transaction, elem_temp)
            if temp_field != field:
                elem_updated[elem_temp] = field
            if temp_field is None:
                elem_updated[elem_temp] = {}
        if bool(elem_updated):
            HipayTransaction.objects.filter(pk=hipay_transaction.id).update(
                **elem_updated
            )
            done_count += 1
        else:
            empty_value += 1
    logger.info(
        {
            "from_id": from_id,
            "to_id": to_id,
            "hipaytransaction_done_count": done_count,
            "hipaytransaction_total": total_count,
            "hipaytransaction_empty_value": empty_value,
            "hipaytransaction_not_modified": not_modified,
        }
    )
    display_progress("HipayTransaction", total_count, total_raw - subset_count)
    return total_raw - subset_count, total_count


def migration_copy_HipayBankAccount_new_fields_in_temp(
    from_id, to_id, total_raw=0, total_count=0
):
    logger.info(
        "Start copy HipayBankAccount field into temp"
        + str(from_id)
        + " to id "
        + str(to_id)
    )
    done_count = 0
    not_modified = 0
    empty_value = 0

    if total_raw == 0:
        total_count = total_raw = HipayBankAccount.objects.filter(
            synchro_error_temp__isnull=True
        ).count()
    all_hipay_bank_account = HipayBankAccount.objects.filter(
        pk__gte=from_id, pk__lt=to_id, synchro_error_temp__isnull=True
    )
    subset_count = all_hipay_bank_account.count()
    logger.info({"subset_count": subset_count})
    for hipay_bank_account in all_hipay_bank_account.page_chunk_iterate():
        if bool(hipay_bank_account.synchro_error):
            if (
                hipay_bank_account.synchro_error_temp
                != hipay_bank_account.synchro_error
            ):
                HipayBankAccount.objects.filter(pk=hipay_bank_account.id).update(
                    synchro_error_temp=hipay_bank_account.synchro_error
                )
                done_count += 1
            else:
                not_modified += 1
        else:
            HipayBankAccount.objects.filter(pk=hipay_bank_account.id).update(
                synchro_error_temp={}
            )
            empty_value += 1
    logger.info(
        {
            "from_id": from_id,
            "to_id": to_id,
            "hipay_done_count": done_count,
            "hipay_total": total_count,
            "hipay_empty_value": empty_value,
            "hipay_not_modified": not_modified,
        }
    )
    display_progress("HipayBankAccount", total_count, total_raw - subset_count)
    return total_raw - subset_count, total_count


def migration_copy_HipayWalletAccount_new_fields_in_temp(
    from_id, to_id, total_raw=0, total_count=0
):
    logger.info(
        "Start copy HipayWalletAccount field into temp"
        + str(from_id)
        + " to id "
        + str(to_id)
    )
    done_count = 0
    not_modified = 0
    empty_value = 0

    if total_raw == 0:
        total_count = total_raw = HipayWalletAccount.objects.filter(
            synchro_error_temp__isnull=True
        ).count()
    all_hipay_wallet_account = HipayWalletAccount.objects.filter(
        pk__gte=from_id, pk__lt=to_id, synchro_error_temp__isnull=True
    )
    subset_count = all_hipay_wallet_account.count()
    logger.info({"subset_count": subset_count})
    for hipay_wallet_account in all_hipay_wallet_account.page_chunk_iterate():
        if bool(hipay_wallet_account.synchro_error):
            if (
                hipay_wallet_account.synchro_error_temp
                != hipay_wallet_account.synchro_error
            ):
                HipayWalletAccount.objects.filter(pk=hipay_wallet_account.id).update(
                    synchro_error_temp=hipay_wallet_account.synchro_error
                )
                done_count += 1
            else:
                not_modified += 1
        else:
            HipayWalletAccount.objects.filter(pk=hipay_wallet_account.id).update(
                synchro_error_temp={}
            )
            empty_value += 1
    logger.info(
        {
            "from_id": from_id,
            "to_id": to_id,
            "product_done_count": done_count,
            "product_total": total_count,
            "product_empty": empty_value,
            "product_not_modified": not_modified,
        }
    )
    display_progress("HipayWalletAccount", total_count, total_raw - subset_count)
    return total_raw - subset_count, total_count


def migration_copy_hipay_new_fields_in_temp():
    # MIG: LegacyJSONField
    # Delete this part of the code when deleted the old field
    split_by_ids_loop_migration_exec(migration_copy_HipayTransaction_new_fields_in_temp)
    split_by_ids_loop_migration_exec(migration_copy_HipayBankAccount_new_fields_in_temp)
    split_by_ids_loop_migration_exec(
        migration_copy_HipayWalletAccount_new_fields_in_temp
    )


def migration_copy_HipayApiCallLog_legacy_fields_in_temp(
    from_id, to_id, total_raw=0, total_count=0
):
    logger.info(
        "Start copy HipayApiCallLog field legacy into temp"
        + str(from_id)
        + " to id "
        + str(to_id)
    )

    done_count = 0
    empty_value = 0
    not_modified = 0

    if total_raw == 0:
        total_count = total_raw = HipayApiCallLog.objects.filter(
            headers_temp__isnull=True
        ).count()
    all_hipay_log = HipayApiCallLog.objects.filter(
        pk__gte=from_id, pk__lt=to_id, headers_temp__isnull=True
    )
    subset_count = all_hipay_log.count()
    logger.info({"subset_count": subset_count})
    for hipay_log in all_hipay_log.page_chunk_iterate():
        if bool(hipay_log.headers):
            if hipay_log.headers_temp != hipay_log.headers:
                HipayApiCallLog.objects.filter(pk=hipay_log.id).update(
                    headers_temp=hipay_log.headers
                )
                done_count += 1
            else:
                not_modified += 1
        else:
            HipayApiCallLog.objects.filter(pk=hipay_log.id).update(headers_temp={})
            empty_value += 1
    logger.info(
        {
            "from_id": from_id,
            "to_id": to_id,
            "hipay_log_count": done_count,
            "hipay_log_total": total_count,
            "hipay_log_error_value": empty_value,
            "hipay_log_not_modified": not_modified,
        }
    )
    display_progress("HipayApiCallLog", total_count, total_raw - subset_count)
    return total_raw - subset_count, total_count


def migration_copy_hipay_log_fields_in_new():
    # MIG: LegacyJSONField
    # Delete this part of the code when deleted the old field
    split_by_ids_loop_migration_exec(
        migration_copy_HipayApiCallLog_legacy_fields_in_temp
    )
