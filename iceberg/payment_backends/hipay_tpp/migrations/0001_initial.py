# Generated by Django 3.2.20 on 2023-08-24 09:28

from decimal import Decimal
import django.core.files.storage
import django.core.serializers.json
from django.db import migrations, models
import django_xworkflows.models
import ims.api.mixins
import ims.models.mixin
import lib.api.file_path
import mp_utils.model_mixins.decimal_field_check_mixin
import payment_backends.hipay_tpp.models.payment_page_css
import uploader.models.mixins


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="HipayApiCallLog",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_on",
                    models.DateTimeField(
                        auto_now_add=True, null=True, verbose_name="Created on"
                    ),
                ),
                (
                    "last_modified",
                    models.DateTimeField(
                        auto_now=True, null=True, verbose_name="Last modified"
                    ),
                ),
                ("target_url", models.CharField(max_length=255)),
                ("data", models.TextField(blank=True, null=True)),
                ("method", models.CharField(max_length=10)),
                (
                    "headers",
                    models.JSONField(
                        blank=True,
                        db_column="headers",
                        default=dict,
                        encoder=django.core.serializers.json.DjangoJSONEncoder,
                    ),
                ),
                ("response_body", models.TextField(blank=True, null=True)),
                (
                    "response_file",
                    models.FileField(
                        blank=True,
                        max_length=1024,
                        null=True,
                        storage=django.core.files.storage.FileSystemStorage(),
                        upload_to=lib.api.file_path.FilePathGenerator(),
                    ),
                ),
                (
                    "response_file_bkp",
                    models.FileField(
                        blank=True,
                        default=None,
                        max_length=1024,
                        null=True,
                        storage=django.core.files.storage.FileSystemStorage(),
                        upload_to="",
                    ),
                ),
                ("status_code", models.IntegerField(blank=True, null=True)),
                ("response_time", models.DurationField(blank=True, null=True)),
                (
                    "action",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("validate", "Payment authorization using token"),
                            ("payment_get_status", "Payment status checking"),
                            ("capture", "Capture request"),
                            ("refund", "Refund request"),
                            ("get_settlement", "Get settlement"),
                            ("create_wallet", "Create wallet"),
                            ("get_wallet", "Get wallet"),
                            ("send_kyc", "Send KYC"),
                            ("get_kyc_status", "Get KYC status"),
                            ("set_bank_account", "Set bank account"),
                            ("get_bank_account", "Get bank account"),
                            ("transfer_money", "Transfer"),
                            ("withdraw", "Withdraw"),
                            ("get_bank_account", "Get user balance"),
                            (
                                "get_user_email_availability",
                                "Get user email availability",
                            ),
                            ("get_locale_codes", "Get locale codes"),
                            ("get_wallet_info", "Get wallet info"),
                        ],
                        max_length=255,
                        null=True,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(
                ims.api.mixins.ApiUtilsMixin,
                ims.models.mixin.SanitizeCharFieldMixin,
                ims.models.mixin.ChangedFieldLookupMixin,
                mp_utils.model_mixins.decimal_field_check_mixin.DecimalFieldCheckMixin,
                models.Model,
            ),
        ),
        migrations.CreateModel(
            name="HipayAppConfiguration",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_on",
                    models.DateTimeField(
                        auto_now_add=True, null=True, verbose_name="Created on"
                    ),
                ),
                (
                    "last_modified",
                    models.DateTimeField(
                        auto_now=True, null=True, verbose_name="Last modified"
                    ),
                ),
                ("active", models.BooleanField(default=False)),
                (
                    "staging",
                    models.BooleanField(
                        blank=True,
                        choices=[
                            (True, "Force to use staging"),
                            (False, "Force to use production"),
                            (None, "Let IZBERG API select the environment"),
                        ],
                        null=True,
                    ),
                ),
                (
                    "offline_refund",
                    models.BooleanField(
                        default=False,
                        help_text="If enabled, all refunds will be treated as bank transfers by default",
                        verbose_name="Force offline refunds",
                    ),
                ),
                (
                    "configuration_description",
                    models.CharField(blank=True, default="", max_length=255),
                ),
                (
                    "cashin_mode",
                    models.CharField(
                        choices=[
                            ("izberg", "Handled - We're in charge of everything"),
                            ("prestashop", "Partially handled - Capture & refund only"),
                            ("unhandled", "Unhanded - Don't handle cashin"),
                        ],
                        default="izberg",
                        help_text="Select cashin-mode i.e. what payment features are used.",
                        max_length=50,
                    ),
                ),
                ("cashout_enabled", models.BooleanField(default=True)),
                ("RestApiLogin", models.CharField(max_length=64)),
                ("RestApiPassword", models.CharField(max_length=64)),
                (
                    "RestApiPassphrase",
                    models.CharField(blank=True, default="", max_length=128),
                ),
                ("EWalletApiEntity", models.CharField(max_length=128)),
                ("EWalletApiLogin", models.CharField(max_length=64)),
                ("EWalletApiPassword", models.CharField(max_length=64)),
                (
                    "account_id",
                    models.IntegerField(verbose_name="Hipay technical account id"),
                ),
                (
                    "eci",
                    models.IntegerField(default=7, verbose_name="Default ECI level"),
                ),
                (
                    "Enable3ds",
                    models.IntegerField(
                        blank=True,
                        choices=[
                            (None, "fraud-module"),
                            (0, "no"),
                            (1, "sometimes"),
                            (2, "always"),
                        ],
                        default=1,
                        help_text="3DS activation level: fraud-module means it's up to hipay's fraud module to activate it using scoring threshold & rules.",
                        null=True,
                    ),
                ),
                (
                    "payment_products",
                    models.CharField(
                        blank=True, default="visa,mastercard", max_length=400
                    ),
                ),
                (
                    "payment_products_categories",
                    models.CharField(blank=True, default="credit-card", max_length=400),
                ),
                (
                    "payment_form_template",
                    models.CharField(
                        choices=[
                            ("basic-js", "no Encapsulation - user must be redirected"),
                            ("iframe-js", "iFrame encapsulated"),
                        ],
                        default="basic-js",
                        max_length=10,
                    ),
                ),
                ("display_payment_method_selector", models.BooleanField(default=False)),
                (
                    "single_payment",
                    models.BooleanField(
                        default=True, verbose_name="Recurring payment activation"
                    ),
                ),
                ("accept_url", models.URLField(blank=True, default="", max_length=255)),
                (
                    "decline_url",
                    models.URLField(blank=True, default="", max_length=255),
                ),
                (
                    "pending_url",
                    models.URLField(blank=True, default="", max_length=255),
                ),
                (
                    "exception_url",
                    models.URLField(blank=True, default="", max_length=255),
                ),
                ("cancel_url", models.URLField(blank=True, default="", max_length=255)),
            ],
            options={
                "abstract": False,
            },
            bases=(
                ims.api.mixins.ApiUtilsMixin,
                ims.models.mixin.SanitizeCharFieldMixin,
                ims.models.mixin.ChangedFieldLookupMixin,
                mp_utils.model_mixins.decimal_field_check_mixin.DecimalFieldCheckMixin,
                models.Model,
            ),
        ),
        migrations.CreateModel(
            name="HipayBankAccount",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_on", models.DateTimeField(auto_now_add=True)),
                ("last_update_on", models.DateTimeField(auto_now=True)),
                (
                    "synchro_status",
                    django_xworkflows.models.StateField(
                        max_length=16,
                        workflow=django_xworkflows.models._SerializedWorkflow(
                            initial_state="1",
                            name="HipayBankAccountWorkflow",
                            states=["1", "2", "3", "4"],
                        ),
                    ),
                ),
                (
                    "synchro_error",
                    models.JSONField(
                        blank=True,
                        db_column="synchro_error",
                        default=dict,
                        encoder=django.core.serializers.json.DjangoJSONEncoder,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=(
                django_xworkflows.models.BaseWorkflowEnabled,
                ims.models.mixin.SanitizeCharFieldMixin,
                ims.models.mixin.ChangedFieldLookupMixin,
                models.Model,
            ),
        ),
        migrations.CreateModel(
            name="HipayPaymentPageCSS",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "css_source_file",
                    models.FileField(
                        max_length=1024,
                        upload_to=payment_backends.hipay_tpp.models.payment_page_css.HipayPaymentPageCSSFilePathGenerator(),
                    ),
                ),
                (
                    "css_source_file_bkp",
                    models.FileField(
                        blank=True,
                        default=None,
                        max_length=1024,
                        null=True,
                        upload_to="",
                    ),
                ),
                (
                    "config_id",
                    models.IntegerField(
                        help_text="Temporarily store configuration ID so we can establish expected link after model's save."
                    ),
                ),
            ],
            bases=(uploader.models.mixins.UploaderMixin, models.Model),
        ),
        migrations.CreateModel(
            name="HipayTransaction",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_on", models.DateTimeField(auto_now_add=True)),
                ("last_update_on", models.DateTimeField(auto_now=True)),
                (
                    "transaction_reference",
                    models.CharField(
                        blank=True, default=None, max_length=255, null=True
                    ),
                ),
                (
                    "dirty_capture",
                    models.BooleanField(
                        default=False, help_text="A capture that has been made"
                    ),
                ),
                ("error_code", models.IntegerField(default=0)),
                ("message", models.CharField(blank=True, default="", max_length=255)),
                ("reason", models.CharField(blank=True, default="", max_length=255)),
                ("state", models.CharField(blank=True, default="", max_length=255)),
                ("status", models.IntegerField(default=None, null=True)),
                ("test", models.BooleanField(blank=True, default=None, null=True)),
                (
                    "hipay_orderid",
                    models.CharField(default=None, max_length=32, null=True),
                ),
                ("forward_url", models.URLField(default=None, null=True)),
                ("eci_level", models.IntegerField(default=7)),
                (
                    "authorized_amount",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=20
                    ),
                ),
                (
                    "captured_amount",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=20
                    ),
                ),
                (
                    "refunded_amount",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=20
                    ),
                ),
                (
                    "settled_amounts",
                    models.JSONField(
                        default=None,
                        encoder=django.core.serializers.json.DjangoJSONEncoder,
                        help_text="Keep tracks of every settled amount by operation id",
                        null=True,
                    ),
                ),
                (
                    "escrow_delivery_settlement",
                    models.JSONField(
                        blank=True,
                        db_column="escrow_delivery_settlement",
                        default=dict,
                        encoder=django.core.serializers.json.DjangoJSONEncoder,
                        help_text="For every merchant-order you can keep trace of its money, being delivered onto escrow wallet.",
                    ),
                ),
                ("attempt_id", models.IntegerField(default=None, null=True)),
                (
                    "ip_address",
                    models.CharField(blank=True, default="", max_length=255),
                ),
                (
                    "payment_product",
                    models.CharField(blank=True, default="", max_length=255),
                ),
                (
                    "payment_method_brand",
                    models.CharField(blank=True, default="", max_length=255),
                ),
                (
                    "payment_method_expiry",
                    models.CharField(blank=True, default="", max_length=255),
                ),
                (
                    "payment_method_token",
                    models.CharField(blank=True, default="", max_length=255),
                ),
                (
                    "payment_method_card_holder",
                    models.CharField(blank=True, default="", max_length=255),
                ),
                (
                    "payment_method_country",
                    models.CharField(blank=True, default="", max_length=255),
                ),
                (
                    "payment_method_issuer_bank",
                    models.CharField(blank=True, default="", max_length=255),
                ),
                (
                    "payment_method_pan",
                    models.CharField(blank=True, default="", max_length=255),
                ),
                (
                    "capture_operations",
                    models.JSONField(
                        blank=True,
                        db_column="capture_operations",
                        default=dict,
                        encoder=django.core.serializers.json.DjangoJSONEncoder,
                        help_text="Link between an Hipay merchant operation ID and IZBERG merchant order Ids for captures",
                    ),
                ),
                (
                    "refund_operations",
                    models.JSONField(
                        blank=True,
                        db_column="refund_operations",
                        default=dict,
                        encoder=django.core.serializers.json.DjangoJSONEncoder,
                        help_text="Link between an Hipay merchant operation ID and IZBERG merchant order Ids for refund",
                    ),
                ),
            ],
            bases=(
                ims.models.mixin.SanitizeCharFieldMixin,
                ims.models.mixin.ChangedFieldLookupMixin,
                models.Model,
            ),
        ),
        migrations.CreateModel(
            name="HipayWalletAccount",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_on", models.DateTimeField(auto_now_add=True)),
                ("last_update_on", models.DateTimeField(auto_now=True)),
                (
                    "synchro_status",
                    django_xworkflows.models.StateField(
                        max_length=16,
                        workflow=django_xworkflows.models._SerializedWorkflow(
                            initial_state="pending",
                            name="SynchronizationWorkflow",
                            states=["pending", "synchronized", "failed"],
                        ),
                    ),
                ),
                (
                    "synchro_error",
                    models.JSONField(
                        blank=True,
                        db_column="synchro_error",
                        default=dict,
                        encoder=django.core.serializers.json.DjangoJSONEncoder,
                    ),
                ),
                ("api_login", models.CharField(blank=True, max_length=64, null=True)),
                (
                    "api_password",
                    models.CharField(blank=True, max_length=64, null=True),
                ),
                ("user_account_id", models.IntegerField(blank=True, null=True)),
                ("user_space_id", models.IntegerField(blank=True, null=True)),
                (
                    "websiteIds",
                    models.CharField(blank=True, default="", max_length=255),
                ),
                (
                    "callback_salt",
                    models.CharField(
                        blank=True,
                        help_text="Salt fetched from Hipay API used to validate notifications",
                        max_length=64,
                    ),
                ),
                ("email", models.EmailField(blank=True, max_length=128)),
                (
                    "account_type",
                    models.IntegerField(
                        choices=[(1, "Corporation"), (2, "Person"), (3, "Association")],
                        default=1,
                    ),
                ),
            ],
            bases=(
                django_xworkflows.models.BaseWorkflowEnabled,
                ims.models.mixin.SanitizeCharFieldMixin,
                ims.models.mixin.ChangedFieldLookupMixin,
                models.Model,
            ),
        ),
    ]
