# Generated by Django 3.2.20 on 2023-08-24 09:28

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("stores", "0001_initial"),
        ("orders", "0001_initial"),
        ("payment", "0001_initial"),
        ("hipay_tpp", "0002_hipaywalletaccount_application"),
    ]

    operations = [
        migrations.AddField(
            model_name="hipaywalletaccount",
            name="merchant",
            field=models.ForeignKey(
                blank=True,
                db_index=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="stores.merchant",
            ),
        ),
        migrations.AddField(
            model_name="hipaytransaction",
            name="order",
            field=models.ForeignKey(
                blank=True,
                db_index=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="hipay_transactions",
                to="orders.order",
            ),
        ),
        migrations.AddField(
            model_name="hipaytransaction",
            name="payment",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="hipay_transaction",
                to="payment.payment",
            ),
        ),
    ]
