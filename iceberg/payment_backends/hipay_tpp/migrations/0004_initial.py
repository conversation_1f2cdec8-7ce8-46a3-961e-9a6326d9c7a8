# Generated by Django 3.2.20 on 2023-08-24 09:28

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import lib.models.index


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("stores", "0001_initial"),
        ("kyc", "0001_initial"),
        ("hipay_tpp", "0003_initial"),
        ("address", "0005_initial"),
        ("ice_applications", "0001_initial"),
        ("currencies", "0001_initial"),
        ("payment", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="hipaypaymentpagecss",
            name="uploaded_by",
            field=models.ForeignKey(
                blank=True,
                db_index=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="hipaybankaccount",
            name="app_bank_account",
            field=models.ForeignKey(
                blank=True,
                db_index=False,
                default=None,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="ice_applications.applicationbankaccount",
            ),
        ),
        migrations.AddField(
            model_name="hipaybankaccount",
            name="application",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="ice_applications.application",
            ),
        ),
        migrations.AddField(
            model_name="hipaybankaccount",
            name="identity_document",
            field=models.ForeignKey(
                blank=True,
                db_index=False,
                default=None,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="stores.merchantidentitydocument",
            ),
        ),
        migrations.AddField(
            model_name="hipaybankaccount",
            name="kyc_information",
            field=models.ForeignKey(
                blank=True,
                db_index=False,
                default=None,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="kyc.kycinformation",
            ),
        ),
        migrations.AddField(
            model_name="hipaybankaccount",
            name="merchant",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="stores.merchant",
            ),
        ),
        migrations.AddField(
            model_name="hipaybankaccount",
            name="store_bank_account",
            field=models.ForeignKey(
                blank=True,
                db_index=False,
                default=None,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="stores.merchantbankaccount",
            ),
        ),
        migrations.AddField(
            model_name="hipayappconfiguration",
            name="application",
            field=models.ForeignKey(
                db_index=False,
                on_delete=django.db.models.deletion.CASCADE,
                to="ice_applications.application",
            ),
        ),
        migrations.AddField(
            model_name="hipayappconfiguration",
            name="country",
            field=models.ForeignKey(
                blank=True,
                db_index=False,
                default=72,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="address.country",
            ),
        ),
        migrations.AddField(
            model_name="hipayappconfiguration",
            name="currency",
            field=models.ForeignKey(
                db_index=False,
                default="EUR",
                on_delete=django.db.models.deletion.CASCADE,
                to="currencies.currency",
            ),
        ),
        migrations.AddField(
            model_name="hipayappconfiguration",
            name="payment_methods",
            field=models.ManyToManyField(blank=True, to="payment.PaymentMethod"),
        ),
        migrations.AddField(
            model_name="hipayappconfiguration",
            name="payment_page_css",
            field=models.ForeignKey(
                blank=True,
                db_index=False,
                help_text="Payment page customization CSS file",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="configs",
                to="hipay_tpp.hipaypaymentpagecss",
                verbose_name="Payment page CSS file",
            ),
        ),
        migrations.AddField(
            model_name="hipayapicalllog",
            name="application",
            field=models.ForeignKey(
                blank=True,
                db_index=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="+",
                to="ice_applications.application",
            ),
        ),
        migrations.AddField(
            model_name="hipayapicalllog",
            name="merchant",
            field=models.ForeignKey(
                blank=True,
                db_index=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="+",
                to="stores.merchant",
            ),
        ),
        migrations.AddField(
            model_name="hipayapicalllog",
            name="payment",
            field=models.ForeignKey(
                blank=True,
                db_index=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="payment.payment",
            ),
        ),
        migrations.AddIndex(
            model_name="hipaywalletaccount",
            index=lib.models.index.PostgresIndex(
                fields=["last_update_on"],
                name="hipay_tpp_hipaywalletaccount_last_update_on_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="hipaytransaction",
            index=lib.models.index.PostgresIndex(
                fields=["transaction_reference"],
                name="hipay_tpp_hipaytransaction_transaction_reference_idx",
            ),
        ),
    ]
