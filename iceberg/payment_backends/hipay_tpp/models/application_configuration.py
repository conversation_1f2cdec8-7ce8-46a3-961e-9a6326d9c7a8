# -*- coding: utf-8 -*-

import logging

from apps.payment.backends import HIPA<PERSON>TP<PERSON>
from constance import config
from django.conf import settings
from django.db import models
from django.db.models.query import QuerySet
from django.utils.functional import cached_property
from django.utils.translation import gettext_lazy as _
from payment_backends.common import BaseAppConfiguration

logger = logging.getLogger(__name__)

TPP_API_ENDPOINT = getattr(
    settings,
    "HIPAY_TPP_API_ENDPOINT",
    "https://%(sandbox)ssecure-gateway.hipay-tpp.com/rest/v1",
)

# this endpoint will be used for hipay hosted page V2
TPP_API_ENDPOINT_V2 = getattr(
    settings,
    "HIPAY_TPP_API_ENDPOINT_V2",
    "https://{sandbox}api.hipay.com/v1",
)

# this endpoint will be used for hipay TPP API V3
TPP_API_ENDPOINT_V3 = getattr(
    settings,
    "HIPAY_TPP_API_ENDPOINT_V3",
    "https://{sandbox}api-gateway.hipay.com/v3",
)

KYC_API_ENDPOINT = getattr(
    settings,
    "HIPAY_KYC_API_ENDPOINT",
    "https://%(sandbox)smerchant.hipaywallet.com/api/identification.json",
)
HIPAY_BANK_INFO_API_ENDPOINT = getattr(
    settings,
    "HIPAY_BANK_INFO_API_ENDPOINT",
    "https://%(sandbox)smerchant.hipaywallet.com/api/user-account/bank-info.json",
)

HIPAY_USER_BALANCE_API_ENDPOINT = getattr(
    settings,
    "HIPAY_USER_BALANCE_API_ENDPOINT",
    "https://%(sandbox)smerchant.hipaywallet.com/api/user-account/balance.json",
)

HIPAY_TRANSFER_API_ENDPOINT = getattr(
    settings,
    "HIPAY_TRANSFER_API_ENDPOINT",
    "https://%(sandbox)smerchant.hipaywallet.com/api/transfer.json",
)

HIPAY_WITHDRAWAL_API_ENDPOINT = getattr(
    settings,
    "HIPAY_WITHDRAWAL_API_ENDPOINT",
    "https://%(sandbox)smerchant.hipaywallet.com/api/withdrawal.json",
)

USER_API_ENDPOINT = getattr(
    settings,
    "HIPAY_USER_API_ENDPOINT",
    "https://%(sandbox)smerchant.hipaywallet.com/api/user-account.json",
)

HIPAY_USER_EMAIL_IS_AVAILABLE_API_ENDPOINT = getattr(
    settings,
    "HIPAY_USER_EMAIL_IS_AVAILABLE_API_ENDPOINT",
    "https://%(sandbox)smerchant.hipaywallet.com/api/user-account/is-available.json",
)

HIPAY_LOCAL_CODES_API_ENDPOINT = getattr(
    settings,
    "HIPAY_LOCAL_CODES_API_ENDPOINT",
    "https://%(sandbox)smerchant.hipaywallet.com/api/tools/locale/codes.json",
)

TPP_FINANCE_ENDPOINT = getattr(
    settings, "HIPAY_FINANCE_API_BASE_URL", "https://%(sandbox)sapi.hipay-tpp.com/v1/"
)


class HipayAppConfigurationQuerySet(QuerySet):
    def active(self):
        return self.filter(active=True)

    def cashout_enabled(self):
        return self.filter(cashout_enabled=True)

    def active_on_application(self):
        return self.filter(
            application__applicationpaymentsettings__payment_backend=HIPAYTPP
        )


class HipayAppConfiguration(BaseAppConfiguration):
    """Hipay configuration model : stores settings into the database.
    It is used when you create a HipayTppPayment (BasePaymentProcessor)
    to get all settings like API keys, 3D Secure enabling level
    and so on...

    Each configuration can be targeted on a specific target. For now we'll
    only get the active config. Make sure you have only one config active
    to avoid conflicts.
    """

    api_resource_path = (
        "payment_backends.hipay_tpp.api.resources.HipayAppConfigurationResource"
    )
    objects = HipayAppConfigurationQuerySet.as_manager()

    payment_backend = HIPAYTPP

    # selection of config can be achieved using filters on these fields to
    # refine config
    FRANCE_COUNTRY_ID = 72
    country = models.ForeignKey(
        "address.Country",
        null=True,
        blank=True,
        default=FRANCE_COUNTRY_ID,
        on_delete=models.CASCADE,
        db_index=False,
    )
    payment_methods = models.ManyToManyField(
        "payment.PaymentMethod",
        blank=True,
    )

    # configuration description is the visible name given to the
    # entry in DB. (so you can choose on config or the other when setting
    # them for either one payment backend on the other)
    configuration_description = models.CharField(max_length=255, default="", blank=True)

    # is Izberg cash-in phase enabled (True) or do we rely on external
    # cash-in service (False)
    CASHIN_MODE_IZBERG = "izberg"
    CASHIN_MODE_PRESTASHOP = "prestashop"
    CASHIN_MODE_UNHANDLED = "unhandled"
    CASHIN_MODE_CHOICES = (
        (CASHIN_MODE_IZBERG, _("Handled - We're in charge of everything")),
        (CASHIN_MODE_PRESTASHOP, _("Partially handled - Capture & refund only")),
        (CASHIN_MODE_UNHANDLED, _("Unhanded - Don't handle cashin")),
    )
    cashin_mode = models.CharField(
        max_length=50,
        default=CASHIN_MODE_IZBERG,
        choices=CASHIN_MODE_CHOICES,
        help_text=_("Select cashin-mode i.e. what payment features are used."),
    )

    # is Izberg cash-out phase enabled (True) or do we rely on manual
    # operator & merchants payment (False)
    # this boolean gets trigged-off when an exception occurs on cashout
    cashout_enabled = models.BooleanField(default=True)

    # API login
    RestApiLogin = models.CharField(max_length=64)
    # API password
    RestApiPassword = models.CharField(max_length=64)
    # API encryption pass phrase
    RestApiPassphrase = models.CharField(max_length=128, default="", blank=True)

    # e-wallet entity
    EWalletApiEntity = models.CharField(max_length=128)
    # e-wallet api login
    EWalletApiLogin = models.CharField(max_length=64)
    # e-wallet api password
    EWalletApiPassword = models.CharField(max_length=64)
    # e-wallet main account (credited by cash-in procedure)
    account_id = models.IntegerField(verbose_name=_("Hipay technical account id"))

    # ECI Electronic Commerce Indicator, default value set on Interface
    eci = models.IntegerField(
        default=7,
        verbose_name=_("Default ECI level"),
    )
    # enable 3DSecure : no, sometimes (when possible), always
    _3DS_LEVELS = {"fraud-module": None, "no": 0, "sometimes": 1, "always": 2}
    Enable3ds = models.IntegerField(
        default=1,
        null=True,
        blank=True,
        choices=((None, "fraud-module"), (0, "no"), (1, "sometimes"), (2, "always")),
        help_text=_(
            "3DS activation level: fraud-module means it's up to hipay's "
            "fraud module to activate it using scoring threshold & rules."
        ),
    )

    payment_products = models.CharField(
        max_length=400, default="visa,mastercard", blank=True
    )

    # TODO: to be removed after migration of Hosted Page V2
    payment_products_categories = models.CharField(
        max_length=400, default="credit-card", blank=True
    )

    # TODO: to be removed after migration of Hosted Page V2
    payment_page_css = models.ForeignKey(
        "hipay_tpp.HipayPaymentPageCSS",
        verbose_name=_("Payment page CSS file"),
        related_name="configs",
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        db_index=False,
        help_text=_("Payment page customization CSS file"),
    )

    @property
    def using_testing_api(self):
        if self.staging is None:
            return self.sandbox
        return self.staging

    @property
    def sandbox(self):
        """Property ensuring retro compatibility with previous code
        (just in case someone still uses it you know)
        """
        return self.application.environment != self.application.PRODUCTION

    # rest API base URL
    @property
    def RestApiEndpoint(self):
        endpoint = TPP_API_ENDPOINT % {
            "sandbox": "stage-" if self.using_testing_api else "",
        }
        if not endpoint.endswith("/"):
            endpoint += "/"
        return endpoint

    # this endpoint will be used for hipay hosted page V2
    @property
    def RestApiV2Endpoint(self):
        endpoint = TPP_API_ENDPOINT_V2.format(
            sandbox="stage-" if self.using_testing_api else ""
        )
        if not endpoint.endswith("/"):
            endpoint += "/"
        return endpoint

    # this endpoint will be used for hipay TPP API V3
    @property
    def RestApiV3Endpoint(self):
        endpoint = TPP_API_ENDPOINT_V3.format(
            sandbox="stage-" if self.using_testing_api else ""
        )
        if not endpoint.endswith("/"):
            endpoint += "/"
        return endpoint

    @property
    def RestApiFinanceUrl(self):
        endpoint = TPP_FINANCE_ENDPOINT % {
            "sandbox": "stage-" if self.using_testing_api else "",
        }
        if not endpoint.endswith("/"):
            endpoint += "/"
        return endpoint

    @property
    def KycApiEndpoint(self):
        return KYC_API_ENDPOINT % {
            "sandbox": "test-" if self.using_testing_api else "",
        }

    @property
    def BankInfoApiEndpoint(self):
        return HIPAY_BANK_INFO_API_ENDPOINT % {
            "sandbox": "test-" if self.using_testing_api else "",
        }

    @property
    def UserBalanceApiEndpoint(self):
        return HIPAY_USER_BALANCE_API_ENDPOINT % {
            "sandbox": "test-" if self.using_testing_api else "",
        }

    @property
    def TransferApiEndpoint(self):
        return HIPAY_TRANSFER_API_ENDPOINT % {
            "sandbox": "test-" if self.using_testing_api else "",
        }

    @property
    def WithdrawalApiEndpoint(self):
        return HIPAY_WITHDRAWAL_API_ENDPOINT % {
            "sandbox": "test-" if self.using_testing_api else "",
        }

    @property
    def UserApiEndpoint(self):
        return USER_API_ENDPOINT % {
            "sandbox": "test-" if self.using_testing_api else "",
        }

    @property
    def UserEmailIsAvailableApiEndpoint(self):
        return HIPAY_USER_EMAIL_IS_AVAILABLE_API_ENDPOINT % {
            "sandbox": "test-" if self.using_testing_api else "",
        }

    @property
    def LocalCodesApiEndpoint(self):
        return HIPAY_LOCAL_CODES_API_ENDPOINT % {
            "sandbox": "test-" if self.using_testing_api else "",
        }

    # payment products list:
    @property
    def payment_product_list(self):
        """Get a list of payment products compatible with the selected
        API configuration. For example you can decide to accept some
        particular payment products like master card only when
        user is in an agent-assisted payment procedure.
        """
        return self.payment_products

    # payment products category list
    @property
    def payment_product_category_list(self):
        """Get a list of accepted payment methods - e-wallet, credit card,
        debit card and so on....
        here too you can change this list amongst your configs.

        TODO: payment_product_category_list should be removed after migration of hosted
        page V2
        """
        return self.payment_products_categories

    @property
    def marketplace_payment_css(self):
        if self.payment_page_css:
            return self.payment_page_css.css_source_file.url
        return None

    @property
    def marketplace_payment_css_url(self):
        logger.debug("self.maketplace_payment_css : %s", self.marketplace_payment_css)
        if self.payment_page_css:
            return self.payment_page_css.css_source_file.url
        return ""

    # template name : basic-js for complete page or
    # iframe-js for an iframe integration
    payment_form_template = models.CharField(
        max_length=10,
        choices=(
            ("basic-js", _("no Encapsulation - user must be redirected")),
            ("iframe-js", _("iFrame encapsulated")),
        ),
        default="basic-js",
    )

    def __str__(self):
        return (
            "Conf for app (app {app_id} country {country} currency {currency})".format(
                app_id=self.application_id,
                country=self.country.code if self.country else "",
                currency=self.currency.code if self.currency else "",
            )
        )

    def save(self, *args, **kwargs):
        self.full_clean()
        res = super(HipayAppConfiguration, self).save(*args, **kwargs)
        if self.active:
            self._disable_other_active_conf()
        return res

    def _disable_other_active_conf(self):
        """Will soon be deprecated in favour of a zoned config selection"""
        logger.warning("`active` boolean soon deprecated")
        deactivated = (
            HipayAppConfiguration.objects.filter(
                application=self.application,
                active=True,
            )
            .exclude(id=self.id)
            .update(active=False)
        )
        if deactivated:
            logger.warning(
                "%s conf(s) set to inactive for application %s because of new "
                "activated config",
                deactivated,
                self.application.id,
            )

    def _merchant_display_name(self):
        """Merchand custom displayed name (overrides order's if exists)"""
        if self.application.company and self.application.company.name:
            return self.application.company.name
        else:
            return self.application.name

    _merchant_display_name.short_description = _(
        "Merchant name displayed on payment page"
    )
    merchant_display_name = property(_merchant_display_name)

    # payment products selector display value: 1 shown 0 hidden
    display_payment_method_selector = models.BooleanField(default=False)

    # multi use : will you make recurrent payments?
    # will the token be used only once? 1 - yes, 0 - no
    single_payment = models.BooleanField(
        default=True, verbose_name=_("Recurring payment activation")
    )

    # ordering flow redirections #

    # redirect success , fail, cancel etc. pages.
    accept_url = models.URLField(max_length=255, default="", blank=True)
    decline_url = models.URLField(max_length=255, default="", blank=True)
    pending_url = models.URLField(max_length=255, default="", blank=True)
    exception_url = models.URLField(max_length=255, default="", blank=True)
    cancel_url = models.URLField(max_length=255, default="", blank=True)

    def set_3DS_activation(self, value):
        """Tell if you want to enable 3D Secure.
        :param value: value in list "no", "sometimes", "always"
        :type value: str

        :raise ValueError: when value is not in expected range
        """
        # alter the value to lower case string
        value = str(value).lower()
        if value not in HipayAppConfiguration._3DS_LEVELS:
            raise ValueError("Unexpected value %s" % value)
        self.Enable3ds = HipayAppConfiguration._3DS_LEVELS[value]

    def get_3DS_activation(self):
        """Return 3D secure enabled status.
        possible value : "no", "sometimes", "always"

        :return : 3DS activation level
        :rtype : str
        """
        return HipayAppConfiguration._REVERSE_3DS_LEVEL[self.Enable3ds]

    # string version of Enable3ds field.
    Enable3dsString = property(get_3DS_activation, set_3DS_activation)

    @property
    def RestApiCredentialsTuple(self):
        """Property returning a tuple containing API login & API password"""
        return self.RestApiLogin, self.RestApiPassword

    @property
    def EWalletApiCredentialsDict(self):
        """Property returning a dict containing the follwing key-valu pairs:
        wsLogin: self.EWalletApiLogin
        wsPassword: self.EWalletApiPassword
        """
        return {
            "wsLogin": self.EWalletApiLogin,
            "wsPassword": self.EWalletApiPassword,
        }

    @property
    def apiConfigDict(self):
        """Convert settings attributes to API compatible settings dict"""
        config_dict = {
            "eci": self.eci,
            "payment_product_list": self.payment_product_list,
            # TODO: deprecated with Hosted Page V2. Should be removed after migration
            "payment_product_category_list": self.payment_product_category_list,
            "template": self.payment_form_template,
            "merchant_display_name": self.merchant_display_name,
            "display_selector": int(self.display_payment_method_selector),
            "multi_use": int(self.single_payment),
            "accept_url": self.accept_url,
            "decline_url": self.decline_url,
            "pending_url": self.pending_url,
            "exception_url": self.pending_url,
            "cancel_url": self.cancel_url,
        }
        config_dict.update(self.get_authentication_indicator())

        # TODO : should be removed after migration of hosted page V2
        config_dict.update(self.get_css_url())

        # TODO: should be removed after migration of hosted page V2
        if config.ENABLE_HIPAY_HOSTED_PAGE_V2:
            if "css" in config_dict:
                config_dict.pop("css")
            if "payment_product_category_list" in config_dict:
                config_dict.pop("payment_product_category_list")
        return config_dict

    # TODO : to be removed after hosted page V2 migration
    def get_css_url(self):
        # get uploaded css file's url
        if not self.marketplace_payment_css:
            return {}

        css_url = self.marketplace_payment_css
        if css_url.startswith("//"):
            css_url = str("https://" + css_url[2:])
        return {"css": css_url}

    def get_authentication_indicator(self):
        if self.Enable3ds is None:
            return {}
        return {"authentication_indicator": int(self.Enable3ds)}

    @cached_property
    def cashin_api(self):
        from payment_backends.hipay_tpp.hipay_api import HipayCashinApi

        return HipayCashinApi(self)

    @cached_property
    def cashout_api(self):
        from payment_backends.hipay_tpp.hipay_api import HipayCashoutApi

        return HipayCashoutApi(self)

    @cached_property
    def kyc_api(self):
        from payment_backends.hipay_tpp.hipay_api import HipayKycApi

        return HipayKycApi(self)
