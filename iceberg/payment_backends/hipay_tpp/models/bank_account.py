# -*- coding: utf-8 -*-

import logging

from apps.ice_applications.models import ApplicationBankAccount
from apps.ice_applications.models.application_models import Application
from apps.stores.models.bank_models import MerchantBankAccount
from apps.stores.models.store_models import Merchant
from django.core.serializers.json import DjangoJ<PERSON>NEncoder
from django.db import models
from django.utils.functional import cached_property
from django_xworkflows import models as xwf_models
from ims.models.mixin import ChangedFieldLookupMixin, SanitizeCharFieldMixin
from ims.workflow import IzbergWorkflow
from lib.models import IzbergQueryset
from payment_backends.hipay_tpp.exceptions import HipayNotConfigured
from payment_backends.hipay_tpp.models.hipay_env_mixin import HipayEnvMixinModel
from payment_backends.hipay_tpp.utils import get_conf_for_application

from ..references import (
    HIPAY_BANK_INFOS_STATUSES,
    HIPAY_BANK_INFOS_STATUSES_INITIAL,
    HIP<PERSON><PERSON>_BANK_INFOS_STATUSES_INVALID,
    HIPAY_BANK_INFOS_STATUSES_VALID,
    HIPAY_BANK_INFOS_STATUSES_WAITING,
)

logger = logging.getLogger(__name__)


class HipayBankAccountWorkflow(IzbergWorkflow):
    states = HIPAY_BANK_INFOS_STATUSES

    transitions = (
        (
            "set_initial",
            [
                HIPAY_BANK_INFOS_STATUSES_WAITING,
                HIPAY_BANK_INFOS_STATUSES_VALID,
                HIPAY_BANK_INFOS_STATUSES_INVALID,
            ],
            HIPAY_BANK_INFOS_STATUSES_INITIAL,
        ),
        (
            "set_waiting",
            [
                HIPAY_BANK_INFOS_STATUSES_INITIAL,
                HIPAY_BANK_INFOS_STATUSES_VALID,
                HIPAY_BANK_INFOS_STATUSES_INVALID,
            ],
            HIPAY_BANK_INFOS_STATUSES_WAITING,
        ),
        (
            "validate",
            [
                HIPAY_BANK_INFOS_STATUSES_INITIAL,
                HIPAY_BANK_INFOS_STATUSES_WAITING,
                HIPAY_BANK_INFOS_STATUSES_INVALID,
            ],
            HIPAY_BANK_INFOS_STATUSES_VALID,
        ),
        (
            "invalidate",
            [
                HIPAY_BANK_INFOS_STATUSES_INITIAL,
                HIPAY_BANK_INFOS_STATUSES_WAITING,
                HIPAY_BANK_INFOS_STATUSES_VALID,
                HIPAY_BANK_INFOS_STATUSES_INVALID,
            ],
            HIPAY_BANK_INFOS_STATUSES_INVALID,
        ),
    )

    initial_state = HIPAY_BANK_INFOS_STATUSES_INITIAL


class HipayBankAccountManager(IzbergQueryset):
    def get_for_merchant(self, merchant_id, defaults=None):
        return self.get_or_create(merchant_id=merchant_id, defaults=defaults)[0]


class HipayBankAccount(
    xwf_models.WorkflowEnabled,
    SanitizeCharFieldMixin,
    ChangedFieldLookupMixin,
    HipayEnvMixinModel,
    models.Model,
):
    """
    Stores bank registration information like iban, swift, bank country and
    bank infos registration, status.
    """

    objects = HipayBankAccountManager.as_manager()

    store_bank_account = models.ForeignKey(
        MerchantBankAccount,
        default=None,
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        db_index=False,
    )
    identity_document = models.ForeignKey(
        "stores.MerchantIdentityDocument",
        default=None,
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        db_index=False,
    )
    kyc_information = models.ForeignKey(
        "kyc.KycInformation",
        default=None,
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        db_index=False,
    )
    app_bank_account = models.ForeignKey(
        ApplicationBankAccount,
        default=None,
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        db_index=False,
    )
    merchant = models.OneToOneField(
        Merchant, null=True, blank=True, on_delete=models.CASCADE
    )
    application = models.OneToOneField(
        Application, null=True, blank=True, on_delete=models.CASCADE
    )

    synchro_status = xwf_models.StateField(HipayBankAccountWorkflow)

    synchro_error = models.JSONField(
        blank=True,
        db_column="synchro_error",
        default=dict,
        encoder=DjangoJSONEncoder,
    )

    # We're forced to make all fields editable due to do the usage of the
    # /v1/hipay_bank_account/getOrCreate/ route
    EDITABLE_FIELDS = (
        "identity_document",
        "kyc_information",
        "app_bank_account",
        "merchant",
        "application",
        "synchro_status",
        "synchro_error",
    )
    CREATABLE_FIELDS = []

    @cached_property
    def actions(self):
        from ..actions import BankAccountActionsManager

        return BankAccountActionsManager(self)

    ####
    # Bank infos
    ####
    @property
    def country(self):
        if self.bank_account is None:
            return None
        return self.bank_account.account_bank_country_code

    @property
    def iban(self):
        if self.bank_account is None:
            return None
        return self.bank_account.account_IBAN

    @property
    def swift(self):
        if self.bank_account is None:
            return None
        return self.bank_account.account_BIC

    @property
    def owner(self):
        if self.bank_account is None:
            return None
        return self.bank_account.account_owner_name

    @property
    def bank_account(self):
        if self.store_bank_account:
            return self.store_bank_account
        elif self.app_bank_account:
            return self.app_bank_account
        return None

    def __str__(self):
        return ("HipayBankAccount[{account_id}] {status}").format(
            account_id=self.id, status=self.get_synchro_status_display()
        )

    @property
    def active(self):
        if self.store_bank_account:
            return self.store_bank_account.status == MerchantBankAccount.ACTIVE
        elif self.app_bank_account:
            return self.app_bank_account.status == ApplicationBankAccount.ACTIVE
        return False

    def save(self, *args, **kwargs):
        if self.synchro_status == HIPAY_BANK_INFOS_STATUSES_VALID:
            self.synchro_error = {}

        super(HipayBankAccount, self).save(*args, **kwargs)

    def get_api(self):
        if self.get_merchant():
            application = self.get_merchant().application
        else:
            application = self.application
        logger.debug("application: %s", application)
        try:
            config = get_conf_for_application(application)
            return config.cashout_api
        except HipayNotConfigured:
            logger.warning("Hipay is not configured yet.")
        return None

    def get_wallet(self):
        return self.get_api().get_wallet(self.merchant, self.application)

    def get_merchant(self):
        if self.store_bank_account is None:
            return self.merchant
        return self.store_bank_account.merchant

    def get_application(self):
        if self.application is not None:
            return self.application
        elif self.merchant is not None:
            return self.merchant.application
        return None
