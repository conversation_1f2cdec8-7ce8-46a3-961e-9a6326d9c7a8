# -*- coding: utf-8 -*-

from django.db import models


class HipayEnvMixinModel(models.Model):
    """
    payment_backends.hipay_tpp.models.hipay_env_mixin model:
    keeps fields common to all hipay models, i.e.
    - created_on      - Time the entry was created
    - last_update_on  - Last time the entry was modified
    """

    class Meta:
        abstract = True
        app_label = "hipay_tpp"

    created_on = models.DateTimeField(auto_now_add=True)
    last_update_on = models.DateTimeField(auto_now=True)
