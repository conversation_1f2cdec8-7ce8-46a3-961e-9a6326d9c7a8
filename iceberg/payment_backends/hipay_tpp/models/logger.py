# -*- coding: utf-8 -*-

from apps.payment.backends import HIPAYTPP
from django.db import models
from lib.models import IzbergQueryset
from payment_backends.common.models import BaseRestCallLog
from payment_backends.hipay_tpp.references import HIPAY_ACTIONS_CHOICES


class HipayApiCallLog(BaseRestCallLog):
    objects = IzbergQueryset.as_manager()

    payment_backend = HIPAYTPP
    action = models.CharField(
        max_length=255, choices=HIPAY_ACTIONS_CHOICES, null=True, blank=True
    )
