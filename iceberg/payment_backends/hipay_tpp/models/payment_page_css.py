# -*- coding: utf-8 -*-

import logging

from django.conf import settings
from django.core.exceptions import ValidationError
from django.db import models
from django.utils.translation import gettext_lazy as _
from lib.api.file_path import FilePathGenerator
from lib.models import IzbergQueryset
from uploader.data_extensions import CSS_EXTENSIONS
from uploader.models import UploaderMixin

logger = logging.getLogger(__name__)


class HipayPaymentPageCSSFilePathGenerator(FilePathGenerator):
    def get_instance_application_id(self, instance):
        return instance.application.id


class HipayPaymentPageCSS(UploaderMixin, models.Model):
    """
    Save payment page css file.

    Warning:
    Each time you save one of these models, it automatically updates the
    targeted hipay_app_configuration to make it point to the payment_page_css
    model you just saved. This behaviour has been implemented to ease assignment
    on upload. In theory you don't update PaymentPageCSS models after they've
    been uploaded ; instead you just upload new files so that is not a big issue
    in day-to-day usage of this model.
    """

    objects = IzbergQueryset.as_manager()

    uploader_fields = {
        "file": "css_source_file",
        "user": "uploaded_by",
    }
    api_resource_path = "apps.assets.api.resources.ImageResource"

    _file_max_size = 10000000  # 10mo
    _allowed_mimetypes = (
        "text/plain",
        "text/css",
        "text/scss",
        "text/sass",
    )

    _allowed_extensions = CSS_EXTENSIONS

    uploaded_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        db_index=False,
        on_delete=models.CASCADE,
    )
    # css file: /!\ images URLs Must be HTTPS
    css_source_file = models.FileField(
        upload_to=HipayPaymentPageCSSFilePathGenerator(), max_length=1024
    )
    css_source_file_bkp = models.FileField(
        null=True, blank=True, default=None, max_length=1024
    )
    # This behaviour helps us assining
    config_id = models.IntegerField(
        help_text=_(
            "Temporarily store configuration ID so we can establish "
            "expected link after model's save."
        ),
    )

    def set_uploader_attributes(self, *args, **extra):
        # uploader's set_uploader_attributes function doesn't deal well with
        # foreign-keys so this function pops the foreign-key's target ID
        # to create/update the relation between targeted configuration &
        # uploaded file.
        self.assign_to_config(extra)

        super(HipayPaymentPageCSS, self).set_uploader_attributes(*args, **extra)

    def assign_to_config(self, extra):
        """Parse extra arguments sent at end of file upload. Then it handles
        finding the config & making sure we can do the rest of the work.
        """
        from payment_backends.hipay_tpp.models import HipayAppConfiguration

        try:
            hipay_app_configuration_id = int(extra.pop("hipay_app_configuration_id"))
            HipayAppConfiguration.objects.get(pk=hipay_app_configuration_id)
        except KeyError:
            raise ValidationError({"hipay_app_configuration_id": _("Argument missing")})
        except ValueError:
            raise ValidationError(
                {"hipay_app_configuration_id": _("Expected integer value")}
            )
        except HipayAppConfiguration.DoesNotExist:
            raise ValidationError(
                {
                    "hipay_app_configuration_id": _(
                        "Invalid config ID. Config not found."
                    )
                }
            )
        self.config_id = hipay_app_configuration_id

    def save(self, *args, **kwargs):
        # import here to avoid circular imports
        from payment_backends.hipay_tpp.models import HipayAppConfiguration

        ret = super(HipayPaymentPageCSS, self).save(*args, **kwargs)
        # config has been found on attribute assignment, we assume
        # config_id is still valid. In case it's not, payment page save
        # function won't crash but nothing will be done...
        # Update targeted config's payment page FK to new file.
        if self.config_id:
            HipayAppConfiguration.objects.filter(pk=self.config_id).update(
                payment_page_css_id=self.id
            )
        return ret

    def __str__(self):
        return "{}[{}]".format(self.__class__.__name__, self.id)

    @property
    def application(self):
        from payment_backends.hipay_tpp.models import HipayAppConfiguration

        config = HipayAppConfiguration.objects.filter(pk=self.config_id).last()
        if config:
            return config.application
        return None
