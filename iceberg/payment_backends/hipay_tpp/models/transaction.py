# -*- coding: utf-8 -*-

import logging
from decimal import Decimal
from typing import Union

import payment_backends.hipay_tpp.references as hipay_refs
from django.core.serializers.json import DjangoJSONEncoder
from django.db import models
from django.utils.translation import gettext_lazy as _
from ims.models.mixin import ChangedFieldLookupMixin, SanitizeCharFieldMixin
from lib.models import IzbergQueryset, PostgresIndex
from payment_backends.common.utils import logging_context
from payment_backends.hipay_tpp.exceptions import (
    HipayNotConfigured,
    TransactionReferenceMismatch,
)
from payment_backends.hipay_tpp.models.hipay_env_mixin import HipayEnvMixinModel
from payment_backends.hipay_tpp.utils import (
    get_conf_for_application,
    get_last_transaction_key,
)

logger = logging.getLogger(__name__)


class HipayTransaction(
    HipayEnvMixinModel, SanitizeCharFieldMixin, ChangedFieldLookupMixin, models.Model
):
    """Stores Hipay internal generated transaction
    reference related to a payment Object
    """

    class Meta:
        app_label = "hipay_tpp"

        indexes = [
            PostgresIndex(
                fields=["transaction_reference"],
                name="hipay_tpp_hipaytransaction_transaction_reference_idx",
            ),
        ]

    # ForeignKey instead of OneToOne in case of recurring payment (3x for ex)
    order = models.ForeignKey(
        "orders.Order",
        null=True,
        blank=True,
        related_name="hipay_transactions",
        on_delete=models.CASCADE,
        db_index=False,
    )
    payment = models.OneToOneField(
        "payment.Payment",
        null=True,
        blank=True,
        related_name="hipay_transaction",
        on_delete=models.CASCADE,
    )
    transaction_reference = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        default=None,
    )
    dirty_capture = models.BooleanField(
        default=False,
        help_text=_("A capture that has been made"),
    )
    # these fields are copies from HipayAPI's status fields. Some fields are
    # missing. To get them all, please use the get_status method (defined in
    # HipayAPI's class).

    # following fields are related to the last maintenance request's
    # return code.
    error_code = models.IntegerField(default=0)
    message = models.CharField(max_length=255, default="", blank=True)

    # these are the transaction state description fields.
    reason = models.CharField(max_length=255, default="", blank=True)
    state = models.CharField(max_length=255, default="", blank=True)
    status = models.IntegerField(null=True, default=None)
    test = models.BooleanField(
        null=True,
        blank=True,
        default=None,
    )

    # transaction ids :
    hipay_orderid = models.CharField(max_length=32, default=None, null=True)
    #    transaction refernce is also an ID

    # forward URL generated on first authorization call
    forward_url = models.TextField(default=None, null=True)
    eci_level = models.IntegerField(default=7)

    # amounts:
    authorized_amount = models.DecimalField(
        max_digits=20, decimal_places=2, default=Decimal("0.00")
    )
    captured_amount = models.DecimalField(
        max_digits=20, decimal_places=2, default=Decimal("0.00")
    )
    refunded_amount = models.DecimalField(
        max_digits=20, decimal_places=2, default=Decimal("0.00")
    )

    settled_amounts = models.JSONField(
        null=True,
        default=None,
        help_text=_("Keep tracks of every settled amount by operation id"),
        encoder=DjangoJSONEncoder,
    )

    escrow_delivery_settlement = models.JSONField(
        blank=True,
        help_text=_(
            "For every merchant-order you can keep trace of its money, being "
            "delivered onto escrow wallet."
        ),
        db_column="escrow_delivery_settlement",
        default=dict,
        encoder=DjangoJSONEncoder,
    )

    # issuer information
    attempt_id = models.IntegerField(null=True, default=None)
    ip_address = models.CharField(max_length=255, default="", blank=True)

    # payment product information used for authorisation
    payment_product = models.CharField(max_length=255, default="", blank=True)
    payment_method_brand = models.CharField(max_length=255, default="", blank=True)
    payment_method_expiry = models.CharField(max_length=255, default="", blank=True)
    payment_method_token = models.CharField(max_length=255, default="", blank=True)
    payment_method_card_holder = models.CharField(
        max_length=255, default="", blank=True
    )
    payment_method_country = models.CharField(max_length=255, default="", blank=True)
    payment_method_issuer_bank = models.CharField(
        max_length=255, default="", blank=True
    )
    payment_method_pan = models.CharField(max_length=255, default="", blank=True)

    capture_operations = models.JSONField(
        blank=True,
        help_text=_(
            "Link between an Hipay merchant "
            "operation ID and IZBERG merchant order Ids for captures"
        ),
        db_column="capture_operations",
        default=dict,
        encoder=DjangoJSONEncoder,
    )

    refund_operations = models.JSONField(
        blank=True,
        help_text=_(
            "Link between an Hipay merchant "
            "operation ID and IZBERG merchant order Ids for refund"
        ),
        db_column="refund_operations",
        default=dict,
        encoder=DjangoJSONEncoder,
    )

    objects = IzbergQueryset.as_manager()

    def __str__(self):
        return "{name}[{pk}]".format(name=self.__class__.__name__, pk=self.id)

    def update_transaction_reference(self, new_ref: Union[int, str]):
        new_ref = str(new_ref)
        existing_ref = self.transaction_reference
        if existing_ref and new_ref != self.transaction_reference:
            raise TransactionReferenceMismatch(
                existing_reference=existing_ref,
                new_reference=new_ref,
            )
        self.transaction_reference = new_ref

    def update_from_request_dict(self, request_response_d):
        if "transaction" not in request_response_d:
            return
        transaction_d = request_response_d["transaction"]
        if "0" in transaction_d.keys():
            last_transaction_key = get_last_transaction_key(transaction_d)
            transaction_d = transaction_d[last_transaction_key]
        if transaction_d.get("transactionReference"):
            self.update_transaction_reference(transaction_d["transactionReference"])
        self.error_code = int(request_response_d.get("code", 0))
        self.message = request_response_d.get("description", 0)
        self.reason = transaction_d["reason"]
        self.state = transaction_d["state"]
        self.status = int(transaction_d["status"])
        self.test = bool(transaction_d["test"])
        self.hipay_orderid = str(transaction_d["order"]["id"])
        self.authorized_amount = Decimal(transaction_d["authorizedAmount"])
        self.captured_amount = Decimal(transaction_d["capturedAmount"])
        self.refunded_amount = Decimal(transaction_d["refundedAmount"])
        self.attempt_id = int(transaction_d["attemptId"])
        self.ip_address = transaction_d["ipAddress"]
        self.payment_product = transaction_d["paymentProduct"]
        if self.payment_product and transaction_d["paymentMethod"]:
            payment_method = transaction_d["paymentMethod"]
            self.payment_method_brand = payment_method["brand"]
            self.payment_method_expiry = "%02d/%d" % (
                int(payment_method["cardExpiryMonth"]),
                int(payment_method["cardExpiryYear"]),
            )
            self.payment_method_token = payment_method["token"]
            self.payment_method_card_holder = payment_method["cardHolder"]
            self.payment_method_country = payment_method["country"]
            self.payment_method_issuer_bank = payment_method["issuer"]
            self.payment_method_pan = payment_method["pan"]

    def get_status(self):
        if self.payment_product and self.payment_method_brand:
            payment_method_dict = {
                "brand": self.payment_method_brand,
                "cardExpiry": self.payment_method_expiry,
                "token": self.payment_method_token,
                "cardHolder": self.payment_method_card_holder,
                "country": self.payment_method_country,
                "issuer": self.payment_method_issuer_bank,
                "pan": self.payment_method_pan,
            }
        else:
            payment_method_dict = {}

        return {
            "code": self.error_code,
            "description": self.message,
            "reason": self.reason,
            "state": self.state,
            "status": self.status,
            "test": self.test,
            "transactionReference": self.transaction_reference,
            "order": {"id": self.hipay_orderid},
            "izberg_order": self.get_order_dict(),
            "izberg_payment": self.get_payment_dict(),
            "authorizedAmount": self.authorized_amount,
            "capturedAmount": self.captured_amount,
            "refundedAmount": self.refunded_amount,
            "attemptId": self.attempt_id,
            "ipAddress": self.ip_address,
            "paymentProduct": self.payment_product,
            "payemntMethod": payment_method_dict,
        }

    def get_order_dict(self):
        if not self.order:
            return None

        from apps.orders.api.order_resource import OrderResource

        return {
            "id": self.order.id,
            "resource_uri": OrderResource().get_resource_uri(self.order),
            "pk": self.order.id,
        }

    def get_payment_dict(self):
        if not self.payment:
            return None

        # don't import me at module level because of deprecation dates.
        from apps.payment.api.payment_resources import PaymentResource

        return {
            "id": self.payment.id,
            "resource_uri": PaymentResource().get_resource_uri(self.payment),
            "pk": self.payment.id,
        }

    def refresh_status(self, status_d=None):
        """Refresh info using provided dict OR by getting info itself"""
        logger.debug("Refreshing bank infos registration status:")

        if not status_d:
            api = self.get_api()
            # if api is None: we failed. Stop HERE
            if api is None:
                return
            # try to get bank transaction status from HipayServer
            context = {
                "action_name": hipay_refs.HIPAY_ACTION_GET_STATUS,
                "db_logger": api.db_logger,
                "payment": self.payment,
            }
            with logging_context(**context):
                status_d = api.get_status(self.payment)

        self.update_from_request_dict(status_d)
        self.save()

    def get_api(self):
        """Return Hipay API instance."""
        # get the Hipay API
        application = self.get_application()
        logger.debug("application: %s ", application)
        try:
            config = get_conf_for_application(application)
            return config.cashin_api
        except HipayNotConfigured:
            logger.warning("Hipay is not configured yet.")
            return None

    def get_application(self):
        return self.payment.application

    def update_settled_amounts(self, operation_id, amount, save=True):
        if self.settled_amounts:
            self.settled_amounts[operation_id] = str(amount)
        else:
            self.settled_amounts = {operation_id: str(amount)}
        if save:
            self.save(update_fields=["settled_amounts"])

    @property
    def settled_amount(self):
        settled_amount = Decimal("0")
        if self.settled_amounts:
            for value in self.settled_amounts.values():
                settled_amount += Decimal(value)
        return settled_amount

    def update_from_hipay_notif(self, notification_data):
        def get_value_in_form(form, key):
            value = form.get(key, None)
            if isinstance(value, list):
                return value[0]
            return value

        transaction_ref = get_value_in_form(notification_data, "transaction_reference")
        if transaction_ref:
            self.update_transaction_reference(transaction_ref)

        state = get_value_in_form(notification_data, "state")
        self.state = str(state) if state is not None else ""

        reason = get_value_in_form(notification_data, "reason[message]")
        self.reason = str(reason) if reason is not None else ""

        test = get_value_in_form(notification_data, "test")
        self.test = bool(test) if test is not None else None

        error_code = get_value_in_form(notification_data, "reason[code]")
        self.error_code = int(error_code or 0)

        attempt_id = get_value_in_form(notification_data, "attempt_id")
        self.attempt_id = int(attempt_id) if attempt_id is not None else None

        status = get_value_in_form(notification_data, "status")
        self.status = int(status) if status is not None else None

        message = get_value_in_form(notification_data, "message")
        self.message = message or ""

        amount = get_value_in_form(notification_data, "authorized_amount")
        self.authorized_amount = Decimal(amount or 0)

        amount = get_value_in_form(notification_data, "captured_amount")
        self.captured_amount = Decimal(amount or 0)

        amount = get_value_in_form(notification_data, "refunded_amount")
        self.refunded_amount = Decimal(amount or 0)

        address = get_value_in_form(notification_data, "ip_address")
        self.ip_address = str(address) if address is not None else ""

        hipay_id = get_value_in_form(notification_data, "order[id]")
        self.hipay_orderid = str(hipay_id) if hipay_id is not None else None

        payment_product = get_value_in_form(notification_data, "payment_product")
        self.payment_product = payment_product
