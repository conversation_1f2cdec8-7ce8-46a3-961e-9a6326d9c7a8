# -*- coding: utf-8 -*-

import xworkflows
from apps.kyc.models import KycInformation, MerchantIdentityDocument
from django.core.exceptions import ValidationError
from django.core.serializers.json import DjangoJSONEncoder
from django.db import models
from django.utils.functional import cached_property
from django.utils.translation import gettext_lazy as _
from django_xworkflows import models as xwf_models
from ims.models.mixin import ChangedFieldLookupMixin, SanitizeCharFieldMixin
from ims.workflow import IzbergWorkflow
from lib.models import IzbergQueryset, PostgresIndex
from payment_backends.hipay_tpp.codes import HIPAY_ERROR_ACCOUNT_SETUP_NOT_COMPLETE
from payment_backends.hipay_tpp.models import HipayAppConfiguration
from payment_backends.hipay_tpp.models.hipay_env_mixin import HipayEnvMixinModel
from payment_backends.hipay_tpp.references import (
    ACCOUNT_CREATION_FAILED,
    ACCOUNT_CREATION_PENDING,
    ACCOUNT_CREATION_STATUSES,
    ACCOUNT_CREATION_SYNCHRONIZED,
    ACCOUNT_TYPE_CHOICES,
    HIPAY_ACCOUNT_TYPE_CORPORATION,
    HIPAY_BANK_INFOS_STATUSES_VALID,
)


class SynchronizationWorkflow(IzbergWorkflow):
    states = ACCOUNT_CREATION_STATUSES

    transitions = (
        (
            "synchronize",
            [ACCOUNT_CREATION_PENDING, ACCOUNT_CREATION_FAILED],
            ACCOUNT_CREATION_SYNCHRONIZED,
        ),
        (
            "fail",
            [
                ACCOUNT_CREATION_PENDING,
                ACCOUNT_CREATION_FAILED,
                ACCOUNT_CREATION_SYNCHRONIZED,
            ],
            ACCOUNT_CREATION_FAILED,
        ),
        (
            "retry",
            [
                ACCOUNT_CREATION_FAILED,
            ],
            ACCOUNT_CREATION_PENDING,
        ),
    )

    initial_state = ACCOUNT_CREATION_PENDING


class HipayWalletAccount(
    xwf_models.WorkflowEnabled,
    HipayEnvMixinModel,
    SanitizeCharFieldMixin,
    ChangedFieldLookupMixin,
    models.Model,
):
    """
    payment_backends.hipay_tpp.models.wallet_account model:
    stores data from Hipay server to let merchants access the API using their
    account id or their own API keys.
    """

    objects = IzbergQueryset.as_manager()

    synchro_status = xwf_models.StateField(SynchronizationWorkflow)
    synchro_error = models.JSONField(
        blank=True,
        db_column="synchro_error",
        default=dict,
        encoder=DjangoJSONEncoder,
    )
    not_sanitized_fields = (
        "api_password",
        "callback_salt",
    )

    # API keys
    api_login = models.CharField(max_length=64, blank=True, null=True)
    api_password = models.CharField(max_length=64, blank=True, null=True)
    # account IDs
    user_account_id = models.IntegerField(blank=True, null=True)
    user_space_id = models.IntegerField(null=True, blank=True)
    # Unused website IDs (yet)
    websiteIds = models.CharField(max_length=255, default="", blank=True)
    callback_salt = models.CharField(
        max_length=64,
        blank=True,
        help_text="Salt fetched from Hipay API used to validate notifications",
    )
    # merchant model linked to this hipay account.
    merchant = models.ForeignKey(
        "stores.Merchant",
        null=True,
        blank=True,
        db_index=False,
        on_delete=models.CASCADE,
    )
    application = models.ForeignKey(
        "ice_applications.Application",
        null=True,
        blank=True,
        db_index=False,
        on_delete=models.CASCADE,
    )

    email = models.EmailField(max_length=128, blank=True, null=False)
    account_type = models.IntegerField(
        choices=ACCOUNT_TYPE_CHOICES, default=HIPAY_ACCOUNT_TYPE_CORPORATION
    )

    class Meta:
        indexes = [
            PostgresIndex(
                fields=["last_update_on"],
                name="hipay_tpp_hipaywalletaccount_last_update_on_idx",
            ),
        ]

    def clean(self):
        self.clean_application_and_merchant()
        self.ensure_unique()
        hipay_account_seems_synchronized = (
            self.user_account_id
            or self.user_space_id
            or self.api_login
            or self.api_password
        )
        if self.id is None:
            self.check_unicity()
            if hipay_account_seems_synchronized and self.synchronize.is_available():
                self.synchronize(save=False)
        self.clean_hipay_data()
        self.clean_synchro_error()

    def clean_synchro_error(self):
        if self.synchro_status == ACCOUNT_CREATION_SYNCHRONIZED:
            self.synchro_error = {}

    def clean_application_and_merchant(self):
        if self.application is None and self.merchant is None:
            raise ValidationError(
                _("Should provide merchant or application, none of them given")
            )
        if self.application_id is None and self.merchant_id:
            self.application_id = self.merchant.application_id
        app_mismatch = (
            self.merchant and self.merchant.application_id != self.application_id
        )
        if app_mismatch:
            msg = _("Application miss matches merchant application")
            raise ValidationError({"application": msg})
        if self.merchant_id is None:
            return

        address = self.merchant.billing_address
        if address is None:
            raise ValidationError(
                _("Merchant must have a billing address to create an Hipay account"),
                code=HIPAY_ERROR_ACCOUNT_SETUP_NOT_COMPLETE,
            )

    def ensure_unique(self):
        self.actions.validate_creation(self.merchant, self.application, account=self)

    def check_unicity(self):
        if self.merchant_id is None:
            return
        accounts = HipayWalletAccount.objects.filter(merchant_id=self.merchant_id)
        if accounts.exists():
            error = {"merchant": _("Merchant cannot have two Hipay accounts")}
            raise ValidationError(error)

    def clean_hipay_data(self):
        if self.synchro_status == ACCOUNT_CREATION_SYNCHRONIZED:
            msg = _(
                "Inconsistent state, cannot be created on Hipay "
                'without "{field_name}" filled!'
            )
            if self.user_account_id is None:
                raise ValidationError(msg.format(field_name="user_account_id"))
            if self.user_space_id is None:
                raise ValidationError(msg.format(field_name="user_space_id"))
            if self.api_login is None:
                raise ValidationError(msg.format(field_name="api_login"))
            if self.api_password is None:
                raise ValidationError(msg.format(field_name="api_password"))

    @property
    def EWalletApiCredentialsDict(self):
        """Property returning a dict containing the follwing key-valu pairs:
        wsLogin: self.EWalletApiLogin
        wsPassword: self.EWalletApiPassword
        """
        return {
            "wsLogin": self.api_login,
            "wsPassword": self.api_password,
        }

    def save(self, *args, **kwargs):
        from payment_backends.hipay_tpp.tasks import (
            create_hipay_account,
            validate_hipay_account,
        )

        # TODO: remove this after tastypie migration
        kwargs.pop("from_api", None)
        self.full_clean()
        is_creation = self.id is None

        super(HipayWalletAccount, self).save(*args, **kwargs)
        if is_creation:
            if self.user_account_id is None:
                create_hipay_account.delay(self.id, self.application_id)
            else:
                validate_hipay_account.delay(self.id, self.application_id)

    @cached_property
    def actions(self):
        from payment_backends.hipay_tpp.actions import WalletAccountActionsManager

        return WalletAccountActionsManager(self)

    @property
    def owner_display_name(self):
        if self.merchant_id is not None:
            return self.merchant.name
        return self.application.name

    @property
    def hipay_config(self):
        application = self.application or self.merchant.application
        from payment_backends.hipay_tpp.utils import get_conf_for_application

        return get_conf_for_application(application)

    @property
    def is_identified(self):
        if self.synchro_status != ACCOUNT_CREATION_SYNCHRONIZED:
            return False
        return self.hipay_config.cashout_api.is_identified(self)

    @property
    def current_balance(self):
        api = self.hipay_config.cashout_api
        return api.get_balance_and_withdrawable_amount(self)[0]

    @property
    def withdrawable_amount(self):
        api = self.hipay_config.cashout_api
        return api.get_balance_and_withdrawable_amount(self)[1]

    @property
    def bank_account_selected(self):
        from payment_backends.hipay_tpp.models import HipayBankAccount

        if self.merchant_id:
            return HipayBankAccount.objects.filter(merchant_id=self.merchant_id).last()
        else:
            return HipayBankAccount.objects.filter(
                application_id=self.application_id
            ).last()

    @property
    def application_configuration(self):
        return HipayAppConfiguration.objects.filter(
            application_id=self.application_id, active=True
        ).first()

    @property
    def is_cashout_possible(self):
        bank_account = self.bank_account_selected
        if not bank_account:
            return "No: Bank account not selected. Do it from Seller settings."
        elif bank_account.synchro_status != HIPAY_BANK_INFOS_STATUSES_VALID:
            return "No: Selected bank account is not validated on hipay side."

        if not self.is_identified:
            return "No: Wallet is not identified. Re-send KYCs."

        balance = self.current_balance
        if balance > 0:
            return (
                "No: Account is not empty. Maybe another cash-out is stuck or "
                "ongoing -- balance should be null, it is %s" % balance
            )
        elif balance < 0:
            if self.merchant_id:
                return (
                    "No: Some taxes have been applied to wallet or another "
                    "issue occurred. If this is not supposed to happen, "
                    "contact hipay support to investigate or remove any "
                    "tax applied to merchant wallets."
                )
            return (
                "Yes: however be careful - there are hipay running fees on "
                "commission account."
            )

        return "Yes: Theoretically, we have no objections."

    @xworkflows.after_transition("synchronize", field="synchro_status")
    def publish_pending_documents(self, *args, **kwargs):
        if self.merchant_id is None:
            # Skip KYC publication when working on an application wallet
            return
        pending_docs = list(
            KycInformation.objects.filter(merchant_id=self.merchant_id)
            .filter_active()
            .filter_backend_pending()
        ) + list(
            MerchantIdentityDocument.objects.filter(merchant_id=self.merchant_id)
            .filter_active()
            .filter_backend_pending()
        )
        for doc in pending_docs:
            doc.publish_document(source="wallet-sync")
