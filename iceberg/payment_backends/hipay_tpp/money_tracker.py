# -*- coding: utf-8 -*-

import logging

from constance import config
from payment_backends.common import BaseFakeMoneyTracker, BaseMoneyTracker

logger = logging.getLogger(__name__)


class HipayMoneyTracker(BaseMoneyTracker):
    def get_psp_transaction(self, payment=None, order=None, merchant_order=None):
        from payment_backends.hipay_tpp.models import HipayTransaction

        if payment is not None:
            return HipayTransaction.objects.get(payment=payment)
        elif order is not None:
            return HipayTransaction.objects.get(payment=order.payment)
        elif merchant_order is not None:
            return HipayTransaction.objects.get(payment=merchant_order.order.payment)
        else:
            raise RuntimeError("<PERSON><PERSON>pper forgot to set one of possible parameters.")

    def is_capture_not_failed(self, merchant_order):
        """
        Check if there is capture requested for current merchant order
        NB : same as is captured for hipay, since its done synchroniously.
        """
        return self.is_captured(merchant_order)

    def is_captured(self, merchant_order):
        """This action is in charge of marking sure the order has been
        captured.

        If yes, then the money is not on client bank account anymore and may be
        either on hipay collect API pipe OR already arrived onto marketplace
        API.

        :return: Money taken from client account?
        :rtype: boolean
        """
        payment = self.transaction.payment
        return payment.collected_amount == payment.authorized_amount

    def is_on_escrow(self, merchant_order):
        """This action is in charge of making sure money has been credited onto
        marketplace API.

        If yes, then funds allocation can occur

        :return: Money distributed onto ESCROW wallet
        :rtype: bool
        """
        transaction = merchant_order.order.payment.hipay_transaction
        return transaction.escrow_delivery_settlement.get(str(merchant_order.id), False)

    def set_on_escrow(self, merchant_order):
        transaction = merchant_order.order.payment.hipay_transaction
        if self.is_on_escrow(merchant_order):
            logger.info(
                "mo {} already on escrow of hipay trans {}".format(
                    merchant_order.id, transaction.id
                )
            )
            return
        logger.info(
            "setting mo {} on escrow of hipay trans {}".format(
                merchant_order.id, transaction.id
            )
        )
        transaction.escrow_delivery_settlement[str(merchant_order.id)] = True
        transaction.save()

    def is_order_resolved_on_payment_backend(self, order):
        """Return true if all merchant-orders have been paid (i.e. if all
        merchant_orders have been captured)

        :params order: Order
        :type order: apps.orders.models.Order
        """
        for merchant_order in order.merchant_orders.exclude_canceled_deleted():
            if not self.is_on_escrow(merchant_order):
                return False
        return True

    def set_on_wallet(self, merchant_order, amount):
        pass

    def is_on_wallet(self, merchant_order):
        return False

    def set_on_operator_wallet(self, order, amount):
        pass

    def is_on_operator_wallet(self, order):
        pass

    def should_refund_from_wallet(self, refund):
        return False

    def can_refund(self, refund):
        pass


def get_money_tracker(payment) -> BaseMoneyTracker:
    use_fake_tracker = getattr(config, "ENABLE_FAKE_TRACKER", False)
    if use_fake_tracker:
        return BaseFakeMoneyTracker(payment)
    else:
        return HipayMoneyTracker(payment)
