# -*- coding: utf-8 -*-
import logging
from decimal import Decimal
from typing import Optional, Tuple

import payment_backends.hipay_tpp.references as hipay_refs
from apps.payment.backends_pool import PaymentBackendsPool
from apps.payment.bases.processor import BasePaymentProcessor
from apps.payment.models import Payment
from apps.returns.models import Refund
from apps.user.models import User
from django.utils.translation import gettext_lazy as _
from django_xworkflows.models import InvalidTransitionError
from ims.models.mixin import Cleaner
from lib.core.base_actions_manager import dummy_context_manager
from payment_backends.common.exceptions import TransactionProcessingError
from payment_backends.common.processor import transaction_error_handler
from payment_backends.common.utils import logging_context
from payment_backends.hipay_tpp.hipay_api.actions.capture import CaptureAction
from payment_backends.hipay_tpp.models import (
    HipayAppConfiguration,
    HipayBankAccount,
    HipayTransaction,
    HipayWalletAccount,
)
from payment_backends.hipay_tpp.money_tracker import get_money_tracker
from payment_backends.hipay_tpp.utils import (
    get_conf_for_application,
    get_merchant_amounts_on_order,
    hipay_get_unique_id,
)
from reference import status as statuses

logger = logging.getLogger(__name__)


def generate_operation_ids(merchant_orders, operation):
    """
    generate a merchant operation ID using the list of
    merchant orders ids and opration.
    return the list of mo ids and the operation id in a tuple

    ex: CAPTURE:1:4:6:7  for a capture operation on merchant orders 1,
    4, 6 and 7.
    """
    mo_ids = [x.pk for x in merchant_orders]
    operation_id = [str(x) for x in mo_ids]
    operation_id = operation + ":" + ":".join(operation_id)
    return mo_ids, operation_id


class HipayTppPayment(BasePaymentProcessor):
    """
    Hipay payment backend processor. Enables IZBERG marketplace to
    use Hipay tpp solution for paying and ventilating money amongst
    merchants.
    """

    name = "hipay_tpp"
    version = "0.0.1"
    verbose_name = _("Hipay")
    can_register_card = False
    direct_collect = False
    can_authorized_payment = True
    handle_term_payment = False
    handle_prepayment = True

    def __init__(self):
        self.config = None

    def _raise_for_merchant_order_not_on_escrow(self, merchant_order) -> None:
        config = self.get_config(merchant_order.application)
        payment = merchant_order.order.payment
        skip_escrow_check = (
            config.cashin_mode == config.CASHIN_MODE_UNHANDLED
            or merchant_order.order.amount_to_collect == Decimal("0.00")
            or merchant_order.is_fully_refunded
            or not payment.hipay_transaction.capture_operations
        )
        if skip_escrow_check:
            return
        tracker = get_money_tracker(payment)
        if not tracker.is_on_escrow(merchant_order):
            msg = "Merchant order amount has not been funded on escrow wallet!"
            raise TransactionProcessingError(msg)

    @transaction_error_handler
    def process_merchant_sale_for_merchant(self, subtransaction):
        self._raise_for_merchant_order_not_on_escrow(
            subtransaction.transaction.merchant_order
        )
        return True

    @transaction_error_handler
    def process_operator_sale_for_application(self, subtransaction):
        self._raise_for_merchant_order_not_on_escrow(
            subtransaction.transaction.merchant_order
        )
        return True

    @transaction_error_handler
    def process_merchant_sale_for_application(self, subtransaction):
        self._raise_for_merchant_order_not_on_escrow(
            subtransaction.transaction.merchant_order
        )
        return True

    def get_config(
        self, application, currency=None, payment_method=None
    ) -> HipayAppConfiguration:
        """return config for application"""
        return get_conf_for_application(application)

    def get_manual_refund(self, application, payment):
        """
        Return True: refunds are managed through external hipay plugins.
        :return: is external intervention required
        """
        self.ensure_api(application)
        config = self.get_config(application)
        return config.cashin_mode == config.CASHIN_MODE_UNHANDLED

    def get_manual_cashout(self, application):
        """
        Return True: cashout are managed through external plugins.
        This can be configured in hipay config.
        :return: is external intervention required
        """
        self.ensure_api(application)
        config = self.get_config(application)
        return not config.cashout_enabled

    ####
    # Confirmation
    ####

    def collect(self, payment, by_user, amount, merchant_order=None):
        """Collect method is called for partial capture. It has to be
        used with:
        0 <= amount <= total_order_amount

        :param payment: Payment model item linked with transaction
        :type payment: payment.models.payment_model.Payment

        :param amount: amount to capture
        :type amount: str or Decimal

        :param merchant_order: merchant_orders collected (or list of)
        :type merchant_order: MerchantOrder or list<MerchantOrder>
        """
        self.ensure_api(payment.application)
        # adjust merchant_order type to a (potentially ezmpty) list of
        # merchant_orders
        if merchant_order is None:
            merchant_order = []
        elif not isinstance(merchant_order, list):
            merchant_order = [merchant_order]
        # call capture
        mo_ids, operation_id = generate_operation_ids(merchant_order, "CAPTURE")
        config = self.get_config(payment.application)
        if config.cashin_mode != config.CASHIN_MODE_UNHANDLED:
            try:
                transaction = HipayTransaction.objects.get(payment=payment)
            except HipayTransaction.DoesNotExist:
                transaction = HipayTransaction.objects.get(order=payment.order)
            payment_product = transaction.payment_product
            if payment_product not in hipay_refs.REALTIME_BANKING_PRODUCTS:
                transaction.capture_operations[operation_id] = mo_ids
            transaction.save()
        else:
            return

        if payment_product in hipay_refs.REALTIME_BANKING_PRODUCTS:
            return

        if transaction.status == hipay_refs.TRANSACTION_CAPTURE_REQUESTED:
            logger.warning(
                "Transaction %s is already in capture requested state, "
                "ignoring capture request",
                transaction.transaction_reference,
            )
            return False

        CaptureAction(self.config.cashin_api).run(
            payment, amount, merchant_order, operation_id
        )

    ####
    # Refund
    ####

    def refund(
        self,
        payment: Payment,
        by_user: User,
        amount: Optional[Decimal] = None,
        refund: Refund = None,
    ):
        self.ensure_api(payment.application)
        config = self.config
        if config.cashin_mode == config.CASHIN_MODE_UNHANDLED:
            return False

        currency = payment.currency
        logger.debug("Hipay will refund %f %s", amount, currency)

        order_id = payment.order_id
        if not order_id:
            return False
        try:
            transaction = HipayTransaction.objects.get(payment=payment)
        except HipayTransaction.DoesNotExist:
            transaction = HipayTransaction.objects.get(order=payment.order)
        transaction_reference = self.synchronize_transaction_reference(
            payment, transaction
        )

        if transaction.payment_product in hipay_refs.NOT_REFUNDABLE_PRODUCTS:
            return False

        logger.info("Hipay: Will refund payment %s of amount %s", order_id, amount)

        if not refund.merchant_order:
            raise NotImplementedError(
                "Hypay payment_backend can only handle "
                "merchant_order based refund for now."
            )

        mo_ids = [refund.merchant_order.pk]
        if hasattr(refund, "id") and refund.id is not None:
            operation_id = f"REFUND:{refund.id}"
        else:
            # fallback: garder l'ancien comportement si pas d'id
            _, operation_id = generate_operation_ids([refund.merchant_order], "REFUND")
        # Nouvelle structure enrichie pour refund_operations
        refund_data = {
            "merchant_order_ids": mo_ids,
            "hipay_operation_id": None,  # à remplir après appel API si possible
            "status": "PENDING",  # statut initial
            "amount": str(amount) if amount is not None else None,
            "created_at": (
                refund.created_at.isoformat()
                if hasattr(refund, "created_at") and refund.created_at
                else None
            ),
            "refund_id": refund.id if hasattr(refund, "id") else None,
            "hipay_created_at": None,  # date de création de l'opération côté HiPay
            "merchant_reference": operation_id,  # pour faciliter la correspondance
        }
        transaction.refund_operations[operation_id] = refund_data

        context = {
            "db_logger": self.config.cashin_api.db_logger,
            "action_name": hipay_refs.HIPAY_ACTION_REFUND,
            "payment": payment,
        }
        with logging_context(**context):
            hipay_response = self.config.cashin_api.refund(
                transaction_reference, operation_id, amount
            )
            # Utiliser get_status_v3 pour obtenir les détails complets des opérations

        status_context = {
            "db_logger": self.config.cashin_api.db_logger,
            "action_name": hipay_refs.HIPAY_ACTION_GET_STATUS,
            "payment": payment,
        }
        with logging_context(**status_context):
            try:
                status_v3 = self.config.cashin_api.get_status_v3(payment)
                if status_v3 and isinstance(status_v3, dict):
                    # Chercher l'opération de refund qui correspond à notre operation_id
                    operations = status_v3.get("operations", [])
                    if operations:
                        matching_refund_op = None
                        # Chercher l'opération avec le merchant_reference qui correspond à notre operation_id
                        for op in operations:
                            if (
                                op.get("type") == "REFUND"
                                and op.get("merchant_reference") == operation_id
                            ):
                                matching_refund_op = op
                                break

                        # Si pas trouvé par merchant_reference, prendre la dernière opération REFUND
                        if not matching_refund_op:
                            for op in reversed(operations):
                                if op.get("type") == "REFUND":
                                    matching_refund_op = op
                                    break

                        if matching_refund_op:
                            refund_data["hipay_operation_id"] = matching_refund_op.get(
                                "id"
                            )
                            refund_data["status"] = matching_refund_op.get(
                                "status", refund_data["status"]
                            )
                            # Ajouter la date de création de l'opération HiPay si disponible
                            if matching_refund_op.get("date_created"):
                                refund_data["hipay_created_at"] = (
                                    matching_refund_op.get("date_created")
                                )
            except Exception as e:
                logger.warning(
                    "Impossible de récupérer les détails v3 pour le refund: %s", e
                )
                # Fallback sur l'ancienne méthode
                if hipay_response and isinstance(hipay_response, dict):
                    refund_data["status"] = hipay_response.get(
                        "status", refund_data["status"]
                    )

            transaction.refund_operations[operation_id] = refund_data
        transaction.refresh_status()

        return True

    ####
    # Cancelling
    ###
    def cancel(self, payment, by_user=None, amount=None, merchant_order=None):
        # local import to avoid circular deps.
        from payment_backends.hipay_tpp.tasks import do_refund_on_cancel

        self.ensure_api(payment.application)
        config = self.config
        if config.cashin_mode == config.CASHIN_MODE_UNHANDLED:
            return True

        order_id = payment.order_id
        if not order_id:
            return True

        try:
            transaction = HipayTransaction.objects.get(payment=payment)
        except HipayTransaction.DoesNotExist:
            transaction = HipayTransaction.objects.get(order=payment.order)

        payment_product = transaction.payment_product

        if payment_product not in hipay_refs.REALTIME_BANKING_PRODUCTS:
            # Do not call the "cancel" operation on Credit-Card authorizations
            # To keep the possibility to capture in case the cancelled order
            # was a mistake. There is no incidence on security or customer
            # bank account so we keep the money allocated as long as we can.
            return True

        if payment_product in hipay_refs.NOT_REFUNDABLE_PRODUCTS:
            # for example. giropay, sdd, etc...
            return True

        self.synchronize_transaction_reference(payment, transaction)

        if merchant_order is not None:
            refunded_orders = [merchant_order]
        else:
            refunded_orders = payment.order.merchant_orders.all()

        mo_ids, operation_id = generate_operation_ids(refunded_orders, "REFUND")
        # Utiliser la même structure enrichie que pour la méthode refund
        from django.utils import timezone

        refund_data = {
            "merchant_order_ids": mo_ids,
            "hipay_operation_id": None,  # sera rempli par la tâche async
            "status": "PENDING",
            "amount": str(amount) if amount is not None else None,
            "created_at": timezone.now().isoformat(),
            "refund_id": None,  # pas de refund object dans cancel
            "hipay_created_at": None,
            "merchant_reference": operation_id,
        }
        transaction.refund_operations[operation_id] = refund_data
        transaction.save()

        do_refund_on_cancel.delay(
            payment.application_id,
            transaction.id,
            payment.id,
            str(amount),
            operation_id,
        )

        return True

    ####
    # Authorization
    ####

    def payment_form(self, payment, extra_data, request):
        """Return payment form infos client will use to make its authorization
        call
        """

        # With Hipay Host V2, gender need to be in upper case : F, M or U.
        # According to the documentation of hipay regarding V1, upper case should work.
        if "gender" in extra_data["hipay_override"]:
            extra_data["hipay_override"]["gender"] = extra_data["hipay_override"][
                "gender"
            ].upper()

        self.ensure_api(payment.application)

        config = self.config
        if config.cashin_mode != config.CASHIN_MODE_IZBERG:
            return self.generate_authorization_response_d("")

        is_operator = extra_data.get("operator", False)
        hipay_override = extra_data.get("hipay_override", {})

        if payment.order:
            self.validate_order(payment.order, request.user)
            self.pending_authorization_on_payment(payment.order, request.user)

        url, __ = self.get_auth_url_and_status(payment, is_operator, hipay_override)

        return self.generate_authorization_response_d(url)

    def get_transaction(self, payment):
        self.ensure_api(payment.application)
        hipay_unique_id = hipay_get_unique_id(payment)

        self.config.cashin_api.db_logger.work_on(
            hipay_refs.HIPAY_ACTION_GET_STATUS, payment=payment
        )
        context = {
            "db_logger": self.config.cashin_api.db_logger,
            "action_name": hipay_refs.HIPAY_ACTION_GET_STATUS,
            "payment": payment,
        }
        with logging_context(**context):
            status = self.config.cashin_api.get_status(payment)

        if not status or "transaction" not in status:
            return None

        trans, _creation = self.get_or_create_transaction(payment, hipay_unique_id)
        trans.update_from_request_dict(status)
        return trans

    def authorize(self, payment, by_user, is_operator=False, extra_data=None):
        """Request an authorization to hipay.

        :raise InvalidTransitionError: Occurs when payment object is not in
        initial sate when calling this method.
        :raise HTTPError: Occurs when some error occur on communication with
        hipay server.

        :param payment_obj: payment object
        :type payment_obj: payment.models.payment_model.Payment

        :param user: User who is ordering
        :type user: django.contrib.auth.models.User

        :param is_operator: Is it an authorization for an Operator
        assisted payment? (Note: incompatible with token payments)
        :type is_operator: bool

        :param extra_data: Unused yet ( specify additional data for
        authorisation)
        :type extra_data: dict

        :return: Authorization information like redirect url or payment
        backend name.
        :rtype: dict
        """
        logger.debug("Hipay authorization call.")

        self.ensure_api(payment.application)
        config = self.config
        if config.cashin_mode != config.CASHIN_MODE_IZBERG:
            self.handle_external_hipay_payments(payment)
            payment.authorize(user=by_user, previous_status=payment.status)
            return self.generate_authorization_response_d("")

        url, hipay_status = self.get_auth_url_and_status(
            payment, is_operator, extra_data
        )

        if hipay_status and "transaction" in hipay_status:
            hipay_state = hipay_status["transaction"]["state"]
            transaction = self.update_hipay_transaction_model(payment, hipay_status)
            payment_product = transaction.payment_product
            if payment_product in hipay_refs.REALTIME_BANKING_PRODUCTS:
                mo_ids, operation_id = generate_operation_ids(
                    payment.order.merchant_orders.all(), "CAPTURE"
                )
                transaction.capture_operations[operation_id] = mo_ids
            tref = hipay_status["transaction"]["transactionReference"]
        else:
            # Missing hipay_state definition. This could lead to failure if
            # we get no 'transaction' in hipay getStatus call. Hopefully it
            # never happend yet.
            tref = None

        if url:
            logger.debug("Hipay generated url : '%s'", url)
            return self.generate_authorization_response_d(url)

        if payment.status not in hipay_refs.AUTHORIZABLE_STATUSES:
            raise InvalidTransitionError

        self.update_payment_from_hipay_state(
            payment,
            hipay_state,
            tref,
            by_user,
            lock=False,  # order/payment should be already locked
        )

        payment.save()

        return self.generate_authorization_response_d(url)

    def is_merchant_activable(self, merchant):
        """Must return False if the merchant is not activable (i.e. he
        can be paid at the end of the month because he did configure
        his payment settings for ventilation)
        :param merchant: Merchant to check
        :type merchant: stores.models.store_models.Merchant
        :return: Whether merchant is ready for activation on payment config side
        :rtype: bool
        """
        try:
            HipayWalletAccount.objects.get(merchant=merchant)
        except HipayWalletAccount.DoesNotExist:
            return False
        return True

    def merchant_bank_account_registration_status(self, merchant):
        """Must return the merchant's bank account registration status.
        A VALIDATED status means the merchant can withdraw his balance (i.e.
        he can be paid at the end of the month because he did configure his
        bank account information for withdrawal of his balance)

        :param merchant: Merchant to check
        :type merchant: stores.models.store_models.Merchant
        :return: Whether merchant's bank account is validated, under validation
        or refused. (Defaults to VALIDATED if this feature is not used) plus
        the invalid status reason as returned by hipay api.
        :rtype: int, str
        """
        try:
            ba = HipayBankAccount.objects.get(merchant=merchant)
        except HipayBankAccount.DoesNotExist:
            return (
                PaymentBackendsPool.BACKEND_VALIDATION_DECLINED,
                "No bank account registered yet",
            )

        if ba.synchro_status == hipay_refs.HIPAY_BANK_INFOS_STATUSES_VALID:
            return PaymentBackendsPool.BACKEND_VALIDATION_VALIDATED, ""
        elif ba.synchro_status == hipay_refs.HIPAY_BANK_INFOS_STATUSES_WAITING:
            return PaymentBackendsPool.BACKEND_VALIDATION_ONGOING, ""
        else:
            return (PaymentBackendsPool.BACKEND_VALIDATION_DECLINED, ba.synchro_status)

    def ensure_api(self, application):
        if self.config and self.config.application_id == application.id:
            return
        # this might raise a HipayNotConfigured but we won't catch it so calling
        # requests receive an exception
        self.config = self.get_config(application)

    def get_auth_url_and_status(
        self, payment, operator, hipay_override, credit_card_token=None
    ):
        """Try to get status for given payment object (used as order ID on
        Hipay API side) and an authorization url. If status cannot be
        retrieved this method will request a new authorization form and
        return freshly received URL.

        :param payment: Payment object to fulfill.
        :type payment: payment.moels.Payment

        :param operator: Is the authorization requested for operator
        assisted payment.
        :type operator: bool

        :param hipay_override: Parameters used for hpayment call. These
        parameters will override any computational result generated by IZBERG
        modules.
        :type hipay_override: dict

        :param credit_card_token: Credit card token to use for authorization
        or None
        :type credit_card_token: str

        :return: tuple of URL string and status dictionary
        :rtype: tuple (str, dict)
        """
        hipay_unique_id = hipay_get_unique_id(payment)
        context = {
            "db_logger": self.config.cashin_api.db_logger,
            "action_name": hipay_refs.HIPAY_ACTION_GET_STATUS,
            "payment": payment,
        }
        with logging_context(**context):
            status = self.config.cashin_api.get_status(payment)
        eci_level = self.get_eci_level(operator)

        if status and "transaction" in status:
            url = status["transaction"]["forwardUrl"]
            return url, status

        trans, new = self.get_or_create_transaction(payment, hipay_unique_id)
        if (
            not new
            and int(trans.eci_level) == int(eci_level)
            and trans.forward_url is not None
        ):
            return trans.forward_url, {}

        request_params = self.get_payment_request_params(payment, hipay_unique_id)
        context = {
            "db_logger": self.config.cashin_api.db_logger,
            "action_name": hipay_refs.HIPAY_ACTION_VALIDATE,
            "payment": payment,
        }
        with logging_context(**context):
            result = self.config.cashin_api.authorize(
                request_params, eci_level, hipay_override, token=credit_card_token
            )

        url = result["forwardUrl"]
        status = {}
        trans.forward_url = url
        trans.eci_level
        trans.save()
        return url, status

    def get_or_create_transaction(
        self, payment, hipay_unique_id
    ) -> Tuple[HipayTransaction, bool]:
        hipay_unique_id = Cleaner().clean(hipay_unique_id)
        if payment.order:
            return HipayTransaction.objects.get_or_create(
                order=payment.order,
                hipay_orderid=hipay_unique_id,
                defaults={"payment": payment},
            )
        else:
            return HipayTransaction.objects.get_or_create(
                payment=payment, hipay_orderid=hipay_unique_id
            )

    def get_payment_request_params(self, payment, hipay_unique_id):
        language = self.config.cashin_api.get_locale_for_language(payment.language)
        params = {
            "description": payment,
            "currency": payment.currency,
            "amount": Decimal(payment.to_collect_amount),
            "merchant_display_name": payment.application.name,
            # language of the paid item (changes language of the payment page)
            # and defaults to marketplace's language.
            "language": language,
            "orderid": str(hipay_unique_id),
            "email": str(payment.user.email),
        }
        if payment.order and self.config.cashout_enabled:
            params["cdata1"] = str(
                get_merchant_amounts_on_order(self.config.cashout_api, payment.order)
            )
        return params

    def get_paid_item(self, payment_object):
        return payment_object.order

    def get_eci_level(self, is_operator=False, recurrent_usage=False):
        if recurrent_usage:
            # eci level is always END_USER_RECUR because we're using a token
            return hipay_refs.HIPAY_ECI_LEVEL_END_USER_RECUR
        if is_operator:
            return hipay_refs.HIPAY_ECI_LEVEL_OPERATOR
        return hipay_refs.HIPAY_ECI_LEVEL_END_USER

    def generate_authorization_response_d(self, url):
        """Return a dict containing following information:
        - paymentFormRedirectionUrl
        - paymentBackendName
        - displayMethod (payment form display method in iframe or redirection)

        :param url: url to use for paymentFormRedirectionUrl
        :type url: str

        :rtype: dict
        """
        return {
            "paymentFormRedirectionUrl": url,
            "paymentBackendName": self.name,
            "displayMethod": self.config.payment_form_template,
        }

    def update_payment_from_hipay_state(
        self, payment_obj, hipay_state, transaction_reference, user, lock=True
    ):
        """Takes a payment object and applies a transition on it so it's
        in a state that matches the hipay's transaction's state.
        Warning: Modifications on payment object are not saved.

        :raise InvalidTransitionError: When payment object is not in initial
        state.

        :param payment_obj: payment object to update
        :type payment_obj: payment.models.payment_model.Payment

        :param hipay_state: human readable state string recieved form
        Hipay api.
        :type hipay_state: str

        :param user: User who is ordering
        :type user: django.contrib.auth.models.User
        """

        order_lock = (
            payment_obj.order.actions.locker()
            if lock and payment_obj.order is not None
            else dummy_context_manager()
        )
        payment_lock = payment_obj.actions.locker() if lock else dummy_context_manager()

        with order_lock, payment_lock:
            payment_obj.refresh_from_db()

            if payment_obj.order is not None:
                self.validate_order(payment_obj.order, user)

            if not payment_obj.external_id and transaction_reference:
                payment_obj.external_id = transaction_reference
                payment_obj.save(update_fields=["external_id"])

            if hipay_state == "completed":
                logger.info("Transferring related payment Object to authorized state.")

                if payment_obj.status == statuses.PAYMENT_STATUS_AUTH_SUCCESS:
                    return
                if not payment_obj.authorize.is_available():
                    logger.exception(
                        "payment %s cannot be authorized because of it's current "
                        "status %s",
                        payment_obj.id,
                        payment_obj.status,
                    )
                    return

                payment_obj.authorize(user=user, previous_status=payment_obj.status)
            elif hipay_state == "declined":
                logger.info(
                    "Payment form filled with invalid credit card "
                    "info. Authorization rejected."
                )
                if not payment_obj.fail.is_available():
                    return
                payment_obj.fail(user=user, previous_status=payment_obj.status)
            elif hipay_state == "pending":
                logger.info(
                    "Transaction request was submitted to the acquirer "
                    "but response is not yet available."
                )
                if not payment_obj.pending_authorization.is_available():
                    return
                payment_obj.pending_authorization(
                    user=user, previous_status=payment_obj.status
                )
            elif hipay_state == "forwarding":
                logger.debug("Still forwarding user.")
            elif hipay_state == "error":
                if not payment_obj.fail.is_available():
                    return
                payment_obj.fail(user=user, previous_status=payment_obj.status)
            else:
                logger.exception("Un-handled hipay transaction state : %s", hipay_state)

    def update_hipay_transaction_model(self, payment, status_d):
        """Update transaction reference stored in HipayTransaction model
        linked to provided payment object.

        :param payment: payment object related to transaction we want to
        update
        :type payment: payment.models.payment_model.Payment

        :param status_d: dict containing information about transation as
        stored on hipay server. (api.get_status)
        :type status_d: dict
        """
        hipay_unique_id = hipay_get_unique_id(payment)
        transaction, _creation = self.get_or_create_transaction(
            payment, hipay_unique_id
        )
        transaction.update_from_request_dict(status_d)
        transaction.save()
        return transaction

    def cash_out(
        self,
        transaction,
        merchant,
        bank_account,
        amount,
        currency,
        application,
        debug=False,
        by_user=None,
    ):
        """
        @merchant: stores.Merchant object
        @bank_account: stores.MerchantBankAccount object
        @amount: Decimal Amount
        @currency: Currency obj or ID
        @application: Application owning the merchant
        @debug: if debug the cashout is not real
        @by_user: user making the cashout
        """
        # ensure API is set
        self.ensure_api(application)
        if not self.config.cashout_enabled:
            logger.info(
                "Cash Out %s%s for merchant %s, bank account %s must be"
                " manually confirmed by application.",
                amount,
                currency.code,
                merchant,
                bank_account,
            )
            return True

        return False

    @transaction_error_handler
    def application_cash_out(
        self,
        transaction,
        application_bank_account,
        amount,
        currency,
        debug=False,
        by_user=None,
    ):
        return True

    def validate_order(self, order, user):
        # We block the order
        if order.status == statuses.ORDER_STATUS_ONGOING:
            order.validate(user=user)

    def pending_authorization_on_payment(self, order, user):
        payment = order.payment
        if payment.pending_authorization.is_available():
            payment.pending_authorization(by_user=user)

    def handle_external_hipay_payments(self, payment_obj):
        config = self.config
        if config.cashin_mode != config.CASHIN_MODE_PRESTASHOP:
            return

        transaction, _ = HipayTransaction.objects.get_or_create(payment=payment_obj)

        if not payment_obj.external_id:
            logger.error(
                "Missing transaction reference on order %s. "
                "It must be set into order-payment's external-id field.",
                payment_obj.order_id,
            )
            return
        transaction.transaction_reference = payment_obj.external_id
        transaction.order_id = payment_obj.order_id
        transaction.save()

        if transaction.authorized_amount == Decimal("0.00"):
            from payment_backends.hipay_tpp.tasks.cashin.sync_hipay_transaction_status_prestashop_mode import (
                sync_hipay_transaction_status,
            )

            logger.info(
                "[HiPay][Sync] Triggering async sync for transaction %s (amount=0) SYNC_TRIGGERED=True",
                transaction.id,
            )
            sync_hipay_transaction_status.delay(transaction.id)

    ###
    # KYC functions
    ###
    def refresh_kyc_status(self, document):
        """Fetch documents statuses on hipay then applies transition on
        document to terminal state

        :type document: apps.stores.models.MerchantIdentityDocument
        """
        if document.is_in_terminal_state():
            # don't work on terminal state documents
            return

        self.ensure_api(document.merchant.application)

        if not self.config.cashout_enabled:
            return

        wallet = self.config.cashout_api.get_wallet_or_none(merchant=document.merchant)

        if wallet is None:
            logger.warning(
                "Trying to fetch KYC document statuses on an unregistered merchant."
            )
            return

        self.config.kyc_api.refresh_document_status_from_hipay_state(wallet, document)

    @staticmethod
    def synchronize_transaction_reference(payment, transaction):
        transaction_reference = transaction.transaction_reference
        if transaction_reference is None and payment.external_id is not None:
            transaction_reference = payment.external_id
            transaction.transaction_reference = payment.external_id
        elif payment.external_id is None and transaction_reference is not None:
            payment.external_id = transaction_reference
            payment.save()
        return transaction_reference
