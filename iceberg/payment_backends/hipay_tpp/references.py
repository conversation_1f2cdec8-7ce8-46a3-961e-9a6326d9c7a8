# -*- coding: utf-8 -*-

from django.utils.translation import gettext_lazy as _
from reference import status

# Hipay account creation status
ACCOUNT_CREATION_PENDING = "pending"
ACCOUNT_CREATION_SYNCHRONIZED = "synchronized"
ACCOUNT_CREATION_FAILED = "failed"
ACCOUNT_CREATION_STATUSES = (
    (ACCOUNT_CREATION_PENDING, _("Pending Hipay creation")),
    (ACCOUNT_CREATION_SYNCHRONIZED, _("Hipay account created")),
    (ACCOUNT_CREATION_FAILED, _("Hipay account creation failed")),
)

# Account type choices
HIPAY_ACCOUNT_TYPE_CORPORATION = 1
HIPAY_ACCOUNT_TYPE_PERSON = 2
HIPAY_ACCOUNT_TYPE_ASSOCIATION = 3
ACCOUNT_TYPE_CHOICES = (
    (HIPAY_ACCOUNT_TYPE_CORPORATION, _("Corporation")),
    (HIPAY_ACCOUNT_TYPE_PERSON, _("Person")),
    (HIPAY_ACCOUNT_TYPE_ASSOCIATION, _("Association")),
)

FILE_TAGS_MAPPING = {"front": "file"}

# internal error
INVALID_ORDER_ID = 1000
NO_TRANSACTION_FOUND = 1001
HIPAY_NOT_CONFIGURED = 1002
PREVIOUS_CASHOUT_WAITING = 1003
UNAPROVISIONNED_WITHDRAWAL = 1004
MERCHANT_WALLET_MISSING = 1005
BANK_ACCOUNT_NOT_SELECTED = 1006
BANK_ACCOUNT_MISMATCH = 1007
BANK_ACCOUNT_NOT_VALIDATED = 1008
WITHDRAWABLE_AMOUNT_INVALID = 1009
WALLET_ACCOUNT_NOT_IDENTIFIED = 1010
WALLET_STILL_EMPTY_AFTER_TRANSFER = 1011

# Hipay Rest API error codes
AMOUNT_LIMIT_EXCEEDED = 3020110
OPERATION_NOT_PERMITTED_ALREADY_CLOSED = 3020111
AUTHORIZATION_ALREADY_COMPLETED = 3020106

# Bank infos registration statuses definition
HIPAY_BANK_INFOS_STATUSES_INITIAL = "1"
HIPAY_BANK_INFOS_STATUSES_WAITING = "2"
HIPAY_BANK_INFOS_STATUSES_VALID = "3"
HIPAY_BANK_INFOS_STATUSES_INVALID = "4"

HIPAY_BANK_INFOS_STATUSES = (
    (HIPAY_BANK_INFOS_STATUSES_INITIAL, _("Initial")),
    (HIPAY_BANK_INFOS_STATUSES_WAITING, _("Waiting")),
    (HIPAY_BANK_INFOS_STATUSES_VALID, _("Valid")),
    (HIPAY_BANK_INFOS_STATUSES_INVALID, _("Invalid")),
)

HIPAY_ECI_LEVEL_END_USER_RECUR = "9"
HIPAY_ECI_LEVEL_END_USER = "7"
HIPAY_ECI_LEVEL_OPERATOR = "1"

# recurrent modes are not displayed since they're automatically selected
# on credit card token usage.
HIPAY_ECI_LEVELS = (
    (HIPAY_ECI_LEVEL_END_USER, _("End-user")),
    (HIPAY_ECI_LEVEL_OPERATOR, _("Operator")),
)

HIPAY_API_STATUS_COLLECTED = 120
HIPAY_API_STATUS_SETTLED = 123

BUSINESS_ACCOUNT = 1
PERSONAL_ACCOUNT = 0
HIPAY_ACCOUNT_TYPE_CHOICES = (
    (BUSINESS_ACCOUNT, _("Business account")),
    (PERSONAL_ACCOUNT, _("Personal account")),
)

BUSINESS_LINE_ID_OTHER = 20


HIPAY_BUSINESS_LINE_OTHERS = 20

REALTIME_BANKING_PRODUCTS = ["sofort-uberweisung", "giropay"]
NOT_REFUNDABLE_PRODUCTS = ["giropay"]
# deliberately considering Miss as Mrs because of french titles reform
TITLES_CONSTANTS = {"mr": 1, "mrs": 2, "miss": 2, "1": 1, "2": 2, "3": 2}
TITLES_CONSTANTS_CHOICES = ((1, _("Mr")), (2, _("Mrs")), (2, _("Miss")))

# wait 1 hour before requesting withdrawal (1h = 3600s)
HIPAY_WITHDRAWAL_AWAIT_TIME = 3600
CASHOUT_LIMIT = 500000

AUTHORIZABLE_STATUSES = (
    status.PAYMENT_STATUS_INITIAL,
    status.PAYMENT_STATUS_PENDING_AUTH,
)

HIPAY_ID_DOCUMENT = 1
HIPAY_KBIS_EXTRACT = 4
HIPAY_ARTICLES_OF_ASSOCIATION = 5
HIPAY_BANK_ACCOUNT_DETAILS = 6
HIPAY_PHYSICAL_KBIS_EXTRACT = 8
HIPAY_PHYSICAL_TAX_STATUS_INFORMATION_DOCUMENT = 9
HIPAY_ASSOCIATION_PRESIDENT = 11
HIPAY_ASSOCIATION_OFFICIAL_JOURNAL = 12
HIPAY_ASSOCIATION_STATUS = 13

HIPAY_BUSINESS_ACCOUNT_TYPE = 1

SUPPORTED_LOCALES = (
    ("fr_FR", _("French (France)")),
    ("en_GB", _("English (Great - Britain)")),
    ("en_US", _("English (United - States)")),
    ("es_ES", _("Spanish (Spain)")),
    ("de_DE", _("Deutsch (Germany)")),
    ("pt_PT", _("Portuguese (Portugal)")),
    ("pt_BR", _("Portuguese (Brazil)")),
    ("nl_NL", _("Dutch (Holland)")),
    ("nl_BE", _("Dutch (Belgium)")),
)

HIPAY_KYC_ALREADY_UNDER_VALIDATION = 415
HIPAY_KYC_NO_MORE_DOCUMENT_TO_VALIDATE = 414

HIPAY_EXISTING_POTENTIALLY_VALID_DOC_TYPE_CODES = (
    HIPAY_KYC_ALREADY_UNDER_VALIDATION,
    HIPAY_KYC_NO_MORE_DOCUMENT_TO_VALIDATE,
)

HIPAY_KYC_MESSAGES = {
    # 400: "don't handle since it's a validation error on upload request it's
    # handled by translated fields on localized hipay fields errors",
    HIPAY_KYC_ALREADY_UNDER_VALIDATION: _(
        "A document of this type is already validated or waiting for validation."
    ),
    HIPAY_KYC_NO_MORE_DOCUMENT_TO_VALIDATE: _(
        "All document have been validated for merchant."
    ),
    # translate english localized hipay fields errors
    "This value should be greater than today": _(
        "This value should be greater than today"
    ),
    # translate status labels:
    -1: _("Document has not been uploaded"),
    0: _("The document has been uploaded but not sent to hipay validation services"),
    1: _("The document has been sent to HiPay validation services"),
    2: _("The document has been validated for identification"),
    3: _(
        "The document has been refused because it is falsified, expired or "
        "inconsistent"
    ),
    5: _("The document is being reviewed"),
    8: _("The document has been refused"),
    9: _("A new review of the document is in progress"),
}


BANK_OUTSIDE_PERIMETER_MESSAGE = "Bank outside of perimeter"
EXPIRED_MESSAGE = "Expired"
FALSIFIED_MESSAGE = "Falsified"
FALSIFIED_UNDER_AGE_MESSAGE = "Falsified or under age"
ADDRESS_ERROR_MESSAGE = "Incoherence: error with address"
HOLDER_ERROR_MESSAGE = "Incoherence: error with holder"
NAME_ERROR_MESSAGE = "Incoherence: error with name"
REVERSE_NAME_FIRST_NAME_MESSAGE = "Incoherence: name and first name reversed"
OTHER_ERROR_MESSAGE = "Incoherence: other error"
NO_BANK_ID_MESSAGE = "Not a bank ID"
NO_ADDRESS_PROOF_MESSAGE = "Not a proof of address"
NOT_AN_ID_MESSAGE = "Not an ID"
ONE_SIDE_MISSING_MESSAGE = "One side missing"
SUPLIER_OUTSIDE_PERIMETER_MESSAGE = "Supplier outside of perimeter"
UNREADABLE_MESSAGE = "Unreadable or cut"
VALID_AND_CERTIFIED_MESSAGE = "Valid and certified"
VALID_NOT_CERTIFIED_MESSAGE = "Valid but not certifiable"
HOLDER_FIRST_NAME_NOT_VERIFIED_MESSAGE = "Verified except holder’s first name"
WAITING_MESSAGE = "Waiting"


HIPAY_NOTIFICATION_MESSAGES_TRANSLATION = {
    BANK_OUTSIDE_PERIMETER_MESSAGE: _(BANK_OUTSIDE_PERIMETER_MESSAGE),
    EXPIRED_MESSAGE: _(EXPIRED_MESSAGE),
    FALSIFIED_MESSAGE: _(FALSIFIED_MESSAGE),
    FALSIFIED_UNDER_AGE_MESSAGE: _(FALSIFIED_UNDER_AGE_MESSAGE),
    ADDRESS_ERROR_MESSAGE: _(ADDRESS_ERROR_MESSAGE),
    HOLDER_ERROR_MESSAGE: _(HOLDER_ERROR_MESSAGE),
    NAME_ERROR_MESSAGE: _(NAME_ERROR_MESSAGE),
    REVERSE_NAME_FIRST_NAME_MESSAGE: _(REVERSE_NAME_FIRST_NAME_MESSAGE),
    OTHER_ERROR_MESSAGE: _(OTHER_ERROR_MESSAGE),
    NO_BANK_ID_MESSAGE: _(NO_BANK_ID_MESSAGE),
    NO_ADDRESS_PROOF_MESSAGE: _(NO_ADDRESS_PROOF_MESSAGE),
    NOT_AN_ID_MESSAGE: _(NOT_AN_ID_MESSAGE),
    ONE_SIDE_MISSING_MESSAGE: _(ONE_SIDE_MISSING_MESSAGE),
    SUPLIER_OUTSIDE_PERIMETER_MESSAGE: _(SUPLIER_OUTSIDE_PERIMETER_MESSAGE),
    UNREADABLE_MESSAGE: _(UNREADABLE_MESSAGE),
    VALID_AND_CERTIFIED_MESSAGE: _(VALID_AND_CERTIFIED_MESSAGE),
    VALID_NOT_CERTIFIED_MESSAGE: _(VALID_NOT_CERTIFIED_MESSAGE),
    HOLDER_FIRST_NAME_NOT_VERIFIED_MESSAGE: _(HOLDER_FIRST_NAME_NOT_VERIFIED_MESSAGE),
    WAITING_MESSAGE: _(WAITING_MESSAGE),
}
HIPAY_ACTION_VALIDATE = "validate"
HIPAY_ACTION_GET_STATUS = "payment_get_status"
HIPAY_ACTION_CAPTURE = "capture"
HIPAY_ACTION_REFUND = "refund"
HIPAY_ACTION_GET_SETTLEMENT = "get_settlement"
HIPAY_ACTION_CREATE_WALLET = "create_wallet"
HIPAY_ACTION_GET_WALLET = "get_wallet"
HIPAY_ACTION_SEND_KYC = "send_kyc"
HIPAY_ACTION_UPDATE_KYC = "get_kyc_status"
HIPAY_ACTION_SET_BANK_ACCOUT = "set_bank_account"
HIPAY_ACTION_GET_ACCOUNT = "get_bank_account"
HIPAY_ACTION_ASK_TRANSFER = "transfer_money"
HIPAY_ACTION_GET_USER_BALANCE = "get_balance"
HIPAY_ACTION_GET_USER_EMAIL_AVAILABILITY = "get_user_email_availability"
HIPAY_ACTION_GET_LOCALE_CODES = "get_locale_codes"
HIPAY_ACTION_ASK_WITHDRAW = "withdraw"
HIPAY_ACTION_GET_WALLET_INFO = "get_wallet_info"

HIPAY_ACTIONS_CHOICES = [
    (HIPAY_ACTION_VALIDATE, _("Payment authorization using token")),
    (HIPAY_ACTION_GET_STATUS, _("Payment status checking")),
    (HIPAY_ACTION_CAPTURE, _("Capture request")),
    (HIPAY_ACTION_REFUND, _("Refund request")),
    (HIPAY_ACTION_GET_SETTLEMENT, _("Get settlement")),
    (HIPAY_ACTION_CREATE_WALLET, _("Create wallet")),
    (HIPAY_ACTION_GET_WALLET, _("Get wallet")),
    (HIPAY_ACTION_SEND_KYC, _("Send KYC")),
    (HIPAY_ACTION_UPDATE_KYC, _("Get KYC status")),
    (HIPAY_ACTION_SET_BANK_ACCOUT, _("Set bank account")),
    (HIPAY_ACTION_GET_ACCOUNT, _("Get bank account")),
    (HIPAY_ACTION_ASK_TRANSFER, _("Transfer")),
    (HIPAY_ACTION_ASK_WITHDRAW, _("Withdraw")),
    (HIPAY_ACTION_GET_USER_BALANCE, _("Get user balance")),
    (HIPAY_ACTION_GET_USER_EMAIL_AVAILABILITY, _("Get user email availability")),
    (HIPAY_ACTION_GET_LOCALE_CODES, _("Get locale codes")),
    (HIPAY_ACTION_GET_WALLET_INFO, _("Get wallet info")),
]


# Hipay Transactions statuses
# Reference:
# https://developer.hipay.com/payment-fundamentals/essentials/transaction-status

TRANSACTION_CANCELLED = "115"
TRANSACTION_CAPTURE_REQUESTED = "117"
TRANSACTION_CAPTURED = "118"
TRANSACTION_REFUND_REQUESTED = "124"
TRANSACTION_REFUNDED = "125"
TRANSACTION_PARTIALLY_REFUNDED = "126"
TRANSACTION_CAPTURE_REFUSED = "173"
