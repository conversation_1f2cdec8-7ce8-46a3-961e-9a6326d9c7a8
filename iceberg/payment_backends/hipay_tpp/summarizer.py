# -*- coding: utf-8 -*-

import logging
from decimal import Decimal

from apps.payment.bases.summarizer import BaseBackendSummarizer
from apps.payment.exceptions import (
    PaymentNotFoundError,
    PaymentSummarizerBackendError,
    PaymentSummarizerUnexpectedBackendFormat,
)
from apps.payment.payment_summary import PaymentSummary
from payment_backends.common.utils import logging_context
from payment_backends.hipay_tpp import references as hipay_refs

logger = logging.getLogger(__name__)


class HipayTppSummarizer(BaseBackendSummarizer):
    backend_name = "hipay_tpp"

    def get_raw_payment_data(self):
        try:
            data = {"cashin": {}, "status": "ok"}
            self.payment_backend.ensure_api(self.payment.application)
            context = {
                "db_logger": self.payment_backend.config.cashin_api.db_logger,
                "action_name": hipay_refs.HIPAY_ACTION_GET_STATUS,
                "payment": self.payment,
            }
            with logging_context(**context):
                status = self.payment_backend.config.cashin_api.get_status(self.payment)
            errors = (
                ("3000001", "Order not found"),
                ("3000002", "Transaction not found"),
            )
            if (status.get("code"), status.get("message")) in errors:
                raise PaymentNotFoundError(
                    reason=status["message"], error_code=status["code"]
                )
            data["cashin"] = status["transaction"]
        except PaymentNotFoundError:  # pylint: disable=try-except-raise
            raise
        except Exception:
            logger.exception("while retrieving raw payment data hipay: ")
            raise PaymentSummarizerBackendError
        return data

    def get_backend_summary(self):
        cashin_data = self.get_raw_payment_data()["cashin"]
        try:
            return PaymentSummary(
                requested_amount=self._get_amount(cashin_data["order"]["amount"]),
                authorized_amount=self._get_amount(cashin_data["authorizedAmount"]),
                collected_amount=self._get_amount(cashin_data["capturedAmount"]),
                refunded_amount=self._get_amount(cashin_data["refundedAmount"]),
                # disputed_amount=None,
                # refundable_amount=None,
                # on_escrow_amount=None,
                # ventilated_amount=None
            )
        except KeyError:
            logger.exception("while extracting amount: ")
            raise PaymentSummarizerUnexpectedBackendFormat

    def _get_amount(self, value):
        return Decimal(value)
