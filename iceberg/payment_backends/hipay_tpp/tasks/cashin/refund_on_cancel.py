from decimal import Decimal

from apps.payment.models import Payment
from celery import shared_task
from payment_backends.common.utils import logging_context
from payment_backends.hipay_tpp.models import HipayTransaction
from payment_backends.hipay_tpp.references import HIPAY_ACTION_REFUND
from payment_backends.hipay_tpp.utils import get_conf_for_application


@shared_task(serializer="json")
def do_refund_on_cancel(
    application_id, transaction_id, payment_id, amount, operation_id
):
    """
    @amount: Amount in decimal string. E.g.: "42.42"
    """
    payment = Payment.objects.get(pk=payment_id)
    config = get_conf_for_application(payment.application)
    transaction = HipayTransaction.objects.get(pk=transaction_id)
    context = {
        "db_logger": config.cashin_api.db_logger,
        "action_name": HIPAY_ACTION_REFUND,
        "payment": payment,
    }

    with logging_context(**context):
        config.cashin_api.refund(
            transaction.transaction_reference, operation_id, Decimal(amount)
        )

    transaction.refresh_status()
