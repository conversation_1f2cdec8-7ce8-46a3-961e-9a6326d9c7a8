from apps.tasking.models import ProcessHipaySettlementAction
from apps.tasking.models.drf import (
    ProcessHipaySettlementAction as DRFProcessHipaySettlementAction,
)
from celery import shared_task
from dateutil.parser import isoparse
from payment_backends.hipay_tpp.actions import process_app_settlements
from payment_backends.hipay_tpp.models import HipayAppConfiguration


@shared_task(serializer="json")
def process_app_settlements_task(application_id, date_from, date_to):
    conf = HipayAppConfiguration.objects.active().get(application=application_id)
    return process_app_settlements(conf, isoparse(date_from), isoparse(date_to))


@shared_task(serializer="json")
def process_hipay_settlement_action_task(
    application_id=None, async_action_id=None, use_drf=False
):
    # TODO: DRF migration -> separating model and Tastypie, need to use the new manager
    if use_drf:
        action = DRFProcessHipaySettlementAction.actions.get_from_id(async_action_id)
    else:
        action = ProcessHipaySettlementAction.get_from_id(async_action_id)
    action.set_or_ensure_in_progress()
    metas = action.get_metas()
    processed_count = 0
    errors = {}
    try:
        conf = HipayAppConfiguration.objects.active().get(
            id=metas["hipay_config_id"], application=application_id
        )
        processed_count, errors = process_app_settlements(
            conf, metas["date_from"], metas["date_to"]
        )
    except Exception as err:
        action.set_failed(err)
        raise
    else:
        if errors:
            action.set_failed({"processed_count": processed_count, "errors": errors})
        else:
            action.set_done({"processed_count": processed_count})
        return action.get_as_dict()
