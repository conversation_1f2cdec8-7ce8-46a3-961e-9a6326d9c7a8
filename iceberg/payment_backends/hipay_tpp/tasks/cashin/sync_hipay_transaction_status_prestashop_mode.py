import logging

from celery import shared_task
from payment_backends.hipay_tpp.models import HipayTransaction

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3, default_retry_delay=60, serializer="json")
def sync_hipay_transaction_status(self, hipay_transaction_id, application_id=None):
    try:
        ht = HipayTransaction.objects.get(id=hipay_transaction_id)

        if not ht.payment or not ht.payment.external_id:
            logger.warning(
                "[HiPay][Sync] Transaction ID %s: Payment or external_id missing.",
                hipay_transaction_id,
            )
            return

        logger.info(
            "[HiPay][Sync] Attempting sync for HiPayTransaction ID: %s",
            hipay_transaction_id,
        )

        api = ht.get_api()

        import payment_backends.hipay_tpp.references as hipay_refs
        from payment_backends.common.utils import logging_context

        with logging_context(
            db_logger=api.db_logger,
            action_name=hipay_refs.HIPAY_ACTION_GET_STATUS,
            payment=ht.payment,
        ):
            status_d = api.get_status(ht.payment)

        if status_d and "transaction" in status_d:
            ht.update_from_request_dict(status_d)
            ht.save()
            logger.info(
                "[HiPay][Sync] Transaction ID %s successfully updated.",
                hipay_transaction_id,
            )
        else:
            logger.warning(
                "[HiPay][Sync] No transaction found in HiPay response for ID %s.",
                hipay_transaction_id,
            )

    except HipayTransaction.DoesNotExist:
        logger.error(
            "[HiPay][Sync] HipayTransaction ID %s does not exist.", hipay_transaction_id
        )
    except Exception as e:
        logger.exception(
            "[HiPay][Sync] Failed to sync HiPayTransaction ID %s: %s",
            hipay_transaction_id,
            str(e),
        )
        raise self.retry(exc=e)
