from datetime import timedelta

from apps.ice_applications.models.application_models import ApplicationWorkflow
from celery import current_app
from django.conf import settings
from django.utils import timezone
from payment_backends.hipay_tpp.models import HipayAppConfiguration

from .settlement import process_app_settlements_task


@current_app.task()
def update_transaction_with_settlements():
    """
    Loop on all active Hipay configurations process all available settlements
    from Hipay API bewtween today 00:00AM and yesterday 00:00AM.
    """
    if getattr(settings, "RUNNING_ENVIRONMENT", "sandbox") != "prod":
        return

    date_to = timezone.now()
    date_from = date_to - timedelta(days=1)
    return [
        process_app_settlements_task.delay(
            application_id=conf.application_id,
            date_from=date_from.isoformat(),
            date_to=date_to.isoformat(),
        ).id
        for conf in HipayAppConfiguration.objects.active().filter(
            application__status=ApplicationWorkflow.ACTIVE
        )
    ]
