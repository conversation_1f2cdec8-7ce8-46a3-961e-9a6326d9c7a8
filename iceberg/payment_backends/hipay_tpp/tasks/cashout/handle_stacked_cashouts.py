# -*- coding: utf-8 -*-
from datetime import datetime
from logging import getLogger

import pytz
from celery import current_app, shared_task
from django.conf import settings
from django.utils import timezone
from payment_backends.hipay_tpp.actions import (
    disengage_and_mail_cashout_module,
    get_pending_cashouts,
    try_to_cashout,
)
from payment_backends.hipay_tpp.models.application_configuration import (
    HipayAppConfiguration,
)

logger = getLogger(__name__)


def is_cashout_day(d: datetime) -> bool:
    # no cash out on weekends
    SATURDAY, SUNDAY = 5, 6
    weeked_day = d.astimezone(pytz.timezone(settings.TIME_ZONE)).weekday()
    return weeked_day not in (SATURDAY, SUNDAY)


@current_app.task()
def handle_stacked_cashouts(ignore_day_check=False):
    if not is_cashout_day(timezone.now()) and not ignore_day_check:
        logger.info("No cashout on weekend because nobody will save you.")
        return False

    hipay_powered_app_ids = list(
        set(
            HipayAppConfiguration.objects.active()
            .cashout_enabled()
            .active_on_application()
            .values_list("application_id", flat=True)
        )
    )
    for application_id in hipay_powered_app_ids:
        handle_stacked_cashouts_for_app.delay(application_id)
    return hipay_powered_app_ids


@shared_task(serializer="json")
def handle_stacked_cashouts_for_app(application_id):
    execution_date = timezone.now()
    cashout_instructions = get_pending_cashouts(application_id, execution_date)
    merchant_to_task_id = {}
    for cashout_index, cashout_instruction in enumerate(cashout_instructions):
        merchant_id = cashout_instruction["merchant_id"]
        merchant_to_task_id[merchant_id] = process_stacked_cashout.apply_async(
            kwargs={
                "cashout_instruction": cashout_instruction,
                "merchant_id": merchant_id,
                "application_id": application_id,
            },
            # space out cashout tasks to go easy on Hipay...
            countdown=15 * cashout_index,
        ).id
    return len(merchant_to_task_id), merchant_to_task_id


@shared_task(serializer="json")
def process_stacked_cashout(
    cashout_instruction: dict, merchant_id: int, application_id: int
) -> bool:
    try:
        return try_to_cashout(cashout_instruction)
    except Exception as e:
        # in case of failure while propagating new transaction-status
        # disengage MP's cashout module
        disengage_and_mail_cashout_module(application_id, e)
        raise
