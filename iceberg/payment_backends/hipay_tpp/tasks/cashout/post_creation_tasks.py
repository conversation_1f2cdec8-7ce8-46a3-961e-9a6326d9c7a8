# -*- coding: utf-8 -*-

import logging

from django.utils.translation import gettext_lazy as _
from payment_backends.hipay_tpp.actions import (
    HipayAccountCreator,
    WalletAccountValidator,
)
from payment_backends.hipay_tpp.exceptions import (
    HipayAccountCreationError,
    HipayInvalidCashoutCredentials,
    HipayNotConfigured,
    PostCreationTaskError,
)
from payment_backends.hipay_tpp.models import (
    HipayWalletAccount,
    get_conf_for_application,
)
from requests import HTTPError

logger = logging.getLogger(__name__)


class HipayPostCreationTasks:
    def __init__(self, account, conf=None):
        self.account = (
            account
            if isinstance(account, HipayWalletAccount)
            else self._get_account(account)
        )
        self.conf = (
            conf if conf is not None else self._get_conf_from_account(self.account)
        )

    def create_hipay_account(self):
        from payment_backends.hipay_tpp.references import ACCOUNT_CREATION_PENDING

        if self.account.synchro_status != ACCOUNT_CREATION_PENDING:
            return self.account.synchro_status, {
                "synchro_status": (
                    f"Can only create account if synchro_status is "
                    f"{ACCOUNT_CREATION_PENDING}."
                )
            }
        try:
            self.account = HipayAccountCreator(self.conf.cashout_api).create_account(
                self.account
            )
        except HipayAccountCreationError as e:
            errors = e.response.get("errors", {})
            if isinstance(errors, dict):
                messages = list(errors.values())
            elif isinstance(errors, list):
                messages = errors
            if not messages:
                messages = [e.response.get("message")]
            self.account.synchro_error = {"__all__": messages}
            self.account.fail()
        else:
            self.account.synchronize()
        return self.account.synchro_status, self.account.synchro_error

    def validation_account(self):
        try:
            is_valid = WalletAccountValidator(self.conf).validate(
                self.account.user_account_id, self.account.user_space_id
            )
        except HipayInvalidCashoutCredentials as e:
            self.account.synchro_error = {"__all__": e.messages}
            self.account.fail()
        except HTTPError as e:
            self.account.synchro_error = {"__all__": [str(e)]}
            self.account.fail()
        else:
            if not is_valid:
                messages = [
                    _(
                        "Hipay account info provided "
                        "do not refer to any account on Hipay!"
                    )
                ]
                self.account.synchro_error = {"__all__": messages}
                self.account.fail()
            elif self.account.synchronize.is_available():
                self.account.synchronize()

    def _get_account(self, account_id):
        try:
            return HipayWalletAccount.objects.get(id=account_id)
        except HipayWalletAccount.DoesNotExist:
            msg = _(
                'HipayWalletAccount with id "{account_id}" does not exists, '
                "can't create account on Hipay"
            ).format(account_id=account_id)
            logger.exception(msg)
            raise PostCreationTaskError()

    def _get_conf_from_account(self, account):
        try:
            return get_conf_for_application(account.application)
        except HipayNotConfigured as e:
            account.synchro_error = {"__all__": e.messages}
            account.fail()
            raise PostCreationTaskError()
