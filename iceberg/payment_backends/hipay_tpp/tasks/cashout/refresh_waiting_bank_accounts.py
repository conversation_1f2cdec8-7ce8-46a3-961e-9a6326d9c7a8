from logging import getLogger

from celery import current_app
from payment_backends.hipay_tpp.models import (
    HIPAY_BANK_INFOS_STATUSES_WAITING,
    HipayBankAccount,
)

logger = getLogger(__name__)


@current_app.task()
def refresh_waiting_hipay_bank_accounts():
    """
    Sync bank info registration status with hipay
    """
    logger.info("refresh_waiting_hipay_bank_accounts starts")

    accounts = HipayBankAccount.objects.filter(
        synchro_status=HIPAY_BANK_INFOS_STATUSES_WAITING
    )

    logger.info("%d waiting accounts to refresh", accounts.count())
    for hipay_bank_account in accounts:
        try:
            hipay_bank_account.actions.refresh()
        except Exception as e:
            logger.exception(e)

    logger.info(
        "refresh_waiting_hipay_bank_accounts ended: %d bank account still waiting",
        accounts.count(),
    )
