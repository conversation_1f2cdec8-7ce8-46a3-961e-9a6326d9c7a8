from apps.ice_applications.app_conf_settings import EnableKycV2
from apps.kyc.models import KycInformation, MerchantIdentityDocument
from apps.stores.models import MerchantBankAccount
from celery import shared_task
from payment_backends.hipay_tpp.models import HipayBankAccount


@shared_task(serializer="json")
def select_bank_account(
    hipay_bank_account_id, store_bank_account_id, identity_document_id, application_id
):
    bank_account = HipayBankAccount.objects.get(id=hipay_bank_account_id)
    application = bank_account.application or bank_account.merchant.application
    use_v2 = application.get_setting(EnableKycV2)
    if use_v2:
        document = KycInformation.objects.get(id=identity_document_id)
    else:
        document = MerchantIdentityDocument.objects.get(id=identity_document_id)

    bank_account.actions.select(
        MerchantBankAccount.objects.get(id=store_bank_account_id), document
    )
