from logging import getLogger

from celery import shared_task

from .post_creation_tasks import HipayPostCreationTasks, PostCreationTaskError

logger = getLogger(__name__)


@shared_task(serializer="json")
def validate_hipay_account(account_id, application_id):
    try:
        HipayPostCreationTasks(account_id).validation_account()
    except PostCreationTaskError:
        msg = "HipayWalletAccount id {} does not exist on Hipay API"
        logger.warning(msg.format(account_id))
