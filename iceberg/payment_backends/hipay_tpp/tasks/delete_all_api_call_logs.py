from celery import current_app
from payment_backends.common.tasks import delete_old_api_rest_log
from payment_backends.hipay_tpp.models import HipayApiCallLog


@current_app.task()
def delete_old_hipay_api_call_logs(age_in_days=6 * 30, dry_run=False, chunk_size=500):
    """Periodically delete old HipayApiCallLog logs."""
    return delete_old_api_rest_log(HipayApiCallLog, age_in_days, dry_run, chunk_size)
