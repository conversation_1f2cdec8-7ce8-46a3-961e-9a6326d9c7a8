# -*- coding: utf-8 -*-
from logging import getLogger

from celery import current_app
from django.conf import settings
from payment_backends.hipay_tpp.models import HipayAppConfiguration

logger = getLogger(__name__)
SATURDAY, SUNDAY = 5, 6


@current_app.task()
def refresh_kyc_states():
    """Refresh KYC document statuses everyday at 5:00AM (server time)"""
    configs = HipayAppConfiguration.objects.filter(active=True, cashout_enabled=True)
    for config in configs:
        if config.using_testing_api:
            continue

        try:
            config.kyc_api.resync_doc_statuses()
        except Exception:
            if settings.RAISE_SILENT_ERRORS:
                raise
            logger.exception(
                "Failed resyncing KYC statuses for app %s", config.application_id
            )
