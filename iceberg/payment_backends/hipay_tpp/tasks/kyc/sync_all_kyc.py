# -*- coding: utf-8 -*-

from celery import shared_task
from django.core.exceptions import ValidationError
from payment_backends.hipay_tpp.kyc import build_backend_from_merchant
from payment_backends.hipay_tpp.utils import get_documents, get_merchant


@shared_task(serializer="json")
def sync_all_kyc(merchant_id, application_id):
    merchant = get_merchant(merchant_id)
    documents = get_documents(merchant)
    backend = build_backend_from_merchant(merchant)
    result = {}
    for document in documents:
        try:
            can_publish, reason = backend.can_publish(document)
            if can_publish:
                result[document.id] = backend.publish(document)
            else:
                result[document.id] = reason
        except ValidationError as err:
            result[document.id] = str(err)

    return result
