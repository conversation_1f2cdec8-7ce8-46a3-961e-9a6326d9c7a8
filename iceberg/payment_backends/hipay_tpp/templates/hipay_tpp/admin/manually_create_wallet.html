{% extends "admin/base_site.html" %}

{% block content %}
    <form action="" method="post">
        {% csrf_token %}
        <input type="hidden" name="action" value="{{ form.action_name }}">
        <input type="hidden" name="do_action" value="yes">
        <div>
            {% if form.application.value %}
                Application: {{ form.application_name }}
                ({{ form.application.value }})
            {% endif %}
            {% if form.merchant.value %}
                Merchant: {{ form.merchant_name }}
                ({{ form.merchant_property.name }})
            {% endif %}
        </div>
        <div>

            {{ form.as_p }}
            <input type="submit" class="default" style="float: none" value="Next">

        </div>

    </form>
{% endblock %}