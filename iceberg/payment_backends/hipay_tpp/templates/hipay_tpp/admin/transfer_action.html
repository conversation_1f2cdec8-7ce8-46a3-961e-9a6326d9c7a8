{% extends "admin/base_site.html" %}

{% block content %}
    <form action="" method="post">

        {% csrf_token %}
        <input type="hidden" name="action" value="{{ form.action_name }}">
        <input type="hidden" name="do_action" value="yes">

        <h2>{{ form.title }}</h2>

        <div>

            Amount: {{ form.amount }}
            {{ form.amount.errors }}

            {{ form.currency }}
            {{ form.currency.errors }}

            Label (optionnal): {{ form.label }}

            <input type="submit" class="default" style="float: none" value="Transfer">

        </div>

        <h2>Transfer will apply on the following wallets</h2>
        <ul>
            {% for object in objects %}
                <li>
                    <a href="{{ object.pk }}/">
                        {{ object.pk }}
                    </a> - {{ object.owner_display_name }}
                    <input type="hidden" name="_selected_action" value="{{ object.pk }}">
                </li>
            {% endfor %}
        </ul>

    </form>
{% endblock %}