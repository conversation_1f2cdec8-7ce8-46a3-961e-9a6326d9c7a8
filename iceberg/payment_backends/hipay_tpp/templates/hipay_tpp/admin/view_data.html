{% extends "admin/base_site.html" %}
{% load i18n %}

{% block content %}
    <form action="" method="post">

        {% csrf_token %}
        <input type="hidden" name="action" value="{{ form.action_name }}">
        <input type="hidden" name="do_action" value="yes">

        <h2>{{ form.title }}</h2>

        <div>
            <table>
                <tr>
                    <td>Is identified: </td>
                    <td>{{ form.is_identified.value }}</td>
                </tr>
                <tr>
                    <td>Current balance: </td>
                    <td>{{ form.current_balance.value }}</td>
                </tr>
                <tr>
                    <td>Withdrawable amount: </td>
                    <td>{{ form.withdrawable_amount.value }}</td>
                </tr>
                <tr>
                    <td>Bank account selected: </td>
                    <td>{{ form.bank_account_selected.value }}</td>
                </tr>
                <tr>
                    <td>Is cashout possible: </td>
                    <td>{{ form.is_cashout_possible.value }}</td>
                </tr>
                <tr>
                    <td>Callback salt: </td>
                    <td>{{ form.callback_salt.value }}</td>
                </tr>
                <tr>
                    <td>Callback url: </td>
                    <td>{{ form.callback_url.value }}</td>
                </tr>
                <tr>
                    <td>Raw Data: </td>
                    <td>{{ form.raw_data.value }}</td>
                </tr>
            </table>
        <input type="submit" value="{% translate "Back" %}">
        </div>

        {% for object in objects %}
                <input type="hidden" name="_selected_action" value="{{ object.pk }}">
        {% endfor %}

    </form>
{% endblock %}
