{% extends "admin/base_site.html" %}

{% block content %}

    <form action="" method="post">
        <h2>Withdraw will apply on the following wallet</h2>
        <ul>
            <table>
                <th>
                    <td>Wallet ID</td>
                    <td>Wallet owner dispaly name</td>
                    <td>wallet withdrawable amount</td>
                    <td>Withdrawal bank account</td>
                </th>
            {% for object in objects %}
                <tr>
                    <td></td><!-- empty for alignment -->
                    <td>
                        <a href="{{ object.pk }}/">
                            {{ object.pk }}
                        </a>
                    </td>
                    <td>
                        {{ object.owner_display_name }}
                    </td>
                    <td>
                        {{ object.withdrawable_amount }}
                    </td>
                    <td>
                        {{ object.bank_account_selected }}
                    </td>
                </tr>

                <input type="hidden" name="_selected_action" value="{{ object.pk }}">
            {% endfor %}
            </table>
        </ul>

        {% csrf_token %}
        <input type="hidden" name="action" value="{{ form.action_name }}">
        <input type="hidden" name="do_action" value="yes">

        <h2>{{ form.title }}</h2>

        <div>

            <p>
                Amount: {{ form.amount }}
                {{ form.amount.errors }}

                {{ form.currency }}
                {{ form.currency.errors }}
            </p>

            <p>
                Label (optionnal): {{ form.label }}
                <br/>
                <span style="color:grey; font-size:0.8em">{{form.label.help_text}}</span>

            </p>

            <input type="submit" class="default" style="float: none" value="Withdraw">

        </div>

    </form>
{% endblock %}