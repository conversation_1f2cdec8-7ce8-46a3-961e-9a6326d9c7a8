from decimal import Decimal


def get_transaction_authorized_completed_response(
    order_id: str = "testqwertyuiop",
    transaction_reference: str = "11111111111",
    amount: Decimal = Decimal("100"),
):
    return {
        "transaction": {
            "state": "completed",
            "reason": "",
            "forwardUrl": "",
            "test": "false",
            "mid": "11111111111",
            "attemptId": "1",
            "authorizationCode": "917663",
            "transactionReference": transaction_reference,
            "dateCreated": "2021-04-12T16:03:48+0000",
            "dateUpdated": "2021-04-12T16:05:25+0000",
            "dateAuthorized": "2021-04-12T16:05:25+0000",
            "status": "116",
            "message": "Authorized",
            "authorizedAmount": str(amount),
            "capturedAmount": "0.00",
            "refundedAmount": "0.00",
            "decimals": "2",
            "currency": "EUR",
            "ipAddress": "*******",
            "ipCountry": "FR",
            "deviceId": "",
            "cdata1": "",
            "cdata2": "",
            "cdata3": "",
            "cdata4": "",
            "cdata5": "",
            "cdata6": "",
            "cdata7": "",
            "cdata8": "",
            "cdata9": "",
            "cdata10": "",
            "avsResult": "",
            "cvcResult": "",
            "eci": "7",
            "paymentProduct": "visa",
            "paymentMethod": {
                "token": "XXXXXXX",
                "cardId": "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
                "brand": "VISA",
                "pan": "111111******2222",
                "cardHolder": "JOHN DOE",
                "cardExpiryMonth": "01",
                "cardExpiryYear": "2025",
                "issuer": "LA BANQUE POSTALE",
                "country": "FR",
            },
            "threeDSecure": {
                "eci": "5",
                "enrollmentStatus": "Y",
                "enrollmentMessage": "Authentication Available",
                "authenticationStatus": "Y",
                "authenticationMessage": "Authentication Successful",
                "authenticationToken": "",
                "xid": "",
            },
            "fraudScreening": {"scoring": "0", "result": "ACCEPTED", "review": ""},
            "order": {
                "id": order_id,
                "dateCreated": "2021-04-12T16:02:59+0000",
                "gender": "U",
                "firstname": "",
                "lastname": "",
                "streetAddress": "",
                "locality": "",
                "postalCode": "",
                "country": "",
                "attempts": "1",
                "amount": str(amount),
                "shipping": "0.00",
                "tax": "0.00",
                "decimals": "2",
                "currency": "EUR",
                "customerId": "",
                "language": "fr_FR",
                "msisdn": "",
                "phone": "",
                "phoneOperator": "",
                "shippingAddress": {
                    "firstname": "",
                    "lastname": "",
                    "streetAddress": "",
                    "locality": "",
                    "postalCode": "",
                    "country": "",
                },
                "email": "<EMAIL>",
            },
            "debitAgreement": {"id": "", "status": ""},
        }
    }


def get_transaction_captured_response(amount: Decimal = Decimal("100")):
    return {
        "operation": "capture",
        "test": "false",
        "mid": "00000000000",
        "authorizationCode": "000000",
        "transactionReference": "000000000000",
        "dateCreated": "2021-05-29T06:38:39+0000",
        "dateUpdated": "2021-05-29T06:45:33+0000",
        "dateAuthorized": "2021-05-29T06:38:44+0000",
        "status": "118",
        "message": "Captured",
        "authorizedAmount": str(amount),
        "capturedAmount": str(amount),
        "refundedAmount": "0.00",
        "decimals": "2",
        "currency": "EUR",
    }


def get_transaction_capture_requested_response(amount: Decimal = Decimal("100")):
    return {
        "operation": "capture",
        "test": "false",
        "mid": "00000000000",
        "authorizationCode": "000000",
        "transactionReference": "000000000000",
        "dateCreated": "2021-05-29T06:38:39+0000",
        "dateUpdated": "2021-05-29T06:45:33+0000",
        "dateAuthorized": "2021-05-29T06:38:44+0000",
        "status": "117",
        "message": "Capture Requested",
        "authorizedAmount": str(amount),
        "capturedAmount": str(amount),
        "refundedAmount": "0.00",
        "decimals": "2",
        "currency": "EUR",
    }


def get_transaction_capture_declined_response(amount: Decimal = Decimal("100")):
    return {
        "operation": "capture",
        "test": "false",
        "mid": "00000000000",
        "authorizationCode": "00000000000000000",
        "transactionReference": "000000000000",
        "dateCreated": "2021-06-08T07:49:22+0000",
        "dateUpdated": "2021-06-08T08:01:34+0000",
        "dateAuthorized": "2021-06-08T07:50:15+0000",
        "status": "173",
        "message": "Capture Refused",
        "authorizedAmount": str(amount),
        "capturedAmount": "0.00",
        "refundedAmount": "0.00",
        "decimals": "2",
        "currency": "EUR",
    }


def get_transaction_capture_amount_limit_exceeded_response():
    return {"code": "3020109", "message": "Amount Limit Exceeded", "description": ""}


def get_transaction_400_response_with_description_response():
    return {
        "code": "1010205",
        "message": "Invalid Parameter",
        "description": "The 'amount' parameter is expected to be greater than 0.005",
    }


def get_transaction_refund_requested_response(amount: Decimal = Decimal("100")):
    return {
        "operation": "refund",
        "test": "false",
        "mid": "00000000000",
        "authorizationCode": "000000",
        "transactionReference": "111111111111",
        "dateCreated": "2021-04-29T21:48:51+0000",
        "dateUpdated": "2021-05-21T00:55:20+0000",
        "dateAuthorized": "2021-04-29T21:49:35+0000",
        "status": "124",
        "message": "Refund Requested",
        "authorizedAmount": str(amount),
        "capturedAmount": str(amount),
        "refundedAmount": str(amount),
        "decimals": "2",
        "currency": "EUR",
    }


def get_transaction_refunded_response(amount: Decimal = Decimal("100")):
    return {
        "operation": "refund",
        "test": "false",
        "mid": "00000000000",
        "authorizationCode": "000000",
        "transactionReference": "111111111111",
        "dateCreated": "2021-04-05T12:29:06+0000",
        "dateUpdated": "2021-05-21T00:58:03+0000",
        "dateAuthorized": "2021-04-05T12:29:11+0000",
        "status": "125",
        "message": "Refunded",
        "authorizedAmount": str(amount),
        "capturedAmount": str(amount),
        "refundedAmount": str(amount),
        "decimals": "2",
        "currency": "EUR",
    }


def get_transaction_partially_refunded_response(
    captured_amount: Decimal = Decimal("100"),
    refunded_amount: Decimal = Decimal("50"),
):
    return {
        "operation": "refund",
        "test": "false",
        "mid": "00000000000",
        "authorizationCode": "000000",
        "transactionReference": "111111111111",
        "dateCreated": "2021-05-02T12:11:02+0000",
        "dateUpdated": "2021-05-21T00:55:46+0000",
        "dateAuthorized": "2021-05-02T12:11:15+0000",
        "status": "126",
        "message": "Partially Refunded",
        "authorizedAmount": str(captured_amount),
        "capturedAmount": str(captured_amount),
        "refundedAmount": str(refunded_amount),
        "decimals": "2",
        "currency": "EUR",
    }


def get_transaction_refund_refused_response(amount: Decimal = Decimal("100")):
    return {
        "operation": "refund",
        "test": "false",
        "mid": "00000000000",
        "authorizationCode": "00000000000000000",
        "transactionReference": "111111111111",
        "dateCreated": "2021-02-18T10:42:11+0000",
        "dateUpdated": "2021-05-21T01:16:18+0000",
        "dateAuthorized": "2021-02-18T10:44:13+0000",
        "status": "165",
        "message": "Refund Refused",
        "authorizedAmount": str(amount),
        "capturedAmount": str(amount),
        "refundedAmount": "0.00",
        "decimals": "2",
        "currency": "EUR",
    }


def get_transaction_authorized_completed_minimal_response(
    order_id: str,
    transaction_reference: str,
    amount: Decimal = Decimal("100.00"),
):
    return {
        "transaction": {
            "status": "116",
            "state": "completed",
            "authorizedAmount": str(amount),
            "capturedAmount": "0.00",
            "refundedAmount": "0.00",
            "reason": "",
            "test": "true",
            "order": {"id": order_id},
            "attemptId": "1",
            "ipAddress": "127.0.0.1",
            "ipCountry": "FR",
            "transactionReference": transaction_reference,
            "dateCreated": "2025-01-01T12:00:00+0000",
            "dateUpdated": "2025-01-01T12:00:00+0000",
            "dateAuthorized": "2025-01-01T12:00:00+0000",
            "currency": "EUR",
            "paymentProduct": "visa",
            "paymentMethod": {
                "brand": "VISA",
                "cardExpiryMonth": "12",
                "cardExpiryYear": "2025",
                "issuer": "BANK",
                "country": "FR",
                "token": "mocked-token",
                "cardHolder": "MOCK HOLDER",
                "pan": "411111******1111",
            },
            "decimals": "2",
        }
    }
