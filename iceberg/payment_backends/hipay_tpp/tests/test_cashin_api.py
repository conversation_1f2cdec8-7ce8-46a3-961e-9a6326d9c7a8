from unittest.mock import patch

from apps.testing.live_setting_override import live_setting_override
from ims.tests import BaseTestCase
from payment_backends.hipay_tpp.hipay_api import HipayCashinApi
from payment_backends.hipay_tpp.tests import TestHipayMixin


class CashinApiTestCase(BaseTestCase, TestHipayMixin):
    def get_api(self, staging):
        # sandbox and production params should not impact our get_url tests
        application = self.create_app(sandbox=False, production=False)
        hipay_config = self.create_hipay_config(application=application, save=False)
        hipay_config.staging = staging
        return HipayCashinApi(hipay_config)

    @live_setting_override("ENABLE_HIPAY_HOSTED_PAGE_V2", True)
    def test_get_url_when_hosted_page_v2_on_staging_env(self):
        api = self.get_api(staging=True)
        self.assertEqual(
            api.get_url("hpayment"), "https://stage-api.hipay.com/v1/hpayment"
        )

        # when the request doesn't concern hpayment, the endpoint V1 should be used
        self.assertEqual(
            api.get_url("transaction"),
            "https://stage-secure-gateway.hipay-tpp.com/rest/v1/transaction",
        )

    @live_setting_override("ENABLE_HIPAY_HOSTED_PAGE_V2", True)
    def test_get_url_when_hosted_page_v2_on_prod_env(self):
        api = self.get_api(staging=False)
        self.assertEqual(api.get_url("hpayment"), "https://api.hipay.com/v1/hpayment")

        # when the request doesn't concern hpayment, the endpoint V1 should be used
        self.assertEqual(
            api.get_url("transaction"),
            "https://secure-gateway.hipay-tpp.com/rest/v1/transaction",
        )

    @live_setting_override("ENABLE_HIPAY_HOSTED_PAGE_V2", False)
    def test_get_url_when_hosted_page_v1_on_staging_env(self):
        api = self.get_api(staging=True)
        self.assertEqual(
            api.get_url("hpayment"),
            "https://stage-secure-gateway.hipay-tpp.com/rest/v1/hpayment",
        )

        # when the request doesn't concern hpayment, the endpoint V1 should be used
        self.assertEqual(
            api.get_url("transaction"),
            "https://stage-secure-gateway.hipay-tpp.com/rest/v1/transaction",
        )

    @live_setting_override("ENABLE_HIPAY_HOSTED_PAGE_V2", False)
    def test_get_url_when_hosted_page_v1_on_prod_env(self):
        api = self.get_api(staging=False)
        self.assertEqual(
            api.get_url("hpayment"),
            "https://secure-gateway.hipay-tpp.com/rest/v1/hpayment",
        )

        # when the request doesn't concern hpayment, the endpoint V1 should be used
        self.assertEqual(
            api.get_url("transaction"),
            "https://secure-gateway.hipay-tpp.com/rest/v1/transaction",
        )

    @live_setting_override("ENABLE_HIPAY_HOSTED_PAGE_V2", False)
    def test_get_request_params_for_order_with_hosted_page_v1(self):
        api = self.get_api(staging=False)
        with patch(
            "payment_backends.hipay_tpp.models.application_configuration."
            "HipayAppConfiguration.get_css_url",
            lambda _: {"css": "https://test.com/file.css"},
        ):
            params = api.get_request_prams_for_order({})
        self.assertTrue("css" in params)
        self.assertTrue("payment_product_category_list" in params)

    @live_setting_override("ENABLE_HIPAY_HOSTED_PAGE_V2", True)
    def test_get_request_params_for_order_with_hosted_page_v2(self):
        api = self.get_api(staging=False)
        with patch(
            "payment_backends.hipay_tpp.models.application_configuration."
            "HipayAppConfiguration.get_css_url",
            lambda _: {"css": "https://test.com/file.css"},
        ):
            params = api.get_request_prams_for_order({})
        self.assertTrue("css" not in params)
        self.assertTrue("payment_product_category_list" not in params)
