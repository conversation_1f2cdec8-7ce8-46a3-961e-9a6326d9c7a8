import functools
import json
from decimal import Decimal

from apps.ice_applications.tests import ApplicationTestSetupMixin
from apps.testing.factories import (
    ApplicationBankAccountFactory,
    HipayAppConfigurationFactory,
    HipayOperatorWalletAccountFactory,
    MerchantAddressFactory,
    MerchantFactory,
)
from ims.tests import BaseResourceTestCase, BaseTestCase
from mock import patch
from payment_backends.hipay_tpp.actions import check_wallet_after_transfer_or_raises
from payment_backends.hipay_tpp.exceptions import (
    HipayAccountCreationError,
    HipayWalletStillEmptyAfterTransfer,
)
from payment_backends.hipay_tpp.hipay_api import Hipay<PERSON>ashout<PERSON><PERSON>
from payment_backends.hipay_tpp.references import WALLET_STILL_EMPTY_AFTER_TRANSFER
from requests import Response

ACCOUNT_CREATION_SUCCESS = b"""{
  "code": 0,
  "wslogin": "login",
  "wspassword": "password",
  "user_space_id": "42",
  "account_id": "12"
}
"""


ACCOUNT_CREATION_FAILED = b"""{
  "code": 42,
  "message": "Try again"
}
"""

ACCOUNT_CREATION_FAILED_CODE_0 = b"""{
  "code": 0,
  "message": "Try again"
}
"""

ACCOUNT_CREATION_FAILED_NO_CODE = b"""{
  "message": "Try again"
}
"""


ACCOUNT_CREATION_FAILED_NO_CODE_NO_MESSAGE = b"""{
}
"""


request_path = (
    "payment_backends.hipay_tpp.hipay_api.rest_api_interface.logged_tls_request"
)
request_warning_path = (
    "payment_backends.hipay_tpp.hipay_api.rest_api_interface.RestAPIInterface"
    "._log_api_call_error_during_tests"
)


@patch(request_path)
@patch(request_warning_path, lambda *_: None)
@patch("payment_backends.common.utils.RestLogsBuilder.log_request", lambda *_: None)
class CashoutTestCase(BaseTestCase):
    def setUp(self):
        self.config = HipayAppConfigurationFactory()
        self.cashout = HipayCashoutApi(self.config)

    def configure_response(self, content, status_code):
        response = Response()
        response.status_code = status_code
        response._content = content
        return response

    def test_should_raise_validation_error_on_hipay_error(self, mock):
        mock.return_value = self.configure_response(ACCOUNT_CREATION_FAILED, 200)
        merchant = MerchantFactory.create()
        MerchantAddressFactory.create(merchant=merchant)

        with self.assertRaises(HipayAccountCreationError):
            self.cashout.create_wallet_account({})

    def test_should_raise_validation_error_on_hipay_error_without_code(self, mock):
        mock.return_value = self.configure_response(
            ACCOUNT_CREATION_FAILED_NO_CODE, 200
        )

        with self.assertRaises(HipayAccountCreationError):
            self.cashout.create_wallet_account({})

    def test_should_raise_validation_error_on_hipay_error_without_code_nor_message(
        self, mock
    ):
        mock.return_value = self.configure_response(
            ACCOUNT_CREATION_FAILED_NO_CODE_NO_MESSAGE, 200
        )

        with self.assertRaises(HipayAccountCreationError):
            self.cashout.create_wallet_account({})

    def test_should_raise_validation_error_on_hipay_http_error(self, mock):
        mock.return_value = self.configure_response(ACCOUNT_CREATION_FAILED_CODE_0, 400)

        with self.assertRaises(HipayAccountCreationError):
            self.cashout.create_wallet_account({})

    def test_should_return_json_decoded_response_body_on_success(self, mock):
        mock.return_value = self.configure_response(ACCOUNT_CREATION_SUCCESS, 201)
        expected_hipay_account = json.loads(ACCOUNT_CREATION_SUCCESS)
        merchant = MerchantFactory.create()
        MerchantAddressFactory.create(merchant=merchant)

        hipay_account = self.cashout.create_wallet_account({})

        self.assertDictEqual(hipay_account, expected_hipay_account)


def mock_withdrawal(fct):
    def wrapper(*args, **kwargs):
        mocked_withdraw_wallet_api_call = (
            "payment_backends.hipay_tpp.hipay_api.cashout.HipayCashoutApi.withdraw"
        )
        with patch(mocked_withdraw_wallet_api_call) as withdrawal:
            result = {
                "code": 0,
                "message": (
                    "Withdrawal request for account ******** with 21955.81 "
                    "amount has been sent !"
                ),
                "transaction_public_id": "5E2EB76EC7AD4197",
            }
            withdrawal.return_value = result

            return fct(*args, **kwargs)

    return functools.wraps(fct)(wrapper)


def mock_transfer(fct):
    def wrapper(*args, **kwargs):
        mocked_credit_wallet_api_call = (
            "payment_backends.hipay_tpp.hipay_api.cashout.HipayCashoutApi"
            ".transfer_amount"
        )
        with patch(mocked_credit_wallet_api_call) as credit_wallet:
            result = {
                "code": 0,
                "message": {
                    "description": "Direct transfer successfully posted.",
                },
                "transaction_public_id": "",
            }
            credit_wallet.return_value = result

            return fct(*args, **kwargs)

    return functools.wraps(fct)(wrapper)


def mock_get_balance(fct):
    def wrapper(*args, **kwargs):
        mocked_wallet_balance_call = (
            "payment_backends.hipay_tpp.hipay_api.cashout.HipayCashoutApi"
            ".get_balance_and_withdrawable_amount"
        )
        with patch(mocked_wallet_balance_call) as api_call:
            api_call.return_value = [-1000, 0]

            return fct(*args, **kwargs)

    return functools.wraps(fct)(wrapper)


@patch(
    "payment_backends.hipay_tpp.actions."
    "merchant_cashout.HIPAY_MINIMUM_TRANSFER_WAIT_TIME",
    0,
)
@patch(
    "payment_backends.hipay_tpp.actions.merchant_cashout.randint",
    return_value=1,
)
class CashoutUnitTestCase(BaseTestCase):
    def test_check_wallet_after_transfer_or_raises(self, _mock):
        # Helper class to test the exponential backoff
        class FakeHipayAPI:
            def __init__(self, expected_result):
                self.expected_result = expected_result

            def check_ewallet_is_empty(self, wallet):
                return self.expected_result

        api = FakeHipayAPI(expected_result=False)
        result = check_wallet_after_transfer_or_raises(api, {})
        self.assertEqual(result, None)

        with (
            patch(
                "payment_backends.hipay_tpp.actions."
                "merchant_cashout.HIPAY_MAXIMUM_WAITING_TIME",
                0,
            ),
            self.assertRaises(HipayWalletStillEmptyAfterTransfer) as err,
        ):
            api = FakeHipayAPI(expected_result=True)

            check_wallet_after_transfer_or_raises(api, {})
        self.assertEqual(err.exception.code, WALLET_STILL_EMPTY_AFTER_TRANSFER)


class EndToEndOperatorCashoutTestCase(ApplicationTestSetupMixin, BaseResourceTestCase):
    @mock_get_balance
    @mock_transfer
    @mock_withdrawal
    def test_operator_cashout_action_on_resource(self):
        # Assuming we have a valid config + wallet + app bank account
        account = HipayOperatorWalletAccountFactory()
        app = account.application
        HipayAppConfigurationFactory.create(application=app)
        ApplicationBankAccountFactory.create(application=app)

        open_balance = app.actions.get_current_balance(currency=app.default_currency)
        open_balance.current_balance = Decimal("93361.25")
        open_balance.save()

        # When asking for cashout
        resp = self.api_client.post(
            "/v1/hipay_merchant_account/{}/cashout/".format(account.id),
            data={"amount": open_balance.current_balance - 500},
            authentication=self.get_token(app.contact_user, application=app),
        )

        # then we succeeed
        self.assertHttpOK(resp)
