# -*- coding: utf-8 -*-


from apps.ice_applications.app_conf_settings import EnableKycV2
from apps.testing.factories import (
    ApplicationFactory,
    HipayAppConfigurationFactory,
    HipayWalletAccountFactory,
    IdentityDocumentPartFactory,
    KycDocumentPartFactory,
    MerchantAddressFactory,
    MerchantBankAccountFactory,
)
from django.core.exceptions import ValidationError
from ims.tests import BaseTestCase
from mock import Mock, patch

from ..forms.bank_account_creation import HipayBankAccountCreationForm


class HipayBankAccountCreationFormTestCase(BaseTestCase):
    def test_using_kyc_v1_success(self):
        identity_document = IdentityDocumentPartFactory().document
        application = identity_document.merchant.application
        MerchantAddressFactory(merchant=identity_document.merchant)
        HipayAppConfigurationFactory(application=application)
        store_bank_account = MerchantBankAccountFactory(
            merchant=identity_document.merchant
        )
        form = HipayBankAccountCreationForm(
            {
                "application": application.id,
                "merchant": identity_document.merchant.id,
                "identity_document": identity_document.id,
                "store_bank_account": store_bank_account.id,
            }
        )
        form.is_valid()
        HipayWalletAccountFactory(
            application=identity_document.merchant.application,
            merchant=identity_document.merchant,
        )
        mock_api = (
            "payment_backends.hipay_tpp.hipay_api.cashout.HipayCashoutApi.request"
        )
        with patch(mock_api) as mocked_api:
            mocked_api.return_value = Mock(code=0)
            form.create_infos_and_set_rib()

    def test_using_kyc_v1_failure(self):
        identity_document = IdentityDocumentPartFactory().document
        application = identity_document.merchant.application
        MerchantAddressFactory(merchant=identity_document.merchant)
        HipayAppConfigurationFactory(application=application)
        store_bank_account = MerchantBankAccountFactory(
            merchant=identity_document.merchant
        )
        form = HipayBankAccountCreationForm(
            {
                "application": application.id,
                "merchant": identity_document.merchant.id,
                "identity_document": identity_document.id,
                "store_bank_account": store_bank_account.id,
            }
        )
        form.is_valid()
        HipayWalletAccountFactory(
            application=identity_document.merchant.application,
            merchant=identity_document.merchant,
        )
        mock_api = (
            "payment_backends.hipay_tpp.hipay_api.cashout.HipayCashoutApi.request"
        )
        with patch(mock_api) as mocked_api:
            mocked_api.return_value = Mock(code=1, description="msg")
            with self.assertRaises(ValidationError):
                form.create_infos_and_set_rib()

    def test_using_kyc_v2_success(self):
        application = ApplicationFactory()
        application.set_setting(EnableKycV2, True)
        kyc = KycDocumentPartFactory(application=application).kyc
        HipayAppConfigurationFactory(application=application)
        store_bank_account = MerchantBankAccountFactory(merchant=kyc.merchant)
        form = HipayBankAccountCreationForm(
            {
                "application": application.id,
                "merchant": kyc.merchant.id,
                "kyc_information": kyc.id,
                "store_bank_account": store_bank_account.id,
            }
        )
        form.is_valid()
        HipayWalletAccountFactory(application=application, merchant=kyc.merchant)
        mock_api = (
            "payment_backends.hipay_tpp.hipay_api.cashout.HipayCashoutApi.request"
        )
        with patch(mock_api) as mocked_api:
            mocked_api.return_value = Mock(code=0)
            form.create_infos_and_set_rib()

    def test_using_kyc_v2_failure(self):
        application = ApplicationFactory()
        application.set_setting(EnableKycV2, True)
        kyc = KycDocumentPartFactory(application=application).kyc
        MerchantAddressFactory(merchant=kyc.merchant)
        HipayAppConfigurationFactory(application=application)
        store_bank_account = MerchantBankAccountFactory(merchant=kyc.merchant)
        form = HipayBankAccountCreationForm(
            {
                "application": application.id,
                "merchant": kyc.merchant.id,
                "kyc_information": kyc.id,
                "store_bank_account": store_bank_account.id,
            }
        )
        form.is_valid()
        HipayWalletAccountFactory(application=application, merchant=kyc.merchant)
        mock_api = (
            "payment_backends.hipay_tpp.hipay_api.cashout.HipayCashoutApi.request"
        )
        with patch(mock_api) as mocked_api:
            mocked_api.return_value = Mock(code=1, description="msg")
            with self.assertRaises(ValidationError):
                form.create_infos_and_set_rib()
