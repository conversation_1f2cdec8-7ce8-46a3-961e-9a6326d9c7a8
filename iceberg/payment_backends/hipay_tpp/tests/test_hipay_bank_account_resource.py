# -*- coding: utf-8 -*-


from apps.ice_applications.app_conf_settings import EnableKycV2
from apps.kyc.models import KycInformationWorkflow
from apps.testing.factories import (
    ApplicationFactory,
    HipayAppConfigurationFactory,
    HipayMerchantBankAccountKycV1Factory,
    HipayMerchantBankAccountKycV2Factory,
    HipayWalletAccountFactory,
    KycInformationFactory,
    MerchantBankAccountFactory,
)
from ims.tests import BaseResourceTestCase
from mock import patch
from payment_backends.hipay_tpp.references import HIPAY_BANK_ACCOUNT_DETAILS


class HipayBankAccountResourceTestCase(BaseResourceTestCase):
    WALLET_MOCK_PATH = (
        "payment_backends.hipay_tpp.models.wallet_account."
        "HipayWalletAccount.clean_application_and_merchant"
    )

    def setUp(self, *args, **kwargs):
        self.application = ApplicationFactory()
        self.app_bearer = self.application.create_user(
            self.application.contact_user, True
        ).get_bearer()
        self.application.set_setting(EnableKycV2, True)
        super(HipayBankAccountResourceTestCase, self).setUp(*args, **kwargs)

    def test_get_list_as_app(self):
        self.application.set_setting(EnableKycV2, False)
        ba_1 = HipayMerchantBankAccountKycV1Factory(
            merchant__application=self.application,
            merchant__default_currency=self.application.default_currency,
            merchant__prefered_language=self.application.language,
        )

        self.application.set_setting(EnableKycV2, True)
        ba_2 = HipayMerchantBankAccountKycV2Factory(
            merchant__application=self.application,
            merchant__default_currency=self.application.default_currency,
            merchant__prefered_language=self.application.language,
        )

        resp = self.api_client.get(
            "/v1/hipay_bank_account/", authentication=self.app_bearer
        )

        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 2)
        self.assertEqual(
            set([data["objects"][0]["id"], data["objects"][1]["id"]]),
            set([ba_1.id, ba_2.id]),
        )

    def test_get_detail_as_app(self):
        ba = HipayMerchantBankAccountKycV2Factory(
            merchant__application=self.application,
            merchant__default_currency=self.application.default_currency,
        )

        resp = self.api_client.get(
            "/v1/hipay_bank_account/{}/".format(ba.id), authentication=self.app_bearer
        )

        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["id"], ba.id)

    @patch("payment_backends.hipay_tpp.tasks.select_bank_account.delay")
    def test_select_bank_account_as_app(self, mocked_task):
        hba = HipayMerchantBankAccountKycV2Factory(
            application=self.application,
            merchant__application=self.application,
            merchant__default_currency=self.application.default_currency,
            kyc_information=None,
        )
        bank_account = MerchantBankAccountFactory(merchant=hba.merchant)
        kyc = KycInformationFactory(
            merchant=hba.merchant,
            status=KycInformationWorkflow.ACTIVE,
            kyc_type__external_id=HIPAY_BANK_ACCOUNT_DETAILS,
        )
        with patch(self.WALLET_MOCK_PATH):
            HipayWalletAccountFactory(
                application=kyc.merchant.application, merchant=kyc.merchant
            )
        HipayAppConfigurationFactory(
            application=self.application, currency=self.application.default_currency
        )

        resp = self.api_client.post(
            "/v1/hipay_bank_account/{}/select_bank_account/".format(hba.id),
            data={"account_id": bank_account.id, "document_id": kyc.id},
            authentication=self.app_bearer,
        )

        self.assertHttpOK(resp)
        mocked_task.assert_called_once_with(
            hba.id, bank_account.id, str(kyc.id), self.application.id
        )
