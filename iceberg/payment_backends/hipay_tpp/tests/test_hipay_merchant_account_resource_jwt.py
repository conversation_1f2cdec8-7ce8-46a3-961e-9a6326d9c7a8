from apps.stores.tests import TestMerchantSetupMixin
from apps.testing.factories import (
    HipayAppConfigurationFactory,
    HipayOperatorWalletAccountFactory,
    HipayWalletAccountFactory,
    MerchantAddressFactory,
)
from ims.tests import BaseResourceTestCase
from ims.tests.jwt_tokens import GLO<PERSON>L_TOKENS
from mock import patch
from payment_backends.hipay_tpp import tasks
from payment_backends.hipay_tpp.api import merchant_account_resource
from payment_backends.hipay_tpp.models import HipayWalletAccount
from payment_backends.hipay_tpp.references import HIPAY_ACCOUNT_TYPE_CORPORATION
from payment_backends.hipay_tpp.tests.tests_utils import (
    VALID_USER_INFO_RESPONSE,
    MockedRequestsResponse,
)


@patch.object(HipayWalletAccount, "clean", lambda x: None)
@patch.object(HipayWalletAccount, "is_identified", lambda x: True)
class TestMerchantAccountResource(TestMerchantSetupMixin, BaseResourceTestCase):
    def setUp(self):
        super().setUp()
        self.address = MerchantAddressFactory.create(
            merchant__application__id=1, merchant__id=2
        )
        self.merchant = self.address.merchant

    @patch.object(tasks, "create_hipay_account")
    def test_should_delay_hipay_account_creation_when_create_resource(
        self, mocked_task
    ):
        data = {
            "merchant": "/v1/merchant/{}/".format(self.merchant.id),
            "account_type": HIPAY_ACCOUNT_TYPE_CORPORATION,
        }

        response = self.api_client.post(
            "/v1/hipay_merchant_account/",
            data=data,
            authentication="Bearer {}".format(
                GLOBAL_TOKENS["APPLICATION_1_MERCHANT_2_ADMIN"]["token"]
            ),
        )

        self.assertHttpCreated(response)
        account = HipayWalletAccount.objects.last()
        mocked_task.delay.assert_called_once_with(account.id, account.application_id)

    def test_accept_email_at_creation(self):
        data = {
            "merchant": "/v1/merchant/{}/".format(self.merchant.id),
            "account_type": HIPAY_ACCOUNT_TYPE_CORPORATION,
            "email": "<EMAIL>",
        }

        response = self.api_client.post(
            "/v1/hipay_merchant_account/",
            data=data,
            authentication="Bearer {}".format(
                GLOBAL_TOKENS["APPLICATION_1_MERCHANT_2_ADMIN"]["token"]
            ),
        )

        self.assertHttpCreated(response)
        data = self.deserialize(response)
        self.assertEqual(data["email"], "<EMAIL>")
        account = HipayWalletAccount.objects.last()
        self.assertEqual(account.email, "<EMAIL>")

    @patch.object(merchant_account_resource, "create_hipay_account")
    def test_retry_creation_should_delay_hipay_creation(self, mocked_task):
        account = HipayWalletAccountFactory.create(
            application=self.merchant.application, merchant=self.merchant
        )
        account.fail()
        url = "/v1/hipay_merchant_account/{}/retry_hipay_creation/".format(account.id)

        response = self.api_client.post(
            url,
            authentication="Bearer {}".format(
                GLOBAL_TOKENS["APPLICATION_1_MERCHANT_2_ADMIN"]["token"]
            ),
        )

        self.assertHttpAccepted(response)
        mocked_task.delay.assert_called_once_with(account.id, account.application_id)

    @patch.object(merchant_account_resource, "create_hipay_account")
    def test_multi_retry_creation_should_return_400(self, mocked_task):
        account = HipayWalletAccountFactory.create(
            application=self.merchant.application, merchant=self.merchant
        )
        account.fail()
        url = "/v1/hipay_merchant_account/{}/retry_hipay_creation/".format(account.id)

        response = self.api_client.post(
            url,
            authentication="Bearer {}".format(
                GLOBAL_TOKENS["APPLICATION_1_MERCHANT_2_ADMIN"]["token"]
            ),
        )
        self.assertHttpAccepted(response)
        mocked_task.delay.assert_called_once_with(account.id, account.application_id)

        response = self.api_client.post(
            url,
            authentication="Bearer {}".format(
                GLOBAL_TOKENS["APPLICATION_1_MERCHANT_2_ADMIN"]["token"]
            ),
        )
        self.assertHttpBadRequest(response)
        mocked_task.delay.assert_called_once_with(account.id, account.application_id)

    @patch.object(merchant_account_resource, "create_hipay_account")
    def test_retry_creation_should_return_400_if_creation_not_failed(self, mock):
        account = HipayWalletAccountFactory.create(
            application=self.merchant.application, merchant=self.merchant
        )
        url = "/v1/hipay_merchant_account/{}/retry_hipay_creation/".format(account.id)

        response = self.api_client.post(
            url,
            authentication="Bearer {}".format(
                GLOBAL_TOKENS["APPLICATION_1_MERCHANT_2_ADMIN"]["token"]
            ),
        )

        self.assertHttpBadRequest(response)
        mock.delay.assert_not_called()

    @patch("requests.get")
    @patch.object(tasks, "create_hipay_account")
    def test_create_from_existing_account(self, mocked_get, mocked_task):
        HipayAppConfigurationFactory.create(application=self.merchant.application)
        mocked_get.return_value = MockedRequestsResponse(
            content=VALID_USER_INFO_RESPONSE
        )
        data = {
            "merchant": "/v1/merchant/{}/".format(self.merchant.id),
            "api_login": "0d88d350b0adf9ecef810a6ea531417b",
            "api_password": "3101286ffbcc607dd3b83d201d009bda",
            "user_account_id": VALID_USER_INFO_RESPONSE.get("user_account_id"),
            "user_space_id": VALID_USER_INFO_RESPONSE.get("user_space_id"),
            "account_type": HIPAY_ACCOUNT_TYPE_CORPORATION,
        }

        response = self.api_client.post(
            "/v1/hipay_merchant_account/create_from_existing/",
            data=data,
            authentication="Bearer {}".format(
                GLOBAL_TOKENS["APPLICATION_1_MERCHANT_2_ADMIN"]["token"]
            ),
        )

        self.assertHttpCreated(response)
        mocked_task.delay.assert_not_called()

    def test_filtering_for_operator_wallet(self):
        merchant_wallet = HipayWalletAccountFactory.create(application__id=1)
        application = merchant_wallet.application
        HipayOperatorWalletAccountFactory.create(
            application=application,
        )

        response = self.api_client.get(
            "/v1/hipay_merchant_account/?application={}&merchant__isnull=true".format(
                application.id
            ),
            authentication="Bearer {}".format(
                GLOBAL_TOKENS["APPLICATION_1_WRITE"]["token"]
            ),
        )
        self.assertHttpOK(response)
        data = self.deserialize(response)
        self.assertEqual(data["meta"]["total_count"], 1)
