from datetime import datetime

import pytz
from apps.testing.factories import (
    HipayAppConfigurationFactory,
    MerchantCashoutTransactionFactory,
)
from apps.testing.factories.hipay import (
    HipayMerchantBankAccountKycV2Factory,
    HipayWalletAccountFactory,
)
from django.utils import timezone
from ims.tests import BaseTestCase
from mock import ANY, patch
from parameterized import parameterized
from payment_backends.hipay_tpp.actions.merchant_cashout import get_pending_cashouts
from payment_backends.hipay_tpp.hipay_api.cashout import TransferParameter
from payment_backends.hipay_tpp.references import HIPAY_BANK_INFOS_STATUSES_VALID
from payment_backends.hipay_tpp.tasks.cashout.handle_stacked_cashouts import (
    handle_stacked_cashouts,
    handle_stacked_cashouts_for_app,
    is_cashout_day,
    process_stacked_cashout,
)


class MerchantCashoutTestCase(BaseTestCase):
    def test_handle_stacked_cashouts_triggers_active_hipay_app_only(self):
        active_hipay_app = HipayAppConfigurationFactory(
            application__payment_backend="hipay_tpp", cashout_enabled=True
        ).application
        # disabled cashout app
        HipayAppConfigurationFactory(
            application__payment_backend="hipay_tpp", cashout_enabled=False
        ).application
        # non-hipay active app
        HipayAppConfigurationFactory(
            application__payment_backend="external", cashout_enabled=True
        ).application
        result = handle_stacked_cashouts(ignore_day_check=True)
        self.assertEqual(result, [active_hipay_app.id])

    def test_get_pending_cashouts_empty_result(self):
        result = get_pending_cashouts(1, timezone.now())
        self.assertEqual(result, [])

    def test_get_pending_cashouts_single_merchant_cashout(self):
        cashout_tx = MerchantCashoutTransactionFactory()
        store_cashout_tx = cashout_tx.balancetransactions.get()
        result = get_pending_cashouts(cashout_tx.application_id, timezone.now())
        self.assertEqual(
            result,
            [
                {
                    "merchant_id": cashout_tx.merchant_id,
                    "merchant_name": cashout_tx.merchant.name,
                    "application_id": cashout_tx.merchant.application_id,
                    "cashout_request_ids_list": [store_cashout_tx.id],
                    "total_amount": str(cashout_tx.amount),
                    "currency": cashout_tx.currency.code,
                }
            ],
        )

    def test_get_pending_cashouts_double_merchant_cashout_aggregation(self):
        cashout_m1 = MerchantCashoutTransactionFactory()
        cashout_m2 = MerchantCashoutTransactionFactory(
            application=cashout_m1.application,
            currency=cashout_m1.currency,
        )
        cashout_m1_bis = MerchantCashoutTransactionFactory(
            application=cashout_m1.application,
            currency=cashout_m1.currency,
            merchant=cashout_m1.merchant,
        )
        store_cashout_m1 = cashout_m1.balancetransactions.get()
        store_cashout_m1_bis = cashout_m1_bis.balancetransactions.get()
        store_cashout_m2 = cashout_m2.balancetransactions.get()
        result = get_pending_cashouts(cashout_m1.application_id, timezone.now())
        self.assertEqual(
            result,
            [
                {
                    "merchant_id": cashout_m1.merchant_id,
                    "merchant_name": cashout_m1.merchant.name,
                    "application_id": cashout_m1.merchant.application_id,
                    "cashout_request_ids_list": [
                        store_cashout_m1.id,
                        store_cashout_m1_bis.id,
                    ],
                    "total_amount": str(cashout_m1.amount + cashout_m1_bis.amount),
                    "currency": cashout_m1.currency.code,
                },
                {
                    "merchant_id": cashout_m2.merchant_id,
                    "merchant_name": cashout_m2.merchant.name,
                    "application_id": cashout_m2.merchant.application_id,
                    "cashout_request_ids_list": [
                        store_cashout_m2.id,
                    ],
                    "total_amount": str(cashout_m2.amount),
                    "currency": cashout_m2.currency.code,
                },
            ],
        )

    @patch(
        "payment_backends.hipay_tpp.actions.merchant_cashout.logger.exception",
        lambda *_: None,
    )
    @patch(
        "payment_backends.hipay_tpp.actions.merchant_cashout.logger.error",
        lambda *_: None,
    )
    def test_process_stacked_cashout_disable_app_cashouts_on_error(self):
        hipay_app_config = HipayAppConfigurationFactory(
            application__payment_backend="hipay_tpp", cashout_enabled=True
        )
        invalid_cashout_instruction = {"unexpected": "dict"}

        with self.assertRaises(KeyError):
            process_stacked_cashout(
                invalid_cashout_instruction, 12345, hipay_app_config.application.id
            )

        hipay_app_config.refresh_from_db()
        self.assertFalse(hipay_app_config.cashout_enabled)

    def test_process_stacked_cashout_missing_hipay_wallet(self):
        hipay_app_config = HipayAppConfigurationFactory(
            application__payment_backend="hipay_tpp", cashout_enabled=True
        )
        cashout_trans = MerchantCashoutTransactionFactory(
            application=hipay_app_config.application,
            currency=hipay_app_config.application.default_currency,
        )
        cashout_subtrans = cashout_trans.balancetransactions.get()
        cashout_instruction = {
            "merchant_id": cashout_trans.merchant_id,
            "merchant_name": cashout_trans.merchant.name,
            "application_id": hipay_app_config.application.id,
            "cashout_request_ids_list": [cashout_subtrans.id],
            "total_amount": str(cashout_trans.amount),
            "currency": "EUR",
        }

        result = process_stacked_cashout(
            cashout_instruction,
            cashout_trans.merchant_id,
            hipay_app_config.application.id,
        )
        self.assertEqual(result, None)

        cashout_subtrans.refresh_from_db()
        cashout_trans.refresh_from_db()

        self.assertEqual(cashout_subtrans.status, "attention_required")
        self.assertEqual(cashout_trans.status, "attention_required")
        self.assertEqual(cashout_trans.error_message, "Not registered on hipay.")

    @patch(
        "payment_backends.hipay_tpp.hipay_api.cashout."
        "HipayCashoutApi.transfer_amount",
    )
    @patch(
        "payment_backends.hipay_tpp.hipay_api.cashout."
        "HipayCashoutApi.withdraw_without_commissions",
    )
    @patch(
        "payment_backends.hipay_tpp.actions.bank_account.manager."
        "BankAccountActionsManager.refresh",
        lambda self: self.model,
    )
    @patch(
        "payment_backends.hipay_tpp.hipay_api.cashout.HipayCashoutApi.is_identified",
        lambda _, __: True,
    )
    @patch(
        "payment_backends.hipay_tpp.hipay_api.cashout."
        "HipayCashoutApi.get_balance_and_withdrawable_amount",
        lambda _, __: (10, 10),
    )
    @patch(
        "payment_backends.hipay_tpp.actions."
        "merchant_cashout.HIPAY_MINIMUM_TRANSFER_WAIT_TIME",
        0,
    )
    @patch(
        "payment_backends.hipay_tpp.actions.merchant_cashout.randint",
        return_value=1,
    )
    @patch(
        "payment_backends.hipay_tpp.hipay_api.cashout."
        "HipayCashoutApi.check_ewallet_is_empty",
        lambda _, __: False,
    )
    def test_process_stacked_cashout_success(
        self, _, mocked_withdraw, mocked_transfer_amount
    ):
        hipay_app_config = HipayAppConfigurationFactory(
            application__payment_backend="hipay_tpp", cashout_enabled=True
        )
        cashout_trans = MerchantCashoutTransactionFactory(
            application=hipay_app_config.application,
            currency=hipay_app_config.application.default_currency,
        )
        wallet = HipayWalletAccountFactory(
            application=hipay_app_config.application, merchant=cashout_trans.merchant
        )
        HipayMerchantBankAccountKycV2Factory(
            merchant=cashout_trans.merchant,
            synchro_status=HIPAY_BANK_INFOS_STATUSES_VALID,
        )
        cashout_subtrans = cashout_trans.balancetransactions.get()
        cashout_instruction = {
            "merchant_id": cashout_trans.merchant_id,
            "merchant_name": cashout_trans.merchant.name,
            "application_id": hipay_app_config.application.id,
            "cashout_request_ids_list": [cashout_subtrans.id],
            "total_amount": str(cashout_trans.amount),
            "currency": cashout_trans.currency.code,
        }

        result = process_stacked_cashout(
            cashout_instruction,
            cashout_trans.merchant_id,
            hipay_app_config.application.id,
        )

        self.assertEqual(result, True)

        cashout_subtrans.refresh_from_db()
        cashout_trans.refresh_from_db()

        self.assertEqual(cashout_trans.error_message, None)
        self.assertEqual(cashout_subtrans.status, "confirmed")
        self.assertEqual(cashout_trans.status, "confirmed")

        expected_transfer_params = {
            "amount": -cashout_trans.amount,
            "currency": cashout_trans.currency.code,
            "wallet": wallet,
            "label_str": (
                f"Withdrawal of "
                f"{-cashout_trans.amount} {cashout_trans.currency.code} "
                f"from Merchant[{cashout_trans.merchant_id}]'s account to bank account"
            ),
        }
        self.assertEqual(
            type(mocked_transfer_amount.call_args[0][0]), TransferParameter
        )
        self.assertEqual(type(mocked_withdraw.call_args[0][0]), TransferParameter)

        self.assertEqual(
            mocked_transfer_amount.call_args[0][0].__dict__, expected_transfer_params
        )
        self.assertEqual(
            mocked_withdraw.call_args[0][0].__dict__, expected_transfer_params
        )

    def test_handle_stacked_cashouts_for_app_triggers_one_task_per_merchant(self):
        hipay_app_config = HipayAppConfigurationFactory(
            application__payment_backend="hipay_tpp", cashout_enabled=True
        )
        cashout_trans_1 = MerchantCashoutTransactionFactory(
            application=hipay_app_config.application,
            currency=hipay_app_config.application.default_currency,
        )
        cashout_trans_2 = MerchantCashoutTransactionFactory(
            application=hipay_app_config.application,
            currency=hipay_app_config.application.default_currency,
        )
        task_count, merchant_to_task_id = handle_stacked_cashouts_for_app(
            hipay_app_config.application_id
        )

        self.assertEqual(task_count, 2)
        self.assertEqual(
            merchant_to_task_id,
            {
                cashout_trans_1.merchant_id: ANY,
                cashout_trans_2.merchant_id: ANY,
            },
        )


class CashoutDayTestCase(BaseTestCase):
    @parameterized.expand(
        [
            (
                "monday_paris_tz",
                datetime(2022, 4, 18, 00, 00, 1, tzinfo=pytz.timezone("Europe/Paris")),
                True,
            ),
            (
                "monday_utc_tz",
                datetime(2022, 4, 17, 22, 00, 1, tzinfo=pytz.utc),
                True,
            ),
            (
                "saturday_paris_tz",
                datetime(2022, 4, 16, 00, 00, 1, tzinfo=pytz.timezone("Europe/Paris")),
                False,
            ),
            (
                "saturday_utc_tz",
                datetime(2022, 4, 15, 22, 00, 1, tzinfo=pytz.utc),
                False,
            ),
        ]
    )
    def test_is_cashout_day_result(self, _, input_value, expected_output):
        self.assertEqual(is_cashout_day(input_value), expected_output)
