from unittest import mock
from unittest.mock import patch

from apps.testing.factories import HipayApiCallLogFactory, HipayPaymentPageCSSFactory
from django.core.files import File
from django.utils import timezone
from ims.tests import BaseTestCase


class HipayApiCallLogTestCase(BaseTestCase):
    def test_response_file_path(self):
        file_mock = mock.MagicMock(spec=File)
        file_mock.name = "test.pdf"
        logger = HipayApiCallLogFactory()
        logger.response_file = file_mock
        logger.save()
        now = timezone.now()
        expected_file_path = (
            f"{logger.application.id}/"
            f"hipay_api_call_log/{now.year}/{now.month}/{now.day}/"
            f"test-.+-.+-.+-.+\\.pdf"
        )
        self.assertRegex(logger.response_file.path, expected_file_path)


class HipayPaymentPageCSSTestCase(BaseTestCase):
    def test_css_source_file_path(self):
        file_mock = mock.MagicMock(spec=File)
        file_mock.name = "test.css"
        with patch(
            "payment_backends.hipay_tpp.models."
            "payment_page_css.HipayPaymentPageCSS.application"
        ):
            page = HipayPaymentPageCSSFactory()
            page.css_source_file = file_mock
            page.save()
            now = timezone.now()
            expected_file_path = (
                f"{page.application.id}/"
                f"hipay_payment_page_css/"
                f"{now.year}/{now.month}/{now.day}/"
                f"test-.+-.+-.+-.+\\.css"
            )
            self.assertRegex(page.css_source_file.path, expected_file_path)
