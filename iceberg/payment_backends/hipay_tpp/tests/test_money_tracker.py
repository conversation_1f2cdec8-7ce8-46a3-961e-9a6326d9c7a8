# -*- coding: utf-8 -*-
from apps.testing.factories import HipayTransactionFactory, MerchantOrderFactory
from ims.tests import BaseTestCase
from payment_backends.hipay_tpp.money_tracker import HipayMoneyTracker


class TestMoneyTracker(BaseTestCase):
    def test_is_on_escrow(self):
        merchant_order = MerchantOrderFactory()
        hipay_transaction = HipayTransactionFactory.create(
            application=merchant_order.application,
            order=merchant_order.order,
        )
        tracker = HipayMoneyTracker(hipay_transaction.payment)
        tracker.set_on_escrow(merchant_order)

        self.assertTrue(tracker.is_on_escrow(merchant_order))

    def test_not_resolved(self):
        merchant_order = MerchantOrderFactory()
        hipay_transaction = HipayTransactionFactory.create(
            application=merchant_order.application,
            order=merchant_order.order,
        )
        MerchantOrderFactory.create(order=merchant_order.order)
        tracker = HipayMoneyTracker(hipay_transaction.payment)

        tracker.set_on_escrow(merchant_order)

        self.assertFalse(
            tracker.is_order_resolved_on_payment_backend(merchant_order.order)
        )

    def test_is_resolved(self):
        merchant_order = MerchantOrderFactory()
        merchant_order_2 = MerchantOrderFactory(
            user=merchant_order.user,
            application=merchant_order.application,
            merchant=merchant_order.merchant,
            order=merchant_order.order,
        )
        hipay_transaction = HipayTransactionFactory.create(
            application=merchant_order.application,
            order=merchant_order.order,
        )
        tracker = HipayMoneyTracker(hipay_transaction.payment)

        tracker.set_on_escrow(merchant_order)
        tracker.set_on_escrow(merchant_order_2)

        self.assertTrue(
            tracker.is_order_resolved_on_payment_backend(merchant_order.order)
        )
