# -*- coding: utf-8 -*-

from decimal import Decimal
from urllib.parse import quote

import requests_mock
from apps.testing.factories import (
    HipayAppConfigurationFactory,
    HipayTransactionFactory,
    MerchantOrderRefundFactory,
    ProductOfferOrderItemFactory,
)
from ims.tests import BaseTestCase
from mock import patch

from ..exceptions import UnexpectedHipayError
from ..processor import HipayTppPayment
from ..utils import hipay_get_unique_id
from .sample_api_responses import (
    get_transaction_authorized_completed_response,
    get_transaction_partially_refunded_response,
    get_transaction_refund_refused_response,
    get_transaction_refund_requested_response,
    get_transaction_refunded_response,
)


@requests_mock.Mocker()
@patch(
    "payment_backends.hipay_tpp.hipay_api.rest_api_interface."
    "RestAPIInterface._log_api_call_error_during_tests",
    lambda *_: None,
)
class RefundActionTestCase(BaseTestCase):
    def setUp(self, *args, **kwargs):
        super().setUp(*args, **kwargs)
        self.order_item = ProductOfferOrderItemFactory(
            merchant_order__order__auto_create_payment=True,
            merchant_order__order___payment__external_id="bla",
        )
        self.hipay_config = HipayAppConfigurationFactory(
            application=self.order_item.application
        )
        self.refund = MerchantOrderRefundFactory(
            order_item=self.order_item,
            merchant_order=self.order_item.merchant_order,
            currency=self.order_item.application.default_currency,
            application=self.order_item.application,
            merchant=self.order_item.merchant_order.merchant,
        )

    def get_transaction(self, hipay_config, has_tref=True):
        return HipayTransactionFactory(
            application=hipay_config.application,
            captured_amount=Decimal(10),
            transaction_reference=123 if has_tref else None,
            order=self.order_item.merchant_order.order,
            payment=self.order_item.merchant_order.order.payment,
        )

    def get_order_id(self, transaction):
        return quote(hipay_get_unique_id(transaction.payment))

    def test_refunded(self, mocked_request):
        processor = HipayTppPayment()
        processor.ensure_api(self.hipay_config.application)
        tr = self.get_transaction(self.hipay_config, has_tref=True)
        order_id = self.get_order_id(tr)
        mocked_request.post(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/maintenance/"
            f"transaction/{tr.transaction_reference}",
            json=get_transaction_refunded_response(),
        )
        mocked_request.get(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/transaction/"
            f"{tr.payment.external_id}",
            json=get_transaction_authorized_completed_response(
                order_id=order_id,
                amount=Decimal("100"),
                transaction_reference=tr.transaction_reference,
            ),
        )

        ret = processor.refund(
            tr.payment, tr.order.application.contact_user, refund=self.refund
        )

        self.assertTrue(ret)

    def test_partially_refunded(self, mocked_request):
        processor = HipayTppPayment()
        processor.ensure_api(self.hipay_config.application)
        tr = self.get_transaction(self.hipay_config, has_tref=True)
        order_id = self.get_order_id(tr)
        mocked_request.post(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/maintenance/"
            f"transaction/{tr.transaction_reference}",
            json=get_transaction_partially_refunded_response(),
        )
        mocked_request.get(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/transaction/"
            f"{tr.payment.external_id}",
            json=get_transaction_authorized_completed_response(
                order_id=order_id,
                amount=Decimal("100"),
                transaction_reference=tr.transaction_reference,
            ),
        )

        ret = processor.refund(
            tr.payment, tr.order.application.contact_user, refund=self.refund
        )

        self.assertTrue(ret)

    def test_refund_requested(self, mocked_request):
        processor = HipayTppPayment()
        processor.ensure_api(self.hipay_config.application)
        tr = self.get_transaction(self.hipay_config, has_tref=True)
        order_id = self.get_order_id(tr)
        mocked_request.post(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/maintenance/"
            f"transaction/{tr.transaction_reference}",
            json=get_transaction_refund_requested_response(),
        )
        mocked_request.get(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/transaction/"
            f"{tr.payment.external_id}",
            json=get_transaction_authorized_completed_response(
                order_id=order_id,
                amount=Decimal("100"),
                transaction_reference=tr.transaction_reference,
            ),
        )

        ret = processor.refund(
            tr.payment, tr.order.application.contact_user, refund=self.refund
        )

        self.assertTrue(ret)

    def test_refund_refused(self, mocked_request):
        processor = HipayTppPayment()
        processor.ensure_api(self.hipay_config.application)
        tr = self.get_transaction(self.hipay_config, has_tref=True)
        mocked_request.post(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/maintenance/"
            f"transaction/{tr.transaction_reference}",
            json=get_transaction_refund_refused_response(),
        )

        with self.assertRaises(UnexpectedHipayError) as err:
            processor.refund(
                tr.payment, tr.order.application.contact_user, refund=self.refund
            )

        self.assertEqual(
            err.exception.as_dict(),
            {
                "error_description": "Unexpected Hipay Error: [165] Refund Refused",
                "error_code": "HIPAY.UNEXPECTED_ERROR",
                "error_context": {
                    "hipay_status": "165",
                    "hipay_message": "Refund Refused",
                },
            },
        )

    def test_unknown_status(self, mocked_request):
        processor = HipayTppPayment()
        processor.ensure_api(self.hipay_config.application)
        tr = self.get_transaction(self.hipay_config, has_tref=True)
        data = get_transaction_refund_refused_response()
        data["status"] = "unknown"
        data["message"] = "Unhandled reason"
        mocked_request.post(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/maintenance/"
            f"transaction/{tr.transaction_reference}",
            json=data,
        )

        with self.assertRaises(UnexpectedHipayError) as err:
            processor.refund(
                tr.payment, tr.order.application.contact_user, refund=self.refund
            )

        self.assertEqual(
            err.exception.as_dict(),
            {
                "error_description": (
                    "Unexpected Hipay Error: [unknown] Unhandled reason"
                ),
                "error_code": "HIPAY.UNEXPECTED_ERROR",
                "error_context": {
                    "hipay_status": "unknown",
                    "hipay_message": "Unhandled reason",
                },
            },
        )

    def test_missing_status(self, mocked_request):
        processor = HipayTppPayment()
        processor.ensure_api(self.hipay_config.application)
        tr = self.get_transaction(self.hipay_config, has_tref=True)
        data = get_transaction_refund_refused_response()
        del data["status"]
        data["message"] = "Unhandled reason"
        mocked_request.post(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/maintenance/"
            f"transaction/{tr.transaction_reference}",
            json=data,
        )

        with self.assertRaises(UnexpectedHipayError) as err:
            processor.refund(
                tr.payment, tr.order.application.contact_user, refund=self.refund
            )

        self.assertEqual(
            err.exception.as_dict(),
            {
                "error_description": "Unexpected Hipay Error: Unhandled reason",
                "error_code": "HIPAY.UNEXPECTED_ERROR",
                "error_context": {
                    "hipay_status": None,
                    "hipay_message": "Unhandled reason",
                },
            },
        )

    def test_missing_message(self, mocked_request):
        processor = HipayTppPayment()
        processor.ensure_api(self.hipay_config.application)
        tr = self.get_transaction(self.hipay_config, has_tref=True)
        data = get_transaction_refund_refused_response()
        del data["message"]
        mocked_request.post(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/maintenance/"
            f"transaction/{tr.transaction_reference}",
            json=data,
        )

        with self.assertRaises(UnexpectedHipayError) as err:
            processor.refund(
                tr.payment, tr.order.application.contact_user, refund=self.refund
            )

        self.assertEqual(
            err.exception.as_dict(),
            {
                "error_description": "Unexpected Hipay Error: [165] Unknown error",
                "error_code": "HIPAY.UNEXPECTED_ERROR",
                "error_context": {"hipay_status": "165", "hipay_message": None},
            },
        )
