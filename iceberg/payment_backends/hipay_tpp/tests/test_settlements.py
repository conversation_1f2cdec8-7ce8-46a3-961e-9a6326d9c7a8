import requests_mock
import responses
from apps.ice_applications.models import ApplicationWorkflow
from apps.payment.backends import HIPAYTPP
from apps.testing.factories import (
    ApplicationFactory,
    HipayAppConfigurationFactory,
    HipayTransactionFactory,
    MerchantOrderFactory,
    MerchantOrderSellTransactionFactory,
    ProductOfferOrderItemFactory,
)
from apps.transactions.models import Transaction
from dateutil.parser import isoparse
from django.utils import timezone
from ims.tests import BaseTransactionTestCase
from mock import patch
from payment_backends.hipay_tpp.actions import process_app_settlements
from payment_backends.hipay_tpp.actions.settlement import (
    _generate_basic_auth,
    hipay_settlement_api,
    process_settlement_operation,
)
from payment_backends.hipay_tpp.models import HipayAppConfiguration
from payment_backends.hipay_tpp.money_tracker import HipayMoneyTracker
from payment_backends.hipay_tpp.tasks.cashin import update_transaction_with_settlements
from payment_backends.hipay_tpp.tests.sample_api_responses import (
    get_transaction_authorized_completed_response,
    get_transaction_captured_response,
)
from reference.status.transaction import TRANSACTION_STATUS_ATTENTION_REQUIRED
from reference.transaction_codes import TRANSACTION_SALE
from requests.models import Response
from responses import matchers


def build_operation_id(merchant_orders, operation="CAPTURE"):
    return ":".join([operation] + [str(mo.id) for mo in merchant_orders])


def build_settlements_dict(amount):
    return {
        "settlements": [
            {
                "recipient_name": "Test SA",
                "recipient_iban": "***************************",
                "recipient_bic": "IZBFRPPXXX",
                "recipient_bank_country": "FR",
                "amount": str(amount),
                "currency": "EUR",
                "decimals": 2,
                "date_value": "2019-03-28T00:11:46+01",
                "date_transfer": "2019-03-29T00:00:00+01",
                "collect_mode": "COLLECTING",
                "merchant_name": "IZB.MKP_PROD",
                "merchant_id": 1,
                "settlementid": 42,
                "sales": str(amount),
                "count_sales": 1,
                "refunds": 0,
                "count_refunds": 0,
                "fees": 0,
                "count_fees": 0,
                "chargeback": 0,
                "count_chargeback": 0,
                "invoices": 0,
                "count_invoices": 0,
                "deferred": 0,
                "count_deferred": 0,
                "rolling": 0,
                "count_rolling": 0,
                "other": 0,
                "count_other": 0,
                "_links": {
                    "self": {"href": "https:\\/\\/api.hipay-tpp.com\\/settlement\\/42"},
                    "raw": {
                        "href": "https:\\/\\/api.hipay-tpp.com\\/settlement\\/42\\/raw"
                    },
                },
            }
        ]
    }


def build_sale_operation_dict(merchant_orders, operation_id, amount):
    """
    Render a "Sale" operation as returned by the Hipay settlement JSON API
    (https://api.hipay-tpp.com/v1/settlement/{id}/raw.json)
    """
    payment = merchant_orders[0].order.payment
    amount = str(amount)
    return {
        "account_publicref": 1234567,
        "account_name": "TEST",
        "date_posted": "2019-03-29 06:15:56.197333+01",
        "date_value": "2019-03-28 00:00:00+01",
        "invoice_reference": None,
        "settlementid": 42,
        "trxid": payment.hipay_transaction.transaction_reference,
        "mch_account_order_id": "0********9AB",
        "product_name": "Carte Bancaire",
        "invoiced": False,
        "settled": True,
        "original_amount": amount,
        "original_currency": "EUR",
        "amount": amount,
        "tax_amount": "0.000",
        "net_amount": amount,
        "currency": "EUR",
        "operation": "Sale",
        "settlement_amount": amount,
        "settlement_currency": "EUR",
        "original_settlementid": None,
        "customer_id": "********",
        "merchant_operation_reference": operation_id,
        "reporting_data_1": None,
        "reporting_data_2": None,
        "reporting_data_3": None,
        "reporting_data_4": None,
        "reporting_data_5": None,
        "collect_mode": "COLLECTING",
        "transfer": "2019-04-01",
        "operation_date": "2019-03-27 21:50:11+01",
    }


class TestProcessSettlements(BaseTransactionTestCase):
    def setUp(self):
        conf = HipayAppConfigurationFactory.create()
        self.application = conf.application
        order_item_1 = ProductOfferOrderItemFactory.create(
            application=self.application,
            currency=self.application.default_currency,
            merchant_order__order__auto_create_payment=True,
        )
        order_item_2 = ProductOfferOrderItemFactory.create(
            application=self.application,
            currency=self.application.default_currency,
            user=order_item_1.merchant_order.order.user,
            cart=order_item_1.merchant_order.order.cart,
            product_tax_group=order_item_1.product_tax_group,
            merchant_order__order=order_item_1.merchant_order.order,
            recompute_amounts=True,
        )
        self.merchant_order = order_item_1.merchant_order
        self.merchant_order_2 = order_item_2.merchant_order
        self.order = self.merchant_order.order
        self.order.refresh_from_db()
        self.order.payment.refresh_from_db()
        self.hipay_transaction = HipayTransactionFactory.create(
            application=self.application,
            order=self.order,
            payment=self.order.payment,
        )

    def test_process_settlement_on_inactive_app(self):
        self.application.status = ApplicationWorkflow.PAUSED
        self.application.save()
        with patch(
            "payment_backends.hipay_tpp.tasks.cashin.settlement."
            "process_app_settlements_task.delay"
        ) as task:
            update_transaction_with_settlements.apply()
            self.assertFalse(task.called)
        self.application.status = ApplicationWorkflow.PAUSED
        self.application.save()

    def test_process_settlement_all_merchant_orders_at_once(self):
        sell_transaction = MerchantOrderSellTransactionFactory.create(
            merchant_order=self.merchant_order,
            merchant=self.merchant_order.merchant,
            order=self.order,
            payment=self.order.payment,
        )
        sell_transaction_2 = MerchantOrderSellTransactionFactory.create(
            merchant_order=self.merchant_order_2,
            merchant=self.merchant_order_2.merchant,
            order=self.order,
            payment=self.order.payment,
        )
        merchant_orders = [self.merchant_order, self.merchant_order_2]
        settlement_dict = build_sale_operation_dict(
            merchant_orders,
            build_operation_id(merchant_orders),
            self.order.total_amount,
        )
        process_settlement_operation(settlement_dict)

        self.hipay_transaction.refresh_from_db()
        sell_transaction.refresh_from_db()
        sell_transaction_2.refresh_from_db()

        tracker = HipayMoneyTracker(self.order.payment)
        for mo in merchant_orders:
            self.assertTrue(tracker.is_on_escrow(mo))
        self.assertTrue("full" in self.hipay_transaction.settled_amounts)
        self.assertEqual(self.hipay_transaction.settled_amount, self.order.total_amount)
        expected_settlement_date = isoparse(settlement_dict["date_value"])
        self.assertEqual(sell_transaction.settlement_date, expected_settlement_date)
        self.assertEqual(sell_transaction_2.settlement_date, expected_settlement_date)

    def test_process_partial_settlement_with_operation_id(self):
        sell_transaction = MerchantOrderSellTransactionFactory.create(
            merchant_order=self.merchant_order,
            merchant=self.merchant_order.merchant,
            order=self.order,
            payment=self.order.payment,
        )
        merchant_orders = [self.merchant_order]
        merchant_orders[0]
        operation_id = build_operation_id(merchant_orders)
        self.hipay_transaction.capture_operations[operation_id] = [
            mo.id for mo in merchant_orders
        ]
        self.hipay_transaction.save()
        settlement_dict = build_sale_operation_dict(
            merchant_orders, operation_id, self.merchant_order.amount_vat_included
        )

        process_settlement_operation(settlement_dict)

        self.hipay_transaction.refresh_from_db()
        sell_transaction.refresh_from_db()
        tracker = HipayMoneyTracker(self.order.payment)
        for mo in merchant_orders:
            self.assertTrue(tracker.is_on_escrow(mo))
        expected_settlement_date = isoparse(settlement_dict["date_value"])
        self.assertEqual(sell_transaction.settlement_date, expected_settlement_date)

    def test_process_partial_save_settled_amounts(self):
        sell_transaction = MerchantOrderSellTransactionFactory.create(
            merchant_order=self.merchant_order,
            merchant=self.merchant_order.merchant,
            order=self.order,
            payment=self.order.payment,
        )
        operation_id = build_operation_id([self.merchant_order])
        self.hipay_transaction.capture_operations[operation_id] = [
            self.merchant_order.id
        ]
        self.hipay_transaction.save()
        settlement_dict = build_sale_operation_dict(
            [self.merchant_order], operation_id, self.merchant_order.amount_vat_included
        )
        self.order.payment.collected_amount = self.order.payment.to_collect_amount
        self.order.payment.save()

        process_settlement_operation(settlement_dict)
        self.hipay_transaction.refresh_from_db()
        sell_transaction.refresh_from_db()
        self.assertTrue(operation_id in self.hipay_transaction.settled_amounts)
        self.assertEqual(
            self.hipay_transaction.settled_amount,
            self.merchant_order.amount_vat_included,
        )
        expected_settlement_date = isoparse(settlement_dict["date_value"])
        self.assertEqual(sell_transaction.settlement_date, expected_settlement_date)

    def test_process_partial_settlement_fully_settled(self):
        sell_transaction = MerchantOrderSellTransactionFactory.create(
            merchant_order=self.merchant_order,
            merchant=self.merchant_order.merchant,
            order=self.order,
            payment=self.order.payment,
        )
        sell_transaction_2 = MerchantOrderSellTransactionFactory.create(
            merchant_order=self.merchant_order_2,
            merchant=self.merchant_order_2.merchant,
            order=self.order,
            payment=self.order.payment,
        )
        merchant_orders = [self.merchant_order]
        operation_id = build_operation_id([self.merchant_order])
        self.hipay_transaction.capture_operations[operation_id] = [
            self.merchant_order.id
        ]
        self.hipay_transaction.settled_amounts = {
            operation_id: str(self.order.total_amount)
        }
        self.hipay_transaction.save()
        settlement_dict = build_sale_operation_dict(
            merchant_orders, operation_id, self.order.total_amount
        )
        self.order.payment.collected_amount = self.order.payment.to_collect_amount
        self.order.payment.save()

        process_settlement_operation(settlement_dict)

        self.hipay_transaction.refresh_from_db()
        sell_transaction.refresh_from_db()
        sell_transaction_2.refresh_from_db()
        tracker = HipayMoneyTracker(self.order.payment)
        for mo in merchant_orders:
            self.assertTrue(tracker.is_on_escrow(mo))
        expected_settlement_date = isoparse(settlement_dict["date_value"])
        self.assertEqual(sell_transaction.settlement_date, expected_settlement_date)
        self.assertEqual(sell_transaction_2.settlement_date, expected_settlement_date)

    @patch(
        "payment_backends.hipay_tpp.actions.settlement.logger.exception",
        lambda *_: None,
    )
    def test_process_partial_settlement_without_operation_id(self):
        merchant_orders = [self.merchant_order]
        settlement_dict = build_sale_operation_dict(
            merchant_orders,
            build_operation_id(merchant_orders),
            self.merchant_order.amount_vat_included,
        )

        process_settlement_operation(settlement_dict)

        self.hipay_transaction.refresh_from_db()
        tracker = HipayMoneyTracker(self.order.payment)
        self.assertFalse(tracker.is_on_escrow(self.merchant_order))

    def test_reschedule_outdated_sell_transactions_total_order_amount(self):
        sell_transaction = MerchantOrderSellTransactionFactory.create(
            merchant_order=self.merchant_order,
            merchant=self.merchant_order.merchant,
            order=self.order,
            payment=self.order.payment,
            status=TRANSACTION_STATUS_ATTENTION_REQUIRED,
        )
        sell_transaction_2 = MerchantOrderSellTransactionFactory.create(
            merchant_order=self.merchant_order_2,
            merchant=self.merchant_order_2.merchant,
            order=self.order,
            payment=self.order.payment,
            status=TRANSACTION_STATUS_ATTENTION_REQUIRED,
        )
        self.merchant_order = sell_transaction.merchant_order
        merchant_orders = [self.merchant_order]
        settlement_dict = build_sale_operation_dict(
            merchant_orders,
            build_operation_id(merchant_orders),
            self.merchant_order.amount_vat_included,
        )
        settlement_dict["settlement_amount"] = self.order.total_amount
        settlement_dict["operation"] = "Sale"
        process_settlement_operation(settlement_dict)
        sell_transaction.refresh_from_db()
        sell_transaction_2.refresh_from_db()
        self.assertTrue(sell_transaction.status.is_pending)
        self.assertTrue(sell_transaction_2.status.is_pending)
        date_value = isoparse(settlement_dict["date_value"])
        self.assertEqual(sell_transaction.settlement_date, date_value)
        self.assertEqual(sell_transaction_2.settlement_date, date_value)

    def test_reschedule_outdated_transactions_two_merchant_orders(self):
        sell_transaction = MerchantOrderSellTransactionFactory.create(
            merchant_order=self.merchant_order,
            merchant=self.merchant_order.merchant,
            order=self.order,
            payment=self.order.payment,
            status=TRANSACTION_STATUS_ATTENTION_REQUIRED,
        )
        sell_transaction_2 = MerchantOrderSellTransactionFactory.create(
            merchant_order=self.merchant_order_2,
            merchant=self.merchant_order_2.merchant,
            order=self.order,
            payment=self.order.payment,
            status=TRANSACTION_STATUS_ATTENTION_REQUIRED,
        )
        self.merchant_order = sell_transaction.merchant_order
        self.hipay_transaction.capture_operations = {
            build_operation_id([self.merchant_order]): [self.merchant_order.id]
        }
        self.hipay_transaction.save()
        merchant_orders = [self.merchant_order]
        settlement_mo_1_dict = build_sale_operation_dict(
            merchant_orders,
            build_operation_id(merchant_orders),
            self.merchant_order.amount_vat_included,
        )
        settlement_mo_1_dict["settlement_amount"] = (
            self.merchant_order.amount_vat_included
        )
        settlement_mo_1_dict["operation"] = "Sale"

        process_settlement_operation(settlement_mo_1_dict)

        sell_transaction.refresh_from_db()
        sell_transaction_2.refresh_from_db()
        self.assertEqual(sell_transaction.status, "pending")
        self.assertEqual(sell_transaction_2.status, "pending")

    def test_non_reg_basic_auth_generation(self):
        auth = _generate_basic_auth("username", "password")

        self.assertEqual(auth, "Basic dXNlcm5hbWU6cGFzc3dvcmQ=")

    @requests_mock.Mocker()
    @patch(
        "payment_backends.common.utils.RestLogsBuilder."
        "raise_for_action_not_configured",
        lambda *_: None,
    )
    def test_settlement_api_return_content_by_default(self, mocked_request):
        url = "http://localhost/v1/"
        data = {"payload": "test"}
        mocked_request.get(url, json=data)

        result = hipay_settlement_api.request(url)

        self.assertEqual(result, data)

    @requests_mock.Mocker()
    @patch(
        "payment_backends.common.utils.RestLogsBuilder."
        "raise_for_action_not_configured",
        lambda *_: None,
    )
    def test_settlement_api_with_return_content_false(self, mocked_request):
        url = "http://localhost/v1/"
        data = {"payload": "test"}
        mocked_request.get(url, json=data)

        result = hipay_settlement_api.request(url, return_content=False)

        self.assertIsInstance(result, Response)
        self.assertEqual(result.json(), data)

    @responses.activate
    @patch(
        "payment_backends.hipay_tpp.hipay_api.rest_api_interface."
        "RestAPIInterface._log_api_call_error_during_tests",
        lambda *_: None,
    )
    def test_adding_settlement_date_with_process_app_settlements(self):
        application = ApplicationFactory()
        application.payment_settings.payment_backend = HIPAYTPP
        application.payment_settings.save()

        HipayAppConfigurationFactory(application=application)
        conf = HipayAppConfiguration.objects.active().get(application=application.id)

        mo = MerchantOrderFactory(
            application=application,
            order__auto_create_payment=True,
        )

        payment = mo.order.payment

        tx = HipayTransactionFactory(
            application=application, order=mo.order, payment=payment
        )

        payment.authorize()

        responses.add(
            responses.POST,
            f"{conf.RestApiEndpoint}maintenance/transaction/{tx.transaction_reference}",
            match=[
                matchers.urlencoded_params_matcher(
                    {
                        "amount": str(mo.order.amount_vat_included),
                        "operation": "capture",
                        "operation_id": f"CAPTURE:{mo.id}",
                    }
                )
            ],
            json=get_transaction_captured_response(mo.order.amount_vat_included),
        )
        responses.add(
            responses.GET,
            f"{conf.RestApiEndpoint}transaction/{tx.transaction_reference}",
            json=get_transaction_authorized_completed_response(
                mo.order.id, tx.transaction_reference, mo.order.amount_vat_included
            ),
        )
        responses.add(
            responses.GET,
            f"{conf.RestApiEndpoint}transaction?orderid={mo.order.id}",
            json=get_transaction_authorized_completed_response(
                order_id=mo.order.id,
                transaction_reference=tx.transaction_reference,
                amount=mo.order.amount_vat_included,
            ),
        )
        import re

        responses.add(
            responses.GET,
            re.compile(f"{conf.RestApiEndpoint}transaction\\?orderid=(local|docker).*"),
            json=get_transaction_authorized_completed_response(
                order_id=f"local{mo.order.external_id}",
                transaction_reference=tx.transaction_reference,
                amount=mo.order.amount_vat_included,
            ),
        )

        payment.actions.collect_amount(amount=mo.amount_vat_included, merchant_order=mo)

        settlements_dict = build_settlements_dict(mo.order.amount_vat_included)

        responses.add(
            responses.GET,
            conf.RestApiFinanceUrl + "settlement",
            json=settlements_dict,
            status=200,
        )

        sale_operation_dict = build_sale_operation_dict(
            [mo],
            build_operation_id([mo]),
            mo.order.amount_vat_included,
        )

        responses.add(
            responses.GET,
            conf.RestApiFinanceUrl + "settlement/42/raw.json",
            json=[sale_operation_dict],
            status=200,
        )

        before_process = timezone.now()
        process_app_settlements(
            conf,
            isoparse("2022-04-01T17:07:01+00:00"),
            isoparse("2022-04-01T18:07:01+00:00"),
        )
        after_process = timezone.now()

        date_value = isoparse(sale_operation_dict["date_value"])

        tx = Transaction.objects.get(
            merchant_order=mo, transaction_type=TRANSACTION_SALE
        )
        self.assertEqual(tx.settlement_date, date_value)
        self.assertTrue(
            before_process < tx.settlement_notification_date < after_process
        )

    @responses.activate
    @patch(
        "payment_backends.hipay_tpp.hipay_api.rest_api_interface."
        "RestAPIInterface._log_api_call_error_during_tests",
        lambda *_: None,
    )
    def test_process_settlements_twice_does_not_update_existing_settlement_notif_date(
        self,
    ):
        application = ApplicationFactory()
        application.payment_settings.payment_backend = HIPAYTPP
        application.payment_settings.save()

        HipayAppConfigurationFactory(application=application)
        conf = HipayAppConfiguration.objects.active().get(application=application.id)

        mo = MerchantOrderFactory(
            application=application,
            order__auto_create_payment=True,
        )

        payment = mo.order.payment

        tx = HipayTransactionFactory(
            application=application, order=mo.order, payment=payment
        )

        payment.authorize()
        responses.add(
            responses.POST,
            f"{conf.RestApiEndpoint}maintenance/transaction/{tx.transaction_reference}",
            match=[
                matchers.urlencoded_params_matcher(
                    {
                        "amount": str(mo.order.amount_vat_included),
                        "operation": "capture",
                        "operation_id": f"CAPTURE:{mo.id}",
                    }
                )
            ],
            json=get_transaction_captured_response(mo.order.amount_vat_included),
        )
        responses.add(
            responses.GET,
            f"{conf.RestApiEndpoint}transaction/{tx.transaction_reference}",
            json=get_transaction_authorized_completed_response(
                mo.order.id, tx.transaction_reference, mo.order.amount_vat_included
            ),
        )
        import re

        responses.add(
            responses.GET,
            re.compile(f"{conf.RestApiEndpoint}transaction\\?orderid=(local|docker).*"),
            json=get_transaction_authorized_completed_response(
                order_id=f"local{mo.order.external_id}",
                transaction_reference=tx.transaction_reference,
                amount=mo.order.amount_vat_included,
            ),
        )
        payment.actions.collect_amount(amount=mo.amount_vat_included, merchant_order=mo)

        settlements_dict = build_settlements_dict(mo.order.amount_vat_included)

        responses.add(
            responses.GET,
            conf.RestApiFinanceUrl + "settlement",
            json=settlements_dict,
            status=200,
        )

        sale_operation_dict = build_sale_operation_dict(
            [mo],
            build_operation_id([mo]),
            mo.order.amount_vat_included,
        )

        responses.add(
            responses.GET,
            conf.RestApiFinanceUrl + "settlement/42/raw.json",
            json=[sale_operation_dict],
            status=200,
        )

        before_first_process = timezone.now()
        process_app_settlements(
            conf,
            isoparse("2022-04-01T17:07:01+00:00"),
            isoparse("2022-04-01T18:07:01+00:00"),
        )
        after_first_process = timezone.now()

        # calling another time
        process_app_settlements(
            conf,
            isoparse("2022-04-01T17:07:01+00:00"),
            isoparse("2022-04-01T18:07:01+00:00"),
        )

        tx = Transaction.objects.get(
            merchant_order=mo, transaction_type=TRANSACTION_SALE
        )
        self.assertTrue(
            before_first_process < tx.settlement_notification_date < after_first_process
        )
