# -*- coding: utf-8 -*-


from apps.ice_applications.app_conf_settings import EnableKycV2
from apps.kyc.models import MerchantIdentityDocument
from apps.testing.factories import (
    ApplicationFactory,
    HipayAppConfigurationFactory,
    HipayMerchantBankAccountKycV1Factory,
    HipayMerchantBankAccountKycV2Factory,
    HipayWalletAccountFactory,
    IdentityDocumentPartFactory,
    KycDocumentPartFactory,
    KycInformationFactory,
    MerchantAddressFactory,
    MerchantBankAccountFactory,
    MerchantIdentityDocumentFactory,
)
from django.test import override_settings
from ims.tests import BaseTestCase
from mock import Mock, patch
from reference.status import DOCUMENT_UPLOADED
from requests import HTTPError, Response

from ..exceptions import HipayAccountCreationError, HipayInternalError
from ..references import (
    ACCOUNT_CREATION_FAILED,
    ACCOUNT_CREATION_SYNCHRONIZED,
    HIPAY_BANK_ACCOUNT_DETAILS,
    HIPAY_BANK_INFOS_STATUSES_INVALID,
    HIPAY_BANK_INFOS_STATUSES_WAITING,
    HIPAY_ID_DOCUMENT,
)
from ..tasks import (
    HipayPostCreationTasks,
    refresh_kyc_states,
    select_bank_account,
    sync_all_kyc,
)


def create_pending_wallet_account(merchant):
    return HipayWalletAccountFactory.create(
        application=merchant.application,
        merchant=merchant,
        user_account_id=None,
        user_space_id=None,
        api_login=None,
        api_password=None,
    )


def create_created_wallet_account(merchant):
    return HipayWalletAccountFactory.create(
        application=merchant.application,
        merchant=merchant,
    )


@patch("socket.socket.connect", lambda *_: "*******")
class TestHipayPostCreationTasks(BaseTestCase):
    def setUp(self):
        self.address = MerchantAddressFactory.create()
        self.account = create_pending_wallet_account(self.address.merchant)
        self.conf = Mock()

    def test_should_run_transition_create_when_hipay_call_succeed(self):
        self.conf.cashout_api.create_wallet_account.return_value = {
            "wslogin": "login",
            "wspassword": "password",
            "account_id": "42",
            "user_space_id": "4242",
        }
        self.conf.cashout_api.get_last_active_merchant_address.return_value = (
            self.address
        )

        HipayPostCreationTasks(self.account, conf=self.conf).create_hipay_account()

        self.account.refresh_from_db()
        self.assertEqual(self.account.synchro_status, ACCOUNT_CREATION_SYNCHRONIZED)

    def test_should_run_fail_transition_when_hipay_call_failed(self):
        expected_errors = {"field": "bad value"}
        self.conf.cashout_api.create_wallet_account.side_effect = (
            HipayAccountCreationError({"errors": expected_errors})
        )
        self.conf.cashout_api.get_last_active_merchant_address.return_value = (
            self.address
        )

        HipayPostCreationTasks(self.account, conf=self.conf).create_hipay_account()

        self.account.refresh_from_db()
        self.assertEqual(self.account.synchro_status, ACCOUNT_CREATION_FAILED)
        self.assertDictEqual(self.account.synchro_error, {"__all__": ["bad value"]})

    def test_synchro_error_with_message(self):
        expected_message = "You did shit"
        self.conf.cashout_api.create_wallet_account.side_effect = (
            HipayAccountCreationError({"message": expected_message})
        )
        self.conf.cashout_api.get_last_active_merchant_address.return_value = (
            self.address
        )

        HipayPostCreationTasks(self.account, conf=self.conf).create_hipay_account()

        self.account.refresh_from_db()
        self.assertEqual(self.account.synchro_status, ACCOUNT_CREATION_FAILED)
        self.assertDictEqual(
            self.account.synchro_error, {"__all__": [expected_message]}
        )

    def test_should_run_transition_invalid_when_validation_failed(self):
        address = MerchantAddressFactory.create()
        account = create_created_wallet_account(
            merchant=address.merchant,
        )
        self.conf.cashout_api.get_hipay_wallet.return_value = {
            "user_account_id": account.user_account_id,
            "user_space_id": account.user_space_id + 1,
        }

        HipayPostCreationTasks(account, conf=self.conf).validation_account()

        account.refresh_from_db()
        self.assertEqual(account.synchro_status, ACCOUNT_CREATION_FAILED)

    def test_should_run_transition_invalid_when_no_account_exists(self):
        address = MerchantAddressFactory.create()
        account = create_created_wallet_account(merchant=address.merchant)
        self.conf.cashout_api.get_hipay_wallet.side_effect = HTTPError()

        HipayPostCreationTasks(account, conf=self.conf).validation_account()

        account.refresh_from_db()
        self.assertEqual(account.synchro_status, ACCOUNT_CREATION_FAILED)

    def test_synchro_error_with_list_of_messages(self):
        self.conf.cashout_api.create_wallet_account.side_effect = (
            HipayAccountCreationError(
                {
                    "code": 400,
                    "message": "Validation Failed",
                    "errors": [
                        {
                            "field": "entity_code",
                            "message": "This entity does not exist.",
                            "code": 222,
                        }
                    ],
                }
            )
        )
        self.conf.cashout_api.get_last_active_merchant_address.return_value = (
            self.address
        )

        HipayPostCreationTasks(self.account, conf=self.conf).create_hipay_account()


class KycRelatedTasksTestCase(BaseTestCase):
    WALLET_MOCK_PATH = (
        "payment_backends.hipay_tpp.models.wallet_account."
        "HipayWalletAccount.clean_application_and_merchant"
    )

    @patch("payment_backends.hipay_tpp.kyc.HipayKycBackend.can_publish")
    @patch("payment_backends.hipay_tpp.kyc.HipayKycBackend.publish")
    def test_sync_all_kyc_v1(self, mocked_publish, mocked_can_publish):
        doc = MerchantIdentityDocumentFactory(
            merchant__application__app_settings={EnableKycV2: False}
        )
        HipayAppConfigurationFactory(application=doc.merchant.application, active=True)
        mocked_can_publish.return_value = True, None

        sync_all_kyc(doc.merchant_id, doc.merchant.application_id)

        mocked_can_publish.assert_called_once_with(doc)
        mocked_publish.assert_called_once_with(doc)

    @patch("payment_backends.hipay_tpp.kyc.HipayKycBackend.can_publish")
    @patch("payment_backends.hipay_tpp.kyc.HipayKycBackend.publish")
    def test_sync_all_kyc_v1_can_not_publish(self, mocked_publish, mocked_can_publish):
        doc = MerchantIdentityDocumentFactory(
            merchant__application__app_settings={EnableKycV2: False}
        )
        HipayAppConfigurationFactory(application=doc.merchant.application, active=True)
        mocked_can_publish.return_value = False, "Invalid"

        sync_all_kyc(doc.merchant_id, doc.merchant.application_id)

        mocked_can_publish.assert_called_once_with(doc)
        mocked_publish.assert_not_called()

    @patch("payment_backends.hipay_tpp.kyc.HipayKycBackend.can_publish")
    @patch("payment_backends.hipay_tpp.kyc.HipayKycBackend.publish")
    def test_sync_all_kyc_v2(self, mocked_publish, mocked_can_publish):
        app = ApplicationFactory()
        HipayAppConfigurationFactory(application=app, active=True)
        app.set_setting(EnableKycV2, True)
        doc = KycInformationFactory(
            merchant__application=app, merchant__default_currency=app.default_currency
        )
        mocked_can_publish.return_value = True, None

        sync_all_kyc(doc.merchant_id, doc.merchant.application_id)

        mocked_can_publish.assert_called_once_with(doc)
        mocked_publish.assert_called_once_with(doc)

    @patch("payment_backends.hipay_tpp.kyc.HipayKycBackend.can_publish")
    @patch("payment_backends.hipay_tpp.kyc.HipayKycBackend.publish")
    def test_sync_all_kyc_v2_can_not_publish(self, mocked_publish, mocked_can_publish):
        app = ApplicationFactory()
        HipayAppConfigurationFactory(application=app, active=True)
        app.set_setting(EnableKycV2, True)
        doc = KycInformationFactory(
            merchant__application=app, merchant__default_currency=app.default_currency
        )
        mocked_can_publish.return_value = False, "Not valid"

        sync_all_kyc(doc.merchant_id, doc.merchant.application_id)

        mocked_can_publish.assert_called_once_with(doc)
        mocked_publish.assert_not_called()

    def test_select_bank_account_success_v1(self):
        app = ApplicationFactory()
        app.set_setting(EnableKycV2, False)
        HipayAppConfigurationFactory(application=app, active=True)
        hipay_bank_account = HipayMerchantBankAccountKycV1Factory(
            store_bank_account=None,
            merchant__application=app,
            merchant__default_currency=app.default_currency,
        )
        with patch(self.WALLET_MOCK_PATH):
            HipayWalletAccountFactory(
                application=hipay_bank_account.merchant.application,
                merchant=hipay_bank_account.merchant,
            )
        store_bank_account = MerchantBankAccountFactory(
            merchant=hipay_bank_account.merchant
        )
        doc = IdentityDocumentPartFactory(
            document__merchant=hipay_bank_account.merchant
        ).document

        mock_api = (
            "payment_backends.hipay_tpp.hipay_api.cashout.HipayCashoutApi.request"
        )
        with patch(mock_api) as mocked_api:
            HIPAY_VALIDATION_IN_PROGRESS = 2
            mocked_api.return_value = {"status_code": HIPAY_VALIDATION_IN_PROGRESS}
            select_bank_account(
                hipay_bank_account.id,
                store_bank_account.id,
                doc.id,
                app.id,
            )

        hipay_bank_account.refresh_from_db()
        self.assertEqual(hipay_bank_account.store_bank_account, store_bank_account)
        self.assertEqual(
            hipay_bank_account.synchro_status, HIPAY_BANK_INFOS_STATUSES_WAITING
        )
        doc.refresh_from_db()
        self.assertEqual(doc.external_id, str(HIPAY_BANK_ACCOUNT_DETAILS))
        self.assertTrue(doc.kyc_backend_status.is_under_analysis)

    @patch(
        "payment_backends.hipay_tpp.actions.bank_account.manager.logger.exception",
        lambda *_, **__: None,
    )
    def test_select_bank_account_failure_v1(self):
        app = ApplicationFactory()
        app.set_setting(EnableKycV2, False)
        HipayAppConfigurationFactory(application=app, active=True)
        hipay_bank_account = HipayMerchantBankAccountKycV1Factory(
            store_bank_account=None,
            merchant__application=app,
            merchant__default_currency=app.default_currency,
        )
        with patch(self.WALLET_MOCK_PATH):
            HipayWalletAccountFactory(
                application=hipay_bank_account.merchant.application,
                merchant=hipay_bank_account.merchant,
            )
        store_bank_account = MerchantBankAccountFactory(
            merchant=hipay_bank_account.merchant
        )
        doc = IdentityDocumentPartFactory(
            document__merchant=hipay_bank_account.merchant
        ).document

        mock_api = (
            "payment_backends.hipay_tpp.hipay_api.cashout.HipayCashoutApi.request"
        )
        with patch(mock_api) as mocked_api:
            mocked_api.side_effect = HTTPError("An error occured")
            mocked_api.side_effect.response = Response()
            mocked_api.side_effect.response._content = b"{}"
            select_bank_account(
                hipay_bank_account.id,
                store_bank_account.id,
                doc.id,
                app.id,
            )

        hipay_bank_account.refresh_from_db()
        self.assertIsNone(hipay_bank_account.store_bank_account)
        self.assertEqual(
            hipay_bank_account.synchro_status, HIPAY_BANK_INFOS_STATUSES_INVALID
        )

    def test_select_bank_account_success_v2(self):
        app = ApplicationFactory()
        app.set_setting(EnableKycV2, True)
        HipayAppConfigurationFactory(application=app, active=True)
        hipay_bank_account = HipayMerchantBankAccountKycV2Factory(
            store_bank_account=None,
            merchant__application=app,
            merchant__default_currency=app.default_currency,
        )
        with patch(self.WALLET_MOCK_PATH):
            HipayWalletAccountFactory(
                application=hipay_bank_account.merchant.application,
                merchant=hipay_bank_account.merchant,
            )
        store_bank_account = MerchantBankAccountFactory(
            merchant=hipay_bank_account.merchant
        )
        doc = KycDocumentPartFactory(kyc__merchant=hipay_bank_account.merchant).kyc

        mock_api = (
            "payment_backends.hipay_tpp.hipay_api.cashout.HipayCashoutApi.request"
        )
        with patch(mock_api) as mocked_api:
            HIPAY_VALIDATION_IN_PROGRESS = 2
            mocked_api.return_value = {"status_code": HIPAY_VALIDATION_IN_PROGRESS}
            select_bank_account(
                hipay_bank_account.id,
                store_bank_account.id,
                str(doc.id),
                app.id,
            )

        hipay_bank_account.refresh_from_db()
        self.assertEqual(hipay_bank_account.store_bank_account, store_bank_account)
        self.assertEqual(
            hipay_bank_account.synchro_status, HIPAY_BANK_INFOS_STATUSES_WAITING
        )
        doc.refresh_from_db()
        self.assertEqual(doc.external_id, str(HIPAY_BANK_ACCOUNT_DETAILS))
        self.assertTrue(doc.kyc_backend_status.is_under_analysis)

    @patch(
        "payment_backends.hipay_tpp.actions.bank_account.manager.logger.exception",
        lambda *_, **__: None,
    )
    def test_select_bank_account_failure_v2(self):
        app = ApplicationFactory()
        app.set_setting(EnableKycV2, True)
        HipayAppConfigurationFactory(application=app, active=True)
        hipay_bank_account = HipayMerchantBankAccountKycV2Factory(
            store_bank_account=None,
            merchant__application=app,
            merchant__default_currency=app.default_currency,
        )
        with patch(self.WALLET_MOCK_PATH):
            HipayWalletAccountFactory(
                application=hipay_bank_account.merchant.application,
                merchant=hipay_bank_account.merchant,
            )
        store_bank_account = MerchantBankAccountFactory(
            merchant=hipay_bank_account.merchant
        )
        doc = KycDocumentPartFactory(kyc__merchant=hipay_bank_account.merchant).kyc

        mock_api = (
            "payment_backends.hipay_tpp.hipay_api.cashout.HipayCashoutApi.request"
        )
        with patch(mock_api) as mocked_api:
            mocked_api.side_effect = HTTPError("An error occured")
            mocked_api.side_effect.response = Response()
            mocked_api.side_effect.response._content = b"{}"
            select_bank_account(
                hipay_bank_account.id,
                store_bank_account.id,
                doc.id,
                app.id,
            )

        hipay_bank_account.refresh_from_db()
        self.assertIsNone(hipay_bank_account.store_bank_account)
        self.assertEqual(
            hipay_bank_account.synchro_status, HIPAY_BANK_INFOS_STATUSES_INVALID
        )


class RefreshKycStatesTestCase(BaseTestCase):
    WALLET_MOCK_PATH = (
        "payment_backends.hipay_tpp.models.wallet_account."
        "HipayWalletAccount.clean_application_and_merchant"
    )

    @patch("payment_backends.hipay_tpp.hipay_api.kyc.HipayKycApi.resync_doc_statuses")
    def test_ignore_staging_config(self, mocked_resync):
        HipayAppConfigurationFactory(active=True, staging=True)

        refresh_kyc_states()

        mocked_resync.assert_not_called()

    @patch("payment_backends.hipay_tpp.tasks.logger.exception")
    @patch("payment_backends.hipay_tpp.hipay_api.kyc.HipayKycApi.resync_doc_statuses")
    @override_settings(RAISE_SILENT_ERRORS=False)
    def test_continue_on_error(self, mocked_resync, mocked_logger):
        HipayAppConfigurationFactory(active=True, staging=False)
        HipayAppConfigurationFactory(active=True, staging=False)
        mocked_resync.side_effect = Exception("Mouahaha")
        refresh_kyc_states()

        self.assertEqual(mocked_resync.call_count, 2)
        self.assertEqual(mocked_logger.call_count, 2)

    @patch(
        "payment_backends.hipay_tpp.hipay_api.cashout.HipayCashoutApi.is_identified",
        lambda _, __: True,
    )
    def test_already_identified_account(self):
        config = HipayAppConfigurationFactory(active=True, staging=False)
        with patch(self.WALLET_MOCK_PATH):
            HipayWalletAccountFactory(application=config.application)

        refresh_kyc_states()

    @patch(
        "payment_backends.hipay_tpp.hipay_api.cashout.HipayCashoutApi.is_identified",
        lambda _, __: False,
    )
    @patch("payment_backends.hipay_tpp.hipay_api.kyc.HipayKycApi.get_all_statuses")
    def test_unidentified_account_with_api_error(self, mocked_get_statuses):
        config = HipayAppConfigurationFactory(active=True, staging=False)
        with patch(self.WALLET_MOCK_PATH):
            HipayWalletAccountFactory(application=config.application)
        mocked_get_statuses.return_value = {"code": 1}

        with self.assertRaises(HipayInternalError):
            refresh_kyc_states()

    @patch(
        "payment_backends.hipay_tpp.hipay_api.cashout.HipayCashoutApi.is_identified",
        lambda _, __: False,
    )
    @patch("payment_backends.hipay_tpp.hipay_api.kyc.HipayKycApi.get_all_statuses")
    @override_settings(LANGUAGE_CODE="en-US", LANGUAGES=(("en", "English"),))
    def test_unidentified_account_status_not_changed_v1_doc(
        self,
        mocked_get_statuses,
    ):
        app = ApplicationFactory()
        app.set_setting(EnableKycV2, False)
        config = HipayAppConfigurationFactory(
            application=app, active=True, staging=False
        )
        with patch(self.WALLET_MOCK_PATH):
            wallet = HipayWalletAccountFactory(
                application=config.application,
            )
        mocked_get_statuses.return_value = {
            "code": 0,
            "documents": [
                {"type": HIPAY_ID_DOCUMENT, "status_code": 0, "status_label": "label"}
            ],
        }
        MerchantIdentityDocumentFactory(
            merchant=wallet.merchant,
            external_id=HIPAY_ID_DOCUMENT,
            status=MerchantIdentityDocument.INITIAL,
            kyc_backend_status=DOCUMENT_UPLOADED,
            kyc_backend_message=(
                "The document has been uploaded but not sent to hipay "
                "validation services"
            ),
        )

        refresh_kyc_states()

    @patch(
        "payment_backends.hipay_tpp.hipay_api.cashout.HipayCashoutApi.is_identified",
        lambda _, __: False,
    )
    @patch("payment_backends.hipay_tpp.hipay_api.kyc.HipayKycApi.get_all_statuses")
    @override_settings(LANGUAGE_CODE="en-US", LANGUAGES=(("en", "English"),))
    def test_unidentified_account_status_v1_doc(self, mocked_get_statuses):
        app = ApplicationFactory()
        app.set_setting(EnableKycV2, False)
        config = HipayAppConfigurationFactory(
            application=app, active=True, staging=False
        )
        with patch(self.WALLET_MOCK_PATH):
            wallet = HipayWalletAccountFactory(
                application=config.application,
            )
        mocked_get_statuses.return_value = {
            "code": 0,
            "documents": [
                {"type": HIPAY_ID_DOCUMENT, "status_code": 1, "status_label": "label"}
            ],
        }
        doc = MerchantIdentityDocumentFactory(
            merchant=wallet.merchant,
            external_id=HIPAY_ID_DOCUMENT,
            status=MerchantIdentityDocument.INITIAL,
        )

        refresh_kyc_states()

        doc.refresh_from_db()
        self.assertEqual(
            doc.kyc_backend_message,
            "The document has been sent to HiPay validation services",
        )
        self.assertTrue(doc.kyc_backend_status.is_under_analysis)

    @patch(
        "payment_backends.hipay_tpp.hipay_api.cashout.HipayCashoutApi.is_identified",
        lambda _, __: False,
    )
    @patch("payment_backends.hipay_tpp.hipay_api.kyc.HipayKycApi.get_all_statuses")
    @override_settings(LANGUAGE_CODE="en-US", LANGUAGES=(("en", "English"),))
    def test_unidentified_account_status_not_changed_v2_doc(
        self,
        mocked_get_statuses,
    ):
        app = ApplicationFactory()
        app.set_setting(EnableKycV2, True)
        config = HipayAppConfigurationFactory(
            application=app, active=True, staging=False
        )
        with patch(self.WALLET_MOCK_PATH):
            wallet = HipayWalletAccountFactory(
                application=config.application,
            )
        mocked_get_statuses.return_value = {
            "code": 0,
            "documents": [
                {"type": HIPAY_ID_DOCUMENT, "status_code": 0, "status_label": "label"}
            ],
        }
        KycInformationFactory(
            merchant=wallet.merchant,
            external_id=HIPAY_ID_DOCUMENT,
            kyc_backend_status=DOCUMENT_UPLOADED,
            kyc_backend_message=(
                "The document has been uploaded but not sent to hipay "
                "validation services"
            ),
        )

        refresh_kyc_states()

    @patch(
        "payment_backends.hipay_tpp.hipay_api.cashout.HipayCashoutApi.is_identified",
        lambda _, __: False,
    )
    @patch("payment_backends.hipay_tpp.hipay_api.kyc.HipayKycApi.get_all_statuses")
    @override_settings(LANGUAGE_CODE="en-US", LANGUAGES=(("en", "English"),))
    def test_unidentified_account_status_v2_doc(self, mocked_get_statuses):
        app = ApplicationFactory()
        app.set_setting(EnableKycV2, True)
        config = HipayAppConfigurationFactory(
            application=app, active=True, staging=False
        )
        with patch(self.WALLET_MOCK_PATH):
            wallet = HipayWalletAccountFactory(
                application=config.application,
            )
        mocked_get_statuses.return_value = {
            "code": 0,
            "documents": [
                {"type": HIPAY_ID_DOCUMENT, "status_code": 1, "status_label": "label"}
            ],
        }
        doc = KycInformationFactory(
            merchant=wallet.merchant,
            external_id=HIPAY_ID_DOCUMENT,
        )

        refresh_kyc_states()

        doc.refresh_from_db()
        self.assertEqual(
            doc.kyc_backend_message,
            "The document has been sent to HiPay validation services",
        )
        self.assertTrue(doc.kyc_backend_status.is_under_analysis)
