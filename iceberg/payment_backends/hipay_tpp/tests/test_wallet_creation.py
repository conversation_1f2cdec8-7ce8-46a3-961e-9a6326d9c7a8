from apps.testing.factories import (
    ApplicationFactory,
    HipayAppConfigurationFactory,
    HipayWalletAccountFactory,
    KycInformationFactory,
    MerchantAddressFactory,
    MerchantCompanyFactory,
    MerchantFactory,
)
from django.core.exceptions import ValidationError
from ims.tests import BaseTestCase
from mock import Mock, patch
from payment_backends.hipay_tpp.actions.wallet_account import (
    HipayAccountCreator,
    HipayCreationParamsBuilder,
)
from payment_backends.hipay_tpp.actions.wallet_account.manager import (
    WalletAccountActionsManager,
)
from payment_backends.hipay_tpp.hipay_api import Hipay<PERSON>ashoutApi
from payment_backends.hipay_tpp.models import HipayWalletAccount
from payment_backends.hipay_tpp.references import (
    HIPAY_BUSINESS_ACCOUNT_TYPE,
    TITLES_CONSTANTS,
)
from payment_backends.hipay_tpp.utils import get_ip_address
from unidecode import unidecode


class TestHipayCreationParamsBuilder(BaseTestCase):
    def setUp(self):
        self.account_api = Mock()
        self.expected_localed = "fr_FR"
        self.account_api.get_locale_from_lang_code.return_value = self.expected_localed
        self.expected_entity = "entity"
        self.account_api.config.EWalletApiEntity = self.expected_entity
        self.expected_login = "login"
        self.account_api.config.EWalletApiLogin = self.expected_login
        self.expected_password = "password"
        self.account_api.config.EWalletApiPassword = self.expected_password
        super().setUp()

    def test_should_fail_if_no_billing_address_configured(self):
        merchant = MerchantFactory.create()
        MerchantAddressFactory.create(merchant=merchant, billing_address=False)

        with self.assertRaises(ValidationError):
            HipayCashoutApi(None).get_last_active_merchant_address(merchant)

    def test_should_succeed_if_billing_address_configured(self):
        merchant = MerchantFactory.create()
        address = MerchantAddressFactory.create(merchant=merchant, billing_address=True)

        self.assertEqual(
            HipayCashoutApi(None).get_last_active_merchant_address(merchant), address
        )

    def test_params_with_custom_email(self):
        company = MerchantCompanyFactory()
        address = MerchantAddressFactory.create(merchant=company.merchant)
        self.account_api.get_last_active_merchant_address.return_value = address
        with patch.object(HipayWalletAccount, "clean"):
            account = HipayWalletAccountFactory.create(
                application=company.application, merchant=company.merchant
            )
        builder = HipayCreationParamsBuilder(self.account_api)

        params = builder.build_creation_params(account)

        expected_civility = TITLES_CONSTANTS[address.contact_social_reason.lower()]
        self.assertDictEqual(
            params,
            {
                "email": account.email,
                "civility": expected_civility,
                "ip_address": get_ip_address(),
                "firstname": unidecode(address.contact_first_name),
                "lastname": unidecode(address.contact_last_name),
                "currency": company.merchant.default_currency.code,
                "locale": self.expected_localed,
                "entity_code": self.expected_entity,
                "controle_type": "CREDENTIALS",
                "account_type": HIPAY_BUSINESS_ACCOUNT_TYPE,
                "pro_type": account.account_type,
                "company_name": unidecode(company.name),
                "address[address]": " ".join(
                    (unidecode(address.address), unidecode(address.address2))
                ),
                "address[zipcode]": address.zipcode,
                "address[city]": unidecode(address.city),
                "address[country]": company.application.country.code,
                "timezone": company.merchant.timezone,
                "vat_number": company.vat_number,
                "credential[wslogin]": self.expected_login,
                "credential[wspassword]": self.expected_password,
            },
        )

    def test_wallet_creation_uses_merchant_company_country(self):
        company = MerchantCompanyFactory()
        address = MerchantAddressFactory.create(merchant=company.merchant)
        self.account_api.get_last_active_merchant_address.return_value = address
        with patch.object(HipayWalletAccount, "clean"):
            account = HipayWalletAccountFactory.create(
                application=company.application, merchant=company.merchant
            )
        builder = HipayCreationParamsBuilder(self.account_api)

        params = builder.build_creation_params(account)

        self.assertEqual(params["address[country]"], company.country.code)

    def test_wallet_creation_fallbacks_on_application_country(self):
        application = ApplicationFactory()
        company = MerchantCompanyFactory(
            country=None,
            application=application,
        )
        address = MerchantAddressFactory.create(merchant=company.merchant)
        self.account_api.get_last_active_merchant_address.return_value = address
        with patch.object(HipayWalletAccount, "clean"):
            account = HipayWalletAccountFactory.create(
                application=company.application, merchant=company.merchant
            )
        builder = HipayCreationParamsBuilder(self.account_api)

        params = builder.build_creation_params(account)

        self.assertEqual(params["address[country]"], application.country.code)

    def test_params_account_with_no_email_fallback_on_contact_email(self):
        company = MerchantCompanyFactory()
        address = MerchantAddressFactory.create(merchant=company.merchant)
        self.account_api.get_last_active_merchant_address.return_value = address
        with patch.object(HipayWalletAccount, "clean"):
            account = HipayWalletAccountFactory.create(
                application=company.application, merchant=company.merchant, email=""
            )
        builder = HipayCreationParamsBuilder(self.account_api)

        params = builder.build_creation_params(account)

        expected_civility = TITLES_CONSTANTS[address.contact_social_reason.lower()]
        self.assertDictEqual(
            params,
            {
                "email": address.contact_email,
                "civility": expected_civility,
                "ip_address": get_ip_address(),
                "firstname": unidecode(address.contact_first_name),
                "lastname": unidecode(address.contact_last_name),
                "currency": company.merchant.default_currency.code,
                "locale": self.expected_localed,
                "entity_code": self.expected_entity,
                "controle_type": "CREDENTIALS",
                "account_type": HIPAY_BUSINESS_ACCOUNT_TYPE,
                "pro_type": account.account_type,
                "company_name": unidecode(company.name),
                "address[address]": " ".join(
                    (unidecode(address.address), unidecode(address.address2))
                ),
                "address[zipcode]": address.zipcode,
                "address[city]": unidecode(address.city),
                "address[country]": company.country.code,
                "timezone": company.merchant.timezone,
                "vat_number": company.vat_number,
                "credential[wslogin]": self.expected_login,
                "credential[wspassword]": self.expected_password,
            },
        )

    def test_params_account_with_custom_company_name(self):
        merchant = MerchantFactory.create()
        merchant.company.name = "Cômpany name"
        merchant.company.save()
        address = MerchantAddressFactory.create(merchant=merchant)
        self.account_api.get_last_active_merchant_address.return_value = address
        with patch.object(HipayWalletAccount, "clean"):
            account = HipayWalletAccountFactory.create(
                application=merchant.application,
                merchant=merchant,
            )
        builder = HipayCreationParamsBuilder(self.account_api)

        params = builder.build_creation_params(account)

        self.assertEqual(params["company_name"], "Company name")


CONFIGURATION_PATH = (
    "payment_backends.hipay_tpp.models"
    ".application_configuration.HipayAppConfiguration.cashout_api"
)


class TestWalletCreation(BaseTestCase):
    def test_should_raise_validation_error_if_merchant_already_have_wallet(self):
        address = MerchantAddressFactory()
        wallet = HipayWalletAccountFactory(
            application=address.merchant.application, merchant=address.merchant
        )

        with self.assertRaises(ValidationError):
            WalletAccountActionsManager.validate_creation(
                wallet.merchant, wallet.merchant.application
            )

    def test_should_not_raise_validation_error_on_first_creation(self):
        merchant = MerchantFactory.create()

        WalletAccountActionsManager.validate_creation(merchant, merchant.application)

    def test_should_raise_validation_error_if_no_address(self):
        merchant = MerchantFactory.create()
        HipayAppConfigurationFactory.create(application=merchant.application)

        with self.assertRaises(ValidationError):
            WalletAccountActionsManager.validate_creation(
                merchant, merchant.application, check_psp=True
            )

    @patch(CONFIGURATION_PATH)
    def test_should_raise_validation_error_when_email_not_available(self, mocked):
        merchant = MerchantFactory.create()
        MerchantAddressFactory.create(merchant=merchant)
        HipayAppConfigurationFactory.create(application=merchant.application)
        mocked.is_email_available.return_value = (
            dict(code=0, message="description"),
            False,
        )

        with self.assertRaises(ValidationError):
            WalletAccountActionsManager.validate_creation(
                merchant, merchant.application, check_psp=True
            )

    @patch(CONFIGURATION_PATH)
    def test_should_raise_validation_error_when_error_code(self, mocked):
        merchant = MerchantFactory.create()
        MerchantAddressFactory.create(merchant=merchant)
        HipayAppConfigurationFactory.create(application=merchant.application)
        mocked.is_email_available.return_value = (
            dict(code=42, message="description"),
            True,
        )

        with self.assertRaises(ValidationError):
            WalletAccountActionsManager.validate_creation(
                merchant, merchant.application, check_psp=True
            )

    @patch(CONFIGURATION_PATH)
    def test_should_not_raise_validation_error(self, mocked):
        merchant = MerchantFactory.create()
        MerchantAddressFactory.create(merchant=merchant)
        HipayAppConfigurationFactory.create(application=merchant.application)
        mocked.is_email_available.return_value = (
            dict(code=0, message="description"),
            True,
        )

        WalletAccountActionsManager.validate_creation(
            merchant, merchant.application, check_psp=True
        )

    def test_should_update_wallet_account_with_hipay_data(self):
        merchant = MerchantFactory.create()
        MerchantAddressFactory.create(merchant=merchant)
        with patch.object(HipayWalletAccount, "clean"):
            account = HipayWalletAccountFactory.create(
                application=merchant.application, merchant=merchant
            )
        expected_login = "login"
        expected_password = "password"
        expected_account_id = "account_id"
        expected_space_id = "space_id"
        account_api = Mock()
        account_api.create_wallet_account.return_value = {
            "wslogin": expected_login,
            "wspassword": expected_password,
            "account_id": expected_account_id,
            "user_space_id": expected_space_id,
        }
        builder = Mock()
        expected_email = "<EMAIL>"
        builder.build_creation_params.return_value = {"email": expected_email}
        creator = HipayAccountCreator(account_api, builder)

        account = creator.create_account(account)

        self.assertEqual(account.api_login, expected_login)
        self.assertEqual(account.api_password, expected_password)
        self.assertEqual(account.user_account_id, expected_account_id)
        self.assertEqual(account.user_space_id, expected_space_id)
        self.assertEqual(account.email, expected_email)

    @patch("apps.kyc.tasks.publish_merchant_document.delay")
    def test_should_publish_pending_kyc_on_wallet_sync(self, mocked_task):
        account = HipayWalletAccountFactory.create(
            synchro_status="pending",
            api_login=None,
            api_password=None,
            user_account_id=None,
            user_space_id=None,
        )
        kyc_info = KycInformationFactory.create(
            merchant=account.merchant, kyc_backend_status="pending", status="active"
        )
        account.api_login = "non_empty"
        account.api_password = "non_empty"
        account.user_account_id = 123
        account.user_space_id = 123
        account.synchronize()
        mocked_task.assert_called_with(
            str(kyc_info.id),
            account.application_id,
            version=2,
            merchant_id=kyc_info.merchant_id,
            source="wallet-sync",
        )
