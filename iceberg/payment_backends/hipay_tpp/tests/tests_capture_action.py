# -*- coding: utf-8 -*-

from decimal import Decimal
from urllib.parse import quote

import mock
import requests_mock
from apps.payment.models import Payment
from apps.testing.factories import HipayAppConfigurationFactory, HipayTransactionFactory
from ims.tests import BaseTestCase
from mock import patch

from ..exceptions import (
    NoTransactionError,
    TransactionReferenceMismatch,
    UnexpectedHipayError,
)
from ..hipay_api.actions.capture import CaptureAction
from ..models import HipayAppConfiguration
from ..utils import hipay_get_unique_id
from .sample_api_responses import (
    get_transaction_400_response_with_description_response,
    get_transaction_authorized_completed_minimal_response,
    get_transaction_authorized_completed_response,
    get_transaction_capture_amount_limit_exceeded_response,
    get_transaction_capture_declined_response,
    get_transaction_capture_requested_response,
    get_transaction_captured_response,
)


@requests_mock.Mocker()
@patch(
    "payment_backends.hipay_tpp.hipay_api.rest_api_interface."
    "RestAPIInterface._log_api_call_error_during_tests",
    lambda *_: None,
)
class CaptureActionTestCase(BaseTestCase):
    def get_transaction(self, hipay_config, has_tref=True):
        return HipayTransactionFactory(
            application=hipay_config.application,
            captured_amount=Decimal(10),
            transaction_reference=123 if has_tref else None,
        )

    def get_order_id(self, transaction):
        return quote(hipay_get_unique_id(transaction.payment))

    def test_get_transaction_for_payment_missing(self, mocked_request):
        # Given
        hipay_config = HipayAppConfigurationFactory()
        action = CaptureAction(hipay_config.cashin_api)

        # When
        with self.assertRaises(NoTransactionError) as err:
            action.get_transaction_for_payment(payment=Payment())

        # Then
        self.assertEqual(err.exception.args[0], "No transaction for order")

    @patch("payment_backends.hipay_tpp.hipay_api.cashin.HipayCashinApi.capture")
    def test_run_in_unhnandled_mode(self, mocked_request, mocked_capture):
        hipay_config = HipayAppConfigurationFactory(
            application__name="my app",
            cashin_mode=HipayAppConfiguration.CASHIN_MODE_UNHANDLED,
        )
        action = CaptureAction(hipay_config.cashin_api)
        ret = action.run(Payment(), Decimal(10), [mock.Mock()], None)

        self.assertFalse(ret)
        mocked_capture.assert_not_called()

    def test_captured(self, mocked_request):
        hipay_config = HipayAppConfigurationFactory()
        action = CaptureAction(hipay_config.cashin_api)
        tr = self.get_transaction(hipay_config, has_tref=True)
        order_id = self.get_order_id(tr)

        mocked_request.post(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/maintenance/"
            f"transaction/{tr.transaction_reference}",
            json=get_transaction_captured_response(),
        )
        mocked_request.get(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/transaction/"
            f"{tr.transaction_reference}",
            json=get_transaction_authorized_completed_response(
                order_id=order_id,
                amount=Decimal("100"),
                transaction_reference=tr.transaction_reference,
            ),
        )

        mocked_request.get(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/transaction?orderid={order_id}",
            json=get_transaction_authorized_completed_response(
                order_id=order_id,
                transaction_reference=tr.transaction_reference,
                amount=Decimal("100"),
            ),
        )

        ret = action.run(tr.order.payment, Decimal(10), [mock.Mock()], None)

        self.assertTrue(ret)

    def test_capture_already_requested(self, mocked_request):
        """Test that CaptureAction.run() does NOT send a new capture request if status=117"""

        hipay_config = HipayAppConfigurationFactory()
        action = CaptureAction(hipay_config.cashin_api)
        tr = self.get_transaction(hipay_config, has_tref=True)

        tr.status = 117
        tr.save()

        mocked_request.get(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/transaction/{tr.transaction_reference}",
            json=get_transaction_capture_requested_response(
                amount=Decimal("100"),
            ),
        )

        mocked_request.get(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/transaction?orderid={self.get_order_id(tr)}",
            json=get_transaction_capture_requested_response(amount=Decimal("100")),
        )

        mocked_post = mocked_request.post(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/maintenance/transaction/{tr.transaction_reference}",
            json=get_transaction_authorized_completed_response(
                order_id=self.get_order_id(tr),
                amount=Decimal("100"),
                transaction_reference=tr.transaction_reference,
            ),
        )

        ret = action.run(tr.order.payment, Decimal(10), [mock.Mock()], None)
        self.assertFalse(ret)
        self.assertFalse(
            mocked_post.called,
            "Une requête de capture a été envoyée alors qu'elle ne devait pas l'être !",
        )

    def test_capture_declined(self, mocked_request):
        hipay_config = HipayAppConfigurationFactory()
        action = CaptureAction(hipay_config.cashin_api)
        tr = self.get_transaction(hipay_config, has_tref=True)
        order_id = self.get_order_id(tr)

        mocked_request.post(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/maintenance/"
            f"transaction/{tr.transaction_reference}",
            json=get_transaction_capture_declined_response(),
        )
        mocked_request.get(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/transaction/"
            f"{tr.transaction_reference}",
            json=get_transaction_authorized_completed_response(
                order_id=order_id, amount=Decimal("100")
            ),
        )
        mocked_request.get(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/transaction?orderid={order_id}",
            json=get_transaction_authorized_completed_response(
                order_id=order_id,
                transaction_reference=tr.transaction_reference,
                amount=Decimal("100"),
            ),
        )

        with self.assertRaises(UnexpectedHipayError) as err:
            action.run(tr.order.payment, Decimal(10), [mock.Mock()], None)

        self.assertEqual(
            err.exception.as_dict(),
            {
                "error_description": "Unexpected Hipay Error: [173] Capture Refused",
                "error_code": "HIPAY.UNEXPECTED_ERROR",
                "error_context": {
                    "hipay_status": "173",
                    "hipay_message": "Capture Refused",
                },
            },
        )

    def test_unknown_status(self, mocked_request):
        hipay_config = HipayAppConfigurationFactory()
        action = CaptureAction(hipay_config.cashin_api)
        tr = self.get_transaction(hipay_config, has_tref=True)
        order_id = self.get_order_id(tr)
        data = get_transaction_capture_declined_response()
        data["status"] = "unknown"
        mocked_request.post(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/maintenance/"
            f"transaction/{tr.transaction_reference}",
            json=data,
        )
        mocked_request.get(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/transaction/"
            f"{tr.transaction_reference}",
            json=get_transaction_authorized_completed_response(
                order_id=order_id, amount=Decimal("100")
            ),
        )

        mocked_request.get(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/transaction?orderid={order_id}",
            json=get_transaction_authorized_completed_response(
                order_id=order_id,
                transaction_reference=tr.transaction_reference,
                amount=Decimal("100"),
            ),
        )

        with self.assertRaises(UnexpectedHipayError) as err:
            action.run(tr.order.payment, Decimal(10), [mock.Mock()], None)

        self.assertEqual(
            err.exception.as_dict(),
            {
                "error_description": (
                    "Unexpected Hipay Error: [unknown] Capture Refused"
                ),
                "error_code": "HIPAY.UNEXPECTED_ERROR",
                "error_context": {
                    "hipay_status": "unknown",
                    "hipay_message": "Capture Refused",
                },
            },
        )

    def test_missing_status(self, mocked_request):
        hipay_config = HipayAppConfigurationFactory()
        action = CaptureAction(hipay_config.cashin_api)
        tr = self.get_transaction(hipay_config, has_tref=True)
        order_id = self.get_order_id(tr)
        data = get_transaction_capture_declined_response()
        del data["status"]
        mocked_request.post(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/maintenance/"
            f"transaction/{tr.transaction_reference}",
            json=data,
        )
        mocked_request.get(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/transaction/"
            f"{tr.transaction_reference}",
            json=get_transaction_authorized_completed_response(
                order_id=order_id, amount=Decimal("100")
            ),
        )
        mocked_request.get(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/transaction?orderid={order_id}",
            json=get_transaction_authorized_completed_response(
                order_id=order_id,
                transaction_reference=tr.transaction_reference,
                amount=Decimal("100"),
            ),
        )

        with self.assertRaises(UnexpectedHipayError) as err:
            action.run(tr.order.payment, Decimal(10), [mock.Mock()], None)

        self.assertEqual(
            err.exception.as_dict(),
            {
                "error_description": "Unexpected Hipay Error: Capture Refused",
                "error_code": "HIPAY.UNEXPECTED_ERROR",
                "error_context": {
                    "hipay_status": None,
                    "hipay_message": "Capture Refused",
                },
            },
        )

    def test_missing_message(self, mocked_request):
        hipay_config = HipayAppConfigurationFactory()
        action = CaptureAction(hipay_config.cashin_api)
        tr = self.get_transaction(hipay_config, has_tref=True)
        order_id = self.get_order_id(tr)
        data = get_transaction_capture_declined_response()
        del data["message"]
        mocked_request.post(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/maintenance/"
            f"transaction/{tr.transaction_reference}",
            json=data,
        )
        mocked_request.get(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/transaction/"
            f"{tr.transaction_reference}",
            json=get_transaction_authorized_completed_response(
                order_id=order_id, amount=Decimal("100")
            ),
        )
        mocked_request.get(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/transaction?orderid={order_id}",
            json=get_transaction_authorized_completed_response(
                order_id=order_id,
                transaction_reference=tr.transaction_reference,
                amount=Decimal("100"),
            ),
        )

        with self.assertRaises(UnexpectedHipayError) as err:
            action.run(tr.order.payment, Decimal(10), [mock.Mock()], None)

        self.assertEqual(
            err.exception.as_dict(),
            {
                "error_description": "Unexpected Hipay Error: [173] Unknown error",
                "error_code": "HIPAY.UNEXPECTED_ERROR",
                "error_context": {"hipay_status": "173", "hipay_message": None},
            },
        )

    def test_capture_bad_request(self, mocked_request):
        hipay_config = HipayAppConfigurationFactory()
        action = CaptureAction(hipay_config.cashin_api)
        tr = self.get_transaction(hipay_config, has_tref=True)
        mocked_request.get(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/transaction/{tr.transaction_reference}",
            json=get_transaction_authorized_completed_minimal_response(
                order_id=self.get_order_id(tr),
                amount=Decimal("100.00"),
                transaction_reference=tr.transaction_reference,
            ),
        )
        mocked_request.get(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/transaction?orderid={self.get_order_id(tr)}",
            json=get_transaction_authorized_completed_minimal_response(
                order_id=self.get_order_id(tr),
                amount=Decimal("100.00"),
                transaction_reference=tr.transaction_reference,
            ),
        )

        mocked_request.post(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/maintenance/"
            f"transaction/{tr.transaction_reference}",
            json=get_transaction_capture_amount_limit_exceeded_response(),
            status_code=400,
        )

        with self.assertRaises(UnexpectedHipayError) as err:
            action.run(tr.order.payment, Decimal(10), [mock.Mock()], None)

        self.assertEqual(
            err.exception.as_dict(),
            {
                "error_description": (
                    "Unexpected Hipay Error: [3020109] Amount Limit Exceeded"
                ),
                "error_code": "HIPAY.UNEXPECTED_ERROR",
                "error_context": {
                    "hipay_status": "3020109",
                    "hipay_message": "Amount Limit Exceeded",
                },
            },
        )

    def test_capture_bad_request_with_description(self, mocked_request):
        hipay_config = HipayAppConfigurationFactory()
        action = CaptureAction(hipay_config.cashin_api)
        tr = self.get_transaction(hipay_config, has_tref=True)

        mocked_request.get(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/transaction?orderid={self.get_order_id(tr)}",
            json=get_transaction_authorized_completed_minimal_response(
                order_id=self.get_order_id(tr),
                transaction_reference=tr.transaction_reference,
                amount=Decimal("100.00"),
            ),
        )
        mocked_request.post(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/maintenance/"
            f"transaction/{tr.transaction_reference}",
            json=get_transaction_400_response_with_description_response(),
            status_code=400,
        )

        with self.assertRaises(UnexpectedHipayError) as err:
            action.run(tr.order.payment, Decimal(10), [mock.Mock()], None)

        self.assertEqual(
            err.exception.as_dict(),
            {
                "error_description": (
                    "Unexpected Hipay Error: [1010205] The 'amount' parameter "
                    "is expected to be greater than 0.005"
                ),
                "error_code": "HIPAY.UNEXPECTED_ERROR",
                "error_context": {
                    "hipay_status": "1010205",
                    "hipay_message": (
                        "The 'amount' parameter is expected to be greater than 0.005"
                    ),
                },
            },
        )


@requests_mock.Mocker()
@patch(
    "payment_backends.hipay_tpp.hipay_api.rest_api_interface."
    "RestAPIInterface._log_api_call_error_during_tests",
    lambda *_: None,
)
class GetAndPropagateTransactionReferenceTestCase(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.hipay_config = HipayAppConfigurationFactory()
        self.action = CaptureAction(self.hipay_config.cashin_api)

    def get_transaction(self, has_tref=True):
        return HipayTransactionFactory(
            application=self.hipay_config.application,
            captured_amount=Decimal(10),
            transaction_reference=123 if has_tref else None,
        )

    def get_order_id(self, transaction):
        return quote(hipay_get_unique_id(transaction.payment))

    def test_tr_ref_missing_in_tr_and_payment(self, mocked_request):
        tr = self.get_transaction(has_tref=False)
        order_id = self.get_order_id(tr)
        mocked_request.get(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/transaction?"
            f"orderid={order_id}",
            json=get_transaction_authorized_completed_response(
                order_id=order_id, amount=Decimal("100")
            ),
        )
        payment = mock.Mock()
        payment.external_id = None

        ret = self.action.get_and_propagate_transaction_reference(tr, payment)

        self.assertIsNone(ret)
        self.assertEqual(mocked_request.call_count, 1)

    def test_transaction_ref_missing(self, mocked_request):
        tr = self.get_transaction(has_tref=False)
        order_id = self.get_order_id(tr)
        mocked_request.get(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/transaction?"
            f"orderid={order_id}",
            json=get_transaction_authorized_completed_response(
                order_id=order_id, amount=Decimal("100")
            ),
        )
        payment = mock.Mock()
        payment.external_id = 123

        ret = self.action.get_and_propagate_transaction_reference(tr, payment)

        self.assertEqual(ret, 123)
        self.assertEqual(mocked_request.call_count, 1)

    def test_external_id_missing(self, mocked_request):
        tr = self.get_transaction(has_tref=True)
        payment = mock.Mock()
        payment.external_id = None

        ret = self.action.get_and_propagate_transaction_reference(tr, payment)

        self.assertEqual(mocked_request.call_count, 0)
        self.assertEqual(payment.external_id, 123)
        self.assertEqual(payment.save.called, True)
        self.assertEqual(ret, 123)

    def test_tref_matches_external_id(self, mocked_request):
        tr = self.get_transaction(has_tref=True)
        payment = mock.Mock()
        payment.external_id = 123

        ret = self.action.get_and_propagate_transaction_reference(tr, payment)

        self.assertEqual(mocked_request.call_count, 0)
        self.assertEqual(payment.save.called, False)
        self.assertEqual(ret, 123)

    def test_capture_with_non_matching_tref_raises_error(self, mocked_request):
        tr = self.get_transaction(has_tref=True)
        order_id = self.get_order_id(tr)

        mocked_request.post(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/maintenance/"
            f"transaction/{tr.transaction_reference}",
            json=get_transaction_captured_response(),
        )
        mocked_request.get(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/transaction/"
            f"{tr.transaction_reference}",
            json=get_transaction_authorized_completed_response(
                order_id=order_id, amount=Decimal("100")
            ),
        )

        mocked_request.get(
            f"https://stage-secure-gateway.hipay-tpp.com/rest/v1/transaction?orderid={order_id}",
            json=get_transaction_authorized_completed_response(
                order_id=order_id,
                transaction_reference="11111111111",  # différent de tr.transaction_reference
                amount=Decimal("100"),
            ),
        )

        with self.assertRaises(TransactionReferenceMismatch) as err_ctx:
            self.action.run(tr.order.payment, Decimal(10), [mock.Mock()], None)
        self.assertEqual(
            err_ctx.exception.as_dict(),
            {
                "error_description": "Transaction reference mismatch",
                "error_code": "HIPAY.TRANSACTION_REFERENCE_MISMATCH",
                "error_context": {
                    "existing_reference": "123",
                    "new_reference": "11111111111",
                },
            },
        )
