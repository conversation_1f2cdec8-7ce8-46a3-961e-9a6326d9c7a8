# -*- coding: utf-8 -*-

from urllib.error import HTTPError

from apps.ice_applications.app_conf_settings import EnableKycV2
from apps.kyc.models import MerchantIdentityDocument
from apps.stores.tests import TestMerchantSetupMixin
from apps.testing.factories import (
    HipayAppConfigurationFactory,
    HipayMerchantBankAccountKycV1Factory,
    HipayMerchantBankAccountKycV2Factory,
    HipayWalletAccountFactory,
    IdentityDocumentPartFactory,
    KycDocumentPartFactory,
    MerchantAddressFactory,
    MerchantBankAccountFactory,
)
from django.core.exceptions import ValidationError
from ims.tests import BaseTestCase
from mock import patch
from payment_backends.hipay_tpp.hipay_api import HipayCashoutApi
from payment_backends.hipay_tpp.models import HipayAppConfiguration
from payment_backends.hipay_tpp.references import (
    HIPAY_ACCOUNT_TYPE_PERSON,
    HIPAY_BANK_ACCOUNT_DETAILS,
    HIPAY_BANK_INFOS_STATUSES_INITIAL,
    HIPAY_BANK_INFOS_STATUSES_VALID,
)
from requests import Response

from ..actions import BankAccountActionsManager


class CashoutApiTestCase(TestMerchantSetupMixin, BaseTestCase):
    def setUp(self):
        self.set_up()

    def get_api(self):
        config = HipayAppConfiguration(application=self.application)
        return HipayCashoutApi(config)

    def test_get_last_merchant_address(self):
        merchant = self.create_active_merchant(force_create=True)
        address_1 = merchant.address.last()
        address_1.hide()
        # add a second address
        address_2 = self.create_merchant_address(merchant=merchant, force_create=True)
        api = self.get_api()

        self.assertEqual(api.get_last_active_merchant_address(merchant), address_2)

        # and when no address is set:
        address_2.hide()
        with self.assertRaises(ValidationError):
            api.get_last_active_merchant_address(merchant)

    @patch(
        "payment_backends.hipay_tpp.hipay_api.rest_api_interface."
        "RestAPIInterface.request"
    )
    @patch("payment_backends.hipay_tpp.models.bank_account.HipayBankAccount.get_api")
    def test_account_selection_success_v1(self, mocked_get_api, mocked_request):
        config = HipayAppConfigurationFactory.build()
        mocked_get_api.return_value = config.cashout_api
        mocked_request.return_value = {
            "status": "Validated",
            "bank_name": "LA BANQUE POSTALE",
            "code": 0,
            "bank_country": "FR",
            "status_code": BankAccountActionsManager.HIPAY_VALIDATED,
            "iban": "***************************",
            "message": "Setting bank info succeeded",
            "swift": "ZZZZZZZZZZZ",
        }
        store_bank_account = MerchantBankAccountFactory()
        merchant = store_bank_account.merchant
        MerchantAddressFactory.create(merchant=merchant)
        HipayWalletAccountFactory(
            application=merchant.application,
            merchant=merchant,
            account_type=HIPAY_ACCOUNT_TYPE_PERSON,
        )
        merchant.application.set_setting(EnableKycV2, False)
        hipay_bank_account = HipayMerchantBankAccountKycV1Factory(
            merchant=merchant,
            application=merchant.application,
        )
        identity_document = IdentityDocumentPartFactory(
            document__merchant=merchant,
            document__document_type=MerchantIdentityDocument.BANK_INFOS_COPY,
        ).document
        hipay_bank_account.actions.select(store_bank_account, identity_document)

        hipay_bank_account.refresh_from_db()
        self.assertEqual(hipay_bank_account.store_bank_account, store_bank_account)
        self.assertEqual(hipay_bank_account.identity_document, identity_document)
        self.assertEqual(
            hipay_bank_account.synchro_status, HIPAY_BANK_INFOS_STATUSES_VALID
        )

    @patch(
        "payment_backends.hipay_tpp.hipay_api.rest_api_interface."
        "RestAPIInterface.request"
    )
    @patch("payment_backends.hipay_tpp.models.bank_account.HipayBankAccount.get_api")
    def test_account_selection_fail_v1(self, mocked_config, mocked_response):
        config = HipayAppConfigurationFactory.build()
        mocked_config.return_value = config.cashout_api

        mocked_response.side_effect = HTTPError("", 401, "", {}, None)
        store_bank_account = MerchantBankAccountFactory()
        merchant = store_bank_account.merchant
        merchant.application.set_setting(EnableKycV2, False)
        MerchantAddressFactory.create(merchant=merchant)
        HipayWalletAccountFactory(application=merchant.application, merchant=merchant)
        hipay_bank_account = HipayMerchantBankAccountKycV1Factory(
            merchant=merchant,
            store_bank_account=None,
            identity_document=None,
        )
        identity_document = IdentityDocumentPartFactory(
            document__merchant=merchant,
            document__document_type=MerchantIdentityDocument.BANK_INFOS_COPY,
        ).document

        with self.assertRaises(HTTPError):
            hipay_bank_account.actions.select(store_bank_account, identity_document)

        hipay_bank_account.refresh_from_db()
        self.assertIsNone(hipay_bank_account.store_bank_account)
        self.assertIsNone(hipay_bank_account.identity_document)
        self.assertEqual(
            hipay_bank_account.synchro_status, HIPAY_BANK_INFOS_STATUSES_INITIAL
        )

    @patch(
        "payment_backends.hipay_tpp.hipay_api.rest_api_interface."
        "RestAPIInterface.request"
    )
    @patch("payment_backends.hipay_tpp.models.bank_account.HipayBankAccount.get_api")
    def test_account_selection_success_v2(self, mocked_get_api, mocked_request):
        config = HipayAppConfigurationFactory.build()
        mocked_get_api.return_value = config.cashout_api
        mocked_request.return_value = {
            "status": "Validated",
            "bank_name": "LA BANQUE POSTALE",
            "code": 0,
            "bank_country": "FR",
            "status_code": BankAccountActionsManager.HIPAY_VALIDATED,
            "iban": "***************************",
            "message": "Setting bank info succeeded",
            "swift": "ZZZZZZZZZZZ",
        }
        store_bank_account = MerchantBankAccountFactory()
        merchant = store_bank_account.merchant
        MerchantAddressFactory.create(merchant=merchant)
        HipayWalletAccountFactory(application=merchant.application, merchant=merchant)
        hipay_bank_account = HipayMerchantBankAccountKycV2Factory(merchant=merchant)
        merchant.application.settings_manager.set_setting(EnableKycV2, True)
        document = KycDocumentPartFactory(
            kyc__merchant=merchant,
            kyc__kyc_type__external_id=HIPAY_BANK_ACCOUNT_DETAILS,
        ).kyc
        hipay_bank_account.actions.select(store_bank_account, document)

        hipay_bank_account.refresh_from_db()
        self.assertEqual(hipay_bank_account.store_bank_account, store_bank_account)
        self.assertEqual(hipay_bank_account.kyc_information, document)
        self.assertEqual(
            hipay_bank_account.synchro_status, HIPAY_BANK_INFOS_STATUSES_VALID
        )

    @patch(
        "payment_backends.hipay_tpp.hipay_api.rest_api_interface."
        "RestAPIInterface.request"
    )
    @patch("payment_backends.hipay_tpp.models.bank_account.HipayBankAccount.get_api")
    def test_account_selection_fail_v2(self, mocked_config, mocked_response):
        config = HipayAppConfigurationFactory.build()
        mocked_config.return_value = config.cashout_api

        mocked_response.side_effect = HTTPError("", 401, "", {}, None)
        store_bank_account = MerchantBankAccountFactory()
        merchant = store_bank_account.merchant
        merchant.application.settings_manager.set_setting(EnableKycV2, True)
        MerchantAddressFactory.create(merchant=merchant)
        HipayWalletAccountFactory(application=merchant.application, merchant=merchant)
        hipay_bank_account = HipayMerchantBankAccountKycV2Factory(
            merchant=merchant,
            store_bank_account=None,
            kyc_information=None,
        )
        document = KycDocumentPartFactory(
            kyc__merchant=merchant,
            kyc__kyc_type__external_id=HIPAY_BANK_ACCOUNT_DETAILS,
        ).kyc
        with self.assertRaises(HTTPError):
            hipay_bank_account.actions.select(store_bank_account, document)

        hipay_bank_account.refresh_from_db()
        self.assertIsNone(hipay_bank_account.store_bank_account)
        self.assertIsNone(hipay_bank_account.kyc_information)
        self.assertEqual(
            hipay_bank_account.synchro_status, HIPAY_BANK_INFOS_STATUSES_INITIAL
        )

    @patch("requests.sessions.Session.request")
    @patch("payment_backends.common.utils.RestLogsBuilder.log_request")
    @patch(
        "payment_backends.hipay_tpp.hipay_api.cashout.HipayCashoutApi."
        "_log_api_call_error_during_tests",
        lambda self, url: None,
    )
    def test_get_hipay_wallet(self, mocked_logger, mocked_request=None):
        api = self.get_api()
        mocked_request.return_value = Response()
        mocked_request.return_value.status_code = 200
        mocked_request.return_value._content = b'{"a": "b"}'

        data = api.get_hipay_wallet(None)

        self.assertEqual(data, {"a": "b"})
        mocked_logger.assert_called_once()
