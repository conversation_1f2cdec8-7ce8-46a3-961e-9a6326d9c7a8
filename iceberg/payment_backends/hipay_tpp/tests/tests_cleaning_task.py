# -*- coding: utf-8 -*-
from apps.testing.factories.hipay import HipayApiCallLogFactory
from dateutil.relativedelta import relativedelta
from django.utils import timezone
from ims.tests import BaseTestCase
from payment_backends.hipay_tpp.tasks import delete_old_hipay_api_call_logs


class TestCleaningTasks(BaseTestCase):
    def test_task_ignore_recent_logs(self):
        HipayApiCallLogFactory.create()
        deleted_count = delete_old_hipay_api_call_logs()
        self.assertEqual(deleted_count, 0)

    def test_task_delete_log_older_than_six_month(self):
        log = HipayApiCallLogFactory.create()
        # change created_on
        log.created_on = timezone.now() - relativedelta(days=180)
        log.save()
        deleted_count = delete_old_hipay_api_call_logs()
        self.assertEqual(deleted_count, 1)
