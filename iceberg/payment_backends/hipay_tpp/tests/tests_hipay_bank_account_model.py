# -*- coding: utf-8 -*-


import uuid

import mock
from apps.ice_applications.app_conf_settings import EnableKycV2
from apps.kyc.models import MerchantIdentityDocument
from apps.testing.factories import (
    ApplicationFactory,
    HipayAppConfigurationFactory,
    HipayMerchantBankAccountKycV1Factory,
    HipayMerchantBankAccountKycV2Factory,
    HipayWalletAccountFactory,
    KycInformationFactory,
    MerchantBankAccountFactory,
    MerchantFactory,
    MerchantIdentityDocumentFactory,
)
from django.core.exceptions import ValidationError
from ims.tests import BaseTestCase
from mock import patch
from payment_backends.hipay_tpp.actions import BankAccountSelectionParamsParser
from payment_backends.hipay_tpp.references import (
    HIPAY_BANK_ACCOUNT_DETAILS,
    HIPAY_BANK_INFOS_STATUSES_INITIAL,
    HIPAY_BANK_INFOS_STATUSES_VALID,
    HIPAY_BANK_INFOS_STATUSES_WAITING,
)
from reference.status import DOCUMENT_UNDER_ANALYSIS

from ..actions import BankAccountActionsManager


@patch(
    "payment_backends.hipay_tpp.hipay_api.rest_api_interface."
    "RestAPIInterface.request"
)
@patch("payment_backends.hipay_tpp.models.bank_account.HipayBankAccount.get_api")
@patch("payment_backends.hipay_tpp.hipay_api.cashout.HipayCashoutApi.get_wallet")
class HipayBankAccountTestCase(BaseTestCase):
    def setUp(self, *args, **kwargs):
        self.config = HipayAppConfigurationFactory.create()
        self.wallet = HipayWalletAccountFactory.create(
            application=self.config.application
        )
        self.bank_account = HipayMerchantBankAccountKycV2Factory.create(
            synchro_status=HIPAY_BANK_INFOS_STATUSES_WAITING,
            merchant=self.wallet.merchant,
        )
        self.bank_account.save = mock.Mock()
        super(HipayBankAccountTestCase, self).setUp(*args, **kwargs)

    def test_refresh_bank_account_status_with_valid_synchro_status(
        self, mocked_get_wallet, mocked_get_api, mocked_request
    ):
        self.bank_account.synchro_status = HIPAY_BANK_INFOS_STATUSES_VALID
        mocked_request.return_value = {
            "code": 0,
            "status_code": BankAccountActionsManager.HIPAY_VALIDATED,
        }
        mocked_get_wallet.return_value = self.wallet
        mocked_get_api.return_value = self.config.cashout_api

        self.bank_account.actions.refresh()

        self.assertEqual(
            self.bank_account.synchro_status, HIPAY_BANK_INFOS_STATUSES_VALID
        )

    def test_refresh_valid_bank_account_status_with_ongoing_synchro_status(
        self, mocked_get_wallet, mocked_get_api, mocked_request
    ):
        self.bank_account.synchro_status = HIPAY_BANK_INFOS_STATUSES_VALID
        mocked_request.return_value = {
            "code": 0,
            "status_code": BankAccountActionsManager.HIPAY_VALIDATION_IN_PROGRESS,
        }
        mocked_get_wallet.return_value = self.wallet
        mocked_get_api.return_value = self.config.cashout_api

        self.bank_account.actions.refresh()

        self.assertEqual(
            self.bank_account.synchro_status, HIPAY_BANK_INFOS_STATUSES_WAITING
        )

    def test_refresh_initial_bank_account_status_with_ongoing_synchro_status(
        self, mocked_get_wallet, mocked_get_api, mocked_request
    ):
        self.bank_account.synchro_status = HIPAY_BANK_INFOS_STATUSES_INITIAL
        mocked_request.return_value = {
            "code": 0,
            "status_code": BankAccountActionsManager.HIPAY_VALIDATION_IN_PROGRESS,
        }
        mocked_get_wallet.return_value = self.wallet
        mocked_get_api.return_value = self.config.cashout_api

        self.bank_account.actions.refresh()

        self.assertEqual(
            self.bank_account.synchro_status, HIPAY_BANK_INFOS_STATUSES_WAITING
        )

    def test_refresh_waiting_bank_account_status_with_initial_synchro_status(
        self, mocked_get_wallet, mocked_get_api, mocked_request
    ):
        self.bank_account.synchro_status = HIPAY_BANK_INFOS_STATUSES_WAITING
        mocked_request.return_value = {
            "code": 0,
            "status_code": BankAccountActionsManager.HIPAY_NO_BANK_INFO,
        }
        mocked_get_wallet.return_value = self.wallet
        mocked_get_api.return_value = self.config.cashout_api

        self.bank_account.actions.refresh()

        self.assertEqual(
            self.bank_account.synchro_status, HIPAY_BANK_INFOS_STATUSES_INITIAL
        )


class BankAccountSelectionActionV1TestCase(BaseTestCase):
    WALLET_MOCK_PATH = (
        "payment_backends.hipay_tpp.models.wallet_account."
        "HipayWalletAccount.clean_application_and_merchant"
    )

    def build_mocked_hipay_bank_account(self):
        hipay_bank_account = mock.Mock()
        hipay_bank_account.synchro_status = HIPAY_BANK_INFOS_STATUSES_INITIAL
        hipay_bank_account.merchant = MerchantFactory.create()
        hipay_bank_account.merchant_id = hipay_bank_account.merchant.id
        hipay_bank_account.application = hipay_bank_account.merchant.application
        hipay_bank_account.application_id = hipay_bank_account.application.id
        hipay_bank_account.store_bank_account = None
        hipay_bank_account.identity_document = None
        app = hipay_bank_account.merchant.application
        app.set_setting(EnableKycV2, False)
        return hipay_bank_account

    def test_get_store_bank_account_with_wrong_id(self):
        hba = self.build_mocked_hipay_bank_account()
        with self.assertRaises(ValidationError):
            BankAccountSelectionParamsParser.get_selected_account(
                hba, {"account_id": None}
            )

        with self.assertRaises(ValidationError):
            BankAccountSelectionParamsParser.get_selected_account(
                hba, {"account_id": "zorg"}
            )

        with self.assertRaises(ValidationError):
            BankAccountSelectionParamsParser.get_selected_document(hba, {})

    def test_get_merchant_document_with_wrong_id(self):
        hba = self.build_mocked_hipay_bank_account()
        with self.assertRaises(ValidationError):
            BankAccountSelectionParamsParser.get_selected_document(
                hba, {"document_id": None}
            )

        with self.assertRaises(ValidationError):
            BankAccountSelectionParamsParser.get_selected_document(
                hba, {"document_id": "zorg"}
            )

        with self.assertRaises(ValidationError):
            BankAccountSelectionParamsParser.get_selected_document(hba, {})

    def test_get_store_bank_account_with_good_data(self):
        hba = self.build_mocked_hipay_bank_account()
        mba = MerchantBankAccountFactory.create(merchant=hba.merchant)

        # when
        doc = BankAccountSelectionParamsParser.get_selected_account(
            hba, {"account_id": mba.id}
        )

        # then
        self.assertEqual(doc.id, mba.id)

    def test_get_merchant_identity_document_with_good_data(self):
        hba = self.build_mocked_hipay_bank_account()
        mid = MerchantIdentityDocumentFactory.create(
            merchant=hba.merchant,
            document_type=MerchantIdentityDocument.BANK_INFOS_COPY,
        )
        with patch(self.WALLET_MOCK_PATH):
            HipayWalletAccountFactory(
                application=mid.merchant.application, merchant=mid.merchant
            )
        HipayAppConfigurationFactory(application=mid.merchant.application, active=True)

        # when
        doc = BankAccountSelectionParamsParser.get_selected_document(
            hba, {"document_id": mid.id}
        )

        # then
        self.assertEqual(doc.id, mid.id)

    def test_check_request_coherence_with_wrong_doc(self):
        document = MerchantIdentityDocumentFactory.build(
            merchant__application__app_settings={EnableKycV2: False},
            document_type=MerchantIdentityDocument.ADDRESS_PROOF,
        )
        with patch(self.WALLET_MOCK_PATH):
            wallet = HipayWalletAccountFactory.build(
                application=document.merchant.application, merchant=document.merchant
            )

        # then it fails on call with a ValidationError
        with self.assertRaises(ValidationError):
            BankAccountSelectionParamsParser.check_request_coherence(wallet, document)

    def test_check_request_coherence_with_bank_doc(self):
        document = MerchantIdentityDocumentFactory.create(
            merchant__application__app_settings={EnableKycV2: False},
            document_type=MerchantIdentityDocument.BANK_INFOS_COPY,
        )
        with patch(self.WALLET_MOCK_PATH):
            wallet = HipayWalletAccountFactory.build(
                application=document.merchant.application, merchant=document.merchant
            )

        # then calling it doesn't fail
        BankAccountSelectionParamsParser.check_request_coherence(wallet, document)

    @patch("payment_backends.hipay_tpp.models.bank_account.HipayBankAccount.get_api")
    @patch("payment_backends.hipay_tpp.models.bank_account.HipayBankAccount.get_wallet")
    @patch(
        "payment_backends.hipay_tpp.hipay_api.cashout.HipayCashoutApi."
        "select_merchant_bank_account"
    )
    @patch(
        "payment_backends.hipay_tpp.actions.bank_account.manager."
        "BankAccountActionsManager._handle_success_response",
        lambda _, __: None,
    )
    def test_call_signature_on_run_method(
        self, mocked_select_merchant_bank_account, mocked_get_wallet, mocked_get_api
    ):
        config = HipayAppConfigurationFactory.build()
        wallet = HipayWalletAccountFactory.build()
        mocked_get_wallet.return_value = wallet
        mocked_get_api.return_value = config.cashout_api
        hba = HipayMerchantBankAccountKycV2Factory()
        application = hba.merchant.application
        config.application = application
        application.set_setting(EnableKycV2, False)
        mba = MerchantBankAccountFactory(merchant=hba.merchant)
        mid = MerchantIdentityDocumentFactory(
            merchant=hba.merchant,
            document_type=MerchantIdentityDocument.BANK_INFOS_COPY,
        )

        # when
        hba.actions.select(mba, mid)
        # then the api is called with the right signature and nothing explodes!
        mocked_select_merchant_bank_account.assert_called_with(mba, mid, wallet)

    def test_select_update_doc_external_id_and_backend_status(self):
        hba = self.build_mocked_hipay_bank_account()
        api_mock = mock.Mock()
        api_mock.select_merchant_bank_account.return_value = {
            "status_code": BankAccountActionsManager.HIPAY_VALIDATION_IN_PROGRESS
        }
        hba.get_api.return_value = api_mock
        mid = MerchantIdentityDocumentFactory.create(
            merchant=hba.merchant,
            document_type=MerchantIdentityDocument.BANK_INFOS_COPY,
        )
        mba = MerchantBankAccountFactory(merchant=hba.merchant)
        hba.identity_document = mid

        # when
        BankAccountActionsManager(hba).select(mba, mid)
        mid.refresh_from_db()

        # then
        self.assertEqual(mid.kyc_backend_status, DOCUMENT_UNDER_ANALYSIS)
        self.assertEqual(mid.external_id, str(HIPAY_BANK_ACCOUNT_DETAILS))


class BankAccountSelectionActionV2TestCase(BaseTestCase):
    WALLET_MOCK_PATH = (
        "payment_backends.hipay_tpp.models.wallet_account."
        "HipayWalletAccount.clean_application_and_merchant"
    )

    def build_mocked_hipay_bank_account(self):
        hipay_bank_account = mock.Mock()
        hipay_bank_account.synchro_status = HIPAY_BANK_INFOS_STATUSES_INITIAL
        hipay_bank_account.merchant = MerchantFactory.create()
        hipay_bank_account.merchant_id = hipay_bank_account.merchant.id
        hipay_bank_account.application = hipay_bank_account.merchant.application
        hipay_bank_account.application_id = hipay_bank_account.application.id
        hipay_bank_account.store_bank_account = None
        hipay_bank_account.identity_document = None
        app = hipay_bank_account.merchant.application
        app.set_setting(EnableKycV2, True)
        return hipay_bank_account

    def test_get_store_bank_account_with_wrong_id(self):
        hba = self.build_mocked_hipay_bank_account()
        with self.assertRaises(ValidationError):
            BankAccountSelectionParamsParser.get_selected_account(
                hba, {"account_id": None}
            )

        with self.assertRaises(ValidationError):
            BankAccountSelectionParamsParser.get_selected_account(
                hba, {"account_id": "zorg"}
            )

        with self.assertRaises(ValidationError):
            BankAccountSelectionParamsParser.get_selected_document(hba, {})

    def test_get_merchant_document_with_wrong_id(self):
        hba = self.build_mocked_hipay_bank_account()
        with self.assertRaises(ValidationError):
            BankAccountSelectionParamsParser.get_selected_document(
                hba, {"document_id": None}
            )

        with self.assertRaises(ValidationError):
            BankAccountSelectionParamsParser.get_selected_document(
                hba, {"document_id": "zorg"}
            )

        with self.assertRaises(ValidationError):
            BankAccountSelectionParamsParser.get_selected_document(hba, {})

    def test_get_store_bank_account_with_good_data(self):
        hba = self.build_mocked_hipay_bank_account()
        mba = MerchantBankAccountFactory.create(merchant=hba.merchant)

        # when
        doc = BankAccountSelectionParamsParser.get_selected_account(
            hba, {"account_id": mba.id}
        )

        # then
        self.assertEqual(doc.id, mba.id)

    def test_get_kyc_info_with_good_data(self):
        hba = self.build_mocked_hipay_bank_account()
        kyc = KycInformationFactory(
            merchant=hba.merchant,
            kyc_type__external_id=MerchantIdentityDocument.BANK_INFOS_COPY,
        )
        with patch(self.WALLET_MOCK_PATH):
            HipayWalletAccountFactory(
                application=kyc.merchant.application, merchant=kyc.merchant
            )
        HipayAppConfigurationFactory(application=kyc.merchant.application, active=True)

        # when
        doc = BankAccountSelectionParamsParser.get_selected_document(
            hba, {"document_id": kyc.id}
        )

        # then
        self.assertEqual(doc.id, kyc.id)

    def test_check_request_coherence_with_wrong_doc(self):
        application = ApplicationFactory()
        application.set_setting(EnableKycV2, True)
        kyc = KycInformationFactory.build(
            merchant__application=application, kyc_type__external_id="9999"
        )
        with patch(self.WALLET_MOCK_PATH):
            wallet = HipayWalletAccountFactory.build(
                application=kyc.merchant.application, merchant=kyc.merchant
            )

        # then it fails on call with a ValidationError
        with self.assertRaises(ValidationError):
            BankAccountSelectionParamsParser.check_request_coherence(wallet, kyc)

    def test_check_request_coherence_with_bank_doc(self):
        application = ApplicationFactory()
        application.set_setting(EnableKycV2, True)
        kyc = KycInformationFactory.build(
            merchant__application=application,
            kyc_type__external_id=HIPAY_BANK_ACCOUNT_DETAILS,
        )
        with patch(self.WALLET_MOCK_PATH):
            wallet = HipayWalletAccountFactory.build(
                application=kyc.merchant.application, merchant=kyc.merchant
            )

        # then calling it doesn't fail
        BankAccountSelectionParamsParser.check_request_coherence(wallet, kyc)

    @patch("payment_backends.hipay_tpp.models.bank_account.HipayBankAccount.get_api")
    @patch("payment_backends.hipay_tpp.models.bank_account.HipayBankAccount.get_wallet")
    @patch(
        "payment_backends.hipay_tpp.hipay_api.cashout.HipayCashoutApi."
        "select_merchant_bank_account"
    )
    @patch(
        "payment_backends.hipay_tpp.actions.bank_account.manager."
        "BankAccountActionsManager._handle_success_response",
        lambda _, __: None,
    )
    def test_call_signature_on_run_method(
        self, mocked_select_merchant_bank_account, mocked_get_wallet, mocked_get_api
    ):
        config = HipayAppConfigurationFactory.build()
        wallet = HipayWalletAccountFactory.build()
        mocked_get_wallet.return_value = wallet
        mocked_get_api.return_value = config.cashout_api
        hba = HipayMerchantBankAccountKycV2Factory()
        application = hba.merchant.application
        config.application = application
        application.set_setting(EnableKycV2, True)
        mba = MerchantBankAccountFactory(merchant=hba.merchant)
        mid = KycInformationFactory(
            merchant=hba.merchant, kyc_type__external_id=HIPAY_BANK_ACCOUNT_DETAILS
        )

        # when
        hba.actions.select(mba, mid)
        # then the api is called with the right signature and nothing explodes!
        mocked_select_merchant_bank_account.assert_called_with(mba, mid, wallet)

    @patch("apps.kyc.models.KycInformation.save", lambda *_, **__: None)
    def test_select_update_doc_external_id_and_backend_status(self):
        hba = self.build_mocked_hipay_bank_account()
        api_mock = mock.Mock()
        api_mock.select_merchant_bank_account.return_value = {
            "status_code": BankAccountActionsManager.HIPAY_VALIDATION_IN_PROGRESS
        }
        hba.get_api.return_value = api_mock
        kyc = KycInformationFactory.build(
            id=uuid.uuid4(),
            merchant=hba.merchant,
            kyc_type__external_id=HIPAY_BANK_ACCOUNT_DETAILS,
        )
        mba = MerchantBankAccountFactory.build(merchant=kyc.merchant)
        hba.kyc_information = kyc

        # when
        BankAccountActionsManager(hba).select(mba, kyc)
        # external_id is converted to string when saved to DB, so mimic this
        # by casting to string.
        kyc.external_id = str(kyc.external_id)

        # then
        self.assertTrue(kyc.kyc_backend_status.is_under_analysis)
        self.assertEqual(kyc.external_id, str(HIPAY_BANK_ACCOUNT_DETAILS))

    def test_select_bank_account_list_error_case(self):
        ba = HipayMerchantBankAccountKycV1Factory.build()
        response_data = {
            "code": 400,
            "errors": [
                {
                    "code": 222,
                    "error_code_detail": 13,
                    "field": "file",
                    "message": (
                        "A document of this type is already waiting for "
                        "validation. <NAME_EMAIL>."
                    ),
                }
            ],
            "message": "Validation Failed",
        }
        with patch.object(ba, "save", lambda: None):
            ba.actions._handle_error_response(response_data)
        self.assertEqual(
            ba.synchro_error,
            {
                "__all__": [
                    "A document of this type is already waiting for "
                    "validation. <NAME_EMAIL>."
                ]
            },
        )
        self.assertEqual(ba.synchro_status, "4")

    @patch(
        "payment_backends.hipay_tpp.actions.bank_account.manager.logger.exception",
        lambda *_, **__: None,
    )
    def test_select_bank_account_unhandled_error_case(self):
        ba = HipayMerchantBankAccountKycV1Factory.build()
        response_data = {"unhandled error": None}
        with patch.object(ba, "save", lambda: None):
            ba.actions._handle_error_response(response_data)
        self.assertEqual(ba.synchro_error, {"__all__": "Unknown error"})
        self.assertEqual(ba.synchro_status, "4")

    def test_select_bank_account_string_error_case(self):
        ba = HipayMerchantBankAccountKycV1Factory.build()
        response_data = {"message": "my message"}
        with patch.object(ba, "save", lambda: None):
            ba.actions._handle_error_response(response_data)
        self.assertEqual(ba.synchro_error, {"__all__": ["my message"]})
        self.assertEqual(ba.synchro_status, "4")

    def test_select_bank_account_dict_error_case(self):
        ba = HipayMerchantBankAccountKycV1Factory.build()
        response_data = {
            "errors": {
                "error 1": "my error 1",
                "error 2": "my error 2",
            },
        }
        with patch.object(ba, "save", lambda: None):
            ba.actions._handle_error_response(response_data)
        self.assertEqual(ba.synchro_error, {"__all__": ["my error 1", "my error 2"]})
        self.assertEqual(ba.synchro_status, "4")

    @patch(
        "payment_backends.hipay_tpp.actions.bank_account.manager.logger.exception",
        lambda *_, **__: None,
    )
    def test_select_bank_account_invalid_list_error_case(self):
        ba = HipayMerchantBankAccountKycV1Factory.build()
        response_data = {
            "errors": [{"invalid_key": "(missing message key)"}],
        }
        with patch.object(ba, "save", lambda: None):
            ba.actions._handle_error_response(response_data)
        self.assertEqual(ba.synchro_error, {"__all__": "Unknown error"})
        self.assertEqual(ba.synchro_status, "4")
