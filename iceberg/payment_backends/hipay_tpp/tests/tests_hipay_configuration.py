# -*- coding: utf-8 -*-

from apps.testing.factories import ApplicationFactory
from ims.tests import BaseTestCase

from .tests_hipay_mixin import TestHipayMixin


class HipayApplicationConfigurationTestCase(TestHipayMixin, BaseTestCase):
    def test_endpoint_for_sandbox_and_staging_none(self):
        application = self.create_app(sandbox=True)
        self.hipay_config = self.create_hipay_config(
            application=application, save=False
        )

        self.assertEqual(
            self.hipay_config.RestApiEndpoint,
            "https://stage-secure-gateway.hipay-tpp.com/rest/v1/",
        )
        self.assertEqual(
            self.hipay_config.RestApiV2Endpoint,
            "https://stage-api.hipay.com/v1/",
        )

    def test_endpoint_for_prod_and_staging_none(self):
        application = self.create_app(production=True)
        self.hipay_config = self.create_hipay_config(
            application=application, save=False
        )

        self.assertEqual(
            self.hipay_config.RestApiEndpoint,
            "https://secure-gateway.hipay-tpp.com/rest/v1/",
        )
        self.assertEqual(
            self.hipay_config.RestApiV2Endpoint,
            "https://api.hipay.com/v1/",
        )

    def test_endpoint_for_prod_and_staging_true(self):
        application = self.create_app(production=True)
        self.hipay_config = self.create_hipay_config(
            application=application, save=False
        )
        self.hipay_config.staging = True

        self.assertEqual(
            self.hipay_config.RestApiEndpoint,
            "https://stage-secure-gateway.hipay-tpp.com/rest/v1/",
        )
        self.assertEqual(
            self.hipay_config.RestApiV2Endpoint,
            "https://stage-api.hipay.com/v1/",
        )

    def test_endpoint_for_prod_and_staging_false(self):
        application = self.create_app(production=True)
        self.hipay_config = self.create_hipay_config(
            application=application, save=False
        )
        self.hipay_config.staging = False

        self.assertEqual(
            self.hipay_config.RestApiEndpoint,
            "https://secure-gateway.hipay-tpp.com/rest/v1/",
        )
        self.assertEqual(
            self.hipay_config.RestApiV2Endpoint,
            "https://api.hipay.com/v1/",
        )

    def test_endpoint_for_sandbox_and_staging_false(self):
        application = self.create_app(sandbox=True)
        self.hipay_config = self.create_hipay_config(
            application=application, save=False
        )
        self.hipay_config.staging = False

        self.assertEqual(
            self.hipay_config.RestApiEndpoint,
            "https://secure-gateway.hipay-tpp.com/rest/v1/",
        )
        self.assertEqual(
            self.hipay_config.RestApiV2Endpoint,
            "https://api.hipay.com/v1/",
        )

    def test_endpoint_for_sandbox_and_staging_true(self):
        application = self.create_app(sandbox=True)
        self.hipay_config = self.create_hipay_config(
            application=application, save=False
        )
        self.hipay_config.staging = True

        self.assertEqual(
            self.hipay_config.RestApiEndpoint,
            "https://stage-secure-gateway.hipay-tpp.com/rest/v1/",
        )
        self.assertEqual(
            self.hipay_config.RestApiV2Endpoint,
            "https://stage-api.hipay.com/v1/",
        )


class HipayApplicationConfigurationQuerySetTestCase(TestHipayMixin, BaseTestCase):
    def test_active_method(self):
        application = ApplicationFactory.create()
        hipay_config = self.create_hipay_config(
            application=application, save=True, active=True
        )

        self.assertEqual(
            list(hipay_config.__class__.objects.active().values_list("id", flat=True)),
            [hipay_config.id],
        )

    def test_active_method_2(self):
        application = ApplicationFactory.create()
        hipay_config = self.create_hipay_config(
            application=application, save=True, active=False
        )

        self.assertEqual(
            list(hipay_config.__class__.objects.active().values_list("id", flat=True)),
            [],
        )

    def test_cashout_enabled_method(self):
        application = ApplicationFactory.create()
        hipay_config = self.create_hipay_config(
            application=application, save=True, cashout_enabled=True
        )

        self.assertEqual(
            list(
                hipay_config.__class__.objects.cashout_enabled().values_list(
                    "id", flat=True
                )
            ),
            [hipay_config.id],
        )

    def test_cashout_enabled_method_2(self):
        application = ApplicationFactory.create()
        hipay_config = self.create_hipay_config(
            application=application, save=True, cashout_enabled=False
        )

        self.assertEqual(
            list(
                hipay_config.__class__.objects.cashout_enabled().values_list(
                    "id", flat=True
                )
            ),
            [],
        )

    def test_active_on_application_method(self):
        application = ApplicationFactory.create()
        hipay_config = self.create_hipay_config(
            application=application,
            save=True,
        )
        application.payment_settings.payment_backend = hipay_config.payment_backend
        application.payment_settings.save()
        self.assertEqual(
            list(
                hipay_config.__class__.objects.active_on_application().values_list(
                    "id", flat=True
                )
            ),
            [hipay_config.id],
        )

    def test_active_on_application_method_2(self):
        application = ApplicationFactory.create()
        hipay_config = self.create_hipay_config(
            application=application,
            save=True,
        )
        application.payment_settings.payment_backend = "external"
        application.payment_settings.save()
        self.assertEqual(
            list(
                hipay_config.__class__.objects.active_on_application().values_list(
                    "id", flat=True
                )
            ),
            [],
        )
