# -*- coding: utf-8 -*-

import factory
import mock
from apps.ice_applications.app_conf_settings import EnableKycV2
from apps.ice_applications.tests import ApplicationTestSetupMixin
from apps.kyc.models import MerchantIdentityDocument
from apps.payment.backends import HIPAYTPP
from apps.stores.actions.publish_document import DocumentPublisher
from apps.testing.factories import (
    HipayAppConfigurationFactory,
    HipayWalletAccountFactory,
    IdentityDocumentPartFactory,
    KycInformationFactory,
    MerchantFactory,
)
from ims.tests import BaseTestCase
from mock import patch
from payment_backends.hipay_tpp.actions.merchant_publish_kyc import send_kyc
from payment_backends.hipay_tpp.hipay_api.kyc import <PERSON><PERSON><PERSON>yc<PERSON><PERSON>
from payment_backends.hipay_tpp.models import HipayWalletAccount
from payment_backends.hipay_tpp.references import (
    ACCOUNT_CREATION_FAILED,
    ACCOUNT_CREATION_SYNCHRONIZED,
    HIPAY_ACCOUNT_TYPE_CORPORATION,
    HIPAY_KBIS_EXTRACT,
)


class OldHipayWalletAccountFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = HipayWalletAccount

    api_login = "login"
    api_password = "passwd"
    user_account_id = "userid"
    websiteIds = "mywebsite"
    account_type = HIPAY_ACCOUNT_TYPE_CORPORATION


UPLOAD_ALREADY_DONE = {
    "code": 415,
    "message": (
        "A document of this type is already validated or "
        "waiting for validation. <NAME_EMAIL>."
    ),
}

ALL_DOC_VALIDATED = {
    "code": 414,
    "message": "No more document to validate for this merchant.",
}


UPLOAD_DOC_SUCCEED = {"code": 0, "message": "Your good to go."}

UPLOAD_REVIEW_IN_PROGRESS = {"code": 5, "message": "The document is being reviewed"}


@patch(
    "payment_backends.hipay_tpp.actions.merchant_publish_kyc.send_error_mail",
    lambda *_, **__: None,
)
class RestApiInterfaceTestCase(BaseTestCase):
    CHECKER_PATH = (
        "payment_backends.hipay_tpp.hipay_api.rest_api_interface."
        "raise_known_operation_exception"
    )
    REQUEST_MAKER_PATH = (
        "payment_backends.hipay_tpp.hipay_api.rest_api_interface.logged_tls_request"
    )

    def setUp(self):
        config = mock.Mock()
        config.KycApiEndpoint = "endpoint"
        self.api = HipayKycApi(config)

    def test_known_internal_exception_response(self):
        self.api.known_internal_exception_response()

    def test_check_computed_params_for_indentity_doc(self):
        doc = mock.Mock()
        doc.document_type = MerchantIdentityDocument.IDENTITY_PROOF
        wallet = OldHipayWalletAccountFactory.stub()

        params = self.api.get_upload_parameters(wallet, doc)

        self.assertEqual(len(params), 2)
        self.assertIn("type", params)
        self.assertIn("validity_date", params)

    def test_check_computed_params_for_not_a_indentity_doc(self):
        doc = mock.Mock()
        doc.document_type = MerchantIdentityDocument.REGISTRATION_PROOF
        wallet = OldHipayWalletAccountFactory.stub()

        params = self.api.get_upload_parameters(wallet, doc)

        self.assertEqual(len(params), 1)
        self.assertIn("type", params)

    def test_check_computed_params_do_not_contains_deprecated_param(self):
        doc = mock.Mock()
        doc.document_type = MerchantIdentityDocument.REGISTRATION_PROOF
        wallet = OldHipayWalletAccountFactory.stub()

        params = self.api.get_upload_parameters(wallet, doc)

        self.assertNotIn("user_space", params)


@patch(
    "payment_backends.hipay_tpp.actions.merchant_publish_kyc.send_error_mail",
    lambda *_, **__: None,
)
class TestSendKyc(ApplicationTestSetupMixin, BaseTestCase):
    def test_should_ignore_document_on_415_code(self):
        self.set_up()
        self.conf = mock.Mock()
        self.conf.kyc_api.send_kyc.return_value = UPLOAD_ALREADY_DONE
        doc = mock.Mock()
        doc.document_type = MerchantIdentityDocument.REGISTRATION_PROOF
        wallet = OldHipayWalletAccountFactory.stub()

        send_kyc(self.conf, wallet, doc)

        doc.not_required_doc.assert_called_once()

    def test_should_ignore_document_on_414_code(self):
        self.set_up()
        self.conf = mock.Mock()
        self.conf.kyc_api.send_kyc.return_value = ALL_DOC_VALIDATED
        doc = mock.Mock()
        doc.document_type = MerchantIdentityDocument.REGISTRATION_PROOF
        wallet = OldHipayWalletAccountFactory.stub()

        send_kyc(self.conf, wallet, doc)

        doc.not_required_doc.assert_called_once()

    @mock.patch(
        "payment_backends.hipay_tpp.actions.merchant_publish_kyc.expire_other_documents"
    )
    def test_should_analyse_document_on_0_code(self, expire_docs):
        self.set_up()
        self.conf = mock.Mock()
        self.conf.kyc_api.send_kyc.return_value = UPLOAD_DOC_SUCCEED
        doc = mock.Mock()
        doc.id = 42
        doc.document_type = MerchantIdentityDocument.REGISTRATION_PROOF
        wallet = OldHipayWalletAccountFactory.stub()

        send_kyc(self.conf, wallet, doc)

        doc.analyse_doc.assert_called_once()
        expire_docs.assert_called_once()

    @mock.patch(
        "payment_backends.hipay_tpp.actions.merchant_publish_kyc.expire_other_documents"
    )
    def test_should_refuse_document_on_5_code(self, expire_docs):
        self.set_up()
        self.conf = mock.Mock()
        self.conf.kyc_api.send_kyc.return_value = UPLOAD_REVIEW_IN_PROGRESS
        doc = mock.Mock()
        doc.id = 42
        doc.document_type = MerchantIdentityDocument.REGISTRATION_PROOF
        wallet = OldHipayWalletAccountFactory.stub()

        send_kyc(self.conf, wallet, doc)

        doc.refuse_doc.assert_called_once()
        expire_docs.assert_called_once()

    @patch(
        "payment_backends.hipay_tpp.hipay_api.rest_api_interface."
        "RestAPIInterface.request",
        lambda *args, **kwargs: {"identified": 0, "code": 0},
    )
    @patch("payment_backends.hipay_tpp.hipay_api.cashout.HipayCashoutApi.get_wallet")
    @patch("payment_backends.hipay_tpp.processor.HipayTppPayment.get_config")
    def test_upload_valid_kbis_using_kyc_v2_sets_in_analyse_doc(
        self, get_config, get_wallet
    ):
        doc = KycInformationFactory(
            application__app_settings={EnableKycV2: True},
            kyc_type__external_id=HIPAY_KBIS_EXTRACT,
            application__payment_backend=HIPAYTPP,
            status="active",
        )
        get_config.return_value = HipayAppConfigurationFactory.build(
            application=doc.kyc_type.application,
            currency=doc.kyc_type.application.default_currency,
            country=doc.kyc_type.application.country,
        )
        get_wallet.return_value = HipayWalletAccountFactory.build(
            application=doc.kyc_type.application,
            account_type=HIPAY_ACCOUNT_TYPE_CORPORATION,
            merchant=doc.merchant,
            synchro_status=ACCOUNT_CREATION_SYNCHRONIZED,
        )

        result = DocumentPublisher(doc).publish()
        self.assertEqual(result, (True, "analyse_doc"))
        doc.refresh_from_db()
        self.assertEqual(doc.kyc_backend_status, "under_analysis")

    @patch("payment_backends.hipay_tpp.hipay_api.cashout.HipayCashoutApi.get_wallet")
    @patch("payment_backends.hipay_tpp.processor.HipayTppPayment.get_config")
    def test_upload_initial_doc_is_refused_and_stay_pending(
        self, get_config, get_wallet
    ):
        doc = KycInformationFactory(
            application__app_settings={EnableKycV2: True},
            kyc_type__external_id=HIPAY_KBIS_EXTRACT,
            application__payment_backend=HIPAYTPP,
            status="initial",
        )
        get_config.return_value = HipayAppConfigurationFactory.build(
            application=doc.kyc_type.application,
            currency=doc.kyc_type.application.default_currency,
            country=doc.kyc_type.application.country,
        )
        get_wallet.return_value = HipayWalletAccountFactory.build(
            application=doc.kyc_type.application,
            account_type=HIPAY_ACCOUNT_TYPE_CORPORATION,
            merchant=doc.merchant,
            synchro_status=ACCOUNT_CREATION_SYNCHRONIZED,
        )

        result = DocumentPublisher(doc).publish()
        self.assertEqual(result, (False, "Inactive document"))
        doc.refresh_from_db()
        self.assertEqual(doc.kyc_backend_status, "pending")

    @patch("payment_backends.hipay_tpp.hipay_api.cashout.HipayCashoutApi.get_wallet")
    @patch("payment_backends.hipay_tpp.processor.HipayTppPayment.get_config")
    def test_upload_doc_with_failed_sync_wallet_is_refused_and_stay_pending(
        self, get_config, get_wallet
    ):
        doc = KycInformationFactory(
            application__app_settings={EnableKycV2: True},
            kyc_type__external_id=HIPAY_KBIS_EXTRACT,
            application__payment_backend=HIPAYTPP,
            status="active",
        )
        get_config.return_value = HipayAppConfigurationFactory.build(
            application=doc.kyc_type.application,
            currency=doc.kyc_type.application.default_currency,
            country=doc.kyc_type.application.country,
        )
        get_wallet.return_value = HipayWalletAccountFactory.build(
            application=doc.kyc_type.application,
            account_type=HIPAY_ACCOUNT_TYPE_CORPORATION,
            merchant=doc.merchant,
            synchro_status=ACCOUNT_CREATION_FAILED,
        )

        result = DocumentPublisher(doc).publish()
        self.assertEqual(result, (False, "Missing or unsynced wallet"))
        doc.refresh_from_db()
        self.assertEqual(doc.kyc_backend_status, "pending")

    @patch("payment_backends.hipay_tpp.hipay_api.kyc.HipayKycApi.request")
    def test_send_kyc_using_v1(self, mocked_request):
        merchant = MerchantFactory.create(
            application__payment_backend=HIPAYTPP,
            application__app_settings={EnableKycV2: False},
        )
        config = HipayAppConfigurationFactory.create(
            application=merchant.application,
            currency=merchant.application.default_currency,
            country=merchant.application.country,
        )
        wallet = HipayWalletAccountFactory.create(
            application=merchant.application,
            account_type=HIPAY_ACCOUNT_TYPE_CORPORATION,
            merchant=merchant,
        )
        part = IdentityDocumentPartFactory.create(document__merchant=merchant)
        api = HipayKycApi(config)

        # the test is valid if no exception is raised
        api.send_kyc(wallet, part.document)

        mocked_request.assert_called_once()
