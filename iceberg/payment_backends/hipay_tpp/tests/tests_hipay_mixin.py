# -*- coding: utf-8 -*-

import mock
from apps.ice_applications.models import Application
from payment_backends.hipay_tpp.models import HipayAppConfiguration


class TestHipayMixin:
    def create_hipay_config(
        self, application=None, save=True, cashout_enabled=True, active=True
    ):
        config = HipayAppConfiguration(
            active=active,
            RestApiLogin="abc",
            RestApiPassword="def",
            EWalletApiEntity="entity",
            EWalletApiLogin="ghi",
            EWalletApiPassword="jkl",
            account_id=12345,
            cashout_enabled=cashout_enabled,
        )
        if application is not None:
            config.application = application
        if save:
            config.save()
        return config

    def create_app(self, sandbox=False, production=False):
        application = mock.Mock(Application)
        application._state = mock.Mock()
        application.id = mock.Mock()

        if sandbox and production:
            raise RuntimeError("Invalid app env requested")

        if sandbox is True:
            application.environment = application.SANDBOX

        if production is True:
            application.environment = application.PRODUCTION

        return application
