# -*- coding: utf-8 -*-

from decimal import Decimal

import mock
from apps.ice_applications.app_conf_settings import EnableCommissionTransactions
from apps.orders.exceptions import OrderLockedError
from apps.payment.backends import HIPAYTPP
from apps.promotions.models import Discount
from apps.stores.tests.test_merchant_setup_mixin import TestMerchantSetupMixin
from apps.testing.factories import (
    HipayAppConfigurationFactory,
    HipayTransactionFactory,
    MerchantCommissionRuleFactory,
    MerchantOrderFactory,
    MerchantOrderOperatorSaleTransactionFactory,
    MerchantOrderRefundTransactionFactory,
    MerchantOrderSellTransactionFactory,
    OrderPaymentFactory,
)
from constance import config
from ims.api.exceptions import ResourceLockedException
from ims.tests import BaseResourceTestCase, BaseTestCase
from payment_backends.hipay_tpp import processor
from payment_backends.hipay_tpp.hipay_api import HipayCashinApi
from payment_backends.hipay_tpp.models import HipayAppConfiguration
from payment_backends.hipay_tpp.processor import HipayTppPayment, generate_operation_ids
from payment_backends.hipay_tpp.references import (
    HIPAY_ECI_LEVEL_END_USER,
    HIPAY_ECI_LEVEL_END_USER_RECUR,
    HIPAY_ECI_LEVEL_OPERATOR,
)
from reference.status import (
    PAYMENT_STATUS_COLLECTED,
    TRANSACTION_STATUS_ATTENTION_REQUIRED,
    TRANSACTION_STATUS_CONFIRMED,
)


class MockTransaction:
    class MockOrder:
        application = None

    order = MockOrder()


class HipayProcessorTestCase(TestMerchantSetupMixin, BaseResourceTestCase):
    def setUp(self):
        super().setUp()
        TestMerchantSetupMixin.set_up(self)

        self.create_config()
        self.cashin_api = HipayCashinApi(self.hipay_config)
        self.processor = HipayTppPayment()
        self.processor.cashin_api = self.cashin_api
        self.processor.cashout_api = mock.Mock()
        self.processor.kyc_api = mock.Mock()

    def create_config(self):
        self.hipay_config = HipayAppConfiguration.objects.create(
            application=self.application,
            active=True,
            RestApiLogin="abc",
            RestApiPassword="def",
            EWalletApiEntity="entity",
            EWalletApiLogin="ghi",
            EWalletApiPassword="jkl",
            account_id=12345,
        )

    def test_get_eci_level_for_operators(self):
        eci = self.processor.get_eci_level(is_operator=True)
        self.assertEqual(eci, HIPAY_ECI_LEVEL_OPERATOR)

    def test_get_eci_level_for_end_users(self):
        eci = self.processor.get_eci_level(is_operator=False)
        self.assertEqual(eci, HIPAY_ECI_LEVEL_END_USER)

    def test_get_eci_level_for_recurring_payments(self):
        eci1 = self.processor.get_eci_level(is_operator=False, recurrent_usage=True)
        eci2 = self.processor.get_eci_level(is_operator=True, recurrent_usage=True)

        self.assertEqual(eci1, HIPAY_ECI_LEVEL_END_USER_RECUR)
        self.assertEqual(eci2, HIPAY_ECI_LEVEL_END_USER_RECUR)

    def test_operator_sale_is_processed_if_resolved_payment(self):
        self.application.payment_settings.payment_backend = "hipay_tpp"
        self.application.payment_settings.save()
        op_sale = MerchantOrderOperatorSaleTransactionFactory(
            application=self.application,
            currency=self.application.default_currency,
        )
        hipay_trans = HipayTransactionFactory.create(
            order=op_sale.merchant_order.order,
            application=self.application,
            payment=op_sale.order.payment,
        )
        mo_ids, operation_id = generate_operation_ids(
            op_sale.payment.order.merchant_orders.all(), "CAPTURE"
        )
        hipay_trans.capture_operations[operation_id] = mo_ids
        hipay_trans.save()
        hipay_trans.escrow_delivery_settlement[str(op_sale.merchant_order_id)] = True
        hipay_trans.save()

        op_sale.actions.process()

        op_sale.refresh_from_db()
        self.assertTrue(op_sale.status.is_confirmed)

    def test_operator_sale_is_not_processed_if_unresolved_payment(self):
        self.application.payment_settings.payment_backend = "hipay_tpp"
        self.application.payment_settings.save()
        op_sale = MerchantOrderOperatorSaleTransactionFactory(
            application=self.application,
            currency=self.application.default_currency,
        )
        hipay_trans = HipayTransactionFactory.create(
            order=op_sale.merchant_order.order,
            application=self.application,
            payment=op_sale.order.payment,
        )
        mo_ids, operation_id = generate_operation_ids(
            op_sale.payment.order.merchant_orders.all(), "CAPTURE"
        )
        hipay_trans.capture_operations[operation_id] = mo_ids
        hipay_trans.save()

        op_sale.actions.process()

        op_sale.refresh_from_db()
        self.assertTrue(op_sale.status.is_attention_required)
        self.assertEqual(
            op_sale.error_message,
            "Merchant order amount has not been funded on escrow wallet!",
        )


get_api_path = "payment_backends.hipay_tpp.models.transaction.HipayTransaction.get_api"

update_path = "payment_backends.hipay_tpp.models.transaction.HipayTransaction.get_api"


class TestPaymentProcessor(BaseTestCase):
    def setUp(self):
        self.merchant_order = MerchantOrderFactory(
            application__payment_backend=HIPAYTPP
        )
        self.application = self.merchant_order.application
        HipayAppConfigurationFactory.create(application=self.application)
        self.hipay_transaction = HipayTransactionFactory.create(
            order=self.merchant_order.order, application=self.application
        )

    @mock.patch.object(processor.CaptureAction, "run")
    def test_should_store_operation_id_in_transaction_for_capture(self, mock):
        merchant_order = self.hipay_transaction.order.merchant_orders.first()
        transaction = MerchantOrderSellTransactionFactory.create(
            currency=self.application.default_currency,
            application=self.application,
            user=self.merchant_order.order.user,
            order=self.merchant_order.order,
            merchant=self.merchant_order.merchant,
            merchant_order=self.merchant_order,
        )

        processor.HipayTppPayment().collect(
            transaction.payment, None, transaction.amount, [merchant_order]
        )

        expected_id = "CAPTURE:%s" % merchant_order.id
        self.hipay_transaction.refresh_from_db()
        mock.assert_called_once_with(
            transaction.payment, transaction.amount, [merchant_order], expected_id
        )
        self.assertIn(expected_id, self.hipay_transaction.capture_operations)
        self.assertEqual(
            self.hipay_transaction.capture_operations[expected_id], [merchant_order.id]
        )

    @mock.patch.object(processor.CaptureAction, "run")
    def test_skips_capture_on_sofort(self, collect_call):
        self.hipay_transaction.payment_product = "sofort-uberweisung"
        self.hipay_transaction.save(update_fields=["payment_product"])
        merchant_order = self.hipay_transaction.order.merchant_orders.first()
        transaction = MerchantOrderSellTransactionFactory.create(
            currency=self.application.default_currency,
            application=self.application,
            user=self.merchant_order.order.user,
            order=self.merchant_order.order,
            merchant=self.merchant_order.merchant,
            merchant_order=self.merchant_order,
        )

        processor.HipayTppPayment().collect(
            transaction.payment, None, transaction.amount, [merchant_order]
        )

        self.hipay_transaction.refresh_from_db()
        collect_call.assert_not_called()
        # Nothing added because realtime banking assumes it's been captured
        # at 'authorization' step
        self.assertEqual(len(self.hipay_transaction.capture_operations), 0)

    @mock.patch(get_api_path)
    @mock.patch(update_path, lambda x: None)
    def test_should_store_operation_id_in_transaction_for_refund(self, _):
        self.hipay_transaction.get_api = mock.Mock()
        self.hipay_transaction.update_from_request_dict = mock.Mock()
        transaction = MerchantOrderRefundTransactionFactory.create(
            currency=self.application.default_currency,
            application=self.application,
            user=self.merchant_order.order.user,
            order=self.merchant_order.order,
            merchant=self.merchant_order.merchant,
            merchant_order=self.merchant_order,
        )
        hipay_processor = processor.HipayTppPayment()
        mocked_config = mock.Mock()
        hipay_processor.get_config = mocked_config

        # Mock the get_status_v3 method to avoid the API call
        mocked_config().cashin_api.get_status_v3.return_value = {}

        hipay_processor.refund(
            transaction.payment, None, transaction.amount, transaction.refund
        )

        expected_id = f"REFUND:{transaction.refund.id}"
        self.hipay_transaction.refresh_from_db()
        mocked_config().cashin_api.refund.assert_called_once_with(
            self.hipay_transaction.transaction_reference,
            expected_id,
            transaction.amount,
        )
        self.assertIn(expected_id, self.hipay_transaction.refund_operations)
        refund_data = self.hipay_transaction.refund_operations[expected_id]
        self.assertEqual(refund_data["merchant_order_ids"], [self.merchant_order.id])
        self.assertEqual(refund_data["status"], "PENDING")
        self.assertEqual(refund_data["refund_id"], transaction.refund.id)
        self.assertEqual(refund_data["merchant_reference"], expected_id)
        self.assertIsNone(refund_data["hipay_operation_id"])  # Pas encore rempli


class TestTransactionHooks(BaseTestCase):
    def setUp(self):
        self.merchant_order = MerchantOrderFactory(
            application__payment_backend=HIPAYTPP,
            order__auto_create_payment=True,
            order___payment__status=PAYMENT_STATUS_COLLECTED,
        )
        self.application = self.merchant_order.application
        MerchantCommissionRuleFactory(
            application=self.application,
            commission_rate_price_base="product_tax_free",
            commission_rate_value=20,
            commission_fixed_price_amount=0,
            commission_method="rate",
        )

        self.hipay_transaction = HipayTransactionFactory.create(
            order=self.merchant_order.order,
            application=self.application,
            payment=self.merchant_order.order.payment,
        )
        HipayAppConfigurationFactory.create(
            application=self.hipay_transaction.payment.application
        )
        self.application.set_setting(EnableCommissionTransactions, False)
        self.transaction = MerchantOrderSellTransactionFactory.create(
            amount=self.merchant_order.order.price,
            currency=self.application.default_currency,
            application=self.application,
            user=self.merchant_order.order.user,
            order=self.merchant_order.order,
            merchant=self.merchant_order.merchant,
            merchant_order=self.merchant_order,
            payment=self.merchant_order.order.payment,
        )

    def test_processing_sale_transaction_settled_but_not_resolved(self):
        config.ENABLE_FAKE_TRACKER = True
        config.TRACKER_CONFIG = "ON_ESCROW"
        self.hipay_transaction.capture_operations["fake_operation_id"] = "id"
        self.hipay_transaction.save()

        self.transaction.process()

        self.transaction.refresh_from_db()
        self.assertEqual(self.transaction.status, TRANSACTION_STATUS_CONFIRMED)
        self.assertEqual(
            self.transaction.appbalancetransactions.get().status,
            TRANSACTION_STATUS_CONFIRMED,
        )
        self.assertEqual(
            self.transaction.balancetransactions.get().status,
            TRANSACTION_STATUS_CONFIRMED,
        )

    def test_processing_sale_transaction_settled_and_resolved(self):
        config.ENABLE_FAKE_TRACKER = True
        config.TRACKER_CONFIG = "RESOLVED"
        self.hipay_transaction.capture_operations["fake_operation_id"] = "id"
        self.hipay_transaction.save()

        self.transaction.process()

        self.transaction.refresh_from_db()
        self.assertEqual(self.transaction.status, TRANSACTION_STATUS_CONFIRMED)
        self.assertEqual(
            self.transaction.appbalancetransactions.get().status,
            TRANSACTION_STATUS_CONFIRMED,
        )
        self.assertEqual(
            self.transaction.balancetransactions.get().status,
            TRANSACTION_STATUS_CONFIRMED,
        )

    def test_processing_unsettled_sell_transaction(self):
        config.ENABLE_FAKE_TRACKER = True
        config.TRACKER_CONFIG = "NOT_FUNDED"
        self.hipay_transaction.capture_operations["fake_operation_id"] = "id"
        self.hipay_transaction.save()

        self.transaction.process()

        self.transaction.refresh_from_db()
        self.assertEqual(self.transaction.status, TRANSACTION_STATUS_ATTENTION_REQUIRED)
        self.assertEqual(
            self.transaction.appbalancetransactions.get().status,
            TRANSACTION_STATUS_ATTENTION_REQUIRED,
        )
        self.assertEqual(
            self.transaction.balancetransactions.get().status,
            TRANSACTION_STATUS_ATTENTION_REQUIRED,
        )

    def test_processing_sale_transaction_settled_no_operation_id(self):
        config.ENABLE_FAKE_TRACKER = True
        config.TRACKER_CONFIG = "ON_ESCROW"

        self.transaction.process()

        self.transaction.refresh_from_db()
        self.assertEqual(self.transaction.status, TRANSACTION_STATUS_CONFIRMED)
        self.assertEqual(
            self.transaction.appbalancetransactions.first().status,
            TRANSACTION_STATUS_CONFIRMED,
        )
        self.assertEqual(
            self.transaction.balancetransactions.first().status,
            TRANSACTION_STATUS_CONFIRMED,
        )

    def test_processing_sale_transaction_settled_and_resolved_no_operation_id(self):
        config.ENABLE_FAKE_TRACKER = True
        config.TRACKER_CONFIG = "RESOLVED"

        self.transaction.process()

        self.transaction.refresh_from_db()
        self.assertEqual(self.transaction.status, TRANSACTION_STATUS_CONFIRMED)
        self.assertEqual(
            self.transaction.appbalancetransactions.first().status,
            TRANSACTION_STATUS_CONFIRMED,
        )
        self.assertEqual(
            self.transaction.balancetransactions.first().status,
            TRANSACTION_STATUS_CONFIRMED,
        )

    def test_processing_unsettled_sale_transaction_no_operation_id(self):
        config.ENABLE_FAKE_TRACKER = True
        config.TRACKER_CONFIG = "NOT_FUNDED"

        self.transaction.process()

        self.transaction.refresh_from_db()
        self.assertEqual(self.transaction.status, TRANSACTION_STATUS_CONFIRMED)
        self.assertEqual(
            self.transaction.appbalancetransactions.first().status,
            TRANSACTION_STATUS_CONFIRMED,
        )
        self.assertEqual(
            self.transaction.balancetransactions.first().status,
            TRANSACTION_STATUS_CONFIRMED,
        )

    def test_processing_unsettled_full_refunded_sell_transaction_is_confirmed(self):
        config.ENABLE_FAKE_TRACKER = True
        config.TRACKER_CONFIG = "NOT_FUNDED"
        self.hipay_transaction.capture_operations["fake_operation_id"] = "id"
        self.hipay_transaction.save()

        self.merchant_order.refunded_amount = self.merchant_order.amount_vat_included
        self.merchant_order.save()

        self.transaction.process()

        self.transaction.refresh_from_db()
        self.assertEqual(self.transaction.status, TRANSACTION_STATUS_CONFIRMED)
        self.assertEqual(
            self.transaction.appbalancetransactions.get().status,
            TRANSACTION_STATUS_CONFIRMED,
        )
        self.assertEqual(
            self.transaction.balancetransactions.get().status,
            TRANSACTION_STATUS_CONFIRMED,
        )

    def test_processing_unsettled_partially_refunded_sell_transaction_is_attention_req(
        self,
    ):
        config.ENABLE_FAKE_TRACKER = True
        config.TRACKER_CONFIG = "NOT_FUNDED"
        self.hipay_transaction.capture_operations["fake_operation_id"] = "id"
        self.hipay_transaction.save()

        self.merchant_order.refunded_amount = (
            self.merchant_order.amount_vat_included - Decimal("0.01")
        )
        self.merchant_order.save()

        self.transaction.process()

        self.transaction.refresh_from_db()
        self.assertEqual(self.transaction.status, TRANSACTION_STATUS_ATTENTION_REQUIRED)
        self.assertEqual(
            self.transaction.appbalancetransactions.get().status,
            TRANSACTION_STATUS_ATTENTION_REQUIRED,
        )
        self.assertEqual(
            self.transaction.balancetransactions.get().status,
            TRANSACTION_STATUS_ATTENTION_REQUIRED,
        )


class TestPaymentProcessorUpdateStatus(BaseTestCase):
    def test_update_payment_from_hipay_state_completed(self):
        payment = OrderPaymentFactory.create(
            payment_backend="hipay_tpp",
        )
        hipay_processor = payment.payment_backend_obj
        transaction_reference = "ext_ref"
        hipay_state = "completed"
        hipay_processor.update_payment_from_hipay_state(
            payment, hipay_state, transaction_reference=transaction_reference, user=None
        )
        payment.refresh_from_db()
        self.assertEqual(payment.status, "60")  # authorized
        self.assertEqual(payment.external_id, transaction_reference)

    def test_fail_update_payment_from_hipay_state_completed_on_order_lock(self):
        payment = OrderPaymentFactory.create(
            payment_backend="hipay_tpp",
        )
        hipay_processor = payment.payment_backend_obj
        transaction_reference = "ext_ref"
        hipay_state = "completed"

        order_lock = payment.order.actions.locker()

        with order_lock, self.assertRaises(OrderLockedError):
            hipay_processor.update_payment_from_hipay_state(
                payment,
                hipay_state,
                transaction_reference=transaction_reference,
                user=None,
            )

        payment_lock = payment.actions.locker()

        with payment_lock, self.assertRaises(ResourceLockedException):
            hipay_processor.update_payment_from_hipay_state(
                payment,
                hipay_state,
                transaction_reference=transaction_reference,
                user=None,
            )

    def test_update_payment_from_hipay_state_completed_on_already_authorized_payment_still_saves_external_id(  # pylint: disable=line-too-long
        self,
    ):
        payment = OrderPaymentFactory.create(
            payment_backend="hipay_tpp",
        )
        payment.authorize()
        hipay_processor = payment.payment_backend_obj
        transaction_reference = "ext_ref"
        hipay_state = "completed"
        hipay_processor.update_payment_from_hipay_state(
            payment, hipay_state, transaction_reference=transaction_reference, user=None
        )
        payment.refresh_from_db()
        self.assertEqual(payment.status, "60")  # authorized
        self.assertEqual(payment.external_id, transaction_reference)

    @mock.patch("payment_backends.hipay_tpp.processor.logger")
    def test_update_payment_from_hipay_state_completed_on_canceled_payment_logs_exception_still_saves_external_id(  # pylint: disable=line-too-long
        self, logger_mock
    ):
        payment = OrderPaymentFactory.create(
            payment_backend="hipay_tpp",
        )
        payment.cancel()
        hipay_processor = payment.payment_backend_obj
        transaction_reference = "ext_ref"
        hipay_state = "completed"
        hipay_processor.update_payment_from_hipay_state(
            payment, hipay_state, transaction_reference=transaction_reference, user=None
        )
        payment.refresh_from_db()
        self.assertEqual(payment.status, "2000")  # still canceled
        self.assertEqual(payment.external_id, transaction_reference)
        logger_mock.exception.assert_called_once_with(
            "payment %s cannot be authorized because of it's current status %s",
            payment.id,
            payment.status,
        )

    def test_update_payment_from_hipay_state_pending(self):
        payment = OrderPaymentFactory.create(
            payment_backend="hipay_tpp",
        )
        hipay_processor = processor.HipayTppPayment()
        transaction_reference = "ext_ref"
        hipay_state = "pending"
        hipay_processor.update_payment_from_hipay_state(
            payment, hipay_state, transaction_reference=transaction_reference, user=None
        )
        payment.refresh_from_db()
        self.assertEqual(payment.status, "61")  # pending
        self.assertEqual(payment.external_id, transaction_reference)

    def test_update_payment_from_hipay_state_declined(self):
        payment = OrderPaymentFactory.create(
            payment_backend="hipay_tpp",
        )
        hipay_processor = processor.HipayTppPayment()
        transaction_reference = "ext_ref"
        hipay_state = "declined"
        hipay_processor.update_payment_from_hipay_state(
            payment, hipay_state, transaction_reference=transaction_reference, user=None
        )
        payment.refresh_from_db()
        self.assertEqual(payment.status, "1000")  # failure

    def test_update_payment_from_hipay_state_error(self):
        payment = OrderPaymentFactory.create(
            payment_backend="hipay_tpp",
        )
        hipay_processor = processor.HipayTppPayment()
        transaction_reference = "ext_ref"
        hipay_state = "error"
        hipay_processor.update_payment_from_hipay_state(
            payment, hipay_state, transaction_reference=transaction_reference, user=None
        )
        payment.refresh_from_db()
        self.assertEqual(payment.status, "1000")  # failure

    @mock.patch("payment_backends.hipay_tpp.processor.logger")
    def test_update_payment_from_hipay_state_unknown(self, logger_mock):
        payment = OrderPaymentFactory.create(
            payment_backend="hipay_tpp",
        )
        hipay_processor = processor.HipayTppPayment()
        transaction_reference = "ext_ref"
        hipay_state = "non_existing_state"
        hipay_processor.update_payment_from_hipay_state(
            payment, hipay_state, transaction_reference=transaction_reference, user=None
        )
        payment.refresh_from_db()
        self.assertEqual(payment.status, "0")
        logger_mock.exception.assert_called_once_with(
            "Un-handled hipay transaction state : %s", "non_existing_state"
        )


class TestVoucherTransaction(BaseTestCase):
    def create_config(self, application):
        self.hipay_config = HipayAppConfiguration.objects.create(
            application=application,
            active=True,
            RestApiLogin="abc",
            RestApiPassword="def",
            EWalletApiEntity="entity",
            EWalletApiLogin="ghi",
            EWalletApiPassword="jkl",
            account_id=12345,
        )

    def test_sale_transaction_of_order_totally_covered_by_voucher_use_is_processable(
        self,
    ):
        # given
        rule = MerchantCommissionRuleFactory.create(
            commission_rate_value=10, commission_fixed_price_amount=0
        )
        transaction = MerchantOrderSellTransactionFactory.create(
            application=rule.application,
            payment__payment_backend=HIPAYTPP,
            application__payment_backend=HIPAYTPP,
            currency=rule.price_currency,
        )
        self.create_config(transaction.application)
        order = transaction.merchant_order.order
        discount = Discount.objects.create(
            application=order.application,
            reduction_type=40,
            reduction_value=10,
            name="Discount",
            discount_code="TOTO",
            financed_by_application=100,
        )
        order.discount_uses.create(
            cart=order.cart,
            discount=discount,
            discount_amount=order.amount,
            discount_vat_amount=order.vat,
            discount_amount_financed_by_application=order.amount,
            discount_vat_amount_financed_by_application=order.vat,
        )

        # when
        transaction.actions.process()

        # then
        transaction.refresh_from_db()
        self.assertEqual(transaction.status, TRANSACTION_STATUS_CONFIRMED)
