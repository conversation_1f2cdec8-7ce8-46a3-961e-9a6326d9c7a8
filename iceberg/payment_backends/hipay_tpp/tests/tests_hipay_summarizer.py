# -*- coding: utf-8 -*-

from decimal import Decimal

import mock
from apps.address.tests.test_address_setup_mixin import AddressTestSetupMixin
from apps.cart.models import Cart
from apps.currencies.models import Currency
from apps.ice_applications.tests import ApplicationSettingsTestsSetupMixin
from apps.orders.models import Order
from apps.orders.models.workflows.configuration import IZBERG_V2
from apps.payment.exceptions import (
    PaymentNotFoundError,
    PaymentSummarizerBackendError,
    PaymentSummarizerUnexpectedBackendFormat,
)
from apps.payment.models import Payment
from apps.payment.payment_summary import PaymentSummary
from ims.tests import BaseTestCase
from mock import patch

from ..models import HipayAppConfiguration, HipayTransaction

DUMPED_RAW_RESPONSE = {
    "transaction": {
        "attemptId": "1",
        "authorizationCode": "test123",
        "authorizedAmount": "1087.18",
        "avsResult": "",
        "capturedAmount": "1087.18",
        "cdata1": "",
        "cdata10": "",
        "cdata2": "",
        "cdata3": "",
        "cdata4": "",
        "cdata5": "",
        "cdata6": "",
        "cdata7": "",
        "cdata8": "",
        "cdata9": "",
        "currency": "EUR",
        "cvcResult": "",
        "dateAuthorized": "2017-11-23T16:35:46+0000",
        "dateCreated": "2017-11-23 17:35:42+01",
        "dateUpdated": "2017-11-23 17:35:46+01",
        "debitAgreement": {"id": "", "status": ""},
        "decimals": "2",
        "deviceId": "",
        "eci": "7",
        "forwardUrl": "",
        "fraudScreening": {"result": "ACCEPTED", "review": "", "scoring": "240"},
        "ipAddress": "***********",
        "ipCountry": "FR",
        "message": "Authorized",
        "mid": "00001328190",
        "order": {
            "amount": "1087.18",
            "attempts": "1",
            "country": "",
            "currency": "EUR",
            "customerId": "",
            "dateCreated": "2017-11-23T16:21:24+0000",
            "decimals": "2",
            "email": "<EMAIL>",
            "firstname": "",
            "gender": "U",
            "id": "hugo171123M126",
            "language": "fr_FR",
            "lastname": "",
            "locality": "",
            "msisdn": "",
            "phone": "",
            "phoneOperator": "",
            "postalCode": "",
            "shipping": "0.00",
            "shippingAddress": {
                "country": "",
                "firstname": "",
                "lastname": "",
                "locality": "",
                "postalCode": "",
                "streetAddress": "",
            },
            "streetAddress": "",
            "tax": "0.00",
        },
        "paymentMethod": {
            "brand": "VISA",
            "cardExpiryMonth": "01",
            "cardExpiryYear": "2020",
            "cardHolder": "JOHN DOE",
            "country": "US",
            "issuer": "JPMORGAN CHASE BANK, N.A.",
            "pan": "411111******1111",
            "token": "f39bfab2b6c96fa30dcc0e55aa3da4125a49ab03",
        },
        "paymentProduct": "visa",
        "reason": "",
        "refundedAmount": "0.00",
        "state": "completed",
        "status": "116",
        "test": "true",
        "threeDSecure": "",
        "transactionReference": "************",
    }
}
ORDER_NOT_FOUND_RESPONSE = {
    "code": "3000001",
    "description": "",
    "message": "Order not found",
}
TRANSACTION_NOT_FOUND_RESPONSE = {
    "code": "3000002",
    "description": "",
    "message": "Transaction not found",
}


class TestHiPaySummarizer(
    ApplicationSettingsTestsSetupMixin, AddressTestSetupMixin, BaseTestCase
):
    def setUp(self):
        ApplicationSettingsTestsSetupMixin.set_up(self, force_create=True)
        addr = self.create_user_address(self.app_admin_user)
        self.cart = Cart.objects.create(
            application=self.application,
            user=self.app_admin_user,
            shipping_address=addr,
        )
        self.order = Order.objects.create(
            cart=self.cart,
            user=self.app_admin_user,
            price=Decimal("10"),
            application=self.application,
            shipping_address=addr,
            workflow=IZBERG_V2,
        )
        self.config = self.create_config()
        self.transaction = self.create_transaction("*****************")

    def create_config(self):
        return HipayAppConfiguration.objects.create(
            application=self.application,
            account_id="1",
            RestApiLogin="abc",
            RestApiPassword="abc",
            EWalletApiLogin="abc",
            EWalletApiPassword="abc",
            EWalletApiEntity="abc",
            active=True,
            staging=True,
            cashout_enabled=False,
        )

    def create_transaction(self, transaction_reference):
        payment = Payment.objects.create(
            application=self.application,
            currency=Currency.objects.get(code="EUR"),
            order=self.order,
            payment_backend="hipay_tpp",
        )
        transaction = HipayTransaction.objects.create(
            payment=payment,
            # config=self.config,
            transaction_reference=transaction_reference,
        )
        transaction.payment.payment_backend_obj.ensure_api = mock.Mock()
        # transaction.payment.summarizer.payment_backend.config.cashin_api = mock.Mock()
        return transaction

    def mock_get_status_payload(self, trans, payload):
        payment_backend = self.transaction.payment.summarizer.payment_backend
        payment_backend.config = mock.Mock()
        payment_backend.config.cashin_api.get_status = mock.Mock()
        payment_backend.config.cashin_api.get_status.return_value = payload

    def test_raw_data_returns_cashin_api_transations(self):
        # knowing that

        self.mock_get_status_payload(self.transaction, DUMPED_RAW_RESPONSE)

        # when
        response = self.transaction.payment.summarizer.get_raw_payment_data()

        # then
        expected_data = {"cashin": DUMPED_RAW_RESPONSE["transaction"], "status": "ok"}
        self.assertEqual(sorted(response), sorted(expected_data))

    def test_backend_summary_retreives_sampled_amounts(self):
        # knowing that
        self.mock_get_status_payload(self.transaction, DUMPED_RAW_RESPONSE)

        # when
        backend_summary = self.transaction.payment.summarizer.get_backend_summary()

        # then
        expected_summary = PaymentSummary(
            authorized_amount=Decimal("1087.18"),
            collected_amount=Decimal("1087.18"),
            requested_amount=Decimal("1087.18"),
            refunded_amount=Decimal("0.00"),
        )
        self.assertDictEqual(backend_summary.as_dict(), expected_summary.as_dict())

    @patch("payment_backends.hipay_tpp.summarizer.logger.exception", lambda *_: None)
    def test_get_raw_payment_data_raises_unexpected_format_with_changed_format_payload(
        self,
    ):
        # knowing that
        self.mock_get_status_payload(self.transaction, {"invalid_key": {}})

        with self.assertRaises(PaymentSummarizerBackendError):
            self.transaction.payment.summarizer.get_raw_payment_data()

    @patch("payment_backends.hipay_tpp.summarizer.logger.exception", lambda *_: None)
    def test_get_backend_summary_raises_unexpected_format_with_changed_format_payload(
        self,
    ):
        # knowing that
        self.mock_get_status_payload(self.transaction, {"transaction": {}})

        with self.assertRaises(PaymentSummarizerUnexpectedBackendFormat):
            self.transaction.payment.summarizer.get_backend_summary()

    def test_raw_data_unknown_order_raise_error(self):
        # knowing that
        self.mock_get_status_payload(self.transaction, ORDER_NOT_FOUND_RESPONSE)

        # when
        with self.assertRaises(PaymentNotFoundError) as err:
            self.transaction.payment.summarizer.get_raw_payment_data()
        self.assertEqual(err.exception.error_code, "3000001")
        self.assertEqual(err.exception.reason, "Order not found")

    def test_raw_data_unknown_transaction_raise_error(self):
        # knowing that
        self.mock_get_status_payload(self.transaction, TRANSACTION_NOT_FOUND_RESPONSE)

        # when
        with self.assertRaises(PaymentNotFoundError) as err:
            self.transaction.payment.summarizer.get_raw_payment_data()
        self.assertEqual(err.exception.error_code, "3000002")
        self.assertEqual(err.exception.reason, "Transaction not found")
