# -*- coding: utf-8 -*-

from apps.ice_applications.app_conf_settings import EnableKycV2
from apps.kyc.models import MerchantIdentityDocument
from apps.stores.tests import TestMerchantSetupMixin
from apps.testing.factories import (
    ApplicationFactory,
    HipayAppConfigurationFactory,
    HipayMerchantBankAccountKycV1Factory,
    HipayOperatorWalletAccountFactory,
    HipayWalletAccountFactory,
    IdentityDocumentPartFactory,
    MerchantAddressFactory,
    MerchantFactory,
    MerchantIdentityDocumentFactory,
)
from apps.testing.factories.hipay import AddressLessHipayWalletAccountFactory
from django.core.exceptions import ValidationError
from django.test import override_settings
from ims.tests import BaseTestCase
from mock import Mock, patch
from payment_backends.hipay_tpp import tasks
from payment_backends.hipay_tpp.actions import WalletAccountValidator
from payment_backends.hipay_tpp.actions.wallet_account.wallet_account import (
    HipayWalletAccountHelper,
)
from payment_backends.hipay_tpp.models import HipayWalletAccount
from payment_backends.hipay_tpp.references import HIPAY_ACCOUNT_TYPE_PERSON
from payment_backends.hipay_tpp.tests.tests_utils import VALID_USER_INFO_RESPONSE
from requests import HTTPError


class ValidateWalletInfosTestCase(BaseTestCase):
    def setUp(self):
        self.conf = Mock()
        self.conf.UserApiEndpoint = "endpoint"
        self.conf.EWalletApiLogin = "login"
        self.conf.EWalletApiPassword = "endpoint"

    def test_validate_valid_credentials(self):
        self.conf.cashout_api.get_hipay_wallet.return_value = VALID_USER_INFO_RESPONSE

        validator = WalletAccountValidator(self.conf)

        self.assertTrue(validator.validate(1111111, 2222222))

    def test_validate_invalid_account_id(self):
        self.conf.cashout_api.get_hipay_wallet.return_value = VALID_USER_INFO_RESPONSE

        validator = WalletAccountValidator(self.conf)

        self.assertFalse(validator.validate("invalid", 2222222))

    def test_validate_invalid_space_id(self):
        self.conf.cashout_api.get_hipay_wallet.return_value = VALID_USER_INFO_RESPONSE

        validator = WalletAccountValidator(self.conf)

        self.assertFalse(validator.validate(1111111, "invalid"))

    def test_validate_http_error_should_not_be_catched(self):
        self.conf.cashout_api.get_hipay_wallet.side_effect = HTTPError

        validator = WalletAccountValidator(self.conf)

        with self.assertRaises(HTTPError):
            self.assertFalse(validator.validate(1111111, 2222222))


class HipayWalletAccountModel(TestMerchantSetupMixin, BaseTestCase):
    def setUp(self, *args, **kwargs):
        self.address = MerchantAddressFactory.create(billing_address=True)
        self.merchant = self.address.merchant
        self.application = self.merchant.application
        self.create_merchant_user(merchant=self.merchant)
        HipayAppConfigurationFactory.create(application=self.merchant.application)

    def test_fail_on_create_if_no_billing_address(self):
        with self.assertRaises(ValidationError):
            self.address.billing_address = False
            self.address.save()
            HipayWalletAccount.objects.create(
                application=self.merchant.application,
                merchant=self.merchant,
                user_account_id=1111111,
                user_space_id=2222222,
                api_login="123",
                api_password="123",
            )

    def test_succeed_on_create_with_billing_address(self):
        HipayWalletAccount.objects.create(
            application=self.merchant.application,
            merchant=self.merchant,
            user_account_id=1111111,
            user_space_id=2222222,
            api_login="123",
            api_password="123",
        )

    def test_create_valid_account(self):
        try:
            HipayWalletAccountFactory.create(
                application=self.merchant.application,
                merchant=self.merchant,
                user_account_id=1111111,
                user_space_id=2222222,
            )
        except ValidationError as e:
            msg = (
                "Should not raise validation error "
                "on valid account! Error: {}".format(str(e))
            )
            self.fail(msg)

    def test_create_application_wallet(self):
        HipayOperatorWalletAccountFactory.create(
            application=ApplicationFactory.create(),
            user_account_id=1111111,
            user_space_id=2222222,
        )

    def test_inconsistent_application(self):
        with self.assertRaises(ValidationError):
            HipayWalletAccountFactory.create(
                merchant=self.merchant,
                application=ApplicationFactory.create(),
                user_account_id=1111111,
                user_space_id=2222222,
            )

    def test_inconsistent_hipay_data_no_account_id(self):
        with self.assertRaises(ValidationError):
            HipayWalletAccountFactory.create(
                application=self.merchant.application,
                merchant=self.merchant,
                user_account_id=None,
                user_space_id=2222222,
            )

    def test_inconsistent_hipay_data_no_space_id(self):
        with self.assertRaises(ValidationError):
            HipayWalletAccountFactory.create(
                application=self.merchant.application,
                merchant=self.merchant,
                user_account_id=1111111,
                user_space_id=None,
            )

    def test_inconsistent_hipay_data_no_api_login(self):
        with self.assertRaises(ValidationError):
            HipayWalletAccountFactory.create(
                application=self.merchant.application,
                merchant=self.merchant,
                user_account_id=1111111,
                user_space_id=2222222,
                api_login=None,
            )

    def test_inconsistent_hipay_data_no_api_password(self):
        with self.assertRaises(ValidationError):
            HipayWalletAccountFactory.create(
                application=self.merchant.application,
                merchant=self.merchant,
                user_account_id=1111111,
                user_space_id=2222222,
                api_password=None,
            )

    def test_should_raise_validation_error_if_merchant_already_have_wallet(self):
        with patch.object(HipayWalletAccount, "clean"):
            wallet = HipayWalletAccountFactory.create(
                application=self.merchant.application, merchant=self.merchant
            )

        with self.assertRaises(ValidationError):
            HipayWalletAccountFactory.create(
                application=wallet.merchant.application, merchant=wallet.merchant
            )

    def test_should_not_raise_validation_error_on_first_creation(self):
        try:
            HipayWalletAccountFactory.create(
                application=self.merchant.application,
                merchant=self.merchant,
            )
        except Exception as e:
            self.fail("Should not failed on first creation. Error: {}".format(str(e)))

    def test_should_raise_validation_error_if_no_address(self):
        HipayAppConfigurationFactory.create(application=self.application)
        merchant = MerchantFactory.create(
            application=self.application,
            default_currency=self.application.default_currency,
        )
        merchant.address.all().delete()
        with self.assertRaises(ValidationError):
            AddressLessHipayWalletAccountFactory.create(
                application=self.application, merchant=merchant
            )

    @patch.object(tasks, "create_hipay_account")
    def test_should_delay_hipay_creation(self, mocked_task):
        HipayWalletAccountFactory.create(
            application=self.merchant.application,
            merchant=self.merchant,
            user_account_id=None,
            user_space_id=None,
            api_password=None,
            api_login=None,
        )

        mocked_task.delay.assert_called_once()

    @patch.object(tasks, "validate_hipay_account")
    def test_should_delay_hipay_validation(self, mocked_task):
        HipayWalletAccountFactory.create(
            application=self.merchant.application,
            merchant=self.merchant,
        )

        mocked_task.delay.assert_called_once()

    @patch.object(tasks, "validate_hipay_account")
    def test_should_not_delay_hipay_validation(self, mocked_task):
        HipayWalletAccountFactory.create(
            application=self.merchant.application,
            merchant=self.merchant,
            user_account_id=None,
            user_space_id=None,
            api_password=None,
            api_login=None,
        )

        mocked_task.delay.assert_not_called()


class TestWalletAccountHelper(BaseTestCase):
    def _create_all_docs_but_id_proof(self, merchant):
        MerchantIdentityDocumentFactory.create(
            document_type=MerchantIdentityDocument.BANK_INFOS_COPY,
            merchant=merchant,
            status=MerchantIdentityDocument.ACTIVE,
        )
        MerchantIdentityDocumentFactory.create(
            document_type=MerchantIdentityDocument.REGISTRATION_PROOF,
            merchant=merchant,
            status=MerchantIdentityDocument.ACTIVE,
        )
        MerchantIdentityDocumentFactory.create(
            document_type=MerchantIdentityDocument.ARTICLES_OF_ASSOCIATION,
            merchant=merchant,
            status=MerchantIdentityDocument.ACTIVE,
        )

    def _create_id_proof(self, merchant):
        id_proof = MerchantIdentityDocumentFactory.create(
            document_type=MerchantIdentityDocument.IDENTITY_PROOF,
            merchant=merchant,
            status=MerchantIdentityDocument.ACTIVE,
        )
        IdentityDocumentPartFactory.create(document=id_proof, tag="front")
        IdentityDocumentPartFactory.create(document=id_proof, tag="back")

    def _create_all_docs(self, merchant):
        self._create_all_docs_but_id_proof(merchant)
        self._create_id_proof(merchant)

    def test_should_not_be_able_to_create_account(self):
        merchant = MerchantFactory.create()

        can_create = HipayWalletAccountHelper.can_create(merchant)

        self.assertIsInstance(can_create, dict)
        self.assertIn("can_create", can_create)
        self.assertIn("reason", can_create)
        self.assertFalse(can_create["can_create"])

    def test_should_not_be_able_to_create_a_second_account(self):
        address = MerchantAddressFactory.create()
        merchant = address.merchant
        HipayWalletAccountFactory.create(
            application=merchant.application, merchant=merchant
        )

        can_create = HipayWalletAccountHelper.can_create(merchant)

        self.assertIsInstance(can_create, dict)
        self.assertIn("can_create", can_create)
        self.assertIn("reason", can_create)
        self.assertFalse(can_create["can_create"])

    def test_should_be_able_to_create_account(self):
        address = MerchantAddressFactory.create()
        merchant = address.merchant

        can_create = HipayWalletAccountHelper.can_create(merchant)

        self.assertIsInstance(can_create, dict)
        self.assertIn("can_create", can_create)
        self.assertIn("reason", can_create)
        self.assertTrue(can_create["can_create"])
        self.assertEqual(can_create["reason"], "")

    @override_settings(LANGUAGE_CODE="en-US", LANGUAGES=(("en", "English"),))
    def test_should_not_be_ready_when_missing_docs(self):
        address = MerchantAddressFactory.create()
        merchant = address.merchant
        HipayAppConfigurationFactory.create(application=merchant.application)
        payment_settings = merchant.application.payment_settings
        payment_settings.payment_backend = "hipay_tpp"
        payment_settings.save()
        merchant.application.set_setting(EnableKycV2, False)
        account = HipayWalletAccountFactory.create(
            application=merchant.application,
            merchant=merchant,
            account_type=HIPAY_ACCOUNT_TYPE_PERSON,
        )
        HipayMerchantBankAccountKycV1Factory(merchant=merchant)
        self._create_all_docs_but_id_proof(merchant)

        is_ready = HipayWalletAccountHelper(account).is_ready()

        self.assertIsInstance(is_ready, dict)
        self.assertIn("ready", is_ready)
        self.assertIn("missing", is_ready)
        self.assertFalse(is_ready["ready"])
        self.assertDictEqual(
            is_ready["missing"],
            {
                "hipay_bank_account": False,
                "document_types": [{"help_text": "Identity Proof", "value": 1}],
            },
        )

    def test_should_not_be_ready_when_missing_bank_account(self):
        address = MerchantAddressFactory.create()
        merchant = address.merchant
        merchant.application.set_setting(EnableKycV2, False)
        HipayAppConfigurationFactory.create(application=merchant.application)
        payment_settings = merchant.application.payment_settings
        payment_settings.payment_backend = "hipay_tpp"
        payment_settings.save()
        account = HipayWalletAccountFactory.create(
            application=merchant.application,
            merchant=merchant,
            account_type=HIPAY_ACCOUNT_TYPE_PERSON,
        )
        self._create_all_docs(merchant)

        is_ready = HipayWalletAccountHelper(account).is_ready()

        self.assertIsInstance(is_ready, dict)
        self.assertIn("ready", is_ready)
        self.assertIn("missing", is_ready)
        self.assertFalse(is_ready["ready"])
        self.assertDictEqual(
            is_ready["missing"], {"hipay_bank_account": True, "document_types": []}
        )

    def test_should_be_ready(self):
        address = MerchantAddressFactory.create()
        merchant = address.merchant
        merchant.application.set_setting(EnableKycV2, False)
        HipayAppConfigurationFactory.create(application=merchant.application)
        payment_settings = merchant.application.payment_settings
        payment_settings.payment_backend = "hipay_tpp"
        payment_settings.save()
        account = HipayWalletAccountFactory.create(
            application=merchant.application,
            merchant=merchant,
            account_type=HIPAY_ACCOUNT_TYPE_PERSON,
        )
        HipayMerchantBankAccountKycV1Factory(merchant=merchant)
        self._create_all_docs(merchant)

        is_ready = HipayWalletAccountHelper(account).is_ready()

        self.assertIsInstance(is_ready, dict)
        self.assertIn("ready", is_ready)
        self.assertIn("missing", is_ready)
        self.assertTrue(is_ready["ready"])
        self.assertDictEqual(
            is_ready["missing"], {"hipay_bank_account": False, "document_types": []}
        )
