# -*- coding: utf-8 -*-

from apps.stores.tests import TestMerchantSetupMixin
from ims.tests import BaseResourceTestCase
from mock import patch

GET_CONF_PATH = "payment_backends.hipay_tpp.utils.get_conf_for_application"


class FakeConf:
    def is_identified(self, wallet):
        return True


@patch(GET_CONF_PATH, lambda x: FakeConf())
class ValidateWalletInfosResourceTestCase(TestMerchantSetupMixin, BaseResourceTestCase):
    def setUp(self, *args, **kwargs):
        super(ValidateWalletInfosResourceTestCase, self).setUp(*args, **kwargs)
        self.set_up()

    def test_create_valid_account(self):
        post_data = {
            "api_login": "login",
            "api_password": "password",
            "application": "/v1/application/{}/".format(self.application.id),
            "email": "<EMAIL>",
            "merchant": "/v1/merchant/{}/".format(self.merchant.id),
            "user_account_id": 1111111,
            "user_space_id": 2222222,
        }
        resp = self.api_client.post(
            "/v1/hipay_merchant_account/",
            data=post_data,
            authentication=self.get_token(self.app_admin_user),
        )
        self.assertHttpCreated(resp)
