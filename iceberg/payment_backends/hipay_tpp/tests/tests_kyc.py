# -*- coding: utf-8 -*-

import uuid

from apps.ice_applications.app_conf_settings import EnableKycV2
from apps.kyc.models import KycInformationWorkflow, MerchantIdentityDocument
from apps.stores.tests.test_merchant_setup_mixin import TestMerchantSetupMixin
from apps.testing.factories import (
    ApplicationFactory,
    HipayAppConfigurationFactory,
    HipayMerchantBankAccountKycV1Factory,
    HipayWalletAccountFactory,
    KycInformationFactory,
    MerchantAddressFactory,
    MerchantFactory,
    MerchantIdentityDocumentFactory,
)
from django.test.utils import override_settings
from ims.tests import BaseTestCase
from mock import Mock, patch
from payment_backends.hipay_tpp.hipay_api.kyc import (
    KYC_DOCUMENT_CLASSIFICATION_MATCHING,
)
from payment_backends.hipay_tpp.kyc import HipayKycBackend
from payment_backends.hipay_tpp.models import HipayAppConfiguration
from payment_backends.hipay_tpp.processor import HipayTppPayment
from payment_backends.hipay_tpp.references import (
    HIPAY_ACCOUNT_TYPE_ASSOCIATION,
    HIPAY_ACCOUNT_TYPE_CORPORATION,
    HIPAY_ACCOUNT_TYPE_PERSON,
)
from payment_backends.hipay_tpp.tasks import refresh_kyc_states
from reference.status import DOCUMENT_EXPIRED, DOCUMENT_UNDER_ANALYSIS

from .tests_hipay_mixin import TestHipayMixin


class KYCTestCase(TestHipayMixin, TestMerchantSetupMixin, BaseTestCase):
    def setUp(self):
        TestMerchantSetupMixin.set_up(self)
        self.application.set_setting(EnableKycV2, False)
        super(KYCTestCase, self).setUp()

    def get_document(self, status=MerchantIdentityDocument.ACTIVE, save=True):
        doc = MerchantIdentityDocument(
            document_type=MerchantIdentityDocument.IDENTITY_PROOF,
            status=status,
            merchant=self.merchant,
        )
        if save:
            doc.save()
        return doc

    def test_refresh_documents(self):
        self.doc = self.get_document()
        self.create_hipay_config(application=self.application)
        refresh_kyc_states()


class TestKycBackendKyc(BaseTestCase):
    def setUp(self):
        address = MerchantAddressFactory.create()
        self.merchant = address.merchant
        self.application = self.merchant.application
        self.application.set_setting(EnableKycV2, False)
        self.config = Mock()
        self.account = HipayWalletAccountFactory.create(
            application=self.merchant.application, merchant=self.merchant
        )
        self.bank_account = HipayMerchantBankAccountKycV1Factory(merchant=self.merchant)

    def test_is_acceptable_should_be_false_when_type_miss_matches_account_type(self):
        self.bank_account.account_type = HIPAY_ACCOUNT_TYPE_ASSOCIATION
        document = MerchantIdentityDocumentFactory.build(
            merchant=self.merchant,
            document_type=MerchantIdentityDocument.MARKETPLACE_AGREEMENT,
        )
        backend = HipayKycBackend(self.merchant, self.config)
        backend.merchant_wallet = HipayWalletAccountFactory.build(
            application=document.merchant.application, merchant=document.merchant
        )
        acceptable, _ = backend.is_document_acceptable(document)

        self.assertFalse(acceptable)

    def test_should_return_association_only_documents_meta(self):
        backend = HipayKycBackend(self.merchant, self.config)
        self.account.account_type = HIPAY_ACCOUNT_TYPE_ASSOCIATION
        self.account.save()

        types_meta = backend.meta.doc_types

        mapping = KYC_DOCUMENT_CLASSIFICATION_MATCHING[HIPAY_ACCOUNT_TYPE_ASSOCIATION]
        self.assertListEqual(sorted(types_meta.keys()), sorted(mapping.keys()))

    def test_should_return_corporation_only_documents_meta(self):
        backend = HipayKycBackend(self.merchant, self.config)
        self.account.account_type = HIPAY_ACCOUNT_TYPE_CORPORATION
        self.account.save()

        types_meta = backend.meta.doc_types

        mapping = KYC_DOCUMENT_CLASSIFICATION_MATCHING[HIPAY_ACCOUNT_TYPE_CORPORATION]
        self.assertListEqual(sorted(types_meta.keys()), sorted(mapping.keys()))

    def test_should_return_person_only_documents_meta(self):
        backend = HipayKycBackend(self.merchant, self.config)
        self.account.account_type = HIPAY_ACCOUNT_TYPE_PERSON
        self.account.save()

        types_meta = backend.meta.doc_types

        mapping = KYC_DOCUMENT_CLASSIFICATION_MATCHING[HIPAY_ACCOUNT_TYPE_PERSON]
        self.assertListEqual(sorted(types_meta.keys()), sorted(mapping.keys()))

    def test_should_return_all_types_if_no_account(self):
        merchant = MerchantFactory.build(application=self.application)
        backend = HipayKycBackend(merchant, self.config)

        types_meta = backend.meta.doc_types

        types = list(
            {
                doc_type
                for mapping in KYC_DOCUMENT_CLASSIFICATION_MATCHING.values()
                for doc_type in mapping.keys()
            }
        )
        self.assertListEqual(sorted(types_meta.keys()), sorted(types))

    def test_identity_proof_meta_should_include_back_tag(self):
        backend = HipayKycBackend(self.merchant, self.config)
        self.account.account_type = HIPAY_ACCOUNT_TYPE_PERSON
        self.account.save()

        types_meta = backend.meta.get_document_types_meta()

        id_proof = types_meta.get(MerchantIdentityDocument.IDENTITY_PROOF)
        self.assertIsNotNone(id_proof)
        back_tag = id_proof.get("back")
        self.assertIsNotNone(back_tag)
        self.assertEqual(back_tag.get("name"), "Identity Proof")


def raise_exception(*args, **kwargs):
    raise Exception("Should never be triggered.")


# hipay API should never be queried. Patching requests just in case implem
# changes.
@patch(
    "payment_backends.hipay_tpp.hipay_api.rest_api_interface.tls_request",
    raise_exception,
)
class CanPublishV1TestCase(BaseTestCase):
    WALLET_MOCK_PATH = (
        "payment_backends.hipay_tpp.models.wallet_account."
        "HipayWalletAccount.clean_application_and_merchant"
    )

    def test_merchant_wallet_does_not_exist(self):
        document = MerchantIdentityDocumentFactory.build()
        config = HipayAppConfiguration(
            application=document.merchant.application, active=True
        )
        backend = HipayKycBackend(document.merchant, config)
        self.assertIsNone(backend.merchant_wallet)
        self.assertEqual(
            backend.can_publish(document), (False, "Missing or unsynced wallet")
        )

    def test_inactive_document(self):
        with patch("apps.kyc.models.MerchantIdentityDocument.activate"):
            document = MerchantIdentityDocumentFactory(
                merchant__application__app_settings={EnableKycV2: False},
            )
        with patch(self.WALLET_MOCK_PATH):
            HipayWalletAccountFactory(
                application=document.merchant.application, merchant=document.merchant
            )
        config = HipayAppConfiguration(
            application=document.merchant.application, active=True
        )

        backend = HipayKycBackend(document.merchant, config)
        self.assertEqual(backend.can_publish(document), (False, "Inactive document"))

    def test_document_expired_on_backend(self):
        document = MerchantIdentityDocumentFactory(
            status=MerchantIdentityDocument.ACTIVE,
            kyc_backend_status=DOCUMENT_EXPIRED,
            merchant__application__app_settings={EnableKycV2: False},
        )
        with patch(self.WALLET_MOCK_PATH):
            HipayWalletAccountFactory(
                application=document.merchant.application, merchant=document.merchant
            )
        config = HipayAppConfiguration(
            application=document.merchant.application, active=True
        )
        backend = HipayKycBackend(document.merchant, config)

        self.assertEqual(backend.can_publish(document), (False, "Non pending document"))

    def test_document_under_analysis_on_backend(self):
        document = MerchantIdentityDocumentFactory(
            status=MerchantIdentityDocument.ACTIVE,
            kyc_backend_status=DOCUMENT_UNDER_ANALYSIS,
            merchant__application__app_settings={EnableKycV2: False},
        )
        with patch(self.WALLET_MOCK_PATH):
            HipayWalletAccountFactory(
                application=document.merchant.application, merchant=document.merchant
            )
        config = HipayAppConfiguration(
            application=document.merchant.application, active=True
        )

        backend = HipayKycBackend(document.merchant, config)

        self.assertEqual(backend.can_publish(document), (False, "Non pending document"))

    def test_bank_info_doc(self):
        document = MerchantIdentityDocumentFactory(
            status=MerchantIdentityDocument.ACTIVE,
            merchant__application__app_settings={EnableKycV2: False},
            document_type=MerchantIdentityDocument.BANK_INFOS_COPY,
        )
        with patch(self.WALLET_MOCK_PATH):
            HipayWalletAccountFactory(
                application=document.merchant.application, merchant=document.merchant
            )
        config = HipayAppConfiguration(
            application=document.merchant.application, active=True
        )

        backend = HipayKycBackend(document.merchant, config)
        self.assertEqual(
            backend.can_publish(document), (False, "Bank info are ignored")
        )

    def test_account_already_identified_on_backend(self):
        document = MerchantIdentityDocumentFactory(
            merchant__application__app_settings={EnableKycV2: False},
            status=MerchantIdentityDocument.ACTIVE,
        )
        with patch(self.WALLET_MOCK_PATH):
            HipayWalletAccountFactory(
                application=document.merchant.application, merchant=document.merchant
            )
        config = HipayAppConfiguration(
            application=document.merchant.application, active=True
        )

        backend = HipayKycBackend(document.merchant, config)
        mock_api = (
            "payment_backends.hipay_tpp.hipay_api.cashout.HipayCashoutApi.request"
        )
        with patch(mock_api) as mocked_api:
            mocked_api.return_value = {"identified": 1}
            self.assertEqual(
                backend.can_publish(document), (False, "Merchant already identified")
            )

    def test_valid_case(self):
        document = MerchantIdentityDocumentFactory(
            merchant__application__app_settings={EnableKycV2: False},
            status=MerchantIdentityDocument.ACTIVE,
        )
        with patch(self.WALLET_MOCK_PATH):
            HipayWalletAccountFactory(
                application=document.merchant.application, merchant=document.merchant
            )
        config = HipayAppConfiguration(
            application=document.merchant.application, active=True
        )

        backend = HipayKycBackend(document.merchant, config)
        mock_api = (
            "payment_backends.hipay_tpp.hipay_api.cashout.HipayCashoutApi.request"
        )
        with patch(mock_api) as mocked_api:
            mocked_api.return_value = {"identified": 0}
            self.assertEqual(backend.can_publish(document), (True, None))


# hipay API should never be queried. Patching requests just in case implem
# changes.
@patch(
    "payment_backends.hipay_tpp.hipay_api.rest_api_interface.tls_request",
    raise_exception,
)
class CanPublishV2TestCase(BaseTestCase):
    WALLET_MOCK_PATH = (
        "payment_backends.hipay_tpp.models.wallet_account."
        "HipayWalletAccount.clean_application_and_merchant"
    )

    def test_merchant_wallet_does_not_exist(self):
        document = KycInformationFactory.build(status=KycInformationWorkflow.ACTIVE)
        config = HipayAppConfiguration(
            application=document.merchant.application, active=True
        )
        backend = HipayKycBackend(document.merchant, config)
        self.assertIsNone(backend.merchant_wallet)
        self.assertEqual(
            backend.can_publish(document), (False, "Missing or unsynced wallet")
        )

    def test_inactive_document(self):
        application = ApplicationFactory()
        application.set_setting(EnableKycV2, True)
        document = KycInformationFactory(
            merchant__application=application,
            merchant__default_currency=application.default_currency,
        )
        with patch(self.WALLET_MOCK_PATH):
            HipayWalletAccountFactory(
                application=document.merchant.application, merchant=document.merchant
            )
        config = HipayAppConfiguration(
            application=document.merchant.application, active=True
        )

        backend = HipayKycBackend(document.merchant, config)
        self.assertEqual(backend.can_publish(document), (False, "Inactive document"))

    def test_document_expired_on_backend(self):
        document = KycInformationFactory(
            merchant__application__app_settings={EnableKycV2: True},
            status=KycInformationWorkflow.ACTIVE,
            kyc_backend_status=DOCUMENT_EXPIRED,
        )
        with patch(self.WALLET_MOCK_PATH):
            HipayWalletAccountFactory(
                application=document.merchant.application, merchant=document.merchant
            )
        config = HipayAppConfiguration(
            application=document.merchant.application, active=True
        )

        backend = HipayKycBackend(document.merchant, config)
        self.assertEqual(backend.can_publish(document), (False, "Non pending document"))

    def test_bank_info_doc(self):
        document = KycInformationFactory(
            merchant__application__app_settings={EnableKycV2: True},
            status=KycInformationWorkflow.ACTIVE,
            kyc_type__external_id=MerchantIdentityDocument.BANK_INFOS_COPY,
        )
        with patch(self.WALLET_MOCK_PATH):
            HipayWalletAccountFactory(
                application=document.merchant.application, merchant=document.merchant
            )
        config = HipayAppConfiguration(
            application=document.merchant.application, active=True
        )

        backend = HipayKycBackend(document.merchant, config)
        self.assertEqual(
            backend.can_publish(document), (False, "Bank info are ignored")
        )

    def test_account_already_identified_on_backend(self):
        document = KycInformationFactory(
            merchant__application__app_settings={EnableKycV2: True},
            status=KycInformationWorkflow.ACTIVE,
        )
        with patch(self.WALLET_MOCK_PATH):
            HipayWalletAccountFactory(
                application=document.merchant.application, merchant=document.merchant
            )
        config = HipayAppConfiguration(
            application=document.merchant.application, active=True
        )

        backend = HipayKycBackend(document.merchant, config)
        mock_api = (
            "payment_backends.hipay_tpp.hipay_api.cashout.HipayCashoutApi.request"
        )
        with patch(mock_api) as mocked_api:
            mocked_api.return_value = {"identified": 1}
            self.assertEqual(
                backend.can_publish(document), (False, "Merchant already identified")
            )

    def test_valid_case(self):
        document = KycInformationFactory(
            merchant__application__app_settings={EnableKycV2: True},
            status=KycInformationWorkflow.ACTIVE,
        )
        with patch(self.WALLET_MOCK_PATH):
            HipayWalletAccountFactory(
                application=document.merchant.application, merchant=document.merchant
            )
        config = HipayAppConfiguration(
            application=document.merchant.application, active=True
        )

        backend = HipayKycBackend(document.merchant, config)
        mock_api = (
            "payment_backends.hipay_tpp.hipay_api.cashout.HipayCashoutApi.request"
        )
        with patch(mock_api) as mocked_api:
            mocked_api.return_value = {"identified": 0}
            self.assertEqual(backend.can_publish(document), (True, None))


@patch("apps.payment.backends_pool.PaymentBackendsPool.get_backend")
@patch("payment_backends.hipay_tpp.hipay_api.kyc.HipayKycApi.request")
class RefreshKycStatusTestCase(BaseTestCase):
    WALLET_MOCK_PATH = (
        "payment_backends.hipay_tpp.models.wallet_account."
        "HipayWalletAccount.clean_application_and_merchant"
    )

    def test_terminal_state_case(self, mocked_api, mocked_backend):
        mocked_backend.return_value = HipayTppPayment()
        document = MerchantIdentityDocumentFactory.build(
            merchant__application__app_settings={EnableKycV2: False},
            kyc_backend_status=DOCUMENT_EXPIRED,
        )
        document.refresh_kyc_backend_status()

    def test_cashout_disabled_case(self, mocked_api, mocked_backend):
        mocked_backend.return_value = HipayTppPayment()
        document = MerchantIdentityDocumentFactory(
            merchant__application__app_settings={EnableKycV2: False},
        )
        HipayAppConfigurationFactory(
            application=document.merchant.application,
            active=True,
            cashout_enabled=False,
        )
        document.refresh_kyc_backend_status()

    def test_no_wallet_case(self, mocked_api, mocked_backend):
        mocked_backend.return_value = HipayTppPayment()
        document = MerchantIdentityDocumentFactory(
            merchant__application__app_settings={EnableKycV2: False},
        )
        HipayAppConfigurationFactory(
            application=document.merchant.application, active=True, cashout_enabled=True
        )
        document.refresh_kyc_backend_status()

    def test_with_v1_doc_no_external_id(self, mocked_api, mocked_backend):
        mocked_backend.return_value = HipayTppPayment()
        document = MerchantIdentityDocumentFactory(
            merchant__application__app_settings={EnableKycV2: False},
        )
        HipayAppConfigurationFactory(
            application=document.merchant.application, active=True, cashout_enabled=True
        )
        with patch(self.WALLET_MOCK_PATH):
            HipayWalletAccountFactory(
                application=document.merchant.application, merchant=document.merchant
            )
        document.refresh_kyc_backend_status()

    def test_v1_accepted_doc(self, mocked_api, mocked_backend):
        mocked_backend.return_value = HipayTppPayment()
        document = MerchantIdentityDocumentFactory(
            merchant__application__app_settings={EnableKycV2: False}, external_id="1"
        )
        HipayAppConfigurationFactory(
            application=document.merchant.application, active=True, cashout_enabled=True
        )
        with patch(self.WALLET_MOCK_PATH):
            HipayWalletAccountFactory(
                application=document.merchant.application, merchant=document.merchant
            )
        mocked_api.return_value = {
            "code": 0,
            "documents": [
                {
                    "type": int(document.external_id),
                    "status_code": 2,  # accepted
                    "status_label": "dont care",
                }
            ],
        }
        document.refresh_kyc_backend_status()
        self.assertTrue(document.kyc_backend_status.is_accepted)

    def test_v1_refused_doc(self, mocked_api, mocked_backend):
        mocked_backend.return_value = HipayTppPayment()
        document = MerchantIdentityDocumentFactory(
            merchant__application__app_settings={EnableKycV2: False}, external_id="1"
        )
        HipayAppConfigurationFactory(
            application=document.merchant.application, active=True, cashout_enabled=True
        )
        with patch(self.WALLET_MOCK_PATH):
            HipayWalletAccountFactory(
                application=document.merchant.application, merchant=document.merchant
            )
        mocked_api.return_value = {
            "code": 0,
            "documents": [
                {
                    "type": int(document.external_id),
                    "status_code": 3,  # refused
                    "status_label": "dont care",
                }
            ],
        }
        document.refresh_kyc_backend_status()
        self.assertTrue(document.kyc_backend_status.is_refused)

    def test_v1_under_analysis_doc(self, mocked_api, mocked_backend):
        mocked_backend.return_value = HipayTppPayment()
        document = MerchantIdentityDocumentFactory(
            merchant__application__app_settings={EnableKycV2: False}, external_id="1"
        )
        HipayAppConfigurationFactory(
            application=document.merchant.application, active=True, cashout_enabled=True
        )
        with patch(self.WALLET_MOCK_PATH):
            HipayWalletAccountFactory(
                application=document.merchant.application, merchant=document.merchant
            )
        mocked_api.return_value = {
            "code": 0,
            "documents": [
                {
                    "type": int(document.external_id),
                    "status_code": 9,  # under analysis
                    "status_label": "dont care",
                }
            ],
        }
        document.refresh_kyc_backend_status()
        self.assertTrue(document.kyc_backend_status.is_under_analysis)

    def test_with_v2_doc_no_external_id(self, mocked_api, mocked_backend):
        mocked_backend.return_value = HipayTppPayment()
        document = KycInformationFactory(
            merchant__application__app_settings={EnableKycV2: True},
        )
        HipayAppConfigurationFactory(
            application=document.merchant.application, active=True, cashout_enabled=True
        )
        with patch(self.WALLET_MOCK_PATH):
            HipayWalletAccountFactory(
                application=document.merchant.application, merchant=document.merchant
            )
        document.refresh_kyc_backend_status()

    def test_v2_accepted_doc(self, mocked_api, mocked_backend):
        mocked_backend.return_value = HipayTppPayment()
        document = KycInformationFactory(
            merchant__application__app_settings={EnableKycV2: True}, external_id="1"
        )
        HipayAppConfigurationFactory(
            application=document.merchant.application, active=True, cashout_enabled=True
        )
        with patch(self.WALLET_MOCK_PATH):
            HipayWalletAccountFactory(
                application=document.merchant.application, merchant=document.merchant
            )
        mocked_api.return_value = {
            "code": 0,
            "documents": [
                {
                    "type": int(document.external_id),
                    "status_code": 2,  # accepted
                    "status_label": "dont care",
                }
            ],
        }
        document.refresh_kyc_backend_status()
        self.assertTrue(document.kyc_backend_status.is_accepted)

    def test_v2_refused_doc(self, mocked_api, mocked_backend):
        mocked_backend.return_value = HipayTppPayment()
        document = KycInformationFactory(
            merchant__application__app_settings={EnableKycV2: True}, external_id="1"
        )
        HipayAppConfigurationFactory(
            application=document.merchant.application, active=True, cashout_enabled=True
        )
        with patch(self.WALLET_MOCK_PATH):
            HipayWalletAccountFactory(
                application=document.merchant.application, merchant=document.merchant
            )
        mocked_api.return_value = {
            "code": 0,
            "documents": [
                {
                    "type": int(document.external_id),
                    "status_code": 3,  # refused
                    "status_label": "dont care",
                }
            ],
        }
        document.refresh_kyc_backend_status()
        self.assertTrue(document.kyc_backend_status.is_refused)

    def test_v2_under_analysis_doc(self, mocked_api, mocked_backend):
        mocked_backend.return_value = HipayTppPayment()
        document = KycInformationFactory(
            merchant__application__app_settings={EnableKycV2: True}, external_id="1"
        )
        HipayAppConfigurationFactory(
            application=document.merchant.application, active=True, cashout_enabled=True
        )
        with patch(self.WALLET_MOCK_PATH):
            HipayWalletAccountFactory(
                application=document.merchant.application, merchant=document.merchant
            )
        mocked_api.return_value = {
            "code": 0,
            "documents": [
                {
                    "type": int(document.external_id),
                    "status_code": 9,  # under analysis
                    "status_label": "dont care",
                }
            ],
        }
        document.refresh_kyc_backend_status()
        self.assertTrue(document.kyc_backend_status.is_under_analysis)


class IsDocumentAcceptableV1TestCase(BaseTestCase):
    def test_valid_doc(self):
        document = MerchantIdentityDocumentFactory.build()
        config = HipayAppConfiguration(
            application=document.merchant.application, active=True
        )
        backend = HipayKycBackend(document.merchant, config)
        backend.merchant_wallet = HipayWalletAccountFactory.build(
            account_type=HIPAY_ACCOUNT_TYPE_CORPORATION,
            application=document.merchant.application,
            merchant=document.merchant,
        )
        acceptable, reason = backend.is_document_acceptable(document)
        self.assertTrue(acceptable, reason)

    def test_invalid_doc(self):
        document = MerchantIdentityDocumentFactory.build(document_type="invalid")
        document.merchant.id = 1
        config = HipayAppConfiguration(
            application=document.merchant.application, active=True
        )
        backend = HipayKycBackend(document.merchant, config)
        backend.merchant_wallet = HipayWalletAccountFactory.build(
            application=document.merchant.application, merchant=document.merchant
        )
        acceptable, reason = backend.is_document_acceptable(document)
        self.assertFalse(acceptable)
        self.assertEqual(
            reason,
            "Document type invalid not acceptable for merchant {}".format(
                document.merchant.name
            ),
        )


class IsDocumentAcceptableV2TestCase(BaseTestCase):
    def test_valid_doc(self):
        document = KycInformationFactory.build(kyc_type__external_id=1)
        config = HipayAppConfiguration(
            application=document.merchant.application, active=True
        )
        backend = HipayKycBackend(document.merchant, config)
        backend.merchant_wallet = HipayWalletAccountFactory.build(
            application=document.merchant.application, merchant=document.merchant
        )
        acceptable, _ = backend.is_document_acceptable(document)
        self.assertTrue(acceptable)

    def test_invalid_doc(self):
        document = KycInformationFactory.build(kyc_type__external_id="invalid")
        document.merchant.id = 1
        config = HipayAppConfiguration(
            application=document.merchant.application, active=True
        )
        backend = HipayKycBackend(document.merchant, config)
        backend.merchant_wallet = HipayWalletAccountFactory.build(
            application=document.merchant.application, merchant=document.merchant
        )
        acceptable, reason = backend.is_document_acceptable(document)
        self.assertFalse(acceptable)
        self.assertEqual(
            reason,
            "Document type invalid not acceptable for merchant {}".format(
                document.merchant.name
            ),
        )


@patch("payment_backends.hipay_tpp.hipay_api.kyc.HipayKycApi.request")
@patch(
    "payment_backends.hipay_tpp.actions.merchant_publish_kyc.send_error_mail",
    lambda *_: None,
)
@override_settings(LANGUAGE_CODE="en-US", LANGUAGES=(("en", "English"),))
class PublishV1TestCase(BaseTestCase):
    @patch("apps.kyc.models.MerchantIdentityDocument.save", lambda *_, **__: None)
    def test_not_needed_document(self, mocked_api):
        document = MerchantIdentityDocumentFactory.build(document_type=9999999, id=1)
        config = HipayAppConfiguration(
            application=document.merchant.application, active=True
        )
        backend = HipayKycBackend(document.merchant, config)
        backend.merchant_wallet = HipayWalletAccountFactory.build(
            application=document.merchant.application, merchant=document.merchant
        )
        backend.publish(document)
        self.assertTrue(document.kyc_backend_status.is_not_needed)
        self.assertEqual(
            document.kyc_backend_message, str(document.kyc_backend_message)
        )

    @patch("apps.kyc.models.MerchantIdentityDocument.save", lambda *_, **__: None)
    def test_document_with_validity_date(self, mocked_api):
        document = MerchantIdentityDocumentFactory.build(
            document_type=1, validity_date="toto"
        )
        config = HipayAppConfiguration(
            application=document.merchant.application, active=True
        )
        backend = HipayKycBackend(document.merchant, config)
        backend.merchant_wallet = HipayWalletAccountFactory.build(
            application=document.merchant.application, merchant=document.merchant
        )
        mocked_api.return_value = {"code": 0, "message": "test message"}
        backend.publish(document)
        self.assertEqual(document.validity_date, "toto")

    def test_not_expire_other_type_of_docs(self, mocked_api):
        old_doc = MerchantIdentityDocumentFactory(
            document_type=1,
            merchant__application__app_settings={EnableKycV2: False},
            external_id=1,
        )
        document = MerchantIdentityDocumentFactory(
            document_type=2, merchant=old_doc.merchant
        )
        config = HipayAppConfigurationFactory(
            application=document.merchant.application, active=True
        )
        backend = HipayKycBackend(document.merchant, config)
        backend.merchant_wallet = HipayWalletAccountFactory.build(
            application=document.merchant.application, merchant=document.merchant
        )

        mocked_api.return_value = {"code": 0, "message": "test message"}
        backend.publish(document)
        old_doc.refresh_from_db()
        self.assertTrue(old_doc.kyc_backend_status.is_pending)

    def test_not_expire_docs_from_other_merchant(self, mocked_api):
        application = ApplicationFactory()
        application.set_setting(EnableKycV2, False)
        old_doc = MerchantIdentityDocumentFactory(
            document_type=1,
            merchant__application=application,
            merchant__default_currency=application.default_currency,
            merchant__prefered_language=application.language,
            external_id=1,
        )
        document = MerchantIdentityDocumentFactory(
            document_type=1,
            merchant__application=application,
            merchant__default_currency=application.default_currency,
            merchant__prefered_language=application.language,
        )
        config = HipayAppConfiguration(
            application=document.merchant.application, active=True
        )
        backend = HipayKycBackend(document.merchant, config)
        backend.merchant_wallet = HipayWalletAccountFactory.build(
            application=document.merchant.application, merchant=document.merchant
        )

        mocked_api.return_value = {"code": 0, "message": "test message"}
        backend.publish(document)
        old_doc.refresh_from_db()
        self.assertTrue(old_doc.kyc_backend_status.is_pending)

    def test_document_with_other_doc_to_expire(self, mocked_api):
        old_doc = MerchantIdentityDocumentFactory(
            document_type=1,
            merchant__application__app_settings={EnableKycV2: False},
            external_id=1,
        )
        document = MerchantIdentityDocumentFactory(
            document_type=1, merchant=old_doc.merchant
        )
        config = HipayAppConfiguration(
            application=document.merchant.application, active=True
        )
        backend = HipayKycBackend(document.merchant, config)
        backend.merchant_wallet = HipayWalletAccountFactory.build(
            application=document.merchant.application, merchant=document.merchant
        )

        mocked_api.return_value = {"code": 0, "message": "test message"}
        backend.publish(document)
        old_doc.refresh_from_db()
        self.assertTrue(old_doc.kyc_backend_status.is_expired)
        self.assertEqual(
            str(old_doc.kyc_backend_message),
            "Old document replaced by the uploaded one",
        )

    @patch("apps.kyc.models.MerchantIdentityDocument.save", lambda *_, **__: None)
    def test_needed_document_accepted(self, mocked_api):
        document = MerchantIdentityDocumentFactory.build(document_type=1)
        config = HipayAppConfiguration(
            application=document.merchant.application, active=True
        )
        backend = HipayKycBackend(document.merchant, config)
        backend.merchant_wallet = HipayWalletAccountFactory.build(
            application=document.merchant.application, merchant=document.merchant
        )
        mocked_api.return_value = {"code": 0}

        backend.publish(document)

        self.assertTrue(document.kyc_backend_status.is_under_analysis)
        self.assertEqual(document.external_id, 1)
        self.assertEqual(
            str(document.kyc_backend_message),
            "Document successfully submitted for validation.",
        )

    @patch("apps.kyc.models.MerchantIdentityDocument.save", lambda *_, **__: None)
    def test_needed_document_refused(self, mocked_api):
        document = MerchantIdentityDocumentFactory.build(document_type=1, id=1)
        config = HipayAppConfiguration(
            application=document.merchant.application, active=True
        )
        backend = HipayKycBackend(document.merchant, config)
        backend.merchant_wallet = HipayWalletAccountFactory.build(
            application=document.merchant.application, merchant=document.merchant
        )
        mocked_api.return_value = {"code": 1}

        backend.publish(document)

        self.assertTrue(document.kyc_backend_status.is_refused)
        self.assertEqual(document.external_id, 1)
        self.assertEqual(
            str(document.kyc_backend_message),
            "The document has been sent to HiPay validation services",
        )


@patch("payment_backends.hipay_tpp.hipay_api.kyc.HipayKycApi.request")
@patch(
    "payment_backends.hipay_tpp.actions.merchant_publish_kyc.send_error_mail",
    lambda *_: None,
)
@override_settings(LANGUAGE_CODE="en-US", LANGUAGES=(("en", "English"),))
class PublishV2TestCase(BaseTestCase):
    @patch("apps.kyc.models.KycInformation.save", lambda *_, **__: None)
    def test_not_needed_document(self, mocked_api):
        document = KycInformationFactory.build(
            kyc_type__external_id=9999999, id=uuid.uuid4()
        )
        config = HipayAppConfiguration(
            application=document.merchant.application, active=True
        )
        backend = HipayKycBackend(document.merchant, config)
        backend.merchant_wallet = HipayWalletAccountFactory.build(
            application=document.merchant.application, merchant=document.merchant
        )
        backend.publish(document)
        self.assertTrue(document.kyc_backend_status.is_not_needed)
        self.assertEqual(
            document.kyc_backend_message, str(document.kyc_backend_message)
        )

    @patch("apps.kyc.models.KycInformation.save", lambda *_, **__: None)
    def test_document_with_validity_date(self, mocked_api):
        document = KycInformationFactory.build(
            kyc_type__external_id=1, validity_date="toto", id=uuid.uuid4()
        )
        config = HipayAppConfiguration(
            application=document.merchant.application, active=True
        )
        backend = HipayKycBackend(document.merchant, config)
        backend.merchant_wallet = HipayWalletAccountFactory.build(
            application=document.merchant.application, merchant=document.merchant
        )
        mocked_api.return_value = {"code": 0, "message": "test message"}
        backend.publish(document)
        self.assertEqual(document.validity_date, "toto")

    def test_not_expire_other_type_of_docs(self, mocked_api):
        application = ApplicationFactory()
        application.set_setting(EnableKycV2, True)
        old_doc = KycInformationFactory(
            kyc_type__external_id=1,
            merchant__application=application,
            merchant__default_currency=application.default_currency,
            external_id=1,
        )
        document = KycInformationFactory(
            kyc_type__external_id=2, merchant=old_doc.merchant
        )
        config = HipayAppConfiguration(
            application=document.merchant.application, active=True
        )
        backend = HipayKycBackend(document.merchant, config)
        backend.merchant_wallet = HipayWalletAccountFactory.build(
            application=document.merchant.application, merchant=document.merchant
        )

        mocked_api.return_value = {"code": 0, "message": "test message"}
        backend.publish(document)
        old_doc.refresh_from_db()
        self.assertTrue(old_doc.kyc_backend_status.is_pending)

    def test_not_expire_docs_from_other_merchant(self, mocked_api):
        application = ApplicationFactory()
        application.set_setting(EnableKycV2, True)
        old_doc = KycInformationFactory(
            kyc_type__external_id=1,
            merchant__application=application,
            merchant__default_currency=application.default_currency,
            external_id=1,
        )
        document = KycInformationFactory(
            kyc_type__external_id=1,
            merchant__application=application,
            merchant__default_currency=application.default_currency,
        )
        config = HipayAppConfigurationFactory(
            application=document.merchant.application, active=True
        )
        backend = HipayKycBackend(document.merchant, config)
        backend.merchant_wallet = HipayWalletAccountFactory.build(
            application=document.merchant.application, merchant=document.merchant
        )

        mocked_api.return_value = {"code": 0, "message": "test message"}
        backend.publish(document)
        old_doc.refresh_from_db()
        self.assertTrue(old_doc.kyc_backend_status.is_pending)

    def test_document_with_other_doc_to_expire(self, mocked_api):
        application = ApplicationFactory()
        application.set_setting(EnableKycV2, True)
        old_doc = KycInformationFactory(
            kyc_type__external_id=1,
            merchant__application=application,
            merchant__default_currency=application.default_currency,
            external_id=1,
        )
        document = KycInformationFactory(
            kyc_type__external_id=1, merchant=old_doc.merchant
        )
        config = HipayAppConfigurationFactory(
            application=document.merchant.application, active=True
        )
        backend = HipayKycBackend(document.merchant, config)
        backend.merchant_wallet = HipayWalletAccountFactory.build(
            application=document.merchant.application, merchant=document.merchant
        )

        mocked_api.return_value = {"code": 0, "message": "test message"}
        backend.publish(document)
        old_doc.refresh_from_db()
        self.assertTrue(old_doc.kyc_backend_status.is_expired)
        self.assertEqual(
            str(old_doc.kyc_backend_message),
            "Old document replaced by the uploaded one",
        )

    @patch("apps.kyc.models.KycInformation.save", lambda *_, **__: None)
    def test_needed_document_accepted(self, mocked_api):
        document = KycInformationFactory.build(kyc_type__external_id=1, id=uuid.uuid4())
        config = HipayAppConfiguration(
            application=document.merchant.application, active=True
        )
        backend = HipayKycBackend(document.merchant, config)
        backend.merchant_wallet = HipayWalletAccountFactory.build(
            application=document.merchant.application, merchant=document.merchant
        )
        mocked_api.return_value = {"code": 0}

        backend.publish(document)

        self.assertTrue(document.kyc_backend_status.is_under_analysis)
        self.assertEqual(document.external_id, 1)
        self.assertEqual(
            str(document.kyc_backend_message),
            "Document successfully submitted for validation.",
        )

    @patch("apps.kyc.models.KycInformation.save", lambda *_, **__: None)
    def test_needed_document_refused(self, mocked_api):
        document = KycInformationFactory.build(kyc_type__external_id=1, id=uuid.uuid4())
        config = HipayAppConfiguration(
            application=document.merchant.application, active=True
        )
        backend = HipayKycBackend(document.merchant, config)
        backend.merchant_wallet = HipayWalletAccountFactory.build(
            application=document.merchant.application, merchant=document.merchant
        )
        mocked_api.return_value = {"code": 1}

        backend.publish(document)

        self.assertTrue(document.kyc_backend_status.is_refused)
        self.assertEqual(document.external_id, 1)
        self.assertEqual(
            str(document.kyc_backend_message),
            "The document has been sent to HiPay validation services",
        )
