# -*- coding: utf-8 -*-
from io import BytesIO

import mock
from apps.testing.factories import MerchantFactory, OrderPaymentFactory
from django.core.files.uploadedfile import InMemoryUploadedFile
from ims.tests import BaseTestCase
from mock import patch
from payment_backends.common.tests.utils import request
from payment_backends.hipay_tpp.exceptions import (
    AuthorizationAlreadyCompletedException,
    CapturingTooMuchException,
)
from payment_backends.hipay_tpp.hipay_api.rest_api_interface import RestAPIInterface
from payment_backends.hipay_tpp.models import HipayApiCallLog
from payment_backends.hipay_tpp.references import HIPAY_ACTION_VALIDATE
from PIL import Image
from requests import HTTPError


class ApiInterfaceImplementation(RestAPIInterface):
    def get_application(self):
        app_mock = mock.Mock()
        app_mock.name = "Applïcation"
        return app_mock

    def get_credentials_tuple(self):
        return "login", "password"


class RestApiInterfaceTestCase(BaseTestCase):
    CHECKER_PATH = (
        "payment_backends.hipay_tpp.hipay_api.rest_api_interface."
        "raise_known_operation_exception"
    )
    REQUEST_MAKER_PATH = (
        "payment_backends.hipay_tpp.hipay_api.rest_api_interface.logged_tls_request"
    )

    def setUp(self):
        self.api = ApiInterfaceImplementation()

    def get_image(self):
        im = Image.new(mode="RGB", size=(1, 1))
        im_io = BytesIO()
        im.save(im_io, "JPEG")
        im_io.seek(0)
        size = len(im_io.read())
        im_io.seek(0)
        return InMemoryUploadedFile(
            im_io, None, "random-name.jpg", "image/jpeg", size, None
        )

    def create_response(
        self, http_code=200, too_much_capture=False, already_captured=False
    ):
        if http_code in (401, 403):
            raw_response = b'{"error": "Invalid credentials"}'
        elif http_code in (200, 400):
            if too_much_capture:
                raw_response = b'{"code": 3020110}'
            elif already_captured:
                raw_response = b'{"code": 3020106}'
            else:
                # unhandled code
                raw_response = b'{"code": 12345}'
        else:
            raise ValueError("Unexpected code for response maker")
        return request(
            url="http://fake-tests.com",
            data="",
            headers={"Content-Type": "application/json"},
            method="GET",
            raw_response=raw_response,
            status_code=http_code,
        )

    def make_header(self, overridden=False):
        headers = {"x-http-myheader": "with-some-value"}
        if overridden:
            headers.update(
                {"Accept": "SomethingElse", "Authorization": "SomeOtherAuth"}
            )
        return headers

    def test_get_auth_header(self):
        header = self.api.get_auth_header()
        # expected BasicAuth header for "login", "password" credentials tuple
        expected = {"Authorization": "Basic bG9naW46cGFzc3dvcmQ="}
        self.assertEqual(header, expected)

    def test_get_application_name(self):
        application = self.api.get_application()

        self.assertEqual(application.name, "Applïcation")

    def test_enrich_headers(self):
        headers = self.make_header()
        enriched_headers = self.api.enrich_headers(headers)

        self.assertEqual(
            enriched_headers,
            {
                "Accept": "application/json",
                "Authorization": "Basic bG9naW46cGFzc3dvcmQ=",
                "x-http-myheader": "with-some-value",
            },
        )

    def test_enrich_headers_override(self):
        headers = self.make_header(overridden=True)
        enriched_headers = self.api.enrich_headers(headers)

        self.assertEqual(
            enriched_headers,
            {
                "Accept": "SomethingElse",
                "Authorization": "SomeOtherAuth",
                "x-http-myheader": "with-some-value",
            },
        )

    def test_enrich_headers_with_nonetype(self):
        enriched_headers = self.api.enrich_headers(None)

        self.assertEqual(
            enriched_headers,
            {
                "Accept": "application/json",
                "Authorization": "Basic bG9naW46cGFzc3dvcmQ=",
            },
        )

    @patch("requests.Response.raise_for_status", lambda *_: None)
    def test_raise_for_error_response_400_and_auth_completed(self):
        response = self.create_response(already_captured=True, http_code=400)
        with mock.patch(self.CHECKER_PATH) as known_operations_raiser:
            self.api.raise_for_error_response(response)
        self.assertEqual(known_operations_raiser.called, True)

        with self.assertRaises(AuthorizationAlreadyCompletedException):
            self.api.raise_for_error_response(response)

    @patch("requests.Response.raise_for_status", lambda *_: None)
    def test_raise_for_error_response_400_and_limit_exceeded(self):
        response = self.create_response(too_much_capture=True, http_code=400)

        with mock.patch(self.CHECKER_PATH) as known_operations_raiser:
            self.api.raise_for_error_response(response)
        self.assertEqual(known_operations_raiser.called, True)

        with self.assertRaises(CapturingTooMuchException):
            self.api.raise_for_error_response(response)

    @mock.patch(
        "payment_backends.hipay_tpp.hipay_api.rest_api_interface.logger.error",
        lambda *_: None,
    )
    def test_raise_for_error_response_401(self):
        response = self.create_response(http_code=401)

        with self.assertRaises(HTTPError):
            self.api.raise_for_error_response(response)

    @mock.patch(
        "payment_backends.hipay_tpp.hipay_api.rest_api_interface"
        ".RestAPIInterface._log_api_call_error_during_tests",
        lambda *_, **__: None,
    )
    @patch(
        "payment_backends.common.utils.RestLogsBuilder."
        "raise_for_action_not_configured",
        lambda *_: None,
    )
    def test_get_request_made(self):
        mock_response = self.create_response()

        with mock.patch(self.REQUEST_MAKER_PATH) as mock_request_function:
            mock_request_function.return_value = mock_response
            response = self.api.request(
                url="http://www/toto.com/url/",
                args={"test": True},
                method="GET",
                headers={"x-my-header": "test header"},
                silent=True,
            )

            mock_request_function.assert_called_with(
                method="GET",
                # url with GET arg concatenated
                url="http://www/toto.com/url/",
                params={"test": True},
                data=None,
                files=None,
                headers={
                    "Accept": "application/json",
                    "Authorization": "Basic bG9naW46cGFzc3dvcmQ=",
                    "x-my-header": "test header",
                },
                mute_ssl_warnings=False,
                timeout=60,
            )

        self.assertEqual(response, {"code": 12345})

    @mock.patch(
        "payment_backends.hipay_tpp.hipay_api.rest_api_interface"
        ".RestAPIInterface._log_api_call_error_during_tests",
        lambda *_, **__: None,
    )
    @patch(
        "payment_backends.common.utils.RestLogsBuilder."
        "raise_for_action_not_configured",
        lambda *_: None,
    )
    def test_post_request_made_with_file(self):
        mock_response = self.create_response()
        mock_image = self.get_image()
        with mock.patch(self.REQUEST_MAKER_PATH) as mock_request_function:
            mock_request_function.return_value = mock_response
            response = self.api.request(
                url="http://www/toto.com/url/",
                post_args={"image": "my_image"},
                files={"my_image": mock_image},
                method="POST",
                headers={"x-my-header": "test header"},
                silent=True,
            )

            mock_request_function.assert_called_with(
                method="POST",
                # url with GET arg concatenated
                url="http://www/toto.com/url/",
                params=None,
                data={"image": "my_image"},
                files={"my_image": mock_image},
                headers={
                    "Accept": "application/json",
                    "Authorization": "Basic bG9naW46cGFzc3dvcmQ=",
                    "x-my-header": "test header",
                },
                mute_ssl_warnings=False,
                timeout=60,
            )

        self.assertEqual(response, {"code": 12345})

    @mock.patch(
        "payment_backends.hipay_tpp.hipay_api.rest_api_interface.logged_tls_request"
    )
    @mock.patch(
        "payment_backends.hipay_tpp.hipay_api.rest_api_interface"
        ".RestAPIInterface._log_api_call_error_during_tests",
        lambda *_, **__: None,
    )
    def test_logging_is_made(self, logged_tls_request):
        response = self.create_response()
        logged_tls_request.return_value = response

        payment = OrderPaymentFactory.create()
        merchant = MerchantFactory.create()

        self.api.db_logger.work_on(
            HIPAY_ACTION_VALIDATE, payment=payment, merchant=merchant
        )

        self.api.request(
            "https://www.example.com/",
            args={"abc": "as easy as 123"},
            post_args={"abc": "baby you and meeeee"},
            method="ZOOOORG",
            headers={"my head": "spinning round round"},
            silent=False,
            mute_ssl_warnings=False,
        )

        log = HipayApiCallLog.objects.last()
        self.assertEqual(log.action, HIPAY_ACTION_VALIDATE)
        self.assertEqual(log.payment, payment)
        self.assertEqual(log.merchant, merchant)
        self.assertEqual(log.target_url, response.request.url)
        self.assertEqual(log.merchant, merchant)
        self.assertEqual(log.data, str(response.request.body))
        self.assertEqual(log.method, response.request.method)
        self.assertEqual(log.headers, response.request.headers)
        self.assertEqual(log.response_body, response._content.decode("utf-8"))
        self.assertEqual(log.status_code, response.status_code)
        self.assertEqual(log.response_time, response.elapsed)
