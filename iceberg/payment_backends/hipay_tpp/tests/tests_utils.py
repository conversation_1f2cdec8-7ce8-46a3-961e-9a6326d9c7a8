# -*- coding: utf-8 -*-
import json

import mock
from apps.ice_applications.app_conf_settings import EnableKycV2
from apps.kyc.models import MerchantIdentityDocument
from apps.stores.tests.test_merchant_setup_mixin import TestMerchantSetupMixin
from apps.testing.factories import MerchantBankAccountFactory, MerchantFactory
from apps.testing.factories.hipay import (
    HipayMerchantBankAccountKycV2Factory,
    HipayWalletAccountFactory,
)
from django.core.exceptions import ValidationError
from ims.tests import BaseTestCase
from payment_backends.hipay_tpp.actions import BankAccountActionsManager
from payment_backends.hipay_tpp.exceptions import (
    HipayBankAccountNotSelected,
    HipayBankAccountNotValidated,
    HipayWrongBankAccount,
)
from payment_backends.hipay_tpp.hipay_api.cashout import HipayCashoutApi
from payment_backends.hipay_tpp.references import (
    HIPAY_BANK_INFOS_STATUSES_VALID,
    HIPAY_BANK_INFOS_STATUSES_WAITING,
)
from payment_backends.hipay_tpp.utils import (
    get_and_check_account_identification,
    get_and_check_bank_infos,
    send_internal_error_mail,
)
from requests import HTTPError

REST_API_CALL_PATH = (
    "payment_backends.hipay_tpp.hipay_api.rest_api_interface."
    "RestAPIInterface.request"
)
GET_BANK_INFOS_PATH = (
    "payment_backends.hipay_tpp.hipay_api.cashout.HipayCashoutApi."
    "get_bank_infos_status"
)


class MockedRequestsResponse:
    def __init__(
        self,
        status_code=200,
        content=b"",
        headers=None,
        reason="sample reason",
        url="http://sample.url",
    ):
        self.status_code = status_code
        self.content = content
        self.headers = headers or {}
        self.reason = reason
        self.url = url

    def raise_for_status(self):
        http_error_msg = ""

        if 400 <= self.status_code < 500:
            http_error_msg = "%s Client Error: %s for url: %s" % (
                self.status_code,
                self.reason,
                self.url,
            )

        elif 500 <= self.status_code < 600:
            http_error_msg = "%s Server Error: %s for url: %s" % (
                self.status_code,
                self.reason,
                self.url,
            )

        if http_error_msg:
            raise HTTPError(http_error_msg, response=self)

    def json(self, **kwargs):
        if isinstance(self.content, dict):
            return self.content
        return json.loads(self.content)


VALID_USER_INFO_RESPONSE = {
    "code": 0,
    "message": "Getting user account infos succeeded",
    "entity": "ixilo",
    "user_account_id": 1111111,
    "user_space_id": 2222222,
    "currency": "EUR",
    "activated": 1,
    "identified": 1,
    "bank_info_validated": 0,
    "callback_salt": "",
    "websites": [
        {
            "website_id": 111111,
            "website_name": "ixdev.trixir.com",
            "website_url": "http://ixdev.trixir.com",
            "website_email": "<EMAIL>",
            "business_line": "Multiproduct purchases",
            "website_topic": "General merchant websites",
        }
    ],
}


class UtilsTestCase(TestMerchantSetupMixin, BaseTestCase):
    def setUp(self):
        TestMerchantSetupMixin.set_up(self)

    def get_document(self):
        return MerchantIdentityDocument.objects.create(
            document_type=MerchantIdentityDocument.IDENTITY_PROOF,
            status=MerchantIdentityDocument.ACTIVE,
            merchant=self.merchant,
        )

    def test_create_message(self):
        self.application.set_setting(EnableKycV2, False)
        message = send_internal_error_mail(
            from_seller=self.merchant,
            to_application=self.application,
            concerning_document=self.get_document(),
            error_detail="test error message",
        )

        self.assertFalse(message.id is None, "Message not saved")

    def test_obfuscate_util(self):
        from payment_backends.hipay_tpp.utils import obfuscate

        self.assertEqual(obfuscate("abcdef"), "******")
        self.assertEqual(obfuscate("abcde"), "*****")
        self.assertEqual(obfuscate("abcd"), "****")
        self.assertEqual(obfuscate("abc"), "***")
        self.assertEqual(obfuscate("ab"), "**")
        self.assertEqual(obfuscate("a"), "*")
        self.assertEqual(obfuscate(""), "")
        self.assertEqual(obfuscate(None), None)
        self.assertEqual(obfuscate("abcdefg"), "abc*efg")
        self.assertEqual(obfuscate("abcdefghijklm"), "abc*******klm")

    def test_get_and_check_account_identification_success(self):
        wallet = HipayWalletAccountFactory()
        config = mock.Mock()
        config.UserApiEndpoint = "https://api.hipay.com"
        api = HipayCashoutApi(config)
        with mock.patch(REST_API_CALL_PATH) as request:
            request.return_value = {"identified": 1}
            get_and_check_account_identification(api, wallet)
            request.assert_called_with(
                url=config.UserApiEndpoint,
                method="GET",
                headers={"php-auth-subaccount-id": str(wallet.user_account_id)},
                mute_ssl_warnings=True,
            )

    def test_get_and_check_account_identification_not_identified(self):
        wallet = HipayWalletAccountFactory()
        config = mock.Mock()
        config.UserApiEndpoint = "https://api.hipay.com"
        api = HipayCashoutApi(config)
        with mock.patch(REST_API_CALL_PATH) as request:
            request.return_value = {"identified": 0}
            with self.assertRaises(ValidationError):
                get_and_check_account_identification(api, wallet)
            request.assert_called_with(
                url=config.UserApiEndpoint,
                method="GET",
                headers={"php-auth-subaccount-id": str(wallet.user_account_id)},
                mute_ssl_warnings=True,
            )

    def test_get_and_check_bank_infos_success(self):
        account = HipayMerchantBankAccountKycV2Factory(
            synchro_status=HIPAY_BANK_INFOS_STATUSES_VALID
        )

        get_and_check_bank_infos(account.merchant, account.store_bank_account)

    def test_get_and_check_bank_infos_not_selected(self):
        merchant = MerchantFactory()
        bank_account = MerchantBankAccountFactory(merchant=merchant)

        with self.assertRaises(HipayBankAccountNotSelected):
            get_and_check_bank_infos(merchant, bank_account)

        account = HipayMerchantBankAccountKycV2Factory(
            synchro_status=HIPAY_BANK_INFOS_STATUSES_VALID, store_bank_account=None
        )
        with self.assertRaises(HipayBankAccountNotSelected):
            get_and_check_bank_infos(account.merchant, account.store_bank_account)

    def test_get_and_check_bank_infos_wrong_account(self):
        account = HipayMerchantBankAccountKycV2Factory()
        store_bank_account_2 = MerchantBankAccountFactory(
            merchant=account.merchant, status=account.store_bank_account.status
        )
        with self.assertRaises(HipayWrongBankAccount):
            get_and_check_bank_infos(account.merchant, store_bank_account_2)

    @mock.patch("payment_backends.hipay_tpp.models.HipayBankAccount.get_api")
    def test_get_and_check_bank_infos_desynchronized(self, get_api):
        # assuming we have a waiting_validation account locally
        account = HipayMerchantBankAccountKycV2Factory(
            synchro_status=HIPAY_BANK_INFOS_STATUSES_WAITING
        )
        # But account is valid on hipay side
        get_api().get_bank_infos_status.return_value = {
            "status_code": BankAccountActionsManager.HIPAY_VALIDATED,
        }

        # when I ask for a check
        get_and_check_bank_infos(
            account.merchant, account.store_bank_account
        )  # it succeeds

    @mock.patch("payment_backends.hipay_tpp.models.HipayBankAccount.get_api")
    def test_get_and_check_bank_infos_not_validated_yet(self, get_api):
        # Assuming I have a pending validation account locally
        account = HipayMerchantBankAccountKycV2Factory(
            synchro_status=HIPAY_BANK_INFOS_STATUSES_WAITING
        )
        # But account is valid on hipay side
        get_api().get_bank_infos_status.return_value = {
            "status_code": BankAccountActionsManager.HIPAY_VALIDATION_IN_PROGRESS,
        }

        # When I ask for a check then
        # it should explode mid-air with an "AccountNotValidated" error
        with self.assertRaises(HipayBankAccountNotValidated):
            get_and_check_bank_infos(account.merchant, account.store_bank_account)
