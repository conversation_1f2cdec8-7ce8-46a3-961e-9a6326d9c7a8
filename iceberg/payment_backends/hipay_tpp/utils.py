# -*- coding: utf-8 -*-

from urllib.parse import urlencode

from apps.currencies.models import Currency
from apps.ice_applications.app_conf_settings import EnableKycV2
from apps.kyc.models import KycInformation
from apps.mp_messages.models import Message
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from django.utils.translation import override

from .exceptions import (
    AuthorizationAlreadyCompletedException,
    CapturingTooMuchException,
    HipayBankAccountNotSelected,
    HipayBankAccountNotValidated,
    HipayImpossibleCashout,
    HipayNotConfigured,
    HipayWrongBankAccount,
    InvalidOrderId,
    MerchantAccountNotIdentified,
    TransactionAlreadyClosedException,
)
from .references import (
    AMOUNT_LIMIT_EXCEEDED,
    AUTHORIZATION_ALREADY_COMPLETED,
    HIPAY_BANK_INFOS_STATUSES_VALID,
    HIPAY_KYC_MESSAGES,
    OPERATION_NOT_PERMITTED_ALREADY_CLOSED,
)

HIPAY_ORDERID_PREFIX = getattr(settings, "HIPAY_ORDERID_PREFIX", "")


def get_conf_for_application(application):
    from payment_backends.hipay_tpp.models import HipayAppConfiguration

    try:
        return HipayAppConfiguration.objects.get(application=application, active=True)
    except HipayAppConfiguration.DoesNotExist:
        raise HipayNotConfigured()


def get_ip_address():
    """Connect to google dns ******* with a socket and get the IP

    :required: firewall's port 67 to be opened for output

    :return: Ip address
    """
    import socket

    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    s.connect(("*******", 67))
    ip_address = s.getsockname()[0]
    s.close()
    return ip_address


def get_and_check_bank_infos(merchant, bank_account):
    """Get HipayBankAccount related to given djangoBankAccount
    or created it if not set.
    """
    # import made inside to avoid circular dependency
    from payment_backends.hipay_tpp.models.bank_account import HipayBankAccount

    try:
        account = HipayBankAccount.objects.get(merchant=merchant)
    except HipayBankAccount.DoesNotExist:
        raise HipayBankAccountNotSelected()

    if account.store_bank_account is None:
        raise HipayBankAccountNotSelected()

    if account.store_bank_account != bank_account:
        raise HipayWrongBankAccount()

    account = account.actions.refresh()

    if account.synchro_status != HIPAY_BANK_INFOS_STATUSES_VALID:
        raise HipayBankAccountNotValidated()

    return account


def get_and_check_account_identification(cashout_api, wallet):
    if not cashout_api.is_identified(wallet):
        raise MerchantAccountNotIdentified(wallet)


def get_currency_code(currency):
    if isinstance(currency, Currency):
        return currency.code
    else:
        return str(currency)


def get_merchant_amounts_on_order(cashout_api, order):
    details = {}

    for merchant_order in order.merchant_orders.all():
        # get merchant account id for merchant
        merchant = merchant_order.merchant
        merchant_account = cashout_api.get_wallet_or_none(merchant=merchant)

        if merchant_account is None:
            # skip merchant if his hipay account is not configured.
            continue

        merchant_account_id = merchant_account.user_account_id
        details[merchant_account_id] = merchant_order.amount_vat_included

    return urlencode(details)


def raise_known_operation_exception(error_text):
    import json

    try:
        error_details = json.loads(error_text)
    except ValueError:
        # can't introspect the error since it's no JSON
        return

    if "code" not in error_details:
        # no error code, still can't instrospect
        return

    err_code = int(error_details["code"])

    if err_code == AUTHORIZATION_ALREADY_COMPLETED:
        raise AuthorizationAlreadyCompletedException

    elif err_code == AMOUNT_LIMIT_EXCEEDED:
        raise CapturingTooMuchException

    elif err_code == OPERATION_NOT_PERMITTED_ALREADY_CLOSED:
        raise TransactionAlreadyClosedException


def hipay_get_unique_id(payment_object):
    """Do stuffs to generate the most unique ID ever!

    generated ID will look like:
        "<environment_prefix>151009S110000070"
        or
        "<environment_prefix>-payment-151009S110000070"

    depending on payment's linked item type.

    :param payment_object: Payment object to generate id for
    :return: reversable unique ID string
    """
    if payment_object.order:
        order = payment_object.order
        if not order.id_number:
            raise InvalidOrderId()
        return HIPAY_ORDERID_PREFIX + order.id_number

    if not payment_object.dated_id:
        raise InvalidOrderId()

    return "%s-payment-%s" % (HIPAY_ORDERID_PREFIX, payment_object.dated_id)


def get_payment_from_hipay_unique_id(hipay_unique_id):
    """Reverse call of hipay_get_unique_id"""
    from apps.payment.models import Payment

    start_idx = len(HIPAY_ORDERID_PREFIX)
    id_number = hipay_unique_id[start_idx:]

    if not id_number.startswith("-payment-"):
        return Payment.objects.get(order__id_number=id_number)

    id_number = id_number[len("-payment-") :]
    payment_id = id_number[13:]
    try:
        return Payment.objects.get(id=payment_id)
    except ValueError:
        raise Payment.DoesNotExist("Payment matching query does not exist.")


def get_last_transaction_key(dictionary):
    return str(len(dictionary.keys()) - 1)


def get_merchant(merchant_id):
    from apps.stores.models import Merchant

    return Merchant.objects.get(id=merchant_id)


def get_documents(for_merchant):
    from apps.kyc.models import MerchantIdentityDocument

    if for_merchant.application.get_setting(EnableKycV2):
        return KycInformation.objects.filter(merchant=for_merchant).exclude_deleted()
    else:  # v1
        return MerchantIdentityDocument.objects.filter(
            merchant=for_merchant
        ).exclude_deleted()


def send_internal_error_mail(
    from_seller, to_application, concerning_document, error_detail
):
    with override(to_application.language):
        message = Message()
        message.sender = "/v1/merchant/{}/".format(from_seller.id)
        message.receiver = "/v1/application/{}/".format(to_application.id)
        message.subject = _("Merchant identification incomplete.")
        message.body = _(
            "Document {doc_id} upload failed on seller {seller_name}'s hipay "
            "account: \n {error_message}.\n remove it or fix the problem"
        ).format(
            doc_id=concerning_document.id,
            seller_name=from_seller.name,
            error_message=error_detail,
        )
        message.save()
    return message


def unpack_dict(dictionary, *keys):
    out = []
    for key in keys:
        out.append(dictionary.get(key, None))
    return out


def humanify_kyc_errors(errors):
    """Generates a multi-lines error string containing on each line:
    <field> : <translated_message>\n

    :param errors: list of dicts
    :type errors: list<dict>

    :rtype: str
    """
    output = []
    for error in errors:
        field, message = unpack_dict(error, "field", "message")
        # translate message using pre-defined mesages mapping
        default_message = _("Unknown error: %s") % message
        message = HIPAY_KYC_MESSAGES.get(message, default_message)
        error_line = "%s: %s" % (field, message)
        output.append(error_line)
    return "\n".join(output)


def obfuscate(text):
    """Returns 3 first chars + N times the * char + 3 last chars so that you
    get as many characters as there are in the original text.

    If text is shorter than 6 chars it returns only stars because in other cases
    the challenge level of obfuscation becomes too low.

    ex: abcdefghijkl
    becomes: abc******jkl

    and: abcdef
    becomes: ******

    :param text: Data to obfuscate
    :type text: str

    :rtype: unicode
    """
    if not isinstance(text, str):
        return text
    if len(text) > 6:
        return text[:3] + "*" * len(text[3:-3]) + text[-3:]
    return "*" * len(text)


def get_hipay_kyc_type(wallet, document):
    """Helper needed to get the kyc type on v1 and v2 document"""
    from .hipay_api.kyc import KYC_DOCUMENT_CLASSIFICATION_MATCHING

    account_type_matching = KYC_DOCUMENT_CLASSIFICATION_MATCHING[wallet.account_type]
    if isinstance(document, KycInformation):  # v2
        try:
            # Expecting integer for Hipay KYC
            doc_type = int(document.kyc_type.external_id)
        except (ValueError, TypeError):
            doc_type = document.kyc_type.external_id
    else:  # v1
        doc_type = account_type_matching.get(document.document_type, None)
    is_valid_hipay_type = doc_type in account_type_matching.values()
    return doc_type, is_valid_hipay_type


def get_wallet_and_conf_from_merchant_id(merchant_id):
    from .models import HipayAppConfiguration, HipayWalletAccount

    try:
        wallet = HipayWalletAccount.objects.get(merchant_id=merchant_id)
    except HipayWalletAccount.DoesNotExist:
        raise HipayImpossibleCashout("Not registered on hipay.")

    conf = HipayAppConfiguration.objects.filter(
        application_id=wallet.merchant.application_id, active=True
    )
    if conf.count() != 1:
        raise HipayImpossibleCashout(
            _("Hipay not configured yet / multiple active configs")
        )

    return wallet, conf.get()
