# -*- coding: utf-8 -*-


import logging

from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from ims.api.exceptions import BaseResponseException

logger = logging.getLogger(__name__)


__all__ = ("PspGatewayActionManager",)


class ActionNotAvailableException(BaseResponseException):
    status = 403
    error_code = "PSP.GATEWAY.ACTION_NOT_AVAILABLE"
    error_description = _("This action is not available for this gateway type")


def _pay_merchant_order(
    merchant_order,
    external_id=None,
    external_id_provided=False,
    settlement_date=None,
    **kwargs,
):
    order = merchant_order.order
    payment = order.payment
    if payment is None:
        raise ValidationError(_("Merchant order has no payment yet."))
    if external_id_provided:
        payment.external_id = external_id

    if payment.authorize.is_available():
        payment.actions.authorize()

    payment.actions.collect_merchant_order_amount(
        merchant_order=merchant_order, settlement_date=settlement_date
    )

    # Drop cached property on payment.
    order.delete_cached_payment()


def _pay_invoice(
    invoice, amount=None, external_id=None, settlement_date=None, **kwargs
):
    invoice.actions.pay(amount, external_id, settlement_date=settlement_date)


def _pay_refund(refund, **kwargs):
    if refund.complete.is_available():
        refund.actions.process()
    elif refund.manually_complete.is_available():
        refund.actions.manually_complete()
    else:
        raise ValidationError(_("Current status does not allow refund completion."))


def _process_transaction(transaction):
    if transaction.confirmed.is_available():
        transaction.actions.process()
    else:
        raise ValidationError(
            _("Current status does not allow transaction confirmation.")
        )


def _pay_payout(transaction, **kwargs):
    _process_transaction(transaction)


def _pay_store_adjustment(transaction, **kwargs):
    _process_transaction(transaction)


def _pay_marketing_operation(transaction, **kwargs):
    _process_transaction(transaction)


def _pay_license(transaction, **kwargs):
    _process_transaction(transaction)


def _pay_commission(transaction, **kwargs):
    _process_transaction(transaction)


def _pay_commission_refund(transaction, **kwargs):
    _process_transaction(transaction)


class PspGatewayActionManager:
    def __init__(self, psp_gateway):
        self.psp_gateway = psp_gateway
        self.PAY_ACTION_MAPPING = {
            # ingress
            psp_gateway.PREPAYMENT_TYPE: _pay_merchant_order,
            psp_gateway.TERM_PAYMENT_TYPE: _pay_invoice,
            # egress
            psp_gateway.REFUND_TYPE: _pay_refund,
            psp_gateway.PAYOUT_TRANSACTION_TYPE: _pay_payout,
            # internal
            psp_gateway.COMMISSION_TYPE: _pay_commission,
            psp_gateway.COMMISSION_REFUND_TYPE: _pay_commission_refund,
            psp_gateway.STORE_ADJUSTMENT_TRANSACTION_TYPE: _pay_store_adjustment,
            psp_gateway.MARKETING_OPERATION_TRANSACTION_TYPE: _pay_marketing_operation,
            psp_gateway.LICENSE_TRANSACTION_TYPE: _pay_license,
        }

    def pay(
        self,
        amount=None,
        external_id=None,
        external_id_provided=False,
        settlement_date=None,
    ):
        try:
            mapped_action = self.PAY_ACTION_MAPPING[self.psp_gateway.gateway_type]
        except KeyError:
            raise ActionNotAvailableException(
                action="pay", gateway_type=self.psp_gateway.gateway_type
            )
        else:
            return mapped_action(
                self.psp_gateway.type_relation,
                amount=amount,
                settlement_date=settlement_date,
                external_id=external_id,
                external_id_provided=external_id_provided,
            )
