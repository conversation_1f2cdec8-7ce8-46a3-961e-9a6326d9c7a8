# -*- coding: utf-8 -*-


from django.contrib import admin
from mp_utils.admin import RawIDModelAdmin
from payment_backends.psp_gateway.models import PspGateway
from payment_backends.psp_gateway.models.logger import PspGatewayPaymentLog


class PspGatewayAdmin(RawIDModelAdmin):
    list_display = (
        "id",
        "external_id",
        "application",
        "gateway_type",
        "merchant_order",
        "invoice",
        "refund",
        "transaction",
        "created_on",
    )

    search_fields = (
        "id",
        "external_id",
        "application__id",
        "merchant_order__id",
        "invoice__id",
        "refund__id",
        "transaction__id",
    )

    list_filter = (
        "gateway_type",
        "created_on",
        "last_modified",
    )


class PspGatewayPaymentLogAdmin(RawIDModelAdmin):
    list_display = (
        "id",
        "external_payment_backend_id",
        "amount",
        "currency",
        "created_on",
    )
    search_fields = ("external_payment_backend__id", "external_payment_backend")
    list_filter = (
        "created_on",
        "updated_on",
    )


admin.site.register(PspGateway, PspGatewayAdmin)
admin.site.register(PspGatewayPaymentLog, PspGatewayPaymentLogAdmin)
