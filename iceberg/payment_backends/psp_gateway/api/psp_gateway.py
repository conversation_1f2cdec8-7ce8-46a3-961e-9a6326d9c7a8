# -*- coding: utf-8 -*-
import logging
from decimal import Decimal, InvalidOperation

from django.db import transaction
from django.urls import re_path
from django.utils.translation import gettext_lazy as _
from ims.api.authorization import LegacyBaseAuthorization
from ims.api.authorization.scope_auth import (
    ALL_ROLES,
    EDIT_ROLES,
    FINANCE_SCOPE,
    OMS_SCOPE,
    OPERATOR_OWNER,
    ScopeBasedAuth,
)
from ims.api.decorators import api_view
from ims.api.resources import MPModelResource
from invoicing.api import CustomerInvoiceResource
from payment_backends.psp_gateway.models import PspGateway
from tastypie import fields
from tastypie.exceptions import BadRequest, Unauthorized
from tastypie.resources import ALL, ALL_WITH_RELATIONS
from tastypie.utils import trailing_slash

logger = logging.getLogger(__name__)


class LegacyPspGatewayAuthorization(LegacyBaseAuthorization):
    path_to_application = "application"

    authorized_entities = {
        "create_detail": ["application"],
        "update_detail": ["application"],
        "read_detail": ["application"],
        "read_list": ["application"],
    }


class PspGatewayAuthorization(ScopeBasedAuth):
    owner_paths = {
        OPERATOR_OWNER: "application",
    }
    authorized_actions = {
        "read_list": {
            OPERATOR_OWNER: ALL_ROLES,
        },
        "read_detail": {
            OPERATOR_OWNER: ALL_ROLES,
        },
        "update_detail": {
            OPERATOR_OWNER: EDIT_ROLES,
        },
        "create_detail": {
            OPERATOR_OWNER: EDIT_ROLES,
        },
    }
    scopes = (OMS_SCOPE, FINANCE_SCOPE)
    legacy_auth_cls = LegacyPspGatewayAuthorization


class PspGatewayResource(MPModelResource):
    id = fields.CharField(
        "id",
        # override to add sample permanent default value
        # (model's default is uuid4())
        default="aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa",
        readonly=True,
    )
    application = fields.ForeignKey(
        "apps.ice_applications.api.ApplicationResource",
        "application",
    )
    merchant_order = fields.ForeignKey(
        "apps.orders.api.resources.MerchantOrderResource", "merchant_order", null=True
    )
    invoice = fields.ForeignKey(
        "invoicing.api.CustomerInvoiceResource", "invoice", null=True
    )
    merchant = fields.ForeignKey(
        "apps.stores.api.resources.MerchantResource",
        "merchant",
        null=True,
        readonly=True,
    )
    refund = fields.ForeignKey(
        "apps.returns.api.resources.RefundResource", "refund", null=True
    )
    transaction = fields.ForeignKey(
        "apps.transactions.api.resources.TransactionResource", "transaction", null=True
    )
    cashout_transaction = fields.ForeignKey(
        "apps.transactions.api.resources.TransactionResource",
        "cashout_transaction",
        null=True,
    )

    class Meta:
        queryset = PspGateway.objects.all()
        resource_name = "psp/gateway"
        always_return_data = True
        authorization = PspGatewayAuthorization()
        filtering = {
            "id": ALL,
            "application": ALL,
            "merchant_order": ALL_WITH_RELATIONS,
            "invoice": ALL_WITH_RELATIONS,
            "refund": ALL_WITH_RELATIONS,
            "transaction": ALL_WITH_RELATIONS,
            "cashout_transaction": ALL_WITH_RELATIONS,
            "external_id": ALL,
            "gateway_type": ALL,
            "created_on": ALL,
            "last_modified": ALL,
        }
        creatable_fields = PspGateway.CREATABLE_FIELDS
        editable_fields = PspGateway.EDITABLE_FIELDS
        detail_uri_name = "external_id"

    def prepend_urls(self):
        return [
            re_path(
                r"^(?P<resource_name>%s)/schema%s$"
                % (self._meta.resource_name, trailing_slash()),
                self.wrap_view("get_schema"),
                name="psp_gateway_get_schema",
            ),
            re_path(
                r"^(?P<resource_name>%s)/(?P<external_id>[\w\d_.-]+)%s$"
                % (self._meta.resource_name, trailing_slash()),
                self.wrap_view("dispatch_detail"),
                name="psp_gateway_dispatch_detail",
            ),
            re_path(
                r"^(?P<resource_name>%s)/(?P<external_id>[\w\d_.-]+)/pay%s$"
                % (self._meta.resource_name, trailing_slash()),
                self.wrap_view("pay_action"),
                name="psp_gateway_pay_action",
            ),
        ]

    def obj_get(self, bundle, **kwargs):
        app = getattr(bundle.request, "application", None)
        if not app:
            owner_type = getattr(bundle.request, "owner_type", None)
            if owner_type:
                self._meta.authorization.raise_for_invalid_action_or_invalid_owner_type(
                    bundle, owner_type, "read_detail"
                )
            raise Unauthorized(
                "This resource is only accessible using an operator token"
            )
        kwargs["application"] = app

        return super().obj_get(bundle, **kwargs)

    # don't import me at module level to avoid issues on boot caused by
    # deprecation dates on EDITABLE_FOR_STATUS
    from apps.payment.api.resources import PaymentResource

    ACTION_RESOURCE_MAPPING = {
        PspGateway.PREPAYMENT_TYPE: PaymentResource,
        PspGateway.TERM_PAYMENT_TYPE: CustomerInvoiceResource,
        PspGateway.REFUND_TYPE: None,
        PspGateway.COMMISSION_TYPE: None,
    }

    def generate_action_payload(self, request, psp_gateway, result):
        resource = self.ACTION_RESOURCE_MAPPING.get(psp_gateway.gateway_type)
        if resource:
            return resource().get_detail(request, pk=result.pk)
        return self.get_detail(request, external_id=psp_gateway.external_id)

    @api_view(method_allowed=["post"], check_permissions=True)
    def pay_action(self, request, **kwargs):
        bundle = self.get_cached_object_or_raise(request, **kwargs)
        obj = bundle.obj

        deserialized = self.deserialize(
            request,
            request.body,
            format=request.META.get("CONTENT_TYPE", "application/json"),
        )

        amount = deserialized.get("amount", None)
        try:
            amount = Decimal(str(amount)) if amount is not None else amount
            if amount is not None:
                if amount < Decimal(0):
                    raise BadRequest({"amount": _("Amount must be a positive number.")})
                if amount >= Decimal("1" + 18 * "0"):
                    raise BadRequest(
                        {"amount": _("Amount must have 20 digits, decimals incl.")}
                    )
                if amount.quantize(Decimal("0.01")) != amount:
                    raise BadRequest({"amount": _("Max 2 decimals")})
        except InvalidOperation:
            raise BadRequest(
                {
                    "amount": _(
                        "Must be a decimal number with max 20 digits and "
                        "containing max 2 decimals."
                    )
                }
            )
        settlement_date = self.read_datetime_in_param(request, "settlement_date")

        with transaction.atomic():
            obj.actions.pay(
                amount=amount,
                settlement_date=settlement_date,
                external_id=deserialized.get("payment_external_id"),
                external_id_provided="payment_external_id" in deserialized,
            )
        return self.get_detail(request, **kwargs)

    def hydrate_gateway_type(self, bundle):
        if "gateway_type" in bundle.data:
            bundle.data["gateway_type"] = bundle.data["gateway_type"].upper()
        return bundle
