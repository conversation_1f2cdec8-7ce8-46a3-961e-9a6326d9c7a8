# -*- coding: utf-8 -*-


from django.apps import AppConfig
from ims.core_modules import TECHNICAL_MODULE


class PspGatewayConfig(AppConfig):
    name = "payment_backends.psp_gateway"
    verbose_name = "PSP Gateway payment backend"
    module_name = TECHNICAL_MODULE

    def ready(self):
        from apps.payment.summarizers import register_payment_summarizer
        from payment_backends.psp_gateway.summarizer import PspGatewaySummarizer

        register_payment_summarizer(PspGatewaySummarizer)
