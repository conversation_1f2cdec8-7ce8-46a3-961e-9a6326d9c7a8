from django.utils.translation import gettext_lazy as _
from ims.api.exceptions import BaseResponseException


class DuplicatedExternalId(BaseResponseException):
    status = 400
    error_code = "PSP_GATEWAY.DUPLICATED_EXTERNAL_ID"
    error_description = _("A PSP gateway already exists for this external id")


class DuplicatedMerchantOrder(BaseResponseException):
    status = 400
    error_code = "PSP_GATEWAY.DUPLICATED_MERCHANT_ORDER"
    error_description = _("A PSP gateway already exists for this merchant order")


class DuplicatedInvoice(BaseResponseException):
    status = 400
    error_code = "PSP_GATEWAY.DUPLICATED_INVOICE"
    error_description = _("A PSP gateway already exists for this invoice")


class DuplicatedRefund(BaseResponseException):
    status = 400
    error_code = "PSP_GATEWAY.DUPLICATED_REFUND"
    error_description = _("A PSP gateway already exists for this refund")


class DuplicatedTransaction(BaseResponseException):
    status = 400
    error_code = "PSP_GATEWAY.DUPLICATED_TRANSACTION"
    error_description = _("A PSP gateway already exists for this transaction")


class WrongTransactionType(BaseResponseException):
    status = 400
    error_code = "PSP_GATEWAY.WRONG_TRANSACTION_TYPE"
    error_description = _("Wrong transation type for this gateway type")
