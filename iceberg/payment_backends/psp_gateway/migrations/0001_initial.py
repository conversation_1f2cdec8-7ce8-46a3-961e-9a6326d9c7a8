# Generated by Django 3.2.20 on 2023-08-24 09:28

import django.core.serializers.json
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("ice_applications", "0001_initial"),
        ("currencies", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="PspGatewayPaymentLog",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "debug",
                    models.BooleanField(
                        default=True,
                        help_text="If True, will use the debug api handler",
                    ),
                ),
                (
                    "external_payment_backend_id",
                    models.CharField(
                        blank=True,
                        help_text="External backend payment ID as returned by used service's API",
                        max_length=255,
                        null=True,
                        verbose_name="External payment ID",
                    ),
                ),
                (
                    "external_payment_backend",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("amount", models.DecimalField(decimal_places=2, max_digits=20)),
                ("created_on", models.DateTimeField(auto_now_add=True)),
                ("updated_on", models.DateTimeField(auto_now=True, null=True)),
                (
                    "currency",
                    models.ForeignKey(
                        db_index=False,
                        default="EUR",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="currencies.currency",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="PspGateway",
            fields=[
                (
                    "created_on",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created on"),
                ),
                (
                    "last_modified",
                    models.DateTimeField(auto_now=True, verbose_name="Last modified"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=None,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                        verbose_name="Unique ID",
                    ),
                ),
                (
                    "external_id",
                    models.CharField(
                        help_text="External ID of the Gateway",
                        max_length=255,
                        validators=[
                            django.core.validators.RegexValidator(
                                regex="^[a-zA-Z\\-_0-9]+$"
                            )
                        ],
                        verbose_name="External ID",
                    ),
                ),
                (
                    "gateway_type",
                    models.CharField(
                        choices=[
                            ("PREPAYMENT", "Prepayment"),
                            ("TERM_PAYMENT", "Term payment"),
                            ("REFUND", "Refund"),
                            ("COMMISSION", "Commission"),
                            ("COMMISSION_REFUND", "Commission Refund"),
                            ("PAYOUT_TRANSACTION", "Payout transaction"),
                            (
                                "STORE_ADJUSTMENT_TRANSACTION",
                                "Store adjustment transaction",
                            ),
                            (
                                "MARKETING_OP_TRANSACTION",
                                "Marketing operation transaction",
                            ),
                            ("LICENSE_TRANSACTION", "License transaction"),
                        ],
                        help_text="Type of the gateway",
                        max_length=30,
                        verbose_name="Gateway type",
                    ),
                ),
                (
                    "extra_data",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        encoder=django.core.serializers.json.DjangoJSONEncoder,
                        verbose_name="Extra data (as dict)",
                    ),
                ),
                (
                    "application",
                    models.ForeignKey(
                        db_index=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="psp_gateways",
                        to="ice_applications.application",
                    ),
                ),
            ],
        ),
    ]
