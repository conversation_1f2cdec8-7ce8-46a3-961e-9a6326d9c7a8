# Generated by Django 3.2.20 on 2023-08-24 09:28

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("returns", "0001_initial"),
        ("transactions", "0001_initial"),
        ("invoicing", "0001_initial"),
        ("psp_gateway", "0001_initial"),
        ("orders", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="pspgateway",
            name="cashout_transaction",
            field=models.ForeignKey(
                blank=True,
                db_index=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="cashed_out_gateways",
                to="transactions.transaction",
            ),
        ),
        migrations.AddField(
            model_name="pspgateway",
            name="invoice",
            field=models.ForeignKey(
                blank=True,
                db_index=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="psp_gateways",
                to="invoicing.customerinvoice",
            ),
        ),
        migrations.AddField(
            model_name="pspgateway",
            name="merchant_order",
            field=models.ForeignKey(
                blank=True,
                db_index=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="psp_gateways",
                to="orders.merchantorder",
            ),
        ),
        migrations.AddField(
            model_name="pspgateway",
            name="refund",
            field=models.ForeignKey(
                blank=True,
                db_index=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="psp_gateways",
                to="returns.refund",
            ),
        ),
        migrations.AddField(
            model_name="pspgateway",
            name="transaction",
            field=models.ForeignKey(
                blank=True,
                db_index=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="psp_gateways",
                to="transactions.transaction",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="pspgateway",
            unique_together={
                ("gateway_type", "refund"),
                ("gateway_type", "invoice"),
                ("gateway_type", "merchant_order"),
                ("gateway_type", "transaction"),
                ("application", "external_id"),
            },
        ),
    ]
