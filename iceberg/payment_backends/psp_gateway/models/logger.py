# -*- coding: utf-8 -*-


import logging
import uuid

from django.db import models
from django.utils.translation import gettext_lazy as _

logger = logging.getLogger(__name__)


class PspGatewayPaymentLogManager(models.Manager):
    def create_with_payment_and_amount(self, payment, amount):
        currency = payment.currency
        backend_name = b"psp_gateway"
        external_id = payment.external_id
        return self.create(
            amount=amount,
            currency=currency,
            external_payment_backend=backend_name,
            external_payment_backend_id=external_id,
        )

    def authorized(self, payment):
        amount = payment.to_collect_amount
        return self.create_with_payment_and_amount(payment, amount)

    def collected(self, payment):
        amount = payment.collected_amount
        return self.create_with_payment_and_amount(payment, amount)

    def refunded(self, payment):
        """
        @param payment: Same as authorized but with reverse amount
        @return:
        """
        amount = -payment.refunded_amount
        return self.create_with_payment_and_amount(payment, amount)


class PspGatewayPaymentLog(models.Model):
    objects = PspGatewayPaymentLogManager()

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    debug = models.BooleanField(
        default=True,
        help_text=_("If True, will use the debug api handler"),
    )
    external_payment_backend_id = models.CharField(
        _("External payment ID"),
        max_length=255,
        null=True,
        blank=True,
        help_text=_("External backend payment ID as returned by used service's API"),
    )
    external_payment_backend = models.CharField(
        null=True,
        blank=True,
        max_length=255,
    )
    amount = models.DecimalField(max_digits=20, decimal_places=2)
    currency = models.ForeignKey(
        "currencies.Currency", default="EUR", db_index=False, on_delete=models.CASCADE
    )
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(
        auto_now=True,
        null=True,
        blank=True,
    )

    def __str__(self):
        return "%s - payment of %s %s through %s (%s)" % (
            self.id,
            self.amount,
            self.currency,
            self.external_payment_backend,
            self.external_payment_backend_id,
        )
