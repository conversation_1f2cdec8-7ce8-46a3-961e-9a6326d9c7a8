# -*- coding: utf-8 -*-

import logging

from django.core.exceptions import ValidationError
from django.core.serializers.json import DjangoJSONEncoder
from django.core.validators import RegexValidator
from django.db import models
from django.utils.functional import cached_property
from django.utils.translation import gettext_lazy as _
from lib.models import TimestampedModelMixin, UUIDModelMixin
from payment_backends.psp_gateway.actions import PspGatewayActionManager
from payment_backends.psp_gateway.exceptions import (
    DuplicatedExternalId,
    DuplicatedInvoice,
    DuplicatedMerchantOrder,
    DuplicatedRefund,
    DuplicatedTransaction,
    WrongTransactionType,
)
from reference import transaction_codes as tc

logger = logging.getLogger(__name__)


class PspGateway(TimestampedModelMixin, UUIDModelMixin):
    """
    This model is used as a gateway between IZBERG
    and an external PSP.

    This gateway link an instance of the PSPrepresented through
    the external_id field and one of the following
    IZBERG instance:
        -MerchantOrder
        -CustomerInvoice
        -Refund
        -Transaction
    The Izberg model linked is defined by the gateway_type fields
    In the same order :
        -PREPAYMENT
        -TERM_PAYMENT
        -REFUND
        -COMMISSION

    Mandatory fields:
        -application
        -external_id
        -gateway_type
        -(one of the foreingnkey regarding the gateway_type)
    """

    class Meta:
        unique_together = [
            ["application", "external_id"],
            ["gateway_type", "merchant_order"],
            ["gateway_type", "invoice"],
            ["gateway_type", "refund"],
            ["gateway_type", "transaction"],
        ]

    application = models.ForeignKey(
        "ice_applications.Application",
        related_name="psp_gateways",
        on_delete=models.CASCADE,
        db_index=False,
    )

    external_id = models.CharField(
        _("External ID"),
        max_length=255,
        null=False,
        blank=False,
        help_text=_("External ID of the Gateway"),
        validators=[RegexValidator(regex=r"^[a-zA-Z\-_0-9]+$")],
    )

    PREPAYMENT_TYPE = "PREPAYMENT"
    TERM_PAYMENT_TYPE = "TERM_PAYMENT"
    REFUND_TYPE = "REFUND"
    COMMISSION_TYPE = "COMMISSION"
    COMMISSION_REFUND_TYPE = "COMMISSION_REFUND"
    PAYOUT_TRANSACTION_TYPE = "PAYOUT_TRANSACTION"
    STORE_ADJUSTMENT_TRANSACTION_TYPE = "STORE_ADJUSTMENT_TRANSACTION"
    MARKETING_OPERATION_TRANSACTION_TYPE = "MARKETING_OP_TRANSACTION"
    LICENSE_TRANSACTION_TYPE = "LICENSE_TRANSACTION"

    PSP_GATEWAY_TYPE_CHOICES = (
        (PREPAYMENT_TYPE, _("Prepayment")),
        (TERM_PAYMENT_TYPE, _("Term payment")),
        (REFUND_TYPE, _("Refund")),
        (COMMISSION_TYPE, _("Commission")),
        (COMMISSION_REFUND_TYPE, _("Commission Refund")),
        (PAYOUT_TRANSACTION_TYPE, _("Payout transaction")),
        (STORE_ADJUSTMENT_TRANSACTION_TYPE, _("Store adjustment transaction")),
        (MARKETING_OPERATION_TRANSACTION_TYPE, _("Marketing operation transaction")),
        (LICENSE_TRANSACTION_TYPE, _("License transaction")),
    )

    gateway_type = models.CharField(
        _("Gateway type"),
        max_length=30,
        null=False,
        blank=False,
        choices=PSP_GATEWAY_TYPE_CHOICES,
        help_text=_("Type of the gateway"),
    )

    extra_data = models.JSONField(
        _("Extra data (as dict)"),
        default=dict,
        null=False,
        blank=True,
        encoder=DjangoJSONEncoder,
    )

    # mandatory for PREPAYMENT_TYPE
    merchant_order = models.ForeignKey(
        "orders.MerchantOrder",
        null=True,
        blank=True,
        related_name="psp_gateways",
        on_delete=models.CASCADE,
        db_index=False,
    )

    # mandatory for TERM_PAYMENT_TYPE
    invoice = models.ForeignKey(
        "invoicing.CustomerInvoice",
        null=True,
        blank=True,
        related_name="psp_gateways",
        on_delete=models.CASCADE,
        db_index=False,
    )

    # mandatory for REFUND_TYPE
    refund = models.ForeignKey(
        "returns.Refund",
        null=True,
        blank=True,
        related_name="psp_gateways",
        on_delete=models.CASCADE,
        db_index=False,
    )

    # mandatory for COMMISSION_TYPE
    transaction = models.ForeignKey(
        "transactions.Transaction",
        null=True,
        blank=True,
        related_name="psp_gateways",
        on_delete=models.CASCADE,
        db_index=False,
    )

    # cashout-transaction --> To known when it was cashed out (and if it was)
    cashout_transaction = models.ForeignKey(
        "transactions.Transaction",
        null=True,
        blank=True,
        related_name="cashed_out_gateways",
        on_delete=models.CASCADE,
        db_index=False,
    )

    CREATABLE_FIELDS = [
        "application",
        "merchant_order",
        "invoice",
        "refund",
        "transaction",
        "gateway_type",
        "external_id",
        "extra_data",
    ]
    EDITABLE_FIELDS = [
        "extra_data",
        "cashout_transaction",
    ]

    TYPE_MAPPING = {
        PREPAYMENT_TYPE: "merchant_order",
        TERM_PAYMENT_TYPE: "invoice",
        REFUND_TYPE: "refund",
        COMMISSION_TYPE: "transaction",
        COMMISSION_REFUND_TYPE: "transaction",
        PAYOUT_TRANSACTION_TYPE: "transaction",
        STORE_ADJUSTMENT_TRANSACTION_TYPE: "transaction",
        MARKETING_OPERATION_TRANSACTION_TYPE: "transaction",
        LICENSE_TRANSACTION_TYPE: "transaction",
    }

    @property
    def type_relation_attr(self):
        return self.TYPE_MAPPING.get(self.gateway_type)

    @property
    def type_relation(self):
        return getattr(self, self.type_relation_attr or "", None)

    TRANSACTION_TYPE_MAPPING = {
        PAYOUT_TRANSACTION_TYPE: tc.TRANSACTION_CASHOUT,
        STORE_ADJUSTMENT_TRANSACTION_TYPE: tc.TRANSACTION_STORE_ADJUSTMENT,
        MARKETING_OPERATION_TRANSACTION_TYPE: tc.TRANSACTION_MARKETING_OP,
        LICENSE_TRANSACTION_TYPE: tc.TRANSACTION_LICENSE,
        COMMISSION_TYPE: tc.TRANSACTION_COMMISSION,
        COMMISSION_REFUND_TYPE: tc.TRANSACTION_COMMISSION_REFUND,
    }

    def clean_transaction_type(self):
        if self.gateway_type not in self.TRANSACTION_TYPE_MAPPING.keys():
            return
        if (
            self.transaction.transaction_type
            != self.TRANSACTION_TYPE_MAPPING[self.gateway_type]
        ):
            _kwargs = {
                "application_id": self.application_id,
                "gateway_type": self.gateway_type,
                "transaction_id": self.transaction.id,
                "transaction_type": self.transaction.transaction_type,
            }
            raise WrongTransactionType(**_kwargs)

    def clean(self):
        if self.type_relation_attr and not self.type_relation:
            raise ValidationError(
                {
                    "__all__": _(
                        "{} field is mandatory for gateway_type: {}".format(
                            self.type_relation_attr, self.gateway_type
                        )
                    )
                }
            )
        self.clean_transaction_type()

    def unique_error_message(self, model_class, unique_check):
        if unique_check == ("application", "external_id"):
            _kwargs = {
                "application_id": self.application_id,
                "external_id": self.external_id,
            }
            psp_gateway_id = PspGateway.objects.values_list("id", flat=True).get(
                **_kwargs
            )
            raise DuplicatedExternalId(psp_gateway_id=psp_gateway_id, **_kwargs)
        elif unique_check == ("gateway_type", "merchant_order"):
            _kwargs = {
                "application_id": self.application_id,
                "gateway_type": self.gateway_type,
                "merchant_order_id": self.merchant_order.id,
            }
            psp_gateway_id = PspGateway.objects.values_list("id", flat=True).get(
                **_kwargs
            )
            raise DuplicatedMerchantOrder(psp_gateway_id=psp_gateway_id, **_kwargs)
        elif unique_check == ("gateway_type", "invoice"):
            _kwargs = {
                "application_id": self.application_id,
                "gateway_type": self.gateway_type,
                "invoice_id": self.invoice.id,
            }
            psp_gateway_id = PspGateway.objects.values_list("id", flat=True).get(
                **_kwargs
            )
            raise DuplicatedInvoice(psp_gateway_id=psp_gateway_id, **_kwargs)
        elif unique_check == ("gateway_type", "refund"):
            _kwargs = {
                "application_id": self.application_id,
                "gateway_type": self.gateway_type,
                "refund_id": self.refund.id,
            }
            psp_gateway_id = PspGateway.objects.values_list("id", flat=True).get(
                **_kwargs
            )
            raise DuplicatedRefund(psp_gateway_id=psp_gateway_id, **_kwargs)
        elif unique_check == ("gateway_type", "transaction"):
            _kwargs = {
                "application_id": self.application_id,
                "gateway_type": self.gateway_type,
                "transaction_id": self.transaction.id,
            }
            psp_gateway_id = PspGateway.objects.values_list("id", flat=True).get(
                **_kwargs
            )
            raise DuplicatedTransaction(psp_gateway_id=psp_gateway_id, **_kwargs)
        return super(PspGateway, self).unique_error_message(model_class, unique_check)

    def save(self, *args, **kwargs):
        self.full_clean()
        super(PspGateway, self).save(*args, **kwargs)

    @cached_property
    def actions(self):
        return PspGatewayActionManager(self)

    @cached_property
    def merchant(self):
        if self.merchant_order_id:
            return self.merchant_order.merchant
        elif self.invoice_id:
            return self.invoice.issuer
        elif self.refund_id:
            return self.refund.merchant
        elif self.transaction:
            return self.transaction.merchant
        else:
            return None

    def __str__(self):
        return "[{pk}] {eid} {gtype} <{type} {rel}>".format(
            pk=self.pk,
            eid=self.external_id,
            gtype=self.gateway_type,
            type=self.type_relation_attr,
            rel=self.type_relation,
        )
