# -*- coding: utf-8 -*-


from decimal import Decimal

from apps.cart.models import Cart
from apps.cart.tests.test_cart_setup_mixin import CartTestSetupMixin
from apps.ice_applications.app_conf_settings import (
    AutoCollectOnConfirm,
    AutoGenerateInvoice,
    OrderItemsAggregation,
    OrderWorkflowV2,
)
from apps.payment.backends import PSP_GATEWAY
from apps.payment.models import Payment
from apps.promotions.models import Discount
from apps.returns.models import Refund
from apps.testing.factories import (
    ApplicationFactory,
    CartFactory,
    FixedVoucherFactory,
    InvoiceCommissionRefundTransactionFactory,
    InvoiceCommissionTransactionFactory,
    LicenseTransactionFactory,
    MarketingOperationTransactionFactory,
    MerchantCashoutTransactionFactory,
    OrderItemPaymentTermFactory,
    PaymentTermLineFactory,
    ProductOfferFactory,
    Simple10EurosShippingFactory,
    StoreAdjustmentTransactionFactory,
)
from apps.transactions.tasks import process_pending_refunds
from ims.tests import BaseResourceTestCase, BaseTestCase
from invoicing.factories import CustomerInvoiceLineFactory
from invoicing.models import CreditNote, CustomerInvoice, CustomerInvoiceLine
from invoicing.statuses import CREDIT_NOTE_STATUS_EMITTED, CREDIT_NOTE_STATUS_PENDING
from mock import patch
from payment_backends.psp_gateway.models import PspGateway
from reference.status import (
    MERCHANT_ORDER_STATUS_CONFIRMED,
    MERCHANT_ORDER_STATUS_PROCESSED,
    PAYMENT_STATUS_COLLECTED,
    PRODUCT_OFFER_STATUS_ACTIVE,
)
from reference.transaction_codes import TRANSACTION_REFUND


@patch(
    "invoicing.models.CustomerInvoiceLine"
    "._prevent_edition_of_content_when_not_draft",
    lambda _: None,
)
@patch("invoicing.models.AbstractAccountingDocument.get_file_by_url", lambda _: None)
class TermPaymentTestCase(CartTestSetupMixin, BaseResourceTestCase):
    @patch("invoicing.models.CustomerInvoiceLine.full_clean", lambda _: None)
    def test_add_mo_to_invoice_and_pay_it(self):
        # init
        # I have a term payment merchant order of 800 euros in payment
        # authorized status
        oitp_1 = OrderItemPaymentTermFactory(
            order_item__price=100,
            order_item__vat=0,
            order_item__cart_item__quantity=5,
            order_item__cart_item__cart__selected_payment_type="term_payment",
            order_item__merchant_order__order__auto_create_payment=True,
        )
        oitp_2 = OrderItemPaymentTermFactory(
            order_item__merchant_order=oitp_1.order_item.merchant_order,
            order_item__cart=oitp_1.order_item.cart,
            lines=oitp_1.lines,
            order_item__price=60,
            order_item__vat=0,
            order_item__cart_item__quantity=5,
            order_item__cart_item__cart__selected_payment_type="term_payment",
        )
        merchant_order = oitp_1.order_item.merchant_order
        application = merchant_order.application
        merchant_order.order.payment.actions.authorize()
        application.set_setting(AutoGenerateInvoice, False)
        application.set_setting(AutoCollectOnConfirm, False)

        # I confirm the merchant order
        resp = self.api_client.post(
            "/v1/merchant_order/{}/confirm/".format(merchant_order.id),
            authentication=self.get_token(
                application.contact_user, application=application
            ),
        )
        self.assertHttpOK(resp)
        merchant_order.refresh_from_db()
        self.assertEqual(merchant_order.status, MERCHANT_ORDER_STATUS_CONFIRMED)
        self.assertFalse(merchant_order.transactions.exists())

        # I process the merchant order
        resp = self.api_client.post(
            "/v1/merchant_order/{}/process/".format(merchant_order.id),
            authentication=self.get_token(
                application.contact_user, application=application
            ),
        )
        self.assertHttpOK(resp)

        # I have an invoice I1 in emitted status
        invoice_line_1 = CustomerInvoiceLineFactory(
            order_item=oitp_1.order_item,
            quantity=3,
            invoice__merchant_order=merchant_order,
            invoice__currency=merchant_order.currency,
            invoice__id_number="lol",
            invoice__invoice_source="external_url",
            invoice__invoice_url="http://sample.url",
            invoice__status="emitted",
            invoice__expected_amount=420,  # order items price x quantities
            invoice__total_amount=420,  # order items price x quantities
            order=merchant_order.order,
        )
        invoice = invoice_line_1.invoice
        CustomerInvoiceLineFactory(
            order_item=oitp_2.order_item,
            quantity=2,
            invoice=invoice,
            order=merchant_order.order,
        )

        # I receive a payment of 100 on I1
        gateway_id = "my_id"
        resp = self.api_client.post(
            "/v1/psp/gateway/",
            data={
                "application": "/v1/application/{}/".format(application.id),
                "invoice": "/v1/customer_invoice/{}/".format(invoice.id),
                "gateway_type": "TERM_PAYMENT",
                "external_id": gateway_id,
            },
            authentication=self.get_token(
                application.contact_user, application=application
            ),
        )
        self.assertHttpCreated(resp)
        resp = self.api_client.post(
            "/v1/psp/gateway/{}/pay/".format(gateway_id),
            data={"amount": 100},
            authentication=self.get_token(
                application.contact_user, application=application
            ),
        )
        self.assertHttpOK(resp)
        resp = self.api_client.get(
            "/v1/payment/?customer_invoice={}".format(invoice.id),
            authentication=self.get_token(
                application.contact_user, application=application
            ),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 1)
        self.assertEqual(data["objects"][0]["authorized_amount"], "100.00")
        self.assertEqual(data["objects"][0]["status"], "80")
        invoice.refresh_from_db()
        self.assertEqual(invoice.remaining_amount, 320)
        self.assertEqual(invoice.payment_status, "not_paid")
        payment_1 = invoice.payments.get()
        transaction_1 = payment_1.transactions.filter_sale().get()
        self.assertTrue(transaction_1.status.is_pending)

        # I create a  a credit note CN1 of 50 euros on I1
        resp = self.api_client.post(
            "/v1/credit_note/",
            data={
                "invoice": "/v1/customer_invoice/{}/".format(invoice.id),
                "application": "/v1/application/{}/".format(application.id),
                "issuer": "/v1/merchant/{}/".format(invoice.issuer.id),
                "receiver": "/v1/user/{}/".format(invoice.receiver.id),
                "total_amount": 50,
            },
            authentication=self.get_token(
                application.contact_user, application=application  # merchant
            ),
        )
        self.assertHttpCreated(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["total_amount"], "50")
        self.assertEqual(data["total_amount_with_taxes"], "50.00")
        invoice.refresh_from_db()
        self.assertEqual(invoice.remaining_amount, 320)
        credit_note_id = data["id"]

        response = self.api_client.post(
            "/v1/credit_note_line/",
            data={
                "application": "/v1/application/{}/".format(application.id),
                "credit_note": "/v1/credit_note/{}/".format(credit_note_id),
                "order_item": "/v1/order_item/{}/".format(oitp_1.order_item.id),
                "invoice_line": "/v1/invoice_line/{}/".format(invoice_line_1.id),
                "unit_price": 50,
                "quantity": "1.0",
            },
            authentication=self.get_token(
                application.contact_user, application=application  # merchant
            ),
        )
        self.assertHttpCreated(response)

        # I submit CN 1
        resp = self.api_client.post(
            "/v1/credit_note/{}/submit/".format(credit_note_id),  # merchant
            authentication=self.get_token(
                application.contact_user, application=application
            ),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["status"], CREDIT_NOTE_STATUS_PENDING)

        # I push a PDF file and an id number on CN1
        resp = self.api_client.patch(
            "/v1/credit_note/{}/".format(credit_note_id),  # merchant
            data={"id_number": "my id number", "file_url": "http://sample.url"},
            authentication=self.get_token(
                application.contact_user, application=application  # merchant
            ),
        )
        self.assertHttpAccepted(resp)

        # I emit CN1
        resp = self.api_client.post(
            "/v1/credit_note/{}/emit/".format(credit_note_id),  # merchant
            authentication=self.get_token(
                application.contact_user, application=application
            ),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)

        self.assertEqual(data["status"], CREDIT_NOTE_STATUS_EMITTED)
        invoice.refresh_from_db()
        expected_remaining_amount = (
            invoice.total_amount_with_taxes
            - invoice.paid_amount
            - invoice.actions.credit_note_sum()
        )
        self.assertEqual(invoice.remaining_amount, expected_remaining_amount)
        self.assertEqual(invoice.payment_status, "not_paid")
        self.assertTrue(payment_1.transactions.filter_sale().get().status.is_pending)

        # I receive a payment of 270 on I1
        resp = self.api_client.post(
            "/v1/psp/gateway/{}/pay/".format(gateway_id),
            data={"amount": expected_remaining_amount},
            authentication=self.get_token(
                application.contact_user, application=application
            ),
        )
        self.assertHttpOK(resp)
        resp = self.api_client.get(
            "/v1/payment/?customer_invoice={}".format(invoice.id),
            authentication=self.get_token(
                application.contact_user, application=application
            ),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 2)
        self.assertEqual(
            data["objects"][0]["authorized_amount"], str(expected_remaining_amount)
        )
        self.assertEqual(data["objects"][1]["authorized_amount"], "100.00")
        invoice.refresh_from_db()
        self.assertEqual(invoice.remaining_amount, 0)
        self.assertEqual(invoice.payment_status, "paid")
        payment_2 = Payment.objects.get(id=data["objects"][0]["id"])
        tr_2 = payment_2.transactions.get()
        self.assertTrue(tr_2.status.is_pending)

    def test_pay_payout(self):
        transaction = MerchantCashoutTransactionFactory()
        application = transaction.application
        self.assertTrue(transaction.status.is_pending)

        gateway_id = "my_id"
        resp = self.api_client.post(
            "/v1/psp/gateway/",
            data={
                "application": "/v1/application/{}/".format(application.id),
                "transaction": "/v1/transaction/{}/".format(transaction.id),
                "gateway_type": "PAYOUT_TRANSACTION",
                "external_id": gateway_id,
            },
            authentication=self.get_token(
                application.contact_user, application=application
            ),
        )
        self.assertHttpCreated(resp)
        resp = self.api_client.post(
            "/v1/psp/gateway/{}/pay/".format(gateway_id),
            authentication=self.get_token(
                application.contact_user, application=application
            ),
        )
        self.assertHttpOK(resp)
        transaction.refresh_from_db()
        self.assertTrue(transaction.status.is_confirmed)

    def test_pay_store_adjustement(self):
        transaction = StoreAdjustmentTransactionFactory(status="pending")
        application = transaction.application
        self.assertTrue(transaction.status.is_pending)

        gateway_id = "my_id"
        resp = self.api_client.post(
            "/v1/psp/gateway/",
            data={
                "application": "/v1/application/{}/".format(application.id),
                "transaction": "/v1/transaction/{}/".format(transaction.id),
                "gateway_type": "STORE_ADJUSTMENT_TRANSACTION",
                "external_id": gateway_id,
            },
            authentication=self.get_token(
                application.contact_user, application=application
            ),
        )
        self.assertHttpCreated(resp)
        resp = self.api_client.post(
            "/v1/psp/gateway/{}/pay/".format(gateway_id),
            authentication=self.get_token(
                application.contact_user, application=application
            ),
        )
        self.assertHttpOK(resp)
        transaction.refresh_from_db()
        self.assertTrue(transaction.status.is_confirmed)

    def test_pay_marketing_operation(self):
        transaction = MarketingOperationTransactionFactory(status="pending")
        application = transaction.application
        self.assertTrue(transaction.status.is_pending)

        gateway_id = "my_id"
        resp = self.api_client.post(
            "/v1/psp/gateway/",
            data={
                "application": "/v1/application/{}/".format(application.id),
                "transaction": "/v1/transaction/{}/".format(transaction.id),
                "gateway_type": "MARKETING_OP_TRANSACTION",
                "external_id": gateway_id,
            },
            authentication=self.get_token(
                application.contact_user, application=application
            ),
        )
        self.assertHttpCreated(resp)
        resp = self.api_client.post(
            "/v1/psp/gateway/{}/pay/".format(gateway_id),
            authentication=self.get_token(
                application.contact_user, application=application
            ),
        )
        self.assertHttpOK(resp)
        transaction.refresh_from_db()
        self.assertTrue(transaction.status.is_confirmed)

    def test_pay_license(self):
        transaction = LicenseTransactionFactory(status="pending")
        application = transaction.application
        self.assertTrue(transaction.status.is_pending)

        gateway_id = "my_id"
        resp = self.api_client.post(
            "/v1/psp/gateway/",
            data={
                "application": "/v1/application/{}/".format(application.id),
                "transaction": "/v1/transaction/{}/".format(transaction.id),
                "gateway_type": "LICENSE_TRANSACTION",
                "external_id": gateway_id,
            },
            authentication=self.get_token(
                application.contact_user, application=application
            ),
        )
        self.assertHttpCreated(resp)
        resp = self.api_client.post(
            "/v1/psp/gateway/{}/pay/".format(gateway_id),
            authentication=self.get_token(
                application.contact_user, application=application
            ),
        )
        self.assertHttpOK(resp)
        transaction.refresh_from_db()
        self.assertTrue(transaction.status.is_confirmed)

    def test_pay_commission(self):
        transaction = InvoiceCommissionTransactionFactory(status="pending")
        application = transaction.application
        self.assertTrue(transaction.status.is_pending)

        gateway_id = "my_id"
        resp = self.api_client.post(
            "/v1/psp/gateway/",
            data={
                "application": "/v1/application/{}/".format(application.id),
                "transaction": "/v1/transaction/{}/".format(transaction.id),
                "gateway_type": "COMMISSION",
                "external_id": gateway_id,
            },
            authentication=self.get_token(
                application.contact_user, application=application
            ),
        )
        self.assertHttpCreated(resp)
        resp = self.api_client.post(
            "/v1/psp/gateway/{}/pay/".format(gateway_id),
            authentication=self.get_token(
                application.contact_user, application=application
            ),
        )
        self.assertHttpOK(resp)
        transaction.refresh_from_db()
        self.assertTrue(transaction.status.is_confirmed)

    def test_pay_commission_refund(self):
        transaction = InvoiceCommissionRefundTransactionFactory(status="pending")
        application = transaction.application
        self.assertTrue(transaction.status.is_pending)

        gateway_id = "my_id"
        resp = self.api_client.post(
            "/v1/psp/gateway/",
            data={
                "application": "/v1/application/{}/".format(application.id),
                "transaction": "/v1/transaction/{}/".format(transaction.id),
                "gateway_type": "COMMISSION_REFUND",
                "external_id": gateway_id,
            },
            authentication=self.get_token(
                application.contact_user, application=application
            ),
        )
        self.assertHttpCreated(resp)
        resp = self.api_client.post(
            "/v1/psp/gateway/{}/pay/".format(gateway_id),
            authentication=self.get_token(
                application.contact_user, application=application
            ),
        )
        self.assertHttpOK(resp)
        transaction.refresh_from_db()
        self.assertTrue(transaction.status.is_confirmed)

    def test_refund_transactions_prepayment_case(self):
        self.set_up(payment_backend=PSP_GATEWAY)
        self.application.set_setting(OrderItemsAggregation, True)
        self.application.set_setting(OrderWorkflowV2, True)
        self.application.set_setting(AutoGenerateInvoice, False)
        self.application.set_setting(AutoCollectOnConfirm, False)

        # I have a prepayment merchant order M1 containing
        #     item A : price 100 euros, quantity 3
        #     item B : price 200 euros, quantity 5
        product_offer_a = self.create_product_offer(
            sku="po_a", price=100, product_tax_group_or_id=4
        )
        product_offer_b = self.create_product_offer(
            sku="po_b", price=200, product_tax_group_or_id=4
        )
        self.create_shipping2_rule(free_shipping=True, offer=product_offer_a)
        self.create_shipping2_rule(free_shipping=True, offer=product_offer_b)
        self.create_product_cart_item(quantity=3, product_offer=product_offer_a)
        self.create_product_cart_item(quantity=5, product_offer=product_offer_b)
        order = self.cart.actions.create_order()
        merchant_order = order.merchant_orders.get()
        payment = merchant_order.order.payment
        payment.actions.authorize()
        order_item_a = merchant_order.order_items.get(price=100, quantity=3)
        order_item_b = merchant_order.order_items.get(price=200, quantity=5)

        # I confirm the merchant order
        # --> M1 goes to confirmed
        merchant_order = self.confirm_merchant_order(merchant_order)

        mo_gateway_id = "mo_gateway_id"
        self.create_prepayment_gateway(mo_gateway_id, merchant_order)
        self.pay_gateway(mo_gateway_id)
        payment.refresh_from_db()
        self.assertEqual(payment.status, PAYMENT_STATUS_COLLECTED)

        # I process the merchant order
        merchant_order = self.process_merchant_order(merchant_order)
        order_item_a = self.update_invoiceable_quantity(order_item_a)
        order_item_b = self.update_invoiceable_quantity(order_item_b)

        # I create a customer invoice I1 containing
        #   - 1 quantity of A  == 1 x 100
        #   - 2 quantities of B == 2 x 200
        #   - a total amount of 500 euros == 1 x A + 2 x B
        invoice = self.create_invoice(self.merchant, self.cart_user)
        invoice_line_a = self.create_invoice_line(invoice, order_item_a, 1)
        invoice_line_b = self.create_invoice_line(invoice, order_item_b, 2)
        invoice.refresh_from_db()
        self.assertEqual(invoice.total_amount, Decimal("500"))

        # I submit I1 --> I1 goes to pending status
        # I push a PDF file and an id number on I1 --> I1 is updated
        # I emit I1 --> I1 goes to emitted status.
        invoice.id_number = "lol"
        invoice.file_url = "http://sample.url"
        invoice.file_source = "external_url"
        invoice.status = "emitted"
        invoice.save()

        # I create a refund on M1, with : 1 quantity of A and 1 of B
        # --> refund R1 is created with:
        #   - 1 quantity of A
        #   - 1 quantity of B
        #   - a total amount of 300 euros
        refund_a = self.create_refund([(order_item_a, 1)])
        refund_b = self.create_refund([(order_item_b, 1)])
        refund_item_a = refund_a.refund_items.get(order_item=order_item_a)
        refund_item_b = refund_b.refund_items.get(order_item=order_item_b)
        self.assertEqual(refund_a.total_refund_amount, 100)
        self.assertEqual(refund_b.total_refund_amount, 200)
        self.assertEqual(refund_item_a.quantity, 1)
        self.assertEqual(refund_item_b.quantity, 1)

        # I create a gateway on R1
        #   -> the gateway is created
        gateway_id_a = "my_id_a"
        gateway_id_b = "my_id_b"
        self.create_refund_gateway(gateway_id_a, refund_a)
        self.create_refund_gateway(gateway_id_b, refund_b)

        # I pay the gateway
        #   -> R1 goes to complete status
        #   -> a refund transaction is created of -300 euros for the merchant,
        #   linked to M1, in pending status.
        self.pay_gateway(gateway_id_a)
        self.pay_gateway(gateway_id_b)

        refund_a.refresh_from_db()
        refund_b.refresh_from_db()
        self.assertEqual(refund_a.status, "complete")
        self.assertEqual(refund_b.status, "complete")
        self.assertEqual(refund_a.total_refund_amount, order_item_a.amount_vat_included)
        self.assertEqual(refund_b.total_refund_amount, order_item_b.amount_vat_included)

        main_tr_a = refund_a.transactions.get(transaction_type=TRANSACTION_REFUND)
        main_tr_b = refund_b.transactions.get(transaction_type=TRANSACTION_REFUND)
        self.assertEqual(main_tr_a.amount, Decimal("-100"))
        self.assertEqual(main_tr_b.amount, Decimal("-200"))
        self.assertFalse(main_tr_a.appbalancetransactions.exists())
        self.assertFalse(main_tr_b.appbalancetransactions.exists())
        store_tr_a = main_tr_a.balancetransactions.get()
        store_tr_b = main_tr_b.balancetransactions.get()
        self.assertEqual(store_tr_a.amount, Decimal("-100"))
        self.assertEqual(store_tr_b.amount, Decimal("-200"))
        self.assertEqual(main_tr_a.merchant_order, merchant_order)
        self.assertEqual(main_tr_b.merchant_order, merchant_order)
        self.assertTrue(main_tr_a.status.is_pending)
        self.assertTrue(main_tr_b.status.is_pending)

        # I create a credit note CN1 of 250 euros on I1
        #   --> credit note of 250 euros on I1 is created
        credit_note = self.create_credit_note(invoice)
        credit_note.lines.create(invoice_line_id=invoice_line_a.id, quantity=1)
        credit_note.lines.create(
            invoice_line_id=invoice_line_b.id, quantity=1, unit_price=150
        )
        credit_note.refresh_from_db()
        self.assertEqual(credit_note.total_amount, Decimal("250"))

        # I submit CN 1 on I1
        #   --> CN1 goes to pending status
        self.submit_credit_note(credit_note)

        # I push a PDF file and an id number on CN1
        #   --> CN1 is updated
        credit_note.id_number = "my id"
        credit_note.file_url = "http://my.url"
        credit_note.file_source = "external_url"
        credit_note.save()

        # I emit CN1
        # -> CN1 goes to emitted status
        # -> a refund object R2 is created, in to process manually state,
        #    with total amount 250
        self.emit_credit_note(credit_note)
        self.assertIsNotNone(credit_note.refund)
        refund_2 = credit_note.refund
        self.assertEqual(refund_2.amount_vat_included, Decimal("250"))
        self.assertTrue(refund_2.status.is_manual_process)

        # I create a gateway on R2
        # --> the gateway is created on R2
        external_id = "refund_2"
        self.create_refund_gateway(external_id, refund_2)

        # I pay the gateway :
        #     -> R2 goes to complete status
        #     -> a refund transaction is created of -150 euros for the
        #     merchant, linked to I1, in pending status.
        self.pay_gateway(external_id)
        refund_2.refresh_from_db()
        self.assertTrue(refund_2.status.is_complete)
        main_tr_2 = refund_2.transactions.get(transaction_type=TRANSACTION_REFUND)
        self.assertEqual(main_tr_2.amount, Decimal("-250"))
        self.assertFalse(main_tr_2.appbalancetransactions.exists())
        store_tr_2 = main_tr_2.balancetransactions.get()
        self.assertEqual(store_tr_2.amount, Decimal("-250"))
        self.assertEqual(main_tr_2.merchant_order, merchant_order)
        self.assertTrue(main_tr_2.status.is_pending)

        # emulate the cron task normally running each night
        process_pending_refunds()

        main_tr_a.refresh_from_db()
        main_tr_b.refresh_from_db()
        # transactions are ignored by processing tasks since we're dealing
        # with PSP gateway. Refunds must stay in pending state (but refunds are
        # are completed on cashin-side)
        self.assertTrue(main_tr_a.status.is_pending)
        self.assertTrue(main_tr_b.status.is_pending)
        main_tr_2.refresh_from_db()
        self.assertTrue(main_tr_2.status.is_pending)

    def test_refund_transactions_term_payment_case(self):
        self.set_up(payment_backend=PSP_GATEWAY, payment_type=Cart.TERM_PAYMENT)
        self.application.set_setting(OrderItemsAggregation, True)
        self.application.set_setting(OrderWorkflowV2, True)
        self.application.set_setting(AutoGenerateInvoice, False)
        self.application.set_setting(AutoCollectOnConfirm, False)

        # I have a term payment merchant order M1 containing
        #     item A : price 100 euros, quantity 3
        #     item B : price 200 euros, quantity 5
        PaymentTermLineFactory(payment_term__application=self.application)
        product_offer_a = self.create_product_offer(
            sku="po_a", price=100, product_tax_group_or_id=4
        )
        product_offer_b = self.create_product_offer(
            sku="po_b", price=200, product_tax_group_or_id=4
        )
        self.cart.selected_payment_type = self.cart.TERM_PAYMENT
        self.cart.save()
        self.create_shipping2_rule(free_shipping=True, offer=product_offer_a)
        self.create_shipping2_rule(free_shipping=True, offer=product_offer_b)
        self.create_product_cart_item(quantity=3, product_offer=product_offer_a)
        self.create_product_cart_item(quantity=5, product_offer=product_offer_b)
        order = self.cart.actions.create_order()
        merchant_order = order.merchant_orders.get()
        merchant_order.order.actions.paymentless_authorization()
        order_item_a = merchant_order.order_items.get(price=100, quantity=3)
        order_item_b = merchant_order.order_items.get(price=200, quantity=5)

        # I confirm the merchant order
        # --> M1 goes to confirmed
        merchant_order = self.confirm_merchant_order(merchant_order)

        # I process the merchant order
        merchant_order = self.process_merchant_order(merchant_order)
        order_item_a = self.update_invoiceable_quantity(order_item_a)
        order_item_b = self.update_invoiceable_quantity(order_item_b)

        # I create a customer invoice I1 containing
        #   - 1 quantity of A  == 1 x 100
        #   - 2 quantities of B == 2 x 200
        #   - a total amount of 500 euros == 1 x A + 2 x B
        invoice = self.create_invoice(self.merchant, self.cart_user)
        invoice_line_a = self.create_invoice_line(invoice, order_item_a, 1)
        invoice_line_b = self.create_invoice_line(invoice, order_item_b, 2)
        invoice.refresh_from_db()
        self.assertEqual(invoice.total_amount, Decimal("500"))

        # I submit I1 --> I1 goes to pending status
        # I push a PDF file and an id number on I1 --> I1 is updated
        # I emit I1 --> I1 goes to emitted status.
        invoice.id_number = "lol"
        invoice.file_url = "http://sample.url"
        invoice.file_source = "external_url"
        invoice.status = "emitted"
        invoice.merchant_order = merchant_order
        invoice.save()

        # I receive 2 payments on I1 : one of 100 euros and one of 300 euros
        #   - remaining amount of I1 goes to 100 euros
        invoice_gateway_id = "invoice_gid"
        self.create_invoice_gateway(invoice_gateway_id, invoice)
        self.pay_gateway(invoice_gateway_id, 100)
        self.pay_gateway(invoice_gateway_id, 300)
        invoice.refresh_from_db()
        self.assertEqual(invoice.remaining_amount, Decimal("100.00"))

        # I create a credit note CN1 of 250 euros on I1
        # --> credit note of 250 euros on I1 is created
        credit_note = self.create_credit_note(invoice)
        credit_note.lines.create(invoice_line_id=invoice_line_a.id, quantity=1)
        credit_note.lines.create(
            invoice_line_id=invoice_line_b.id, quantity=1, unit_price=150
        )
        credit_note.refresh_from_db()
        self.assertEqual(credit_note.total_amount, Decimal("250"))

        # I submit CN 1 on I1
        #   --> CN1 goes to pending status
        self.submit_credit_note(credit_note)

        # I push a PDF file and an id number on CN1
        #   --> CN1 is updated
        credit_note.id_number = "my id"
        credit_note.file_url = "http://my.url"
        credit_note.file_source = "external_url"
        credit_note.save()

        # I emit CN1
        # -> CN1 goes to emitted status
        # -> remaining amount of I1 goes to 0
        # -> I1 payment status goes to "paid".
        # -> a refund object R1 is created, in to process manually state,
        #    with total amount 150
        self.emit_credit_note(credit_note)
        self.assertIsNotNone(credit_note.refund)
        refund = credit_note.refund
        self.assertEqual(refund.amount_vat_included, Decimal("150"))
        self.assertTrue(refund.status.is_manual_process)

        # I create a gateway on R1
        #   --> the gateway is created
        refund_gateway_id = "refund_gid"
        self.create_refund_gateway(refund_gateway_id, refund)

        # I pay the gateway :
        #   R1 goes to complete status
        #   a refund transaction is created of -150
        self.pay_gateway(refund_gateway_id)
        refund.refresh_from_db()
        self.assertTrue(refund.status.is_complete)
        self.assertTrue(refund.transactions.filter())
        refund_tr = refund.transactions.get(transaction_type=TRANSACTION_REFUND)
        self.assertEqual(refund_tr.amount, Decimal("-150"))
        self.assertTrue(refund_tr.status.is_pending)
        merchant_sub_tr = refund_tr.balancetransactions.get()
        self.assertTrue(merchant_sub_tr.status.is_pending)
        self.assertEqual(merchant_sub_tr.amount, Decimal("-150"))
        self.assertFalse(refund_tr.appbalancetransactions.exists())

        # emulate the cron task normally running each night
        process_pending_refunds()

        refund_tr.refresh_from_db()
        self.assertTrue(refund_tr.status.is_pending)

    def confirm_merchant_order(self, merchant_order):
        resp = self.api_client.post(
            "/v1/merchant_order/{}/confirm/".format(merchant_order.id),
            authentication=self.get_token(
                self.application.contact_user, application=self.application
            ),
        )
        self.assertHttpOK(resp)
        merchant_order.refresh_from_db()
        self.assertEqual(merchant_order.status, MERCHANT_ORDER_STATUS_CONFIRMED)
        return merchant_order

    def process_merchant_order(self, merchant_order):
        resp = self.api_client.post(
            "/v1/merchant_order/{}/process/".format(merchant_order.id),
            authentication=self.get_token(self.application.contact_user),
        )
        self.assertHttpOK(resp)
        merchant_order.refresh_from_db()
        self.assertEqual(merchant_order.status, MERCHANT_ORDER_STATUS_PROCESSED)
        return merchant_order

    def update_invoiceable_quantity(self, order_item):
        order_item.refresh_from_db()
        order_item.invoiceable_quantity = order_item.quantity
        order_item.save()
        return order_item

    def create_invoice(self, merchant, user):
        data = {
            "issuer": "/v1/merchant/{}/".format(merchant.id),
            "receiver": "/v1/user/{}/".format(user.id),
            "application": "/v1/application/{}/".format(merchant.application.id),
        }
        resp = self.api_client.post(
            "/v1/customer_invoice/", data=data, authentication=self.get_merchant_token()
        )
        return CustomerInvoice.objects.get(id=self.deserialize(resp)["id"])

    def create_invoice_line(self, invoice, order_item, quantity):
        data = {
            "invoice": "/v1/customer_invoice/{}/".format(invoice.id),
            "order_item": "/v1/order_item/{}/".format(order_item.id),
            "quantity": quantity,
        }
        resp = self.api_client.post(
            "/v1/invoice_line/", data=data, authentication=self.get_merchant_token()
        )
        self.assertHttpCreated(resp)
        return CustomerInvoiceLine.objects.get(id=self.deserialize(resp)["id"])

    def create_refund(self, order_items):
        """
        order_items expect a list of tuples:
        [(order_item, <quantity>),]
        """
        mo = order_items[0][0].merchant_order

        data = {
            "order_items": [
                {
                    "order_item": "/v1/order_item/{}/".format(order_item.id),
                    "quantity": quantity,
                }
                for order_item, quantity in order_items
            ],
            "merchant_order": "/v1/merchant_order/{}/".format(mo.id),
            "partial_refund": True,
            "amount": sum([oi.price for oi, quantity in order_items]),
            "credit_note": None,
            "memo": "",
            "return_requests": [],
            "shipping": 0,
        }
        resp = self.api_client.post(
            "/v1/refund/", data=data, authentication=self.get_merchant_token()
        )
        self.assertHttpCreated(resp)
        return Refund.objects.get(id=self.deserialize(resp)["id"])

    def create_refund_gateway(self, external_id, refund):
        resp = self.api_client.post(
            "/v1/psp/gateway/",
            data={
                "application": "/v1/application/{}/".format(refund.application.id),
                "refund": "/v1/refund/{}/".format(refund.id),
                "gateway_type": "REFUND",
                "external_id": external_id,
            },
            authentication=self.get_token(refund.application.contact_user),
        )
        self.assertHttpCreated(resp)

    def create_invoice_gateway(self, external_id, invoice):
        resp = self.api_client.post(
            "/v1/psp/gateway/",
            data={
                "application": "/v1/application/{}/".format(invoice.application_id),
                "invoice": "/v1/customer_invoice/{}/".format(invoice.id),
                "gateway_type": "TERM_PAYMENT",
                "external_id": external_id,
            },
            authentication=self.get_token(invoice.application.contact_user),
        )
        self.assertHttpCreated(resp)

    def create_prepayment_gateway(self, external_id, merchant_order):
        resp = self.api_client.post(
            "/v1/psp/gateway/",
            data={
                "application": "/v1/application/{}/".format(
                    merchant_order.application_id
                ),
                "merchant_order": "/v1/merchant_order/{}/".format(merchant_order.id),
                "gateway_type": "PREPAYMENT",
                "external_id": external_id,
            },
            authentication=self.get_token(merchant_order.application.contact_user),
        )
        self.assertHttpCreated(resp)

    def pay_gateway(self, gateway_id, amount=None):
        resp = self.api_client.post(
            "/v1/psp/gateway/{}/pay/".format(gateway_id),
            data={"amount": amount} if amount is not None else {},
            authentication=self.get_token(self.application.contact_user),
        )
        self.assertHttpOK(resp)

    def create_credit_note(self, invoice):
        data = {
            "issuer": "/v1/merchant/{}/".format(invoice.issuer_id),
            "receiver": "/v1/user/{}/".format(invoice.receiver_id),
            "application": "/v1/application/{}/".format(invoice.application_id),
            "invoice": "/v1/customer_invoice/{}/".format(invoice.id),
        }
        resp = self.api_client.post(
            "/v1/credit_note/", data=data, authentication=self.get_merchant_token()
        )
        self.assertHttpCreated(resp)
        return CreditNote.objects.get(id=self.deserialize(resp)["id"])

    def submit_credit_note(self, credit_note):
        resp = self.api_client.post(
            "/v1/credit_note/{}/submit/".format(credit_note.id),
            authentication=self.get_merchant_token(),
        )
        self.assertHttpOK(resp)
        data = self.deserialize(resp)

        self.assertEqual(data["status"], "pending")
        credit_note.refresh_from_db()
        self.assertTrue(credit_note.status.is_pending)

    def emit_credit_note(self, credit_note):
        resp = self.api_client.post(
            "/v1/credit_note/{}/emit/".format(credit_note.id),
            authentication=self.get_merchant_token(),
        )
        self.assertHttpOK(resp)
        credit_note.refresh_from_db()
        self.assertTrue(credit_note.status.is_emitted)


class PspGatewayPaymentTestCase(BaseTestCase):
    def test_payment_with_voucher_on_unique_merchant_order(self):
        application = ApplicationFactory()
        application.payment_settings.payment_backend = PSP_GATEWAY
        application.payment_settings.save()

        cart = CartFactory(
            selected_payment_backend=PSP_GATEWAY,
            currency=application.default_currency,
            application=application,
        )
        offer_a = ProductOfferFactory.create_for_application(
            application,
            status=PRODUCT_OFFER_STATUS_ACTIVE,
            stock=10,
            price=50,
            tax_rule__tax_rate__rate=0,
        )
        offer_b = ProductOfferFactory.create_for_application(
            application,
            status=PRODUCT_OFFER_STATUS_ACTIVE,
            stock=100,
            price=500,
            tax_rule__tax_rate__rate=0,
            merchant=offer_a.merchant,
        )
        Simple10EurosShippingFactory(application=application, merchant=offer_a.merchant)
        voucher = FixedVoucherFactory.create(
            application=application,
            discount_code="FIX600",
            reduction_value=15,
            currency=cart.currency,
            status=Discount.ACTIVE,
        )
        cart.actions.add_product_offer(offer_a, 1)
        cart.actions.add_product_offer(offer_b, 1)
        cart.add_discount_code(voucher.discount_code)

        cart.actions.shipping.create_choices()
        order = cart.actions.create_order()
        order.payment.actions.authorize()
        merchant_orders = order.merchant_orders.all()

        self.assertEqual(len(merchant_orders), 1)

        psp1 = PspGateway(
            application=application,
            external_id="mo1",
            gateway_type=PspGateway.PREPAYMENT_TYPE,
            merchant_order=merchant_orders[0],
        )
        psp1.actions.pay()

        self.assertEqual(order.payment.collected_amount, 545)
        self.assertEqual(order.payment.voucher_amount, 15)
        self.assertEqual(order.amount, 560)
        self.assertEqual(order.amount_to_collect, 545)
        self.assertEqual(order.voucher_amount, 15)
        self.assertEqual(merchant_orders[0].amount, 560)

    def test_payment_with_voucher_on_multiple_merchant_orders(self):
        application = ApplicationFactory()
        application.payment_settings.payment_backend = PSP_GATEWAY
        application.payment_settings.save()

        cart = CartFactory(
            selected_payment_backend=PSP_GATEWAY,
            currency=application.default_currency,
            application=application,
        )
        offer_a = ProductOfferFactory.create_for_application(
            application,
            status=PRODUCT_OFFER_STATUS_ACTIVE,
            stock=10,
            price=50,
            tax_rule__tax_rate__rate=0,
        )
        offer_b = ProductOfferFactory.create_for_application(
            application,
            status=PRODUCT_OFFER_STATUS_ACTIVE,
            stock=100,
            price=500,
            tax_rule__tax_rate__rate=0,
        )
        Simple10EurosShippingFactory(application=application, merchant=offer_a.merchant)
        Simple10EurosShippingFactory(application=application, merchant=offer_b.merchant)
        voucher = FixedVoucherFactory.create(
            application=application,
            discount_code="FIX600",
            reduction_value=15,
            currency=cart.currency,
            status=Discount.ACTIVE,
        )
        cart.actions.add_product_offer(offer_a, 1)
        cart.actions.add_product_offer(offer_b, 1)
        cart.add_discount_code(voucher.discount_code)

        cart.actions.shipping.create_choices()
        order = cart.actions.create_order()
        order.payment.actions.authorize()
        merchant_orders = order.merchant_orders.all()

        self.assertEqual(len(merchant_orders), 2)

        psp1 = PspGateway(
            application=application,
            external_id="mo1",
            gateway_type=PspGateway.PREPAYMENT_TYPE,
            merchant_order=merchant_orders[0],
        )
        psp1.actions.pay()

        psp2 = PspGateway(
            application=application,
            external_id="mo2",
            gateway_type=PspGateway.PREPAYMENT_TYPE,
            merchant_order=merchant_orders[1],
        )
        psp2.actions.pay()
        self.assertEqual(order.payment.collected_amount, 555)
        self.assertEqual(order.payment.voucher_amount, 15)
        self.assertEqual(order.amount, 570)
        self.assertEqual(order.amount_to_collect, 555)
        self.assertEqual(order.voucher_amount, 15)
        self.assertEqual(merchant_orders[0].amount + merchant_orders[1].amount, 570)
