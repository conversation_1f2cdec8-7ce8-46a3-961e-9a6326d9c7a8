# -*- coding: utf-8 -*-

from apps.cart.tests.test_cart_setup_mixin import CartTestSetupMixin
from apps.ice_applications.app_conf_settings import AutoCollectOnConfirm
from apps.orders.tests.orders_test_case_base import OrdersTestCaseBase
from apps.returns.tests.utils import RefundBuilder
from apps.testing.factories import (
    InvoiceCommissionTransactionFactory,
    MerchantOrderFactory,
    PrepaymentMOCustomerInvoiceLineFactory,
)
from django.test import override_settings
from ims.tests import BaseResourceTestCase
from invoicing.factories import CreditNoteFactory
from invoicing.models import CreditNote, CustomerInvoice, Decimal
from mock import patch
from payment_backends.psp_gateway.models import PspGateway


class PspGatewayApiMixinsTestCase(
    OrdersTestCaseBase, CartTestSetupMixin, BaseResourceTestCase
):
    def setUp(self):
        super().setUp()
        CartTestSetupMixin.set_up(self, workflow_v2=True)
        self.application.set_setting(AutoCollectOnConfirm, False)

        self.create_shipping2_rule(free_shipping=True)
        self.create_product_cart_item()
        self.order = self.cart.actions.create_order()
        self.merchant_order = self.order.merchant_orders.first()
        self.order_item = self.order.merchant_orders.first().order_items.first()
        self.order.payment.actions.authorize()
        self.merchant_order.actions.confirm()

        self.merchant_order.actions._generate_invoice_for_merchant_order()
        self.invoice = CustomerInvoice.objects.get(merchant_order=self.merchant_order)

        self.cart_2 = self.create_cart(force_create=True)
        self.create_product_cart_item(cart=self.cart_2)
        self.order_2 = self.cart_2.actions.create_order()
        self.order_2.payment.actions.authorize()
        self.merchant_order_2 = self.order_2.merchant_orders.first()
        self.merchant_order_2.actions.confirm()
        self.merchant_order_2.actions.process()
        self.refund_builder = RefundBuilder()
        self.refund = self.refund_builder.build(
            self.app_admin_user,
            merchant_order=self.merchant_order_2,
            order_items=self.merchant_order_2.order_items.all(),
            full=True,
        )

        self.transaction = (
            self.merchant_order.merchant.actions.get_or_create_pending_licence()
        )
        self.commission_transaction = InvoiceCommissionTransactionFactory(
            application=self.application, currency=self.application.default_currency
        )
        self.end_user = self.create_izberg_user("end_user")
        self.create_access(self.application, self.end_user, False)

    def setup_gateways(self):
        self.gateway_order = PspGateway.objects.create(
            application=self.application,
            external_id="0001",
            gateway_type=PspGateway.PREPAYMENT_TYPE,
            merchant_order=self.merchant_order,
        )
        self.gateway_order_2 = PspGateway.objects.create(
            application=self.application,
            external_id="0002",
            gateway_type=PspGateway.PREPAYMENT_TYPE,
            merchant_order=self.merchant_order_2,
        )
        self.gateway_invoice = PspGateway.objects.create(
            application=self.application,
            external_id="0003",
            gateway_type=PspGateway.TERM_PAYMENT_TYPE,
            invoice=self.invoice,
        )
        self.gateway_refund = PspGateway.objects.create(
            application=self.application,
            external_id="0004",
            gateway_type=PspGateway.REFUND_TYPE,
            refund=self.refund,
        )
        self.gateway_comm = PspGateway.objects.create(
            application=self.application,
            external_id="0005",
            gateway_type=PspGateway.COMMISSION_TYPE,
            transaction=self.commission_transaction,
        )

    def test_get_list(self):
        self.setup_gateways()
        resp = self.api_client.get(
            "/v1/psp/gateway/", authentication=self.get_token(self.app_admin_user)
        )
        self.assertHttpOK(resp)
        self.assertValidJSON(resp.content)
        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 5)

        resp = self.api_client.get(
            "/v1/psp/gateway/", authentication=self.get_token(self.end_user)
        )
        self.assertHttpOK(resp)
        self.assertValidJSON(resp.content)
        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 0)

        resp = self.api_client.get(
            "/v1/psp/gateway/?gateway_type={}".format(PspGateway.PREPAYMENT_TYPE),
            authentication=self.get_token(self.app_admin_user),
        )
        self.assertHttpOK(resp)
        self.assertValidJSON(resp.content)
        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 2)

    def test_get(self):
        self.setup_gateways()
        resp = self.api_client.get(
            "/v1/psp/gateway/1/", authentication=self.get_token(self.app_admin_user)
        )
        self.assertHttpNotFound(resp)

        resp = self.api_client.get(
            "/v1/psp/gateway/0001/", authentication=self.get_token(self.app_admin_user)
        )
        self.assertHttpOK(resp)
        self.assertValidJSON(resp.content)
        data = self.deserialize(resp)
        self.assertEqual(data["external_id"], "0001")

        resp = self.api_client.get(
            "/v1/psp/gateway/0001/", authentication=self.get_token(self.end_user)
        )
        self.assertHttpUnauthorized(resp)

    def test_create(self):
        post_data = {
            "application": "/v1/application/{}/".format(self.application.pk),
            "external_id": "test_create",
            "gateway_type": PspGateway.PREPAYMENT_TYPE,
            "merchant_order": "/v1/merchant_order/{}/".format(self.merchant_order.pk),
        }
        resp = self.api_client.post(
            "/v1/psp/gateway/",
            data=post_data,
            authentication=self.get_token(self.end_user),
        )
        self.assertHttpUnauthorized(resp)

        resp = self.api_client.post(
            "/v1/psp/gateway/",
            data=post_data,
            authentication=self.get_token(self.app_admin_user),
        )
        self.assertHttpCreated(resp)
        PspGateway.objects.get(external_id=post_data["external_id"])

    def test_pay_action(self):
        self.setup_gateways()
        post_data = {"amount": "50.6"}
        resp = self.api_client.post(
            "/v1/psp/gateway/0001/pay/",
            data={},
            authentication=self.get_token(self.end_user),
        )
        self.assertHttpUnauthorized(resp)

        resp = self.api_client.post(
            "/v1/psp/gateway/0001/pay/",
            data={},
            authentication=self.get_token(self.app_admin_user),
        )
        self.assertHttpOK(resp)
        self.order_2.payment.actions.collect_amount()
        resp = self.api_client.post(
            "/v1/psp/gateway/0004/pay/",
            data=post_data,
            authentication=self.get_token(self.app_admin_user),
        )
        self.assertHttpOK(resp)

    @override_settings(LANGUAGE_CODE="en", LANGUAGES=(("en", "English"),))
    @patch(
        "invoicing.models.abstract_invoice_models."
        "AbstractAccountingDocument.get_file_by_url",
        lambda _: None,
    )
    def test_pay_action_on_paid_invoice(self):
        # Init: We have an invoice where everything is cancelled, so there
        # is no need to pay anything.
        line = PrepaymentMOCustomerInvoiceLineFactory.create(
            quantity=1,
            unit_price=Decimal("10"),
            application=self.application,
            invoice__paid_amount=0,
        )
        line.invoice.emit()
        CreditNoteFactory.create(
            status=CreditNote.EMITTED,
            referred_invoice=line.invoice,
            are_taxes_included=True,
            total_amount=line.invoice.total_amount_with_taxes,
            amount_to_refund=Decimal("0"),
        )
        self.assertEqual(line.invoice.remaining_amount, Decimal("0"))

        # When I pay a new gateway for that invoice,
        PspGateway.objects.create(
            application=line.invoice.application,
            external_id="oops-1",
            gateway_type=PspGateway.TERM_PAYMENT_TYPE,
            invoice=line.invoice,
        )
        has_pmt_terms = "invoicing.models.CustomerInvoice.has_payment_terms"
        with patch(has_pmt_terms, lambda x: True):
            resp = self.api_client.post(
                "/v1/psp/gateway/oops-1/pay/",
                authentication=self.get_token(self.application.contact_user),
                data={
                    "amount": Decimal("1000"),
                },
            )

        # then I get an error saying i have nothing to pay
        self.assertHttpBadRequest(resp)
        # And it contains an info about amount
        data = self.deserialize(resp)
        self.assertEqual(
            data,
            {
                "errors": [
                    {
                        "field": "__all__",
                        "msg": [
                            'Unable to pay "1000", will exceed remaining '
                            'amount of "0.000000"!'
                        ],
                    }
                ]
            },
        )

    def test_gateway_pay_with_invalid_amount(self):
        self.setup_gateways()
        # Pay amount with more than 20 digits
        post_data = {"amount": "5" + 20 * "0" + ".61"}
        resp = self.api_client.post(
            "/v1/psp/gateway/0001/pay/",
            data=post_data,
            authentication=self.get_token(self.app_admin_user),
        )
        self.assertHttpBadRequest(resp)

        # Pay amount with more than two decimals
        post_data = {"amount": "50.611"}
        resp = self.api_client.post(
            "/v1/psp/gateway/0001/pay/",
            data=post_data,
            authentication=self.get_token(self.app_admin_user),
        )
        self.assertHttpBadRequest(resp)

        # Pay amount with negative number
        post_data = {"amount": "-50.61"}
        resp = self.api_client.post(
            "/v1/psp/gateway/0001/pay/",
            data=post_data,
            authentication=self.get_token(self.app_admin_user),
        )
        self.assertHttpBadRequest(resp)

        # Pay amount with valid float
        post_data = {"amount": 50.61}
        resp = self.api_client.post(
            "/v1/psp/gateway/0001/pay/",
            data=post_data,
            authentication=self.get_token(self.app_admin_user),
        )
        self.assertHttpOK(resp)


class PspGatewayApiTestCase(BaseResourceTestCase):
    def test_get_gateway_invalid_id(self):
        mo_1 = MerchantOrderFactory()
        PspGateway.objects.create(
            application=mo_1.application,
            external_id="0001",
            gateway_type=PspGateway.PREPAYMENT_TYPE,
            merchant_order=mo_1,
        )

        resp = self.api_client.get(
            "/v1/psp/gateway/0002/",
            authentication=self.get_operator_token(),
        )

        self.assertHttpNotFound(resp)
        self.assertEqual(resp.content, b"")

    def test_get_gateway_other_app(self):
        mo_1 = MerchantOrderFactory()
        PspGateway.objects.create(
            application=mo_1.application,
            external_id="0001",
            gateway_type=PspGateway.PREPAYMENT_TYPE,
            merchant_order=mo_1,
        )

        resp = self.api_client.get(
            "/v1/psp/gateway/0001/",
            authentication=self.get_operator_token(),
        )

        self.assertHttpNotFound(resp)
        self.assertEqual(resp.content, b"")

    def test_get_gateway_with_same_external_id_in_2_apps(self):
        mo_1 = MerchantOrderFactory()
        mo_2 = MerchantOrderFactory()
        gateway_1 = PspGateway.objects.create(
            application=mo_1.application,
            external_id="0001",
            gateway_type=PspGateway.PREPAYMENT_TYPE,
            merchant_order=mo_1,
        )
        PspGateway.objects.create(
            application=mo_2.application,
            external_id="0001",
            gateway_type=PspGateway.PREPAYMENT_TYPE,
            merchant_order=mo_2,
        )

        resp = self.api_client.get(
            "/v1/psp/gateway/0001/",
            authentication=self.get_operator_token(mo_1.application),
        )

        self.assertHttpOK(resp)
        self.assertEqual(self.deserialize(resp)["id"], str(gateway_1.id))

    def test_get_gateway_with_same_external_id_in_2_apps_as_izb_staff(self):
        mo_1 = MerchantOrderFactory()
        mo_2 = MerchantOrderFactory()
        PspGateway.objects.create(
            application=mo_1.application,
            external_id="0001",
            gateway_type=PspGateway.PREPAYMENT_TYPE,
            merchant_order=mo_1,
        )
        PspGateway.objects.create(
            application=mo_2.application,
            external_id="0001",
            gateway_type=PspGateway.PREPAYMENT_TYPE,
            merchant_order=mo_2,
        )

        resp = self.api_client.get(
            "/v1/psp/gateway/0001/", authentication=self.get_staff_token()
        )

        self.assertHttpUnauthorized(resp)
        self.assertEqual(
            self.deserialize(resp),
            {"errors": ["This resource is only accessible using an operator token"]},
        )

    def test_get_list_filters_on_app(self):
        mo_1 = MerchantOrderFactory()
        mo_2 = MerchantOrderFactory()
        gtw = PspGateway.objects.create(
            application=mo_1.application,
            external_id="0001",
            gateway_type=PspGateway.PREPAYMENT_TYPE,
            merchant_order=mo_1,
        )
        PspGateway.objects.create(
            application=mo_2.application,
            external_id="0002",
            gateway_type=PspGateway.PREPAYMENT_TYPE,
            merchant_order=mo_2,
        )

        resp = self.api_client.get(
            "/v1/psp/gateway/",
            authentication=self.get_operator_token(mo_1.application),
        )

        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 1)
        self.assertEqual(data["objects"][0]["external_id"], gtw.external_id)
