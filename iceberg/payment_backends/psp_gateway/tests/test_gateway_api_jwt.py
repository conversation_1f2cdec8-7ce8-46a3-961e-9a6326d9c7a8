# -*- coding: utf-8 -*-
from datetime import datetime, timed<PERSON>ta
from unittest.mock import patch

from apps.testing.factories import (
    ApplicationFactory,
    InvoiceCommissionTransactionFactory,
    MerchantOrderFactory,
    MerchantOrderRefundFactory,
    OrderItemPaymentTermFactory,
    ProductOfferOrderItemFactory,
)
from apps.transactions.models import Transaction
from django.test.utils import override_settings
from ims.tests import BaseResourceTestCase, BaseResourceTransactionTestCase
from ims.tests.jwt_tokens import GLOBAL_TOKENS, OMS_TOKENS
from invoicing.factories import CustomerInvoiceFactory, CustomerInvoiceLineFactory
from payment_backends.psp_gateway.exceptions import (
    DuplicatedExternalId,
    DuplicatedInvoice,
    DuplicatedMerchantOrder,
    DuplicatedRefund,
    DuplicatedTransaction,
)
from payment_backends.psp_gateway.models import PspGateway
from pytz import timezone
from reference.status import ORDER_ITEM_STATUS_CONFIRMED, PAYMENT_STATUS_AUTH_SUCCESS
from reference.transaction_codes import TRANSACTION_OPERATOR_SALE, TRANSACTION_SALE


class PspGatewayApiJwtTestCase(BaseResourceTestCase):
    def create_gateway(
        self,
        application=None,
        external_id="0001",
        gateway_type=PspGateway.PREPAYMENT_TYPE,
        **kwargs,
    ):
        return PspGateway.objects.create(
            application=application or ApplicationFactory(),
            external_id=external_id,
            gateway_type=gateway_type,
            **kwargs,
        )

    def test_get_list_as_operator(self):
        mo = MerchantOrderFactory(application__id=1)
        mo_2 = MerchantOrderFactory()
        gtw_1 = self.create_gateway(
            external_id="1", application=mo.application, merchant_order=mo
        )
        self.create_gateway(
            external_id="2",
            application=mo_2.application,
            merchant_order=mo_2,
        )

        resp = self.api_client.get(
            "/v1/psp/gateway/",
            authentication=("Bearer " + OMS_TOKENS["APPLICATION_1_READ"]["token"]),
        )

        self.assertHttpOK(resp)
        data = self.deserialize(resp)
        self.assertEqual(data["meta"]["total_count"], 1)
        self.assertEqual(data["objects"][0]["external_id"], gtw_1.external_id)

    def test_get_detail_invalid_id_as_operator(self):
        mo = MerchantOrderFactory(application__id=1)
        self.create_gateway(
            external_id="1", application=mo.application, merchant_order=mo
        )

        resp = self.api_client.get(
            "/v1/psp/gateway/2/",
            authentication=("Bearer " + OMS_TOKENS["APPLICATION_1_READ"]["token"]),
        )

        self.assertHttpNotFound(resp)
        self.assertEqual(resp.content, b"")

    def test_get_detail_other_app_as_operator(self):
        ApplicationFactory(id=1)
        mo = MerchantOrderFactory(application__id=2)
        gtw_1 = self.create_gateway(
            external_id="1", application=mo.application, merchant_order=mo
        )

        resp = self.api_client.get(
            f"/v1/psp/gateway/{gtw_1.external_id}/",
            authentication=("Bearer " + OMS_TOKENS["APPLICATION_1_READ"]["token"]),
        )

        self.assertHttpNotFound(resp)
        self.assertEqual(resp.content, b"")

    def test_get_detail_as_operator(self):
        mo = MerchantOrderFactory(application__id=1)
        gtw_1 = self.create_gateway(
            external_id="1", application=mo.application, merchant_order=mo
        )

        resp = self.api_client.get(
            f"/v1/psp/gateway/{gtw_1.external_id}/",
            authentication=("Bearer " + OMS_TOKENS["APPLICATION_1_READ"]["token"]),
        )

        self.assertHttpOK(resp)
        self.assertEqual(self.deserialize(resp)["external_id"], gtw_1.external_id)

    def test_put_as_operator(self):
        mo = MerchantOrderFactory(application__id=1)
        gtw_1 = self.create_gateway(
            external_id="1", application=mo.application, merchant_order=mo
        )
        extra_data = {"toto": "tata"}
        resp = self.api_client.put(
            f"/v1/psp/gateway/{gtw_1.external_id}/",
            data={"extra_data": extra_data},
            authentication=("Bearer " + OMS_TOKENS["APPLICATION_1_WRITE"]["token"]),
        )

        self.assertHttpAccepted(resp)
        self.assertEqual(self.deserialize(resp)["extra_data"], extra_data)

    def test_patch_as_operator(self):
        mo = MerchantOrderFactory(application__id=1)
        gtw_1 = self.create_gateway(
            external_id="1", application=mo.application, merchant_order=mo
        )
        extra_data = {"toto": "tata"}
        resp = self.api_client.patch(
            f"/v1/psp/gateway/{gtw_1.external_id}/",
            data={"extra_data": extra_data},
            authentication=("Bearer " + OMS_TOKENS["APPLICATION_1_WRITE"]["token"]),
        )

        self.assertHttpAccepted(resp)
        self.assertEqual(self.deserialize(resp)["extra_data"], extra_data)

    def test_create_as_operator(self):
        mo = MerchantOrderFactory(application__id=1)
        ext_id = "test_create"

        resp = self.api_client.post(
            "/v1/psp/gateway/",
            data={
                "application": f"/v1/application/{mo.application.id}/",
                "external_id": ext_id,
                "gateway_type": PspGateway.PREPAYMENT_TYPE,
                "merchant_order": f"/v1/merchant_order/{mo.id}/",
            },
            authentication=("Bearer " + OMS_TOKENS["APPLICATION_1_WRITE"]["token"]),
        )

        self.assertHttpCreated(resp)
        try:
            PspGateway.objects.get(external_id=ext_id)
        except PspGateway.DoesNotExist:  # noqa
            self.fail("Should not raise PspGateway.DoesNotExist")

    def test_create_with_existing_external_id(self):
        mo = MerchantOrderFactory(application__id=1)
        mo_2 = MerchantOrderFactory(application=mo.application)
        ext_id = "test_create"
        psp_gateway = self.create_gateway(
            external_id=ext_id,
            application=mo.application,
            merchant_order=mo,
        )

        resp = self.api_client.post(
            "/v1/psp/gateway/",
            data={
                "application": f"/v1/application/{mo_2.application_id}/",
                "external_id": ext_id,
                "gateway_type": PspGateway.PREPAYMENT_TYPE,
                "merchant_order": f"/v1/merchant_order/{mo_2.id}/",
            },
            authentication=("Bearer " + OMS_TOKENS["APPLICATION_1_WRITE"]["token"]),
        )

        self.assertHttpBadRequest(resp)
        self.assertEqual(
            self.deserialize(resp),
            {
                "error_code": "PSP_GATEWAY.DUPLICATED_EXTERNAL_ID",
                "error_context": {
                    "application_id": 1,
                    "external_id": "test_create",
                    "psp_gateway_id": str(psp_gateway.id),
                },
                "error_description": DuplicatedExternalId.error_description,
            },
        )

    def test_create_with_same_merchant_order(self):
        mo = MerchantOrderFactory(application__id=1)
        ext_id = "test_create"
        psp_gateway = self.create_gateway(
            external_id="other_ext_id",
            application=mo.application,
            merchant_order=mo,
        )

        resp = self.api_client.post(
            "/v1/psp/gateway/",
            data={
                "application": f"/v1/application/{mo.application_id}/",
                "external_id": ext_id,
                "gateway_type": PspGateway.PREPAYMENT_TYPE,
                "merchant_order": f"/v1/merchant_order/{mo.id}/",
            },
            authentication=("Bearer " + OMS_TOKENS["APPLICATION_1_WRITE"]["token"]),
        )

        self.assertHttpBadRequest(resp)
        self.assertEqual(
            self.deserialize(resp),
            {
                "error_code": "PSP_GATEWAY.DUPLICATED_MERCHANT_ORDER",
                "error_context": {
                    "application_id": mo.application_id,
                    "gateway_type": "PREPAYMENT",
                    "merchant_order_id": mo.id,
                    "psp_gateway_id": str(psp_gateway.id),
                },
                "error_description": DuplicatedMerchantOrder.error_description,
            },
        )

    def test_duplicated_gateway_type_invoice(self):
        mo = MerchantOrderFactory(application__id=1)
        customer_invoice = CustomerInvoiceFactory(merchant_order=mo)
        psp_gateway = self.create_gateway(
            external_id="test",
            application=customer_invoice.application,
            gateway_type=PspGateway.TERM_PAYMENT_TYPE,
            invoice=customer_invoice,
        )

        resp = self.api_client.post(
            "/v1/psp/gateway/",
            data={
                "application": (f"/v1/application/{customer_invoice.application.id}/"),
                "gateway_type": PspGateway.TERM_PAYMENT_TYPE,
                "invoice": f"/v1/customer_invoice/{customer_invoice.id}/",
            },
            authentication=("Bearer " + OMS_TOKENS["APPLICATION_1_WRITE"]["token"]),
        )
        self.assertHttpBadRequest(resp)

        data = self.deserialize(resp)
        self.assertDictEqual(
            data,
            {
                "error_description": DuplicatedInvoice.error_description,
                "error_code": "PSP_GATEWAY.DUPLICATED_INVOICE",
                "error_context": {
                    "psp_gateway_id": str(psp_gateway.id),
                    "gateway_type": "TERM_PAYMENT",
                    "application_id": customer_invoice.application.id,
                    "invoice_id": customer_invoice.id,
                },
            },
        )

    def test_duplicated_gateway_type_refund(self):
        refund = MerchantOrderRefundFactory(application__id=1)
        psp_gateway = self.create_gateway(
            external_id="test",
            application=refund.application,
            gateway_type=PspGateway.REFUND_TYPE,
            refund=refund,
        )

        resp = self.api_client.post(
            "/v1/psp/gateway/",
            data={
                "gateway_type": PspGateway.REFUND_TYPE,
                "external_id": "test_bis",
                "refund": f"/v1/refund/{refund.id}/",
            },
            authentication=("Bearer " + OMS_TOKENS["APPLICATION_1_WRITE"]["token"]),
        )

        self.assertHttpBadRequest(resp)
        self.assertDictEqual(
            self.deserialize(resp),
            {
                "error_description": DuplicatedRefund.error_description,
                "error_code": "PSP_GATEWAY.DUPLICATED_REFUND",
                "error_context": {
                    "psp_gateway_id": str(psp_gateway.id),
                    "gateway_type": "REFUND",
                    "application_id": 1,
                    "refund_id": refund.id,
                },
            },
        )

    def test_duplicated_gateway_type_transaction(self):
        tx = InvoiceCommissionTransactionFactory(application__id=1)
        psp_gateway = self.create_gateway(
            external_id="test",
            application=tx.application,
            gateway_type=PspGateway.COMMISSION_TYPE,
            transaction=tx,
        )

        resp = self.api_client.post(
            "/v1/psp/gateway/",
            data={
                "application": f"/v1/application/{tx.application.id}/",
                "external_id": "test_bis",
                "gateway_type": PspGateway.COMMISSION_TYPE,
                "transaction": f"/v1/transaction/{tx.id}/",
            },
            authentication=("Bearer " + OMS_TOKENS["APPLICATION_1_WRITE"]["token"]),
        )
        self.assertHttpBadRequest(resp)

        data = self.deserialize(resp)
        self.assertDictEqual(
            data,
            {
                "error_description": DuplicatedTransaction.error_description,
                "error_code": "PSP_GATEWAY.DUPLICATED_TRANSACTION",
                "error_context": {
                    "psp_gateway_id": str(psp_gateway.id),
                    "application_id": tx.application.id,
                    "gateway_type": "COMMISSION",
                    "transaction_id": tx.id,
                },
            },
        )

    def test_pay_merchant_order_as_operator(self):
        oi = ProductOfferOrderItemFactory(
            application__id=1,
            recompute_amounts=True,
            merchant_order__order__auto_create_payment=True,
        )
        gtw_1 = self.create_gateway(
            application=oi.merchant_order.application, merchant_order=oi.merchant_order
        )

        resp = self.api_client.post(
            f"/v1/psp/gateway/{gtw_1.external_id}/pay/",
            data={},
            authentication=("Bearer " + OMS_TOKENS["APPLICATION_1_WRITE"]["token"]),
        )

        self.assertHttpOK(resp)
        oi.merchant_order.refresh_from_db()
        self.assertEqual(oi.merchant_order.status, PAYMENT_STATUS_AUTH_SUCCESS)

    def test_pay_merchant_order_as_operator_from_locked_order(self):
        oi = ProductOfferOrderItemFactory(
            application__id=1,
            recompute_amounts=True,
            merchant_order__order__auto_create_payment=True,
        )
        gtw_1 = self.create_gateway(
            application=oi.merchant_order.application, merchant_order=oi.merchant_order
        )
        with oi.merchant_order.order.actions.locker():
            resp = self.api_client.post(
                f"/v1/psp/gateway/{gtw_1.external_id}/pay/",
                data={},
                authentication=("Bearer " + OMS_TOKENS["APPLICATION_1_WRITE"]["token"]),
            )

        self.assertHttpBadRequest(resp)
        data = self.deserialize(resp)
        self.assertEqual(
            data["errors"][0]["msg"], [f"Order {oi.merchant_order.order.id} is locked"]
        )

    def test_pay_gateway_with_same_external_id_in_2_apps_as_operator(self):
        oi = ProductOfferOrderItemFactory(
            application__id=1,
            recompute_amounts=True,
            merchant_order__order__auto_create_payment=True,
        )
        gtw_1 = self.create_gateway(
            external_id="0001",
            application=oi.merchant_order.application,
            merchant_order=oi.merchant_order,
        )
        mo_2 = MerchantOrderFactory()
        self.create_gateway(
            external_id="0001", application=mo_2.application, merchant_order=mo_2
        )
        resp = self.api_client.post(
            f"/v1/psp/gateway/{gtw_1.external_id}/pay/",
            data={},
            authentication=("Bearer " + OMS_TOKENS["APPLICATION_1_WRITE"]["token"]),
        )

        self.assertHttpOK(resp)
        oi.merchant_order.refresh_from_db()
        self.assertEqual(oi.merchant_order.status, PAYMENT_STATUS_AUTH_SUCCESS)

    def test_get_gateway_with_same_external_id_in_2_apps_as_operator(self):
        mo_1 = MerchantOrderFactory(application__id=1)
        mo_2 = MerchantOrderFactory()
        gateway_1 = PspGateway.objects.create(
            application=mo_1.application,
            external_id="0001",
            gateway_type=PspGateway.PREPAYMENT_TYPE,
            merchant_order=mo_1,
        )
        PspGateway.objects.create(
            application=mo_2.application,
            external_id="0001",
            gateway_type=PspGateway.PREPAYMENT_TYPE,
            merchant_order=mo_2,
        )

        resp = self.api_client.get(
            "/v1/psp/gateway/0001/",
            authentication=("Bearer " + OMS_TOKENS["APPLICATION_1_READ"]["token"]),
        )

        self.assertHttpOK(resp)
        self.assertEqual(self.deserialize(resp)["id"], str(gateway_1.id))

    def test_get_gateway_with_same_external_id_in_2_apps_as_internal(self):
        mo_1 = MerchantOrderFactory()
        mo_2 = MerchantOrderFactory()
        PspGateway.objects.create(
            application=mo_1.application,
            external_id="0001",
            gateway_type=PspGateway.PREPAYMENT_TYPE,
            merchant_order=mo_1,
        )
        PspGateway.objects.create(
            application=mo_2.application,
            external_id="0001",
            gateway_type=PspGateway.PREPAYMENT_TYPE,
            merchant_order=mo_2,
        )

        resp = self.api_client.get(
            "/v1/psp/gateway/0001/",
            authentication="Bearer {}".format(
                GLOBAL_TOKENS["INTERNAL_M2M_READ"]["token"]
            ),
        )

        self.assertHttpForbidden(resp)
        self.assertEqual(
            self.deserialize(resp),
            {
                "error": "insufficient_scope",
                "error_description": "Invalid owner type for this action",
                "error_code": "OAUTH.INVALID_OWNER_TYPE",
                "error_context": {
                    "action": "read_detail",
                    "owner_type": "internal",
                    "accepted_owner_types": ["operator"],
                },
            },
        )

    def test_pay_merchant_order_with_settlement_date(self):
        oi = ProductOfferOrderItemFactory(
            application__id=1,
            recompute_amounts=True,
            merchant_order__order__auto_create_payment=True,
        )
        mo = oi.merchant_order
        mo.order.payment.authorize()

        gtw_1 = self.create_gateway(application=mo.application, merchant_order=mo)

        settlement_date = datetime.today().replace(tzinfo=timezone("UTC")) - timedelta(
            days=1
        )

        resp = self.api_client.post(
            f"/v1/psp/gateway/{gtw_1.external_id}/pay/",
            data={"settlement_date": f"{settlement_date.isoformat()}"},
            authentication=("Bearer " + OMS_TOKENS["APPLICATION_1_WRITE"]["token"]),
        )

        self.assertHttpOK(resp)

        tx = Transaction.objects.get(
            merchant_order=mo, transaction_type=TRANSACTION_SALE
        )
        self.assertEqual(tx.settlement_date, settlement_date)

    def test_pay_merchant_order_without_settlement_date(self):
        oi = ProductOfferOrderItemFactory(
            application__id=1,
            recompute_amounts=True,
            merchant_order__order__auto_create_payment=True,
        )
        mo = oi.merchant_order

        gtw_1 = self.create_gateway(application=mo.application, merchant_order=mo)

        resp = self.api_client.post(
            f"/v1/psp/gateway/{gtw_1.external_id}/pay/",
            authentication=("Bearer " + OMS_TOKENS["APPLICATION_1_WRITE"]["token"]),
        )

        self.assertHttpOK(resp)

        tx = Transaction.objects.get(
            merchant_order=mo, transaction_type=TRANSACTION_SALE
        )
        self.assertEqual(tx.confirmation_date, None)
        self.assertEqual(tx.settlement_date, None)
        self.assertNotEqual(tx.settlement_notification_date, None)

    def test_pay_merchant_order_with_empty_settlement_date(self):
        oi = ProductOfferOrderItemFactory(
            application__id=1,
            recompute_amounts=True,
            merchant_order__order__auto_create_payment=True,
        )
        mo = oi.merchant_order
        mo.order.payment.authorize()

        gtw_1 = self.create_gateway(application=mo.application, merchant_order=mo)

        resp = self.api_client.post(
            f"/v1/psp/gateway/{gtw_1.external_id}/pay/",
            data={"settlement_date": ""},
            authentication=("Bearer " + OMS_TOKENS["APPLICATION_1_WRITE"]["token"]),
        )

        self.assertHttpBadRequest(resp)
        self.assertDictEqual(
            self.deserialize(resp),
            {
                "errors": [
                    {
                        "field": "settlement_date",
                        "msg": ["Datetime expected (ISO format)"],
                    }
                ]
            },
        )

    def test_pay_merchant_order_with_invalid_settlement_date(self):
        oi = ProductOfferOrderItemFactory(
            application__id=1,
            recompute_amounts=True,
            merchant_order__order__auto_create_payment=True,
        )
        mo = oi.merchant_order
        mo.order.payment.authorize()

        gtw_1 = self.create_gateway(application=mo.application, merchant_order=mo)

        resp = self.api_client.post(
            f"/v1/psp/gateway/{gtw_1.external_id}/pay/",
            data={"settlement_date": "2022-01-02"},
            authentication=("Bearer " + OMS_TOKENS["APPLICATION_1_WRITE"]["token"]),
        )

        # For now, for legacy reasons, we tolerate borken dates
        self.assertHttpOK(resp)

        # TODO: Switch to when `read_datetime_in_param` is fixed
        # self.assertHttpBadRequest(resp)
        # self.assertDictEqual(
        #     self.deserialize(resp),
        #     {
        #         'errors': [{
        #             'field': 'settlement_date',
        #             'msg': ['Datetime expected (ISO format)']
        #         }]
        #     },
        # )

    @patch(
        "apps.orders.actions.merchant_order_actions.manager."
        "MerchantOrderActionsManager._operator_must_collect_vat",
        lambda _: True,
    )
    def test_pay_merchant_order_with_tax_collected_by_operator_with_settlement_date(
        self,
    ):
        oi = ProductOfferOrderItemFactory(
            application__id=1,
            recompute_amounts=True,
            merchant_order__order__auto_create_payment=True,
        )
        mo = oi.merchant_order
        mo.order.payment.authorize()

        gtw_1 = self.create_gateway(application=mo.application, merchant_order=mo)

        settlement_date = datetime.today().replace(tzinfo=timezone("UTC")) - timedelta(
            days=1
        )

        resp = self.api_client.post(
            f"/v1/psp/gateway/{gtw_1.external_id}/pay/",
            data={"settlement_date": f"{settlement_date.isoformat()}"},
            authentication=("Bearer " + OMS_TOKENS["APPLICATION_1_WRITE"]["token"]),
        )

        self.assertHttpOK(resp)

        self.assertEqual(2, len(Transaction.objects.filter(merchant_order=mo)))

        tx = Transaction.objects.get(
            merchant_order=mo, transaction_type=TRANSACTION_OPERATOR_SALE
        )
        self.assertEqual(tx.settlement_date, settlement_date)

    def test_invoice_with_settlement_date(self):
        app = ApplicationFactory(id=1)
        oipt = OrderItemPaymentTermFactory(
            application=app,
            order_item__invoiceable_quantity=1,
            order_item__quantity=1,
            order_item__status=ORDER_ITEM_STATUS_CONFIRMED,
        )
        invoice_line = CustomerInvoiceLineFactory(
            order_item=oipt.order_item,
            invoice__merchant_order=oipt.order_item.merchant_order,
            invoice__id_number="lol",
        )
        invoice = invoice_line.invoice
        invoice.issuer.handle_invoice_upload = True
        invoice.save()
        invoice.emit()

        psp_gateway = self.create_gateway(
            external_id="test",
            application=invoice.application,
            gateway_type=PspGateway.TERM_PAYMENT_TYPE,
            invoice=invoice,
        )

        settlement_date = datetime.today().replace(tzinfo=timezone("UTC")) - timedelta(
            days=1
        )

        resp = self.api_client.post(
            f"/v1/psp/gateway/{psp_gateway.external_id}/pay/",
            data={
                "amount": str(invoice.total_amount_with_taxes),
                "settlement_date": f"{settlement_date.isoformat()}",
            },
            authentication=("Bearer " + OMS_TOKENS["APPLICATION_1_WRITE"]["token"]),
        )

        self.assertHttpOK(resp)
        tx = Transaction.objects.get(
            customer_invoice_id=invoice.id, transaction_type=TRANSACTION_SALE
        )
        self.assertEqual(tx.settlement_date, settlement_date)


class PspGatewayApiJwtTransactionTestCase(BaseResourceTransactionTestCase):
    def create_gateway(
        self,
        application=None,
        external_id="0001",
        gateway_type=PspGateway.PREPAYMENT_TYPE,
        **kwargs,
    ):
        return PspGateway.objects.create(
            application=application or ApplicationFactory(),
            external_id=external_id,
            gateway_type=gateway_type,
            **kwargs,
        )

    @override_settings(SYNCHRONOUS_TRANSACTION_CREATION_ON_COLLECT=False)
    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    @override_settings(CELERY_TASK_EAGER_PROPAGATES_EXCEPTIONS=True)
    def test_invoice_with_settlement_date_async_task(self):
        app = ApplicationFactory(id=1)
        oipt = OrderItemPaymentTermFactory(
            application=app,
            order_item__invoiceable_quantity=1,
            order_item__quantity=1,
            order_item__status=ORDER_ITEM_STATUS_CONFIRMED,
        )
        invoice_line = CustomerInvoiceLineFactory(
            order_item=oipt.order_item,
            invoice__merchant_order=oipt.order_item.merchant_order,
            invoice__id_number="lol",
        )
        invoice = invoice_line.invoice
        invoice.issuer.handle_invoice_upload = True
        invoice.save()
        invoice.emit()

        psp_gateway = self.create_gateway(
            external_id="test",
            application=invoice.application,
            gateway_type=PspGateway.TERM_PAYMENT_TYPE,
            invoice=invoice,
        )

        settlement_date = datetime.today().replace(tzinfo=timezone("UTC")) - timedelta(
            days=1
        )

        resp = self.api_client.post(
            f"/v1/psp/gateway/{psp_gateway.external_id}/pay/",
            data={
                "amount": str(invoice.total_amount_with_taxes),
                "settlement_date": f"{settlement_date}",
            },
            authentication=("Bearer " + OMS_TOKENS["APPLICATION_1_WRITE"]["token"]),
        )

        self.assertHttpOK(resp)
        tx = Transaction.objects.get(
            customer_invoice_id=invoice.id, transaction_type=TRANSACTION_SALE
        )
        self.assertEqual(tx.settlement_date, settlement_date)
