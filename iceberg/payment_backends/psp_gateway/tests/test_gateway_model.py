# -*- coding: utf-8 -*-

from apps.cart.tests.test_cart_setup_mixin import CartTestSetupMixin
from apps.orders.tests.orders_test_case_base import OrdersTestCaseBase
from apps.testing.factories import (
    InvoiceCommissionRefundTransactionFactory,
    InvoiceCommissionTransactionFactory,
    LicenseTransactionFactory,
    MarketingOperationTransactionFactory,
    MerchantCashoutTransactionFactory,
    MerchantOrderRefundFactory,
    ProductOfferOrderItemFactory,
    RefundPspGatewayFactory,
    StoreAdjustmentTransactionFactory,
)
from apps.transactions.models import Transaction
from django.core.exceptions import ValidationError
from ims.tests import BaseTestCase
from invoicing.models import CustomerInvoice
from payment_backends.psp_gateway.exceptions import (
    DuplicatedExternalId,
    DuplicatedInvoice,
    DuplicatedMerchantOrder,
    DuplicatedRefund,
    DuplicatedTransaction,
    WrongTransactionType,
)
from payment_backends.psp_gateway.models import PspGateway


class PspGatewayModelTestCase(OrdersTestCaseBase, CartTestSetupMixin):
    fixtures = OrdersTestCaseBase.fixtures + ["site_fixtures", "tax_fixtures"]

    def setUp(self):
        super(PspGatewayModelTestCase, self).setUp()
        CartTestSetupMixin.set_up(self, workflow_v2=True)

        self.order_item = ProductOfferOrderItemFactory.create(
            user__email="<EMAIL>",
            merchant_order__order__auto_create_payment=True,
        )
        self.merchant_order = self.order_item.merchant_order
        self.merchant_order.order.payment.actions.authorize()
        self.merchant_order.actions.confirm()

        self.merchant_order.actions._generate_invoice_for_merchant_order()
        self.invoice = CustomerInvoice.objects.get(merchant_order=self.merchant_order)
        self.merchant_order_2 = ProductOfferOrderItemFactory.create(
            user__email="<EMAIL>",
        ).merchant_order
        self.order_2 = self.merchant_order.order
        self.refund = MerchantOrderRefundFactory(status="complete", refund_type=4)

        self.sale_transaction = Transaction.objects.filter_sale().get()

        self.commission_transaction = InvoiceCommissionTransactionFactory(
            application=self.application, currency=self.application.default_currency
        )
        self.commission_refund_transaction = InvoiceCommissionRefundTransactionFactory(
            application=self.application, currency=self.application.default_currency
        )
        self.payout_transaction = MerchantCashoutTransactionFactory(
            application=self.application, currency=self.application.default_currency
        )
        self.store_adjustment_transaction = StoreAdjustmentTransactionFactory(
            application=self.application, currency=self.application.default_currency
        )
        self.license_transaction = LicenseTransactionFactory(
            application=self.application, currency=self.application.default_currency
        )
        self.marketing_operation_transaction = MarketingOperationTransactionFactory(
            application=self.application, currency=self.application.default_currency
        )

    def test_config_type_relations(self):
        for choice in PspGateway.PSP_GATEWAY_TYPE_CHOICES:
            self.assertIn(choice[0], PspGateway.TYPE_MAPPING)

    def test_unique_external_id(self):
        """
        external_id must remains uniques for an application.
        """
        PspGateway.objects.create(
            application=self.application,
            external_id="test_unique_external_id",
            gateway_type=PspGateway.PREPAYMENT_TYPE,
            merchant_order=self.merchant_order,
        )
        with self.assertRaises(DuplicatedExternalId):
            PspGateway.objects.create(
                application=self.application,
                external_id="test_unique_external_id",
                gateway_type=PspGateway.PREPAYMENT_TYPE,
                merchant_order=self.merchant_order_2,
            )
        app_2 = self.create_application()
        PspGateway.objects.create(
            application=app_2,
            external_id="test_unique_external_id",
            gateway_type=PspGateway.PREPAYMENT_TYPE,
            merchant_order=self.merchant_order_2,
        )

    def test_prepayment_mandatory_field(self):
        """
        merchant_order is mandatory for PREPAYMENT_TYPE.
        There must be a unique relation between
        gateway_type and merchant_order
        """
        with self.assertRaises(ValidationError):
            PspGateway.objects.create(
                application=self.application,
                external_id="test_prepayment_mandatory_field",
                gateway_type=PspGateway.PREPAYMENT_TYPE,
            )
        PspGateway.objects.create(
            application=self.application,
            external_id="test_prepayment_mandatory_field",
            gateway_type=PspGateway.PREPAYMENT_TYPE,
            merchant_order=self.merchant_order,
        )
        with self.assertRaises(DuplicatedMerchantOrder):
            PspGateway.objects.create(
                application=self.application,
                external_id="test_prepayment_mandatory_field_2",
                gateway_type=PspGateway.PREPAYMENT_TYPE,
                merchant_order=self.merchant_order,
            )

    def test_term_payment_mandatory_field(self):
        """
        invoice is mandatory for TERM_PAYMENT_TYPE.
        There must be a unique relation between
        gateway_type and invoice
        """
        with self.assertRaises(ValidationError):
            PspGateway.objects.create(
                application=self.application,
                external_id="test_term_payment_mandatory_field",
                gateway_type=PspGateway.TERM_PAYMENT_TYPE,
            )
        PspGateway.objects.create(
            application=self.application,
            external_id="test_term_payment_mandatory_field",
            gateway_type=PspGateway.TERM_PAYMENT_TYPE,
            invoice=self.invoice,
        )
        with self.assertRaises(DuplicatedInvoice):
            PspGateway.objects.create(
                application=self.application,
                external_id="test_term_payment_mandatory_field_2",
                gateway_type=PspGateway.TERM_PAYMENT_TYPE,
                invoice=self.invoice,
            )

    def test_refund_mandatory_field(self):
        """
        refund is mandatory for REFUND_TYPE.
        There must be a unique relation between
        gateway_type and refund
        """
        with self.assertRaises(ValidationError):
            PspGateway.objects.create(
                application=self.application,
                external_id="test_refund_mandatory_field",
                gateway_type=PspGateway.REFUND_TYPE,
            )
        PspGateway.objects.create(
            application=self.application,
            external_id="test_refund_mandatory_field",
            gateway_type=PspGateway.REFUND_TYPE,
            refund=self.refund,
        )
        with self.assertRaises(DuplicatedRefund):
            PspGateway.objects.create(
                application=self.application,
                external_id="test_refund_mandatory_field_2",
                gateway_type=PspGateway.REFUND_TYPE,
                refund=self.refund,
            )

    def test_commission_mandatory_field(self):
        """
        transaction is mandatory for COMMISSION_TYPE.
        There must be a unique relation between
        gateway_type and transaction
        """
        with self.assertRaises(ValidationError):
            PspGateway.objects.create(
                application=self.application,
                external_id="test_commission_mandatory_field",
                gateway_type=PspGateway.COMMISSION_TYPE,
            )
        with self.assertRaises(WrongTransactionType):
            PspGateway.objects.create(
                application=self.application,
                external_id="test_commission_mandatory_field",
                gateway_type=PspGateway.COMMISSION_TYPE,
                transaction=self.sale_transaction,
            )
        PspGateway.objects.create(
            application=self.application,
            external_id="test_commission_mandatory_field",
            gateway_type=PspGateway.COMMISSION_TYPE,
            transaction=self.commission_transaction,
        )
        with self.assertRaises(DuplicatedTransaction):
            PspGateway.objects.create(
                application=self.application,
                external_id="test_commission_mandatory_field_2",
                gateway_type=PspGateway.COMMISSION_TYPE,
                transaction=self.commission_transaction,
            )

    def test_commission_refund_mandatory_field(self):
        """
        transaction is mandatory for COMMISSION_REFUND_TYPE.
        There must be a unique relation between
        gateway_type and transaction
        """
        with self.assertRaises(ValidationError):
            PspGateway.objects.create(
                application=self.application,
                external_id="test_commission_refund_mandatory_field",
                gateway_type=PspGateway.COMMISSION_REFUND_TYPE,
            )
        with self.assertRaises(WrongTransactionType):
            PspGateway.objects.create(
                application=self.application,
                external_id="test_commission_refund_mandatory_field",
                gateway_type=PspGateway.COMMISSION_REFUND_TYPE,
                transaction=self.sale_transaction,
            )
        PspGateway.objects.create(
            application=self.application,
            external_id="test_commission_refund_mandatory_field",
            gateway_type=PspGateway.COMMISSION_REFUND_TYPE,
            transaction=self.commission_refund_transaction,
        )
        with self.assertRaises(DuplicatedTransaction):
            PspGateway.objects.create(
                application=self.application,
                external_id="test_commission_refund_mandatory_field_2",
                gateway_type=PspGateway.COMMISSION_REFUND_TYPE,
                transaction=self.commission_refund_transaction,
            )

    def test_payout_mandatory_field(self):
        """
        transaction is mandatory for PAYOUT_TRANSACTION_TYPE.
        There must be a unique relation between
        gateway_type and transaction
        """
        with self.assertRaises(ValidationError):
            PspGateway.objects.create(
                application=self.application,
                external_id="test_payout_mandatory_field",
                gateway_type=PspGateway.PAYOUT_TRANSACTION_TYPE,
            )
        with self.assertRaises(WrongTransactionType):
            PspGateway.objects.create(
                application=self.application,
                external_id="test_payout_mandatory_field",
                gateway_type=PspGateway.PAYOUT_TRANSACTION_TYPE,
                transaction=self.sale_transaction,
            )
        PspGateway.objects.create(
            application=self.application,
            external_id="test_payout_mandatory_field",
            gateway_type=PspGateway.PAYOUT_TRANSACTION_TYPE,
            transaction=self.payout_transaction,
        )
        with self.assertRaises(DuplicatedTransaction):
            PspGateway.objects.create(
                application=self.application,
                external_id="test_payout_mandatory_field_2",
                gateway_type=PspGateway.PAYOUT_TRANSACTION_TYPE,
                transaction=self.payout_transaction,
            )

    def test_store_adjustment_mandatory_field(self):
        """
        transaction is mandatory for STORE_ADJUSTMENT_TRANSACTION_TYPE.
        There must be a unique relation between
        gateway_type and transaction
        """
        with self.assertRaises(ValidationError):
            PspGateway.objects.create(
                application=self.application,
                external_id="test_store_adjustment_mandatory_field",
                gateway_type=PspGateway.STORE_ADJUSTMENT_TRANSACTION_TYPE,
            )
        with self.assertRaises(WrongTransactionType):
            PspGateway.objects.create(
                application=self.application,
                external_id="test_store_adjustment_mandatory_field",
                gateway_type=PspGateway.STORE_ADJUSTMENT_TRANSACTION_TYPE,
                transaction=self.sale_transaction,
            )
        PspGateway.objects.create(
            application=self.application,
            external_id="test_store_adjustment_mandatory_field",
            gateway_type=PspGateway.STORE_ADJUSTMENT_TRANSACTION_TYPE,
            transaction=self.store_adjustment_transaction,
        )
        with self.assertRaises(DuplicatedTransaction):
            PspGateway.objects.create(
                application=self.application,
                external_id="test_store_adjustment_mandatory_field_2",
                gateway_type=PspGateway.STORE_ADJUSTMENT_TRANSACTION_TYPE,
                transaction=self.store_adjustment_transaction,
            )

    def test_license_mandatory_field(self):
        """
        transaction is mandatory for LICENSE_TRANSACTION_TYPE.
        There must be a unique relation between
        gateway_type and transaction
        """
        with self.assertRaises(ValidationError):
            PspGateway.objects.create(
                application=self.application,
                external_id="test_license_mandatory_field",
                gateway_type=PspGateway.LICENSE_TRANSACTION_TYPE,
            )
        with self.assertRaises(WrongTransactionType):
            PspGateway.objects.create(
                application=self.application,
                external_id="test_license_mandatory_field",
                gateway_type=PspGateway.LICENSE_TRANSACTION_TYPE,
                transaction=self.sale_transaction,
            )
        PspGateway.objects.create(
            application=self.application,
            external_id="test_license_mandatory_field",
            gateway_type=PspGateway.LICENSE_TRANSACTION_TYPE,
            transaction=self.license_transaction,
        )
        with self.assertRaises(DuplicatedTransaction):
            PspGateway.objects.create(
                application=self.application,
                external_id="test_license_mandatory_field_2",
                gateway_type=PspGateway.LICENSE_TRANSACTION_TYPE,
                transaction=self.license_transaction,
            )

    def test_marketing_operation_mandatory_field(self):
        """
        transaction is mandatory for MARKETING_OPERATION_TRANSACTION_TYPE.
        There must be a unique relation between
        gateway_type and transaction
        """
        with self.assertRaises(ValidationError):
            PspGateway.objects.create(
                application=self.application,
                external_id="test_marketing_op_mandatory_field",
                gateway_type=PspGateway.MARKETING_OPERATION_TRANSACTION_TYPE,
            )
        with self.assertRaises(WrongTransactionType):
            PspGateway.objects.create(
                application=self.application,
                external_id="test_marketing_op_mandatory_field",
                gateway_type=PspGateway.MARKETING_OPERATION_TRANSACTION_TYPE,
                transaction=self.sale_transaction,
            )
        PspGateway.objects.create(
            application=self.application,
            external_id="test_marketing_op_mandatory_field",
            gateway_type=PspGateway.MARKETING_OPERATION_TRANSACTION_TYPE,
            transaction=self.marketing_operation_transaction,
        )
        with self.assertRaises(DuplicatedTransaction):
            PspGateway.objects.create(
                application=self.application,
                external_id="test_marketing_op_mandatory_field_2",
                gateway_type=PspGateway.MARKETING_OPERATION_TRANSACTION_TYPE,
                transaction=self.marketing_operation_transaction,
            )

    def test_create_gateway_with_invalid_external_ids(self):
        external_ids = ["abc def", "abc,def", "abc&def", "abc?def", "abc.def", "abcdëf"]
        for external_id in external_ids:
            psp_gateway = PspGateway(
                application=self.application,
                external_id=external_id,
                gateway_type=PspGateway.PREPAYMENT_TYPE,
                merchant_order=self.merchant_order,
            )
            with self.assertRaises(ValidationError):
                psp_gateway.full_clean()

    def test_create_gateway_with_valid_external_ids(self):
        external_ids = ["abcDEF123", "abcDEF_123", "abcDEF-123"]
        for external_id in external_ids:
            psp_gateway = PspGateway(
                application=self.application,
                external_id=external_id,
                gateway_type=PspGateway.PREPAYMENT_TYPE,
                merchant_order=self.merchant_order,
            )

            psp_gateway.full_clean()

            self.assertEqual(psp_gateway.external_id, external_id)


class PspGatewayModelFactoryTestCase(BaseTestCase):
    def test_complete_failed_refund_related_gateway(self):
        gateway = RefundPspGatewayFactory.build(refund__status="pending")

        with self.assertRaises(ValidationError):
            gateway.actions.pay()
