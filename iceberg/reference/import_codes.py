# -*- coding: utf-8 -*-

from django.utils.translation import gettext_lazy as _

ERROR_GLOBAL_BASIS = 10000
WARNING_GLOBAL_BASIS = 20000
ERROR_PRODUCT_BASIS = 30000
WARNING_PRODUCT_BASIS = 40000

# Global errors :
ERROR_GLOBAL_UNKNOWN_ERROR = ERROR_GLOBAL_BASIS + 1
ERROR_GLOBAL_UNKNOWN_MERCHANT = ERROR_GLOBAL_BASIS + 2
ERROR_GLOBAL_INVALID_OPERATION = ERROR_GLOBAL_BASIS + 3

# Global warnings :
WARNING_GLOBAL_MISSING_SKU = WARNING_GLOBAL_BASIS + 1

# Product related errors :
ERROR_PRODUCT_UNKNOWN_ERROR = ERROR_PRODUCT_BASIS + 1
ERROR_PRODUCT_GTIN13_MUST_BE_13_CHARACTER_LENGTH = ERROR_PRODUCT_BASIS + 2
ERROR_PRODUCT_NAME_MUST_BE_DEFINED = ERROR_PRODUCT_BASIS + 3
ERROR_PRODUCT_NAME_LENGTH_MUST_BE_AT_MOST_255_CHARACTERS = ERROR_PRODUCT_BASIS + 4
ERROR_PRODUCT_DESCRIPTION_MUST_BE_DEFINED = ERROR_PRODUCT_BASIS + 5
ERROR_PRODUCT_STOCK_MUST_BE_AN_INTEGER = ERROR_PRODUCT_BASIS + 6
ERROR_PRODUCT_STOCK_MUST_BE_GREATER_OR_EQUAL_ZERO = ERROR_PRODUCT_BASIS + 7
ERROR_PRODUCT_CURRENCY_MUST_BE_DEFINED = ERROR_PRODUCT_BASIS + 8
ERROR_PRODUCT_PRICE_MUST_BE_A_DECIMAL = ERROR_PRODUCT_BASIS + 9
ERROR_PRODUCT_PRICE_MUST_BE_POSITIVE = ERROR_PRODUCT_BASIS + 10
ERROR_PRODUCT_VAT_RATE_MUST_BE_A_DECIMAL = ERROR_PRODUCT_BASIS + 11
ERROR_PRODUCT_VAT_RATE_MUST_BE_POSITIVE = ERROR_PRODUCT_BASIS + 12
ERROR_PRODUCT_WRAPPING_MUST_BE_A_DECIMAL = ERROR_PRODUCT_BASIS + 13
ERROR_PRODUCT_WRAPPING_MUST_BE_POSITIVE = ERROR_PRODUCT_BASIS + 14
ERROR_PRODUCT_INVALID_AVAIBILITY_VALUE = ERROR_PRODUCT_BASIS + 15
ERROR_PRODUCT_DELAY_MUST_BE_A_DECIMAL = ERROR_PRODUCT_BASIS + 16
ERROR_PRODUCT_WEIGHT_MUST_BE_A_DECIMAL = ERROR_PRODUCT_BASIS + 17
ERROR_PRODUCT_DISCOUNT_PRICE_MISSING = ERROR_PRODUCT_BASIS + 18
ERROR_PRODUCT_DISCOUNT_START_MISSING = ERROR_PRODUCT_BASIS + 19
ERROR_PRODUCT_DISCOUNT_END_MISSING = ERROR_PRODUCT_BASIS + 20
ERROR_PRODUCT_DISCOUNT_PRICE_MUST_BE_A_DECIMAL = ERROR_PRODUCT_BASIS + 21
ERROR_PRODUCT_DISCOUNT_START_INVALID_FORMAT = ERROR_PRODUCT_BASIS + 22
ERROR_PRODUCT_DISCOUNT_END_INVALID_FORMAT = ERROR_PRODUCT_BASIS + 23
ERROR_PRODUCT_INFO_MUST_BE_ALPHANUMERIC = ERROR_PRODUCT_BASIS + 24
ERROR_PRODUCT_FILE_URL_MISSING = ERROR_PRODUCT_BASIS + 25
ERROR_PRODUCT_FILE_KIND_MISSING = ERROR_PRODUCT_BASIS + 26
ERROR_PRODUCT_FILE_USE_MISSING = ERROR_PRODUCT_BASIS + 27
ERROR_PRODUCT_FILE_NAME_MISSING = ERROR_PRODUCT_BASIS + 28
ERROR_PRODUCT_FILE_URL_IS_INVALID = ERROR_PRODUCT_BASIS + 29
ERROR_PRODUCT_FILE_KIND_IS_INVALID = ERROR_PRODUCT_BASIS + 30
ERROR_PRODUCT_FILE_USE_IS_INVALID = ERROR_PRODUCT_BASIS + 31
ERROR_PRODUCT_FILE_NAME_MUST_BE_ALPHANUMERIC = ERROR_PRODUCT_BASIS + 32
ERROR_PRODUCT_ASSOCIATE_SKU_MISSING = ERROR_PRODUCT_BASIS + 33
ERROR_PRODUCT_ASSOCIATE_KIND_MISSING = ERROR_PRODUCT_BASIS + 34
ERROR_PRODUCT_ASSOCIATE_KIND_IS_INVALID = ERROR_PRODUCT_BASIS + 35
ERROR_PRODUCT_VARIATION_SKU_MISSING = ERROR_PRODUCT_BASIS + 36
ERROR_PRODUCT_VARIATION_SIZE_MISSING = ERROR_PRODUCT_BASIS + 37
ERROR_PRODUCT_VARIATION_STOCK_MISSING = ERROR_PRODUCT_BASIS + 38
ERROR_PRODUCT_VARIATION_GTIN13_MISSING = ERROR_PRODUCT_BASIS + 39
ERROR_PRODUCT_BANNED_CATEGORY = ERROR_PRODUCT_BASIS + 40
ERROR_PRODUCT_CATEGORY_MUST_BE_AN_INTEGER = ERROR_PRODUCT_BASIS + 41
ERROR_PRODUCT_PRICE_WITHOUT_VAT_CANT_BE_COMPUTED = ERROR_PRODUCT_BASIS + 43
ERROR_PRODUCT_VARIATION_SIZE_NOT_RECOGNIZED = ERROR_PRODUCT_BASIS + 44
ERROR_PRODUCT_BRAND_MUST_BE_ALPHANUMERIC = ERROR_PRODUCT_BASIS + 45

# Product related warnings :
WARNING_PRODUCT_CATEGORY_MUST_BE_ALPHANUMERIC = WARNING_PRODUCT_BASIS + 1
WARNING_PRODUCT_INVALID_ISNEW_VALUE = WARNING_PRODUCT_BASIS + 6
WARNING_PRODUCT_INVALID_EXCLUSIVE_VALUE = WARNING_PRODUCT_BASIS + 7
WARNING_PRODUCT_INVALID_WRAPPABLE_VALUE = WARNING_PRODUCT_BASIS + 8
WARNING_PRODUCT_INVALID_CANCELLATION_VALUE = WARNING_PRODUCT_BASIS + 9
WARNING_PRODUCT_INVALID_CHARACTERISTIC = WARNING_PRODUCT_BASIS + 10
WARNING_PRODUCT_URL_MUST_BE_VALID = WARNING_PRODUCT_BASIS + 11
WARNING_PRODUCT_WARRANTY_MUST_BE_ALPHANUMERIC = WARNING_PRODUCT_BASIS + 12
WARNING_PRODUCT_BRAND_MUST_BE_ALPHANUMERIC = WARNING_PRODUCT_BASIS + 13
WARNING_PRODUCT_KEYWORDS_MUST_BE_A_LIST = WARNING_PRODUCT_BASIS + 14
WARNING_PRODUCT_EACH_KEYWORD_MUST_BE_ALPHANUMERIC = WARNING_PRODUCT_BASIS + 15
WARNING_PRODUCT_INVALID_GENDER_VALUE = WARNING_PRODUCT_BASIS + 16
WARNING_PRODUCT_LINE_MUST_BE_ALPHANUMERIC = WARNING_PRODUCT_BASIS + 17
WARNING_PRODUCT_CATEGORY_MUST_BE_AN_INTEGER = WARNING_PRODUCT_BASIS + 18
WARNING_PRODUCT_PRICE_WITHOUT_VAT_CANT_BE_COMPUTED = WARNING_PRODUCT_BASIS + 30
WARNING_PRODUCT_VARIATION_SIZE_NOT_RECOGNIZED = WARNING_PRODUCT_BASIS + 31

CODE_TO_DESCRIPTION = {
    # Global errors :
    ERROR_GLOBAL_UNKNOWN_ERROR: _("Unknown error"),
    ERROR_GLOBAL_UNKNOWN_MERCHANT: _("No merchant with this mid/api_key"),
    ERROR_GLOBAL_INVALID_OPERATION: _("Invalid operation"),
    # Global warnings :
    WARNING_GLOBAL_MISSING_SKU: _("sku is missing on, at least, a product"),
    # Product related errors :
    ERROR_PRODUCT_UNKNOWN_ERROR: _("Product unknown error"),
    ERROR_PRODUCT_GTIN13_MUST_BE_13_CHARACTER_LENGTH: _(
        "gtin13 must be 13-character length"
    ),
    ERROR_PRODUCT_NAME_MUST_BE_DEFINED: _("name must be defined"),
    ERROR_PRODUCT_NAME_LENGTH_MUST_BE_AT_MOST_255_CHARACTERS: _(
        "name length must be at most 255 characters"
    ),
    ERROR_PRODUCT_DESCRIPTION_MUST_BE_DEFINED: _("description must be defined"),
    ERROR_PRODUCT_STOCK_MUST_BE_AN_INTEGER: _("stock must be an integer"),
    ERROR_PRODUCT_STOCK_MUST_BE_GREATER_OR_EQUAL_ZERO: _(
        "stock must be greater than zero or equal zero"
    ),
    ERROR_PRODUCT_CURRENCY_MUST_BE_DEFINED: _(
        "currency must be defined using ISO 4217 3-letters code"
    ),
    ERROR_PRODUCT_PRICE_MUST_BE_A_DECIMAL: _("price must be a decimal"),
    ERROR_PRODUCT_PRICE_MUST_BE_POSITIVE: _("price must be positive"),
    ERROR_PRODUCT_VAT_RATE_MUST_BE_A_DECIMAL: _("vat_rate must be a decimal"),
    ERROR_PRODUCT_VAT_RATE_MUST_BE_POSITIVE: _("vat_rate must be positive"),
    ERROR_PRODUCT_WRAPPING_MUST_BE_A_DECIMAL: _("wrapping must be a decimal"),
    ERROR_PRODUCT_WRAPPING_MUST_BE_POSITIVE: _("wrapping must be positive"),
    ERROR_PRODUCT_INVALID_AVAIBILITY_VALUE: _("invalid availiblity value"),
    ERROR_PRODUCT_DELAY_MUST_BE_A_DECIMAL: _("delay must be a decimal"),
    ERROR_PRODUCT_WEIGHT_MUST_BE_A_DECIMAL: _("weight must be a decimal"),
    ERROR_PRODUCT_DISCOUNT_PRICE_MISSING: _("discount price is missing"),
    ERROR_PRODUCT_DISCOUNT_START_MISSING: _("discount start is missing"),
    ERROR_PRODUCT_DISCOUNT_END_MISSING: _("discount end is missing"),
    ERROR_PRODUCT_DISCOUNT_PRICE_MUST_BE_A_DECIMAL: _(
        "discount price must be a decimal"
    ),
    ERROR_PRODUCT_DISCOUNT_START_INVALID_FORMAT: _(
        'discount start invalid format. must be "YYYY-MM-DD hh:mm:ss"'
    ),
    ERROR_PRODUCT_DISCOUNT_END_INVALID_FORMAT: _(
        'discount end invalid format. must be "YYYY-MM-DD hh:mm:ss"'
    ),
    ERROR_PRODUCT_INFO_MUST_BE_ALPHANUMERIC: _("info must be alphanumeric"),
    ERROR_PRODUCT_FILE_URL_MISSING: _("file url missing"),
    ERROR_PRODUCT_FILE_KIND_MISSING: _("file kind missing"),
    ERROR_PRODUCT_FILE_USE_MISSING: _("file use missing"),
    ERROR_PRODUCT_FILE_NAME_MISSING: _("file name missing"),
    ERROR_PRODUCT_FILE_URL_IS_INVALID: _("file url is invalid"),
    ERROR_PRODUCT_FILE_KIND_IS_INVALID: _(
        'file kind is invalid. mist be one of "img"/"pdf".'
    ),
    ERROR_PRODUCT_FILE_USE_IS_INVALID: _(
        'file use is invalid. must be one of "primary"/"secondary"/"warranty"'
    ),
    ERROR_PRODUCT_FILE_NAME_MUST_BE_ALPHANUMERIC: _("file name must be alphanumeric"),
    ERROR_PRODUCT_ASSOCIATE_SKU_MISSING: _("associate sku is missing"),
    ERROR_PRODUCT_ASSOCIATE_KIND_MISSING: _("associate kind is missing"),
    ERROR_PRODUCT_ASSOCIATE_KIND_IS_INVALID: _(
        "associate kind is invalid. must be one of "
        '"is_accessory"/"has_accessory"/"is_similar"'
    ),
    ERROR_PRODUCT_VARIATION_SKU_MISSING: _("variation sku is missing"),
    ERROR_PRODUCT_VARIATION_SIZE_MISSING: _("variation size is missing"),
    ERROR_PRODUCT_VARIATION_STOCK_MISSING: _("variation stock is missing"),
    ERROR_PRODUCT_VARIATION_GTIN13_MISSING: _("variation gtin13 is missing"),
    ERROR_PRODUCT_BANNED_CATEGORY: _("banned category"),
    ERROR_PRODUCT_CATEGORY_MUST_BE_AN_INTEGER: _("category must be an integer"),
    ERROR_PRODUCT_PRICE_WITHOUT_VAT_CANT_BE_COMPUTED: _(
        "cant compute an exact price without vat based on the vat included price"
    ),
    ERROR_PRODUCT_VARIATION_SIZE_NOT_RECOGNIZED: _("no variation size was recognized"),
    ERROR_PRODUCT_BRAND_MUST_BE_ALPHANUMERIC: _("brand must be alphanumeric"),
    # Product related warnings :
    WARNING_PRODUCT_CATEGORY_MUST_BE_ALPHANUMERIC: _("category must be alphanumeric"),
    WARNING_PRODUCT_INVALID_ISNEW_VALUE: _(
        "Invalid isnew value. Must be true or false"
    ),
    WARNING_PRODUCT_INVALID_EXCLUSIVE_VALUE: _(
        "Invalid exclusive value. Must be true or false"
    ),
    WARNING_PRODUCT_INVALID_WRAPPABLE_VALUE: _(
        "Invalid wrappable value. Must be true or false"
    ),
    WARNING_PRODUCT_INVALID_CANCELLATION_VALUE: _(
        "Invalid cancellation value. Must be true or false"
    ),
    WARNING_PRODUCT_INVALID_CHARACTERISTIC: _("Invalid characteristic"),
    WARNING_PRODUCT_URL_MUST_BE_VALID: _("url must be valid"),
    WARNING_PRODUCT_WARRANTY_MUST_BE_ALPHANUMERIC: _("warranty must be alphanumeric"),
    WARNING_PRODUCT_BRAND_MUST_BE_ALPHANUMERIC: _("brand must be alphanumeric"),
    WARNING_PRODUCT_KEYWORDS_MUST_BE_A_LIST: _("keywords must be a list"),
    WARNING_PRODUCT_EACH_KEYWORD_MUST_BE_ALPHANUMERIC: _(
        "each keyword in keywords list must be alphanumeric"
    ),
    WARNING_PRODUCT_INVALID_GENDER_VALUE: _(
        'invalid gender value. must be one of "F"/"M"/"U"'
    ),
    WARNING_PRODUCT_LINE_MUST_BE_ALPHANUMERIC: _("line must be alphanumeric"),
    WARNING_PRODUCT_CATEGORY_MUST_BE_AN_INTEGER: _("category must be alphanumeric"),
    WARNING_PRODUCT_PRICE_WITHOUT_VAT_CANT_BE_COMPUTED: _(
        "cant compute an exact price without vat based on the vat included price"
    ),
    WARNING_PRODUCT_VARIATION_SIZE_NOT_RECOGNIZED: _(
        "variation size is not recognized"
    ),
}
