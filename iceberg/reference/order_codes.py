# -*- coding: utf-8 -*-

from django.utils.translation import gettext_lazy as _

# NEVER, EVER use 0 as a value in the following codes :
# 0 means no error...

ERROR_BASIS = 50000

ERROR_UNKNOWN_ERROR = ERROR_BASIS + 1
ERROR_PRODUCT_OFFER_DOES_NOT_EXIST = ERROR_BASIS + 4
ERROR_QUANTITY_MUST_BE_AN_INTEGER = ERROR_BASIS + 6
ERROR_UNIT_PRICE_MUST_BE_A_STRICTLY_POSITIVE_DECIMAL = ERROR_BASIS + 7
ERROR_UNIT_VAT_MUST_BE_A_STRICTLY_POSITIVE_DECIMAL = ERROR_BASIS + 8
ERROR_NOT_ENOUGH_STOCK = ERROR_BASIS + 9
ERROR_BUNDLED_NOT_POSSIBLE = ERROR_BASIS + 10
ERROR_PAYMENT_INFO_DOES_NOT_EXIST = ERROR_BASIS + 13
ERROR_BILLING_ADDRESS_USER_MUST_BE_EQUAL_TO_CART_USER = ERROR_BASIS + 15
ERROR_PRODUCT_OFFERS_MUST_HAVE_CONSISTENT_CURRENCY_WHEN_ADDED_TO_A_CART = (
    ERROR_BASIS + 48
)
ERROR_CART_NOT_VALID = ERROR_BASIS + 69
ERROR_ORDER_ALREADY_IN_PROGRESS = ERROR_BASIS + 75
ERROR_EMPTY_CART = ERROR_BASIS + 76
ERROR_DONE_CART = ERROR_BASIS + 77

ERROR_SHIPPING_NOT_POSSIBLE = ERROR_BASIS + 81
ERROR_SHIPPING_HAS_CHANGED = ERROR_BASIS + 84
ERROR_UNIT_PRICE_MUST_BE_A_POSITIVE_DECIMAL = ERROR_BASIS + 85

ERROR_PRODUCT_STATUS_MUST_BE_ACTIVE = ERROR_BASIS + 100
ERROR_PRODUCT_OFFER_STATUS_MUST_BE_ACTIVE = ERROR_BASIS + 101
ERROR_MERCHANT_STATUS_MUST_BE_ACTIVE = ERROR_BASIS + 102
ERROR_PRODUCT_VARIATION_STATUS_MUST_BE_ACTIVE = ERROR_BASIS + 103

ERROR_PAYMENT_CANNOT_COLLECT = ERROR_BASIS + 111
ERROR_DISCOUNT_IS_NOT_APPLICABLE = ERROR_BASIS + 130
ERROR_DISCOUNT_CODE_IS_NOT_ACTIVE_OR_NO_LONGER_VALID = ERROR_BASIS + 131
ERROR_DISCOUNT_CODE_IS_ALREADY_APPLIED = ERROR_BASIS + 132
ERROR_DISCOUNT_CODE_NOT_IN_ENTERED_DISCOUNT_CODES = ERROR_BASIS + 133
ERROR_DISCOUNT_CODE_DOES_NOT_EXISTS = ERROR_BASIS + 134
ERROR_DISCOUNT_CODE_TOO_MUCH_APPLIED = ERROR_BASIS + 135

ERROR_CART_ITEM_PRICES_CHANGED = ERROR_BASIS + 140
ERROR_MISSING_PAYMENT_BACKEND = ERROR_BASIS + 142
ERROR_CART_ITEM_INVALID = ERROR_BASIS + 143
ERROR_CART_ITEM_PRICE_OVERRIDE_NOT_ALLOWED = ERROR_BASIS + 144
ERROR_CART_OVERRIDE_COMMISSION_NOT_ALLOWED = ERROR_BASIS + 145

ERROR_INVALID_ORDER_ITEM = ERROR_BASIS + 200

ERROR_ORDER_AMOUNTS_NOT_UPDATABLE = ERROR_BASIS + 300
ERROR_MERCHANT_ORDER_AMOUNTS_NOT_UPDATABLE = ERROR_BASIS + 301
ERROR_ORDER_ALREADY_IN_CREATION = ERROR_BASIS + 302
ERROR_ORDER_LOCKED = ERROR_BASIS + 303

ERROR_PRODUCT_OFFER_DOES_NOT_BELONG_TO_ISOLATED_APPLICATION = ERROR_BASIS + 400

ERROR_MISSING_PAYMENT_TERMS = ERROR_BASIS + 901
ERROR_INVALID_WORKFLOW = ERROR_BASIS + 902

ERROR_READONLY_MERCHANT_ORDER = ERROR_BASIS + 1001

ERROR_MERCHANT_ITEMS_MULTI_ORIGIN_FORBIDDEN = ERROR_BASIS + 1101
ERROR_MISSING_OFFER_ORIGIN = ERROR_BASIS + 1102
ERROR_MISSING_MANDATORY_MERCHANT_INFO = ERROR_BASIS + 1103

SHIPPING_OUTDATED = "SHIPPING.UPDATE_NEEDED"
DISCOUNTS_OUTDATED = "DISCOUNTS.UPDATE_NEEDED"


CODE_TO_DESCRIPTION = {
    # Errors :
    ERROR_UNKNOWN_ERROR: _("Unknown error"),
    ERROR_PRODUCT_OFFER_DOES_NOT_EXIST: _("Product offer does not exist"),
    ERROR_QUANTITY_MUST_BE_AN_INTEGER: _("Quantity must be an integer"),
    ERROR_UNIT_PRICE_MUST_BE_A_STRICTLY_POSITIVE_DECIMAL: _(
        "unit_price must be a strictly positive decimal"
    ),
    ERROR_UNIT_PRICE_MUST_BE_A_POSITIVE_DECIMAL: _(
        "unit_price must be a positive decimal"
    ),
    ERROR_NOT_ENOUGH_STOCK: _("Not enough stock"),
    ERROR_BUNDLED_NOT_POSSIBLE: _("Bundle is not possible"),
    ERROR_PAYMENT_INFO_DOES_NOT_EXIST: _("Payment info does not exist"),
    ERROR_BILLING_ADDRESS_USER_MUST_BE_EQUAL_TO_CART_USER: _(
        "Billing address user must be equal to cart user"
    ),
    ERROR_PRODUCT_OFFERS_MUST_HAVE_CONSISTENT_CURRENCY_WHEN_ADDED_TO_A_CART: _(
        "Product offers must have consistent currency when added to cart"
    ),
    ERROR_ORDER_ALREADY_IN_PROGRESS: _("An order is already in progress"),
    SHIPPING_OUTDATED: _(
        "Shipping looks outdated. Please call POST "
        "/v1/cart/{cart_id}/shipping_options/update/ before placing an "
        "order."
    ),
    DISCOUNTS_OUTDATED: _(
        "Discounts look outdated. Please call POST "
        "/v1/cart/{cart_id}/recompute-discounts/ before placing an order."
    ),
    ERROR_EMPTY_CART: _("Cannot create order with empty cart"),
    ERROR_DONE_CART: _("Cannot create order from a done cart"),
    ERROR_SHIPPING_NOT_POSSIBLE: _("The store cant ship to this address"),
    ERROR_PRODUCT_STATUS_MUST_BE_ACTIVE: _("The product must be active"),
    ERROR_MERCHANT_STATUS_MUST_BE_ACTIVE: _("The merchant must be active"),
    ERROR_PRODUCT_OFFER_STATUS_MUST_BE_ACTIVE: _("The product offer must be active"),
    ERROR_PAYMENT_CANNOT_COLLECT: _("Payment Cannot be collected"),
    ERROR_DISCOUNT_IS_NOT_APPLICABLE: _(
        "The discount code is valid but not applicable. Its terms of use are not "
        "respected or another promotion is already applied"
    ),
    ERROR_DISCOUNT_CODE_DOES_NOT_EXISTS: _("The discount code does not exist"),
    ERROR_DISCOUNT_CODE_IS_NOT_ACTIVE_OR_NO_LONGER_VALID: _(
        "The discount code is not active or no longer valid"
    ),
    ERROR_DISCOUNT_CODE_TOO_MUCH_APPLIED: _(
        "The maximum number of code uses has already been reached"
    ),
    ERROR_DISCOUNT_CODE_IS_ALREADY_APPLIED: _("The discount code is already applied"),
    ERROR_DISCOUNT_CODE_NOT_IN_ENTERED_DISCOUNT_CODES: _(
        "The discount code is not the entered discount codes set"
    ),
    ERROR_CART_ITEM_PRICES_CHANGED: _("The price of 1 item (or more) has changed"),
    ERROR_MISSING_PAYMENT_BACKEND: _("Missing payment backend"),
    ERROR_CART_ITEM_INVALID: _("Invalid Cart Item"),
    ERROR_CART_ITEM_PRICE_OVERRIDE_NOT_ALLOWED: _(
        "Price override on cart item is not allowed for your user"
    ),
    ERROR_INVALID_ORDER_ITEM: _("Invalid order item"),
    ERROR_ORDER_AMOUNTS_NOT_UPDATABLE: _("Amounts of order can't be changed"),
    ERROR_MERCHANT_ORDER_AMOUNTS_NOT_UPDATABLE: _(
        "Amounts of merchant order can't be changed"
    ),
    ERROR_ORDER_ALREADY_IN_CREATION: _("Order is already in creation"),
    ERROR_ORDER_LOCKED: _("Order is locked"),
    ERROR_PRODUCT_OFFER_DOES_NOT_BELONG_TO_ISOLATED_APPLICATION: _(
        "Product offer does not belong to the application"
    ),
    ERROR_MISSING_PAYMENT_TERMS: _(
        "All cart items need payment terms to create an order"
    ),
    ERROR_INVALID_WORKFLOW: _("Cannot create an order using the provided workflow"),
    ERROR_READONLY_MERCHANT_ORDER: _("Merchant order is readonly"),
    ERROR_MERCHANT_ITEMS_MULTI_ORIGIN_FORBIDDEN: _(
        "Ordering items from multiple origins for the same merchant is forbidden"
    ),
    ERROR_MISSING_OFFER_ORIGIN: _(
        "Informations about the origin of the offer are missing"
    ),
}
