# -*- coding: utf-8 -*-

from django.conf import settings
from django.utils.translation import gettext_lazy as _

SHIPPING_POLICY_PER_WEIGHT = 1
SHIPPING_POLICY_PER_ITEM = 2
SHIPPING_POLICY_BANDED_PRICES = 3
SHIPPING_POLICY_BANDED_WEIGHT = 4
SHIPPING_POLICY_PER_ITEM_COUNT = 5

SHIPPING_POLICIES = (
    (SHIPPING_POLICY_PER_WEIGHT, _("Per weight shipping policy")),
    (SHIPPING_POLICY_PER_ITEM, _("Per item shipping policy")),
    (SHIPPING_POLICY_BANDED_PRICES, _("Banded price shipping policy")),
    (SHIPPING_POLICY_BANDED_WEIGHT, _("Banded weight shipping policy")),
    (SHIPPING_POLICY_PER_ITEM_COUNT, _("Per item count shipping policy")),
)


DEFAULT_VAT_ON_SHIPPING = settings.DEFAULT_VAT_RATE
