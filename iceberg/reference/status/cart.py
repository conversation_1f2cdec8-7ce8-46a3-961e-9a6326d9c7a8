# -*- coding: utf-8 -*-

from django.utils.translation import gettext_lazy as _

# Cart
CART_STATUS_EMPTY = "0"
CART_STATUS_ACTIVE = "10"
CART_STATUS_VALID = "20"
CART_STATUS_ORDER_IN_PROGRESS = "25"  # Not yet used
CART_STATUS_DONE = "30"
CART_STATUS_SHIPPING_NOT_POSSIBLE = "50"
CART_STATUS_PARTIALLY_SOLD_OUT = "100"
CART_STATUS_CANCELLED = "1000"

CART_VALIDATED_STATUSES = [
    CART_STATUS_VALID,
    CART_STATUS_ORDER_IN_PROGRESS,
    CART_STATUS_DONE,
]

CART_STATUSES = (
    (CART_STATUS_EMPTY, _("Empty")),
    (CART_STATUS_ACTIVE, _("Active")),
    (CART_STATUS_VALID, _("Valid")),  # An order has been created from the cart
    (CART_STATUS_DONE, _("Done")),  # The created order has been finalized
    (CART_STATUS_SHIPPING_NOT_POSSIBLE, _("Shipping Not Possible")),
    (CART_STATUS_PARTIALLY_SOLD_OUT, _("Partially Sold Out")),
    (CART_STATUS_CANCELLED, _("Cancelled")),
)

# CartItem
CART_ITEM_STATUS_ACTIVE = "10"
CART_ITEM_STATUS_VALID = "20"
CART_ITEM_STATUS_DONE = "30"
CART_ITEM_STATUS_SHIPPING_NOT_POSSIBLE = "50"
CART_ITEM_STATUS_SOLD_OUT = "100"
CART_ITEM_STATUS_CANCELLED = "1000"

CART_ITEM_VALIDATED_STATUSES = [CART_ITEM_STATUS_VALID, CART_ITEM_STATUS_DONE]

CART_ITEM_STATUSES = (
    (CART_ITEM_STATUS_ACTIVE, _("Active cart item")),
    (CART_ITEM_STATUS_VALID, _("Valid cart item")),
    (CART_ITEM_STATUS_DONE, _("Done cart item")),
    (CART_ITEM_STATUS_SHIPPING_NOT_POSSIBLE, _("Shipping Not Possible")),
    (CART_ITEM_STATUS_SOLD_OUT, _("Sold Out")),
    (CART_ITEM_STATUS_CANCELLED, _("Cancelled")),
)

USER_CURRENT_CART_STATUSES = [
    CART_STATUS_EMPTY,
    CART_STATUS_ACTIVE,
    CART_STATUS_VALID,
    CART_STATUS_SHIPPING_NOT_POSSIBLE,
    CART_STATUS_PARTIALLY_SOLD_OUT,
]

ANONYMOUS_CURRENT_CART_STATUSES = [
    CART_STATUS_EMPTY,
    CART_STATUS_ACTIVE,
    CART_STATUS_SHIPPING_NOT_POSSIBLE,
    CART_STATUS_PARTIALLY_SOLD_OUT,
]
