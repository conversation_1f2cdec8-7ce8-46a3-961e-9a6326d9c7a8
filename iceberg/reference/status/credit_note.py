from django.utils.translation import gettext_lazy as _

CREDIT_NOTE_STATUS_DRAFT = "draft"
CREDIT_NOTE_STATUS_PENDING = "pending"
CREDIT_NOTE_STATUS_EMITTED = "emitted"
CREDIT_NOTE_STATUS_DELETED = "deleted"

CREDIT_NOTE_STATUSES = (
    (CREDIT_NOTE_STATUS_DRAFT, _("Draft")),
    (CREDIT_NOTE_STATUS_PENDING, _("Pending")),
    (CREDIT_NOTE_STATUS_EMITTED, _("Emitted")),
    (CREDIT_NOTE_STATUS_DELETED, _("Deleted")),
)


NOT_PAID, PAID, FAILED_PAYMENT = ("not_paid", "paid", "failed_payment")
PAYMENT_STATUS_CHOICES = (
    (NOT_PAID, _("Not paid")),
    (PAID, _("Successfully Paid")),
    (FAILED_PAYMENT, _("Failed on payment")),
)
