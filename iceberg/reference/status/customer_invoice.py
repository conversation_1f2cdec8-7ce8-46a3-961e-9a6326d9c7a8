from django.utils.translation import gettext_lazy as _

CUSTOMER_INVOICE_STATUS_DRAFT = "draft"
CUSTOMER_INVOICE_STATUS_PENDING = "pending"
CUSTOMER_INVOICE_STATUS_EMITTED = "emitted"
CUSTOMER_INVOICE_STATUS_DELETED = "deleted"
CUSTOMER_INVOICE_STATUS_PENDING_PROFORMA = "pending_proforma"
CUSTOMER_INVOICE_STATUS_EMITTED_PROFORMA = "emitted_proforma"

CUSTOMER_INVOICE_STATUSES = (
    (CUSTOMER_INVOICE_STATUS_DRAFT, _("Draft")),
    (CUSTOMER_INVOICE_STATUS_PENDING, _("Pending")),
    (CUSTOMER_INVOICE_STATUS_EMITTED, _("Emitted")),
    (CUSTOMER_INVOICE_STATUS_DELETED, _("Deleted")),
    (CUSTOMER_INVOICE_STATUS_PENDING_PROFORMA, _("Pending Proforma")),
    (CUSTOMER_INVOICE_STATUS_EMITTED_PROFORMA, _("Emitted Proforma")),
)


PAYMENT_STATUS_NOT_PAID, PAYMENT_STATUS_PAID, PAYMENT_STATUS_FAILED = (
    "not_paid",
    "paid",
    "failed_payment",
)
PAYMENT_STATUS_CHOICES = (
    (PAYMENT_STATUS_NOT_PAID, _("Not paid")),
    (PAYMENT_STATUS_PAID, _("Successfully Paid")),
    (PAYMENT_STATUS_FAILED, _("Failed on payment")),
)
