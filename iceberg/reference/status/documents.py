# -*- coding: utf-8 -*-
from django.utils.translation import gettext_lazy as _

DOCUMENT_PENDING = "pending"
DOCUMENT_UPLOADED = "uploaded"
DOCUMENT_NOT_NEEDED = "not_needed"
DOCUMENT_UNDER_ANALYSIS = "under_analysis"
DOCUMENT_ACCEPTED = "accepted"
DOCUMENT_REFUSED = "refused"
DOCUMENT_EXPIRED = "expired"

KYC_BACKEND_STATUSES = (
    (DOCUMENT_PENDING, _("Pending")),
    (DOCUMENT_UPLOADED, _("Uploaded")),
    (DOCUMENT_NOT_NEEDED, _("Not needed")),
    (DOCUMENT_UNDER_ANALYSIS, _("Under analysis")),
    (DOCUMENT_ACCEPTED, _("Accepted")),
    (DOCUMENT_REFUSED, _("Refused")),
    (DOCUMENT_EXPIRED, _("Expired")),
)
