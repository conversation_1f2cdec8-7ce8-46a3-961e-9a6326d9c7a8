# -*- coding: utf-8 -*-

from django.utils.translation import gettext_lazy as _

# Order
# NEW
# AWAITING_BANK_WIRE
# AWAITING_CHEQUE
# ORDER_STATUS_PARTIAL_REFUNDED
# ORDER_STATUS_REFUNDED
# ORDER_STATUS_PAYMENT_ACCEPTED

"""
Workflow of orders will be rationalized. To do so, we will depreciate
all statuses that are linked to external workflows like return/refunds and
payment.

To prepare this changes, we will have both new and old statuses living together
but only one workflow will be activated by configuration.
"""
#                                                   V1    V2
# TO REMOVE
ORDER_STATUS_INITIAL                    = "0"    #  X  |  X  |  # noqa
ORDER_STATUS_AUTH_UNKNOWN               = "50"   #  X  |     |  # noqa
                                                 #     |     |  # noqa
ORDER_STATUS_ONGOING                    = "10"   #  X  |     |  # noqa
ORDER_STATUS_EXPIRED                    = "15"   #  X  |     |  # noqa
                                                 #     |     |  # noqa
ORDER_STATUS_VALID                      = "20"   #  X  |     |  # noqa
ORDER_STATUS_VALID_FOR_DIRECT_PAYMENT   = "25"   #  X  |     |  # noqa

# Pending Payment
ORDER_STATUS_PENDING_PAYMENT            = "27"   #  X  |     |  # noqa
ORDER_STATUS_PENDING_PAYPAL             = "28"   #  X  |     |  # noqa
ORDER_STATUS_PENDING_BANK_WIRE          = "29"   #  X  |     |  # noqa
ORDER_STATUS_PENDING_CHEQUE             = "295"  #  X  |     |  # noqa

ORDER_STATUS_AUTH_SUCCESS               = "60"   #  X  |  X  |  # noqa
ORDER_STATUS_PARTIALLY_CONFIRMED        = "65"   #  X  |     |  # noqa
ORDER_STATUS_PAID                       = "70"   #  X  |     |  # noqa
ORDER_STATUS_CONFIRMED                  = "80"   #  X  |  X  |  # noqa
ORDER_STATUS_PROCESSED                  = "85"   #     |  X  |  # noqa
ORDER_STATUS_SENT                       = "90"   #  X  |     |  # noqa

ORDER_STATUS_RECEIVED                   = "100"  #  X  |     |  # noqa
ORDER_STATUS_FINALIZED                  = "110"  #  X  |  X  |  # noqa

ORDER_STATUS_RETURN_IN_PROGRESS         = "120"  #  X  |     |  # noqa
ORDER_STATUS_PARTIALLY_RETURNED         = "150"  #  X  |     |  # noqa
ORDER_STATUS_RETURNED                   = "160"  #  X  |     |  # noqa

# Error Code
ORDER_STATUS_AUTH_FAILURE               = "30"   #  X  |     |  # noqa
ORDER_STATUS_AUTH_CANCELLED             = "40"   #  X  |     |  # noqa
ORDER_STATUS_PAY_PARTIAL_FAILURE        = "1000" #  X  |     |  # noqa
ORDER_STATUS_PAY_FAILURE                = "1500" #  X  |     |  # noqa
ORDER_STATUS_CANCELLED                  = "2000" #  X  |     |  # noqa

ORDER_STATUS_DELETED                    = "3000" #  X  |     |  # noqa
ORDER_STATUS_FRAUD                      = "4000" #  X  |     |  # noqa

EXPIRABLE_STATUSES = [
    ORDER_STATUS_INITIAL,
    ORDER_STATUS_ONGOING,
    ORDER_STATUS_VALID,
    ORDER_STATUS_VALID_FOR_DIRECT_PAYMENT,
    ORDER_STATUS_PENDING_PAYMENT,
]

ORDER_VALIDATED_STATUSES = [
    ORDER_STATUS_VALID,
    ORDER_STATUS_VALID_FOR_DIRECT_PAYMENT,
    ORDER_STATUS_PENDING_PAYMENT,
    ORDER_STATUS_PENDING_PAYPAL,
    ORDER_STATUS_PENDING_BANK_WIRE,
    ORDER_STATUS_PENDING_CHEQUE,
    ORDER_STATUS_AUTH_SUCCESS,
    ORDER_STATUS_PARTIALLY_CONFIRMED,
    ORDER_STATUS_PAID,
    ORDER_STATUS_CONFIRMED,
    ORDER_STATUS_PROCESSED,
    ORDER_STATUS_SENT,
    ORDER_STATUS_RECEIVED,
    ORDER_STATUS_FINALIZED,
]

ORDER_AUTHORIZED_STATUSES = [
    ORDER_STATUS_AUTH_SUCCESS,
]

ORDER_STATUSES = (
    (ORDER_STATUS_INITIAL, _("Initial")),
    (ORDER_STATUS_EXPIRED, _("Expired")),
    (ORDER_STATUS_ONGOING, _("Ongoing")),
    (ORDER_STATUS_VALID, _("Valid")),
    (ORDER_STATUS_VALID_FOR_DIRECT_PAYMENT, _("Direct Payment")),
    (ORDER_STATUS_PENDING_PAYMENT, _("Payment Pending")),
    (ORDER_STATUS_AUTH_FAILURE, _("Not authorized")),
    (ORDER_STATUS_AUTH_CANCELLED, _("Authorization cancelled")),
    (ORDER_STATUS_AUTH_UNKNOWN, _("Authorized (?)")),
    (ORDER_STATUS_AUTH_SUCCESS, _("Payment Authorized")),
    (ORDER_STATUS_PARTIALLY_CONFIRMED, _("Partially Confirmed")),
    (ORDER_STATUS_PAID, _("Paid")),
    (ORDER_STATUS_CONFIRMED, _("Confirmed")),
    (ORDER_STATUS_PROCESSED, _("Processed")),
    (ORDER_STATUS_SENT, _("Sent")),
    (ORDER_STATUS_RECEIVED, _("Received")),
    (ORDER_STATUS_FINALIZED, _("Finalized")),
    (ORDER_STATUS_RETURN_IN_PROGRESS, _("Return in progress")),
    (ORDER_STATUS_PARTIALLY_RETURNED, _("Partially Returned")),
    (ORDER_STATUS_RETURNED, _("Returned")),
    (ORDER_STATUS_CANCELLED, _("Cancelled")),
    (ORDER_STATUS_DELETED, _("Deleted")),
    (ORDER_STATUS_PAY_PARTIAL_FAILURE, _("Payment partial failure")),
    (ORDER_STATUS_PAY_FAILURE, _("Payment failure")),
    # NEW
    (ORDER_STATUS_PENDING_PAYMENT, _("Pending Payment")),
    (ORDER_STATUS_PENDING_PAYPAL, _("Pending Payment Paypal")),
    (ORDER_STATUS_PENDING_BANK_WIRE, _("Pending Payment Bank Wire")),
    (ORDER_STATUS_PENDING_CHEQUE, _("Pending Payment Cheque")),
)

ORDER_STATUSES_LIST = [status[0] for status in ORDER_STATUSES]
ORDER_STATUSES_NON_DELETED = list(
    set(ORDER_STATUSES_LIST) - set([ORDER_STATUS_DELETED])
)

ORDER_STATUSES_DICT = {}
for code, trad in ORDER_STATUSES:
    ORDER_STATUSES_DICT[code] = trad

ORDER_STATUSES_WITH_CHANGEABLE_AMOUNTS = [
    ORDER_STATUS_INITIAL,
    ORDER_STATUS_ONGOING,
    ORDER_STATUS_AUTH_SUCCESS,
    ORDER_STATUS_PARTIALLY_CONFIRMED,
    # because amount recomputed just after canceled merchant_order
    ORDER_STATUS_CANCELLED,
]
#                                                        V1    V2
# MerchantOrder
MERCHANT_ORDER_STATUS_INITIAL             = "0"    #  |  X  |  X  |  # noqa
MERCHANT_ORDER_STATUS_EXPIRED             = "10"   #  |  X  |     |  # noqa
MERCHANT_ORDER_STATUS_PENDING_PAYMENT     = "27"   #  |  X  |     |  # noqa
MERCHANT_ORDER_STATUS_AUTH_FAILURE        = "30"   #  |  X  |     |  # noqa
MERCHANT_ORDER_STATUS_AUTH_CANCELLED      = "40"   #  |  X  |     |  # noqa
MERCHANT_ORDER_STATUS_AUTH_UNKNOWN        = "50"   #  |  X  |     |  # noqa
MERCHANT_ORDER_STATUS_AUTH_SUCCESS        = "60"   #  |  X  |  X  |  # noqa
MERCHANT_ORDER_STATUS_PARTIALLY_PAID      = "65"   #  |  X  |     |  # noqa
MERCHANT_ORDER_STATUS_PAID                = "70"   #  |  X  |     |  # noqa
MERCHANT_ORDER_STATUS_CONFIRMED           = "80"   #  |  X  |  X  |  # noqa
MERCHANT_ORDER_STATUS_PROCESSED           = "85"   #  |     |  X  |  # noqa
MERCHANT_ORDER_STATUS_SENT                = "90"   #  |  X  |     |  # noqa
MERCHANT_ORDER_STATUS_RECEIVED            = "100"  #  |  X  |     |  # noqa
MERCHANT_ORDER_STATUS_FINALIZED           = "110"  #  |  X  |  X  |  # noqa

MERCHANT_ORDER_STATUS_RETURN_IN_PROGRESS  = "120"  #  |  X  |     |  # noqa
MERCHANT_ORDER_STATUS_PARTIALLY_RETURNED  = "150"  #  |  X  |     |  # noqa
MERCHANT_ORDER_STATUS_RETURNED            = "160"  #  |  X  |     |  # noqa

MERCHANT_ORDER_STATUS_PAY_FAILURE         = "1000" #  |  X  |     |  # noqa
MERCHANT_ORDER_STATUS_PAY_PARTIAL_FAILURE = "1010" #  |  X  |     |  # noqa
MERCHANT_ORDER_STATUS_CANCELLED           = "2000" #  |  X  |     |  # noqa
MERCHANT_ORDER_STATUS_DELETED             = "3000" #  |  X  |     |  # noqa


MERCHANT_ORDER_UNPROCESSED_STATUSES = [
    MERCHANT_ORDER_STATUS_INITIAL,
    MERCHANT_ORDER_STATUS_PENDING_PAYMENT,
    MERCHANT_ORDER_STATUS_AUTH_UNKNOWN,
    MERCHANT_ORDER_STATUS_AUTH_SUCCESS,
    MERCHANT_ORDER_STATUS_PARTIALLY_PAID,
    MERCHANT_ORDER_STATUS_PAID,
    MERCHANT_ORDER_STATUS_CONFIRMED,
]

MERCHANT_ORDER_VALIDATED_STATUSES = [
    MERCHANT_ORDER_STATUS_PAID,
    MERCHANT_ORDER_STATUS_CONFIRMED,
    MERCHANT_ORDER_STATUS_PROCESSED,
    MERCHANT_ORDER_STATUS_SENT,
    MERCHANT_ORDER_STATUS_RECEIVED,
    MERCHANT_ORDER_STATUS_FINALIZED,
    MERCHANT_ORDER_STATUS_RETURN_IN_PROGRESS,
    MERCHANT_ORDER_STATUS_PARTIALLY_RETURNED,
    MERCHANT_ORDER_STATUS_RETURNED,
]

MERCHANT_ORDER_AUTHORIZED_STATUSES = [
    MERCHANT_ORDER_STATUS_AUTH_SUCCESS,
]

MERCHANT_ORDER_TO_CONFIRM_STATUSES = [
    MERCHANT_ORDER_STATUS_AUTH_SUCCESS,
    MERCHANT_ORDER_STATUS_PAID,
]

MERCHANT_ORDER_STATUSES_WITH_CHANGEABLE_AMOUNTS = [
    MERCHANT_ORDER_STATUS_INITIAL,
    MERCHANT_ORDER_STATUS_AUTH_SUCCESS,
    MERCHANT_ORDER_STATUS_PARTIALLY_PAID,
    # because amount recomputed just after canceled item
    MERCHANT_ORDER_STATUS_CANCELLED,
]

MERCHANT_ORDER_REVIEWABLE_STATUSES = list(
    set(MERCHANT_ORDER_VALIDATED_STATUSES)
    - set([MERCHANT_ORDER_STATUS_PAID, MERCHANT_ORDER_STATUS_CONFIRMED])
)

MERCHANT_ORDER_STATUSES = (
    (MERCHANT_ORDER_STATUS_INITIAL, _("Initial")),
    (MERCHANT_ORDER_STATUS_EXPIRED, _("Expired")),
    (MERCHANT_ORDER_STATUS_PENDING_PAYMENT, _("Pending Payment")),
    (MERCHANT_ORDER_STATUS_AUTH_FAILURE, _("Not authorized")),
    (MERCHANT_ORDER_STATUS_AUTH_CANCELLED, _("Authorization cancelled")),
    (MERCHANT_ORDER_STATUS_AUTH_UNKNOWN, _("Authorized (?)")),
    (MERCHANT_ORDER_STATUS_AUTH_SUCCESS, _("Payment Authorized")),
    (MERCHANT_ORDER_STATUS_PARTIALLY_PAID, _("MerchantOrder partially Paid")),
    (MERCHANT_ORDER_STATUS_PAID, _("Paid")),
    (MERCHANT_ORDER_STATUS_CONFIRMED, _("Confirmed")),
    (MERCHANT_ORDER_STATUS_PROCESSED, _("Processed")),
    (MERCHANT_ORDER_STATUS_SENT, _("Sent")),
    (MERCHANT_ORDER_STATUS_RECEIVED, _("Received")),
    (MERCHANT_ORDER_STATUS_FINALIZED, _("Finalized")),
    (MERCHANT_ORDER_STATUS_RETURN_IN_PROGRESS, _("Return in progress")),
    (MERCHANT_ORDER_STATUS_PARTIALLY_RETURNED, _("Partially Refunded")),
    (MERCHANT_ORDER_STATUS_RETURNED, _("Returned")),
    (MERCHANT_ORDER_STATUS_CANCELLED, _("Cancelled")),
    (MERCHANT_ORDER_STATUS_DELETED, _("Deleted")),
    (MERCHANT_ORDER_STATUS_PAY_FAILURE, _("Payment failure")),
    (MERCHANT_ORDER_STATUS_PAY_PARTIAL_FAILURE, _("Payment Partial failure")),
)

MERCHANT_ORDER_STATUSES_LIST = [status[0] for status in MERCHANT_ORDER_STATUSES]
MERCHANT_ORDER_STATUSES_NON_DELETED = list(
    set(MERCHANT_ORDER_STATUSES_LIST) - set([MERCHANT_ORDER_STATUS_DELETED])
)
MERCHANT_ORDER_STATUSES_NON_CANCELLED_DELETED = list(
    set(MERCHANT_ORDER_STATUSES_LIST)
    - set([MERCHANT_ORDER_STATUS_CANCELLED, MERCHANT_ORDER_STATUS_DELETED])
)

MERCHANT_ORDER_STATUSES_TO_HIDE = [
    MERCHANT_ORDER_STATUS_AUTH_FAILURE,
    MERCHANT_ORDER_STATUS_AUTH_CANCELLED,
    MERCHANT_ORDER_STATUS_AUTH_UNKNOWN,
    MERCHANT_ORDER_STATUS_PARTIALLY_PAID,
    MERCHANT_ORDER_STATUS_PAID,
    MERCHANT_ORDER_STATUS_DELETED,
    MERCHANT_ORDER_STATUS_PAY_PARTIAL_FAILURE,
]

MERCHANT_ORDER_VIRTUAL_STATUS_CONFIRMED_OR_MORE = [
    MERCHANT_ORDER_STATUS_CONFIRMED,
    MERCHANT_ORDER_STATUS_SENT,
    MERCHANT_ORDER_STATUS_RECEIVED,
    MERCHANT_ORDER_STATUS_PROCESSED,
    MERCHANT_ORDER_STATUS_FINALIZED,
    MERCHANT_ORDER_STATUS_RETURN_IN_PROGRESS,
    MERCHANT_ORDER_STATUS_PARTIALLY_RETURNED,
    MERCHANT_ORDER_STATUS_RETURNED,
]
MERCHANT_ORDER_VIRTUAL_STATUS_AUTH_SUCCESS_OR_MORE = [
    MERCHANT_ORDER_STATUS_AUTH_SUCCESS,
    MERCHANT_ORDER_STATUS_PARTIALLY_PAID,
    MERCHANT_ORDER_STATUS_PAID,
    MERCHANT_ORDER_STATUS_PROCESSED,
] + MERCHANT_ORDER_VIRTUAL_STATUS_CONFIRMED_OR_MORE

MERCHANT_ORDER_VIRTUAL_STATUSES = (
    (
        MERCHANT_ORDER_VIRTUAL_STATUS_AUTH_SUCCESS_OR_MORE,
        _('Authorized or more')
    ),
    (MERCHANT_ORDER_VIRTUAL_STATUS_CONFIRMED_OR_MORE, _('Confirmed or more')),
)
#                                                      V1    v2
# OrderItem
ORDER_ITEM_STATUS_INITIAL             = "0"    #    |  X  |  X  |  # noqa
ORDER_ITEM_STATUS_EXPIRED             = "10"   #    |  X  |     |  # noqa
ORDER_ITEM_STATUS_PENDING_PAYMENT     = "27"   #    |  X  |     |  # noqa
ORDER_ITEM_STATUS_AUTH_FAILURE        = "30"   #    |  X  |     |  # noqa
ORDER_ITEM_STATUS_AUTH_CANCELLED      = "40"   #    |  X  |     |  # noqa
ORDER_ITEM_STATUS_AUTH_UNKNOWN        = "50"   #    |  X  |     |  # noqa
ORDER_ITEM_STATUS_AUTH_SUCCESS        = "60"   #    |  X  |  X  |  # noqa
ORDER_ITEM_STATUS_PAID                = "70"   #    |  X  |     |  # noqa
ORDER_ITEM_STATUS_CONFIRMED           = "80"   #    |  X  |  X  |  # noqa
ORDER_ITEM_STATUS_PROCESSED           = "85"   #    |     |  X  |  # noqa
ORDER_ITEM_STATUS_SENT                = "90"   #    |  X  |     |  # noqa
ORDER_ITEM_STATUS_RECEIVED            = "100"  #    |  X  |     |  # noqa
ORDER_ITEM_STATUS_FINALIZED           = "110"  #    |  X  |  X  |  # noqa

ORDER_ITEM_STATUS_RETURN_IN_PROGRESS  = "120"  #    |  X  |     |  # noqa
ORDER_ITEM_STATUS_RETURNED            = "160"  #    |  X  |     |  # noqa
                                               #    |     |     |  # noqa
ORDER_ITEM_STATUS_PAY_FAILURE         = "1000" #    |  X  |     |  # noqa
ORDER_ITEM_STATUS_PAY_PARTIAL_FAILURE = "1010" #    |  X  |     |  # noqa
                                               #    |     |     |  # noqa
ORDER_ITEM_STATUS_CANCELLED           = "2000" #    |  X  |     |  # noqa
ORDER_ITEM_STATUS_DELETED             = "3000" #    |  X  |     |  # noqa

# To Add
# ORDER_ITEM_ON_BACKORDER

ORDER_ITEM_VALIDATED_STATUSES = [
    ORDER_ITEM_STATUS_AUTH_SUCCESS,
    ORDER_ITEM_STATUS_PAID,
    ORDER_ITEM_STATUS_CONFIRMED,
    ORDER_ITEM_STATUS_PROCESSED,
    ORDER_ITEM_STATUS_SENT,
    ORDER_ITEM_STATUS_RECEIVED,
    ORDER_ITEM_STATUS_FINALIZED,
]

ORDER_ITEM_STATUSES = (
    (ORDER_ITEM_STATUS_INITIAL, _("Initial")),
    (ORDER_ITEM_STATUS_EXPIRED, _("Expired")),
    (ORDER_ITEM_STATUS_PENDING_PAYMENT, _("Payment Pending")),
    (ORDER_ITEM_STATUS_AUTH_FAILURE, _("Not authorized")),
    (ORDER_ITEM_STATUS_AUTH_CANCELLED, _("Authorization cancelled")),
    (ORDER_ITEM_STATUS_AUTH_UNKNOWN, _("Authorized (?)")),
    (ORDER_ITEM_STATUS_AUTH_SUCCESS, _("Payment Authorized")),
    (ORDER_ITEM_STATUS_PAID, _("Paid")),
    (ORDER_ITEM_STATUS_CONFIRMED, _("Confirmed")),
    (ORDER_ITEM_STATUS_PROCESSED, _("Processed")),
    (ORDER_ITEM_STATUS_SENT, _("Sent")),
    (ORDER_ITEM_STATUS_RECEIVED, _("Received")),
    (ORDER_ITEM_STATUS_FINALIZED, _("Finalized")),
    (ORDER_ITEM_STATUS_RETURN_IN_PROGRESS, _("Return in progress")),
    (ORDER_ITEM_STATUS_RETURNED, _("Returned")),
    (ORDER_ITEM_STATUS_CANCELLED, _("Cancelled")),
    (ORDER_ITEM_STATUS_DELETED, _("Deleted")),
    (ORDER_ITEM_STATUS_PAY_FAILURE, _("Payment failure")),
    (ORDER_ITEM_STATUS_PAY_PARTIAL_FAILURE, _("Payment Partial failure")),
)

ORDER_ITEM_STATUSES_LIST = [status[0] for status in ORDER_ITEM_STATUSES]

ORDER_ITEM_STATUSES_NON_DELETED = list(
    set(ORDER_ITEM_STATUSES_LIST) - set([ORDER_ITEM_STATUS_DELETED])
)
ORDER_ITEM_STATUSES_NON_CANCELLED_DELETED = list(
    set(ORDER_ITEM_STATUSES_LIST)
    - set([ORDER_ITEM_STATUS_CANCELLED, ORDER_ITEM_STATUS_DELETED])
)
