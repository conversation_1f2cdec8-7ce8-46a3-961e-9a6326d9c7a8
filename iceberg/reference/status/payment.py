# -*- coding: utf-8 -*-

from django.utils.translation import gettext_lazy as _

PAYMENT_STATUS_INITIAL = "0"
PAYMENT_STATUS_AUTH_SUCCESS = "60"
PAYMENT_STATUS_PENDING_AUTH = "61"  # When 3D secure needed
PAYMENT_STATUS_PENDING = "77"
PAYMENT_STATUS_COLLECTED = "80"
PAYMENT_STATUS_PARTIALLY_COLLECTED = "85"
PAYMENT_STATUS_REFUNDED = "90"
PAYMENT_STATUS_PARTIALLY_REFUNDED = "100"
PAYMENT_STATUS_PAY_FAILURE = "1000"
PAYMENT_STATUS_CANCELLED = "2000"  # Deprecated


PAYMENT_STATUS_VALID_FOR_COMMISSION = (
    PAYMENT_STATUS_COLLECTED,
    PAYMENT_STATUS_PARTIALLY_COLLECTED,
    PAYMENT_STATUS_REFUNDED,
    PAYMENT_STATUS_PARTIALLY_REFUNDED,
)

PAYMENT_STATUSES = (
    (PAYMENT_STATUS_INITIAL, _("Initial")),
    (PAYMENT_STATUS_PENDING, _("Pending")),
    (PAYMENT_STATUS_AUTH_SUCCESS, _("Payment Authorized")),
    (PAYMENT_STATUS_PENDING_AUTH, _("Payment Pending Authorization")),
    (PAYMENT_STATUS_COLLECTED, _("Collected")),
    (PAYMENT_STATUS_PARTIALLY_COLLECTED, _("Partially Collected")),
    (PAYMENT_STATUS_REFUNDED, _("Refunded")),
    (PAYMENT_STATUS_PARTIALLY_REFUNDED, _("Partially Refunded")),
    (PAYMENT_STATUS_CANCELLED, _("Cancelled")),
    (PAYMENT_STATUS_PAY_FAILURE, _("Collect failure")),
)
PAYMENT_STATUSES_LIST = [status[0] for status in PAYMENT_STATUSES]

PAYMENT_STATUSES_NON_PENDING_AUTH = list(
    set(PAYMENT_STATUSES_LIST) - set([PAYMENT_STATUS_PENDING_AUTH])
)


PAYMENT_CONSISTENCY_UNKNOWN = "consistency_unknown"
PAYMENT_INCONSISTENT = "inconsistent"
PAYMENT_CONSISTENT = "consistent"

PAYMENT_CONSISTENCY_STATUSES = (
    (PAYMENT_CONSISTENCY_UNKNOWN, _("Unknown Consistency")),
    (PAYMENT_INCONSISTENT, _("Inconsistent")),
    (PAYMENT_CONSISTENT, _("Consistent")),
)


PAYMENT_INCONSISTENCY_OPEN = "open"
PAYMENT_INCONSISTENCY_RESOLVED = "resolved"
PAYMENT_INCONSISTENCY_IGNORED = "ignored"


PAYMENT_INCONSISTENCY_STATUSES = (
    (PAYMENT_INCONSISTENCY_OPEN, _("Open")),
    (PAYMENT_INCONSISTENCY_RESOLVED, _("Resolved")),
    (PAYMENT_INCONSISTENCY_IGNORED, _("Ignored")),
)
