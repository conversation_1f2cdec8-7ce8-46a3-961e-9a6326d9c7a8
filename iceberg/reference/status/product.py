# -*- coding: utf-8 -*-

from django.utils.translation import gettext_lazy as _

# Products
PRODUCT_STATUS_DRAFT = "draft"
PRODUCT_STATUS_PENDING = "pending"
PRODUCT_STATUS_REJECTED = "rejected"
PRODUCT_STATUS_ACTIVE = "active"
PRODUCT_STATUS_INACTIVE = "inactive"
PRODUCT_STATUS_DELETED = "deleted"

# Offer
PRODUCT_OFFER_STATUS_INACTIVE = "inactive"
PRODUCT_OFFER_STATUS_DRAFT = "draft"
PRODUCT_OFFER_STATUS_PENDING = "pending"
PRODUCT_OFFER_STATUS_ACTIVE = "active"
PRODUCT_OFFER_STATUS_BANNED = "banned"
PRODUCT_OFFER_STATUS_TRASHED = "trashed"
PRODUCT_OFFER_STATUS_DELETED = "deleted"

# Variation
PRODUCT_VARIATION_STATUS_INACTIVE = "inactive"
PRODUCT_VARIATION_STATUS_DRAFT = "draft"
PRODUCT_VARIATION_STATUS_ACTIVE = "active"
PRODUCT_VARIATION_STATUS_TRASHED = "trashed"
PRODUCT_VARIATION_STATUS_DELETED = "deleted"


PRODUCT_STATUSES = (
    (PRODUCT_STATUS_DRAFT, _("Draft product")),
    (PRODUCT_STATUS_PENDING, _("Pending review")),
    (PRODUCT_STATUS_REJECTED, _("Rejected product")),
    (PRODUCT_STATUS_ACTIVE, _("Active product")),
    (PRODUCT_STATUS_INACTIVE, _("Inactive product")),
    (PRODUCT_STATUS_DELETED, _("Deleted product")),
)

PRODUCT_STATUSES_LIST_NEW = [status for status in PRODUCT_STATUSES]
PRODUCT_STATUSES_LIST = [status[0] for status in PRODUCT_STATUSES]

PRODUCT_STATUSES_NON_DELETED = list(
    set(PRODUCT_STATUSES_LIST) - set([PRODUCT_STATUS_DELETED])
)


PRODUCT_OFFER_STATUSES = (
    (PRODUCT_OFFER_STATUS_INACTIVE, _("Inactive offer")),
    (PRODUCT_OFFER_STATUS_DRAFT, _("Draft mode")),
    (PRODUCT_OFFER_STATUS_PENDING, _("Pending review")),
    (PRODUCT_OFFER_STATUS_ACTIVE, _("Active offer")),
    (PRODUCT_OFFER_STATUS_BANNED, _("Banned offer (by staff)")),
    (
        PRODUCT_OFFER_STATUS_TRASHED,
        _('"Trashed" by staff or merchant (visible in inventory)'),
    ),
    (
        PRODUCT_OFFER_STATUS_DELETED,
        _('"Deleted" by staff or merchant (not visible even in inventory)'),
    ),
)

PRODUCT_OFFER_STATUSES_LIST_NEW = [status for status in PRODUCT_OFFER_STATUSES]
PRODUCT_OFFER_STATUSES_LIST = [status[0] for status in PRODUCT_OFFER_STATUSES]

PRODUCT_OFFER_STATUSES_NON_DELETED = list(
    set(PRODUCT_OFFER_STATUSES_LIST) - set([PRODUCT_OFFER_STATUS_DELETED])
)
PRODUCT_OFFER_STATUSES_NON_TRASHED_DELETED = list(
    set(PRODUCT_OFFER_STATUSES_LIST)
    - set([PRODUCT_OFFER_STATUS_DELETED, PRODUCT_OFFER_STATUS_TRASHED])
)
PRODUCT_VARIATION_STATUSES = (
    (PRODUCT_VARIATION_STATUS_INACTIVE, _("Inactive variation")),
    (PRODUCT_VARIATION_STATUS_DRAFT, _("Draft mode")),
    (PRODUCT_VARIATION_STATUS_ACTIVE, _("Active variation")),
    (
        PRODUCT_VARIATION_STATUS_TRASHED,
        _('"Trashed" by staff or merchant (visible in inventory)'),
    ),
    (
        PRODUCT_VARIATION_STATUS_DELETED,
        _('"Deleted" by staff or merchant (not visible even in inventory)'),
    ),
)
PRODUCT_VARIATION_STATUSES_LIST = [status[0] for status in PRODUCT_VARIATION_STATUSES]

PRODUCT_VARIATION_STATUSES_NON_DELETED = list(
    set(PRODUCT_VARIATION_STATUSES_LIST) - set([PRODUCT_VARIATION_STATUS_DELETED])
)
PRODUCT_VARIATION_STATUSES_NON_TRASHED_DELETED = list(
    set(PRODUCT_VARIATION_STATUSES_LIST)
    - set([PRODUCT_VARIATION_STATUS_DELETED, PRODUCT_VARIATION_STATUS_TRASHED])
)

PRODUCT_BRAND_STATUS_ACTIVE = "active"
PRODUCT_BRAND_STATUS_DELETED = "deleted"

PRODUCT_BRAND_STATUSES = (
    (PRODUCT_BRAND_STATUS_ACTIVE, _("Active")),
    (PRODUCT_BRAND_STATUS_DELETED, _("Deleted")),
)
PRODUCT_BRAND_STATUSES_LIST = [status[0] for status in PRODUCT_BRAND_STATUSES]

PRODUCT_BRAND_STATUSES_NON_DELETED = list(
    set(PRODUCT_BRAND_STATUSES_LIST) - set([PRODUCT_BRAND_STATUS_DELETED])
)
