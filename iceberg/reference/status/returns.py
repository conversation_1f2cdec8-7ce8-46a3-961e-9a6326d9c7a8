# -*- coding: utf-8 -*-

from django.utils.translation import gettext_lazy as _

RETURN_OPEN = "open"
RETURN_ACCEPTED = "accepted"
RETURN_CLOSED_BY_BUYER = "closed_by_buyer"
RETURN_CLOSED_BY_SELLER = "closed_by_seller"
RETURN_PACKAGE_RECEIVED = "package_received"
RETURN_CANCELLED = "cancelled"

RETURNS_STATUSES = (
    (RETURN_OPEN, _("Waiting for merchant approval")),
    (RETURN_ACCEPTED, _("Accepted, waiting for package")),
    (RETURN_CLOSED_BY_BUYER, _("Closed by buyer")),
    (RETURN_CLOSED_BY_SELLER, _("Closed by seller")),
    (RETURN_PACKAGE_RECEIVED, _("Package Received")),
    (RETURN_CANCELLED, _("Cancelled")),
)

# Refunds
REFUND_PENDING = "pending"
REFUND_TO_PROCESS = "to_process"
# not "to_manually_process" because max length of transition name is 16 chars.
# and it takes 19. :'(
REFUND_TO_PROCESS_MANUALLY = "manual_process"
REFUND_COMPLETE = "complete"
REFUND_CANCELLED = "cancelled"
REFUND_FAILED = "failed"
REFUND_DELETED = "deleted"

REFUNDS_STATUSES = (
    # Refunds created manually will be processed 2 hours after creation.
    # This allows cancellation
    (REFUND_PENDING, _("Pending")),
    (REFUND_TO_PROCESS, _("To Process")),
    (REFUND_TO_PROCESS_MANUALLY, _("To Process Manually")),
    (REFUND_COMPLETE, _("Complete")),
    (REFUND_CANCELLED, _("Cancelled")),
    (REFUND_FAILED, _("Failed")),
    (REFUND_DELETED, _("Deleted")),
)

REFUND_STATUSES_LIST = [status[0] for status in REFUNDS_STATUSES]

REFUND_STATUSES_NON_DELETED = list(set(REFUND_STATUSES_LIST) - set([REFUND_DELETED]))
REFUND_STATUSES_NON_CANCELLED_DELETED = list(
    set(REFUND_STATUSES_LIST) - set([REFUND_CANCELLED, REFUND_DELETED])
)
