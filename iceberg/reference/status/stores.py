# -*- coding: utf-8 -*-

from django.utils.translation import gettext_lazy as _

# ======================================================
# STORE APP
# ======================================================
MERCHANT_STATUS_INACTIVE = "0"
MERCHANT_STATUS_PENDING = "5"
MERCHANT_STATUS_ACTIVE = "10"
MERCHANT_STATUS_PAUSED = "20"
MERCHANT_STATUS_STOPPED = "30"
MERCHANT_STATUS_DELETED = "90"

MERCHANT_STATUSES = (
    (MERCHANT_STATUS_INACTIVE, _("Inactive")),
    (MERCHANT_STATUS_PENDING, _("Pending Review")),
    (MERCHANT_STATUS_ACTIVE, _("Active")),
    (MERCHANT_STATUS_PAUSED, _("Paused")),
    (MERCHANT_STATUS_STOPPED, _("Stopped")),
    (MERCHANT_STATUS_DELETED, _("Deleted")),
)
MERCHANT_STATUSES_LIST = [status[0] for status in MERCHANT_STATUSES]

MERCHANT_STATUSES_NON_DELETED = list(
    set(MERCHANT_STATUSES_LIST) - set([MERCHANT_STATUS_DELETED])
)
