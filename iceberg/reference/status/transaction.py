# -*- coding: utf-8 -*-
from django.utils.translation import gettext_lazy as _

""" Transactions are reflection of sells and other money transfers involved
in marketplace running. They are bound to orders, commissions, promotions,
refunds money cash-out operations.

Real life transactions are never atomic. However fast they may be, there might
occur somme issuess, messages etc. This file contains constants telling a
little more about existing transaction statuses.

These statuses APPLY TO BOTH AbstractSubTransaction and Transaction models.

A successful transaction flow goes from PENDING state at transaction creation
to PROCESSING state when system is actually taking actions to handle it.
If transaction requires manual intervention, it will last the necessary time on
this PROCESSING state.
When every-thing is completed and no error occurred, the transaction goes
from PROCESSING state to CONFIRMED state.

Now let's see the failure case :

When an issue occurs, the transaction doesn't go to the CONFIRMED state but
to the ATTENTION_REQUIRED state which is equivalent to a PROCESSING state, but
displayed in a different way on dashboards. This state lets us filtering
transactions that require attention using this specific state.

When Order is in ATTENTION REQUIRED state, it contains a description of last
encountered issue or warning message and still features the possibility to
transit into one of the CONFIRMED or DELETED states when problem is resolved.

The DELETED state is used whenever a pending transaction is considered
invalid or must be cancelled. In such case, the marketplace can choose to
remove the conflicting transaction.

A transaction that is not in CONFIRMED state is not counted in its balance
however it still appears in upcoming operations of the balance.

"""

TRANSACTION_STATUS_INITIAL = "initial"
TRANSACTION_STATUS_PENDING = "pending"
# moderation required is almost pending. It's considered in upcomming amounts
# BUT is never handled by auto-processing tasks
TRANSACTION_STATUS_MODERATION_REQUIRED = "moderation_required"
TRANSACTION_STATUS_PROCESSING = "processing"
# attention required is still a pending transaction but something requires
# user attention (long action delays, failed confirmation, etc).
TRANSACTION_STATUS_ATTENTION_REQUIRED = "attention_required"
TRANSACTION_STATUS_CONFIRMED = "confirmed"
TRANSACTION_STATUS_DELETED = "deleted"

TRANSACTION_STATUSES = (
    (TRANSACTION_STATUS_INITIAL, _("Initial")),
    (TRANSACTION_STATUS_PENDING, _("Pending")),
    (TRANSACTION_STATUS_MODERATION_REQUIRED, _("Moderation required")),
    (TRANSACTION_STATUS_PROCESSING, _("Processing")),
    (TRANSACTION_STATUS_ATTENTION_REQUIRED, _("Warning")),
    (TRANSACTION_STATUS_CONFIRMED, _("Confirmed")),
    (TRANSACTION_STATUS_DELETED, _("Removed")),
)

TRANSACTION_IN_PROGRESS_STATUSES = (
    TRANSACTION_STATUS_PENDING,
    TRANSACTION_STATUS_MODERATION_REQUIRED,
    TRANSACTION_STATUS_PROCESSING,
    TRANSACTION_STATUS_ATTENTION_REQUIRED,
)
