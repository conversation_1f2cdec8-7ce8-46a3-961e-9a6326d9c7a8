from django.utils.translation import gettext_lazy as _

"""
those statuses are for Webhook
"""
TO_VALIDATE, ACTIVE, PAUSED, DELETED = (
    "to_validate",
    "active",
    "paused",
    "deleted",
)

WEBHOOK_STATUSES = (
    (TO_VALIDATE, _("Inactive")),
    (ACTIVE, _("Active")),
    (PAUSED, _("Paused")),
    (DELETED, _("Deleted")),
)


"""
those statuses are for WebhookTrigger
"""

IN_PROGRESS, SUCCEEDED, FAILED, ABORTED = [
    "in_progress",
    "succeeded",
    "failed",
    "aborted",
]

WEBHOOK_TRIGGER_STATUSES = (
    (IN_PROGRESS, _("In progress")),
    (SUCCEEDED, _("Succeeded")),
    (FAILED, _("Failed")),
    (ABORTED, _("Aborted")),
)
