# -*- coding: utf-8 -*-


from django.utils.translation import gettext_lazy as _

TRANSACTION_SALE = 1  # A sell: Product, etc.
TRANSACTION_REFUND = 2  # Refund from a sell

TRANSACTION_LICENSE = 4  # Marketplace License
TRANSACTION_MARKETING_OP = 5  # Ex: Newsletter, etc.

TRANSACTION_CASHOUT = 6  # If Store ask for payment at the end of the month

# With payment term, use a specific transaction for commissions with:
# - a +x euros subtransaction for app
# a -x euros subtransaction for merchant
TRANSACTION_COMMISSION = 10
TRANSACTION_COMMISSION_REFUND = 11

# When we have to transfer money to a store because something bad happen before
# for example
TRANSACTION_STORE_ADJUSTMENT = 15

# When an application (operator) finances a promotion (discount), this kind of
# transaction is created to charge the application the amount of the discount
TRANSACTION_APPLICATION_PROMOTION = 20
TRANSACTION_APPLICATION_PROMOTION_REFUND = 21

TRANSACTION_APPLICATION_VOUCHER_USE = 30
TRANSACTION_APPLICATION_VOUCHER_REFUND = 31

# In EU, the marketplace can be responsible for the sale,
# leading to 2 kinds of sales :
# marketplace sale
TRANSACTION_OPERATOR_SALE = 40
TRANSACTION_OPERATOR_SALE_REFUND = 41
# internal sale between the merchant and the marketplace
TRANSACTION_INTERNAL_SALE = 50
TRANSACTION_INTERNAL_SALE_REFUND = 51

TRANSACTION_CODES = (
    (TRANSACTION_SALE, _("Merchant sale")),
    (TRANSACTION_REFUND, _("Merchant sale refund")),
    (TRANSACTION_LICENSE, _("License")),
    (TRANSACTION_MARKETING_OP, _("Marketing operation")),
    (TRANSACTION_CASHOUT, _("Cashout")),
    (TRANSACTION_STORE_ADJUSTMENT, _("Merchant adjustment")),
    (TRANSACTION_APPLICATION_PROMOTION, _("Marketplace promotion")),
    (TRANSACTION_APPLICATION_PROMOTION_REFUND, _("Marketplace promotion refund")),
    (TRANSACTION_APPLICATION_VOUCHER_USE, _("Marketplace voucher use")),
    (TRANSACTION_APPLICATION_VOUCHER_REFUND, _("Marketplace voucher refund")),
    (TRANSACTION_COMMISSION, _("Commission")),
    (TRANSACTION_COMMISSION_REFUND, _("Commission refund")),
    (TRANSACTION_OPERATOR_SALE, _("Marketplace sale")),
    (TRANSACTION_OPERATOR_SALE_REFUND, _("Marketplace sale refund")),
    (TRANSACTION_INTERNAL_SALE, _("Merchant to marketplace sale")),
    (TRANSACTION_INTERNAL_SALE_REFUND, _("Merchant to marketplace sale refund")),
)

MANUALLY_CONFIRMABLE_TRANSACTIONS = (
    TRANSACTION_CASHOUT,
    TRANSACTION_COMMISSION,
    TRANSACTION_COMMISSION_REFUND,
    TRANSACTION_SALE,
    TRANSACTION_REFUND,
    TRANSACTION_OPERATOR_SALE,
    TRANSACTION_OPERATOR_SALE_REFUND,
    TRANSACTION_INTERNAL_SALE,
    TRANSACTION_INTERNAL_SALE_REFUND,
    TRANSACTION_APPLICATION_PROMOTION,
    TRANSACTION_APPLICATION_PROMOTION_REFUND,
    TRANSACTION_APPLICATION_VOUCHER_USE,
    TRANSACTION_APPLICATION_VOUCHER_REFUND,
)

MERCHANT_REQUIRED_TRANSACTIONS = (
    TRANSACTION_SALE,
    TRANSACTION_REFUND,
    TRANSACTION_LICENSE,
    TRANSACTION_MARKETING_OP,
    TRANSACTION_STORE_ADJUSTMENT,
    TRANSACTION_APPLICATION_PROMOTION_REFUND,
    TRANSACTION_INTERNAL_SALE,
    TRANSACTION_INTERNAL_SALE_REFUND,
)

TRANSACTION_CODES_KEYS = tuple(code for code, _ in TRANSACTION_CODES)
INVOICEABLE_TRANSACTIONS = tuple(
    set(TRANSACTION_CODES_KEYS)
    - {
        TRANSACTION_CASHOUT,
        TRANSACTION_STORE_ADJUSTMENT,
    }
)

# money comes from the outside into IZBERG
INBOUND_TRANSACTIONS = (
    TRANSACTION_SALE,
    TRANSACTION_APPLICATION_VOUCHER_REFUND,
    TRANSACTION_OPERATOR_SALE,
)

# money goes from IZBERG to the outside
OUTBOUND_TRANSACTIONS = (
    TRANSACTION_REFUND,
    TRANSACTION_CASHOUT,
    TRANSACTION_APPLICATION_VOUCHER_USE,
    TRANSACTION_OPERATOR_SALE_REFUND,
)

# money goes from the App balance to the Merchant balance
APP_TO_MERCHANT_TRANSACTIONS = (
    TRANSACTION_COMMISSION_REFUND,
    TRANSACTION_APPLICATION_PROMOTION,
    TRANSACTION_INTERNAL_SALE,
)

# money goes from the Merchant balance to the App balance
MERCHANT_TO_APP_TRANSACTIONS = (
    TRANSACTION_LICENSE,
    TRANSACTION_MARKETING_OP,
    TRANSACTION_COMMISSION,
    TRANSACTION_APPLICATION_PROMOTION_REFUND,
    TRANSACTION_INTERNAL_SALE_REFUND,
)


NEGATIVE_TRANSACTIONS = (
    TRANSACTION_REFUND,
    TRANSACTION_COMMISSION_REFUND,
    TRANSACTION_CASHOUT,
    TRANSACTION_APPLICATION_VOUCHER_USE,
    TRANSACTION_OPERATOR_SALE_REFUND,
    TRANSACTION_INTERNAL_SALE_REFUND,
)

POSITIVE_TRANSACTIONS = (
    TRANSACTION_SALE,
    TRANSACTION_LICENSE,
    TRANSACTION_MARKETING_OP,
    TRANSACTION_COMMISSION,
    TRANSACTION_APPLICATION_PROMOTION,
    TRANSACTION_APPLICATION_PROMOTION_REFUND,
    TRANSACTION_APPLICATION_VOUCHER_REFUND,
    TRANSACTION_OPERATOR_SALE,
    TRANSACTION_INTERNAL_SALE,
)

# We use the description of the transaction as a way to differentiate between
# shipping or items
# The US 3800 is open to resolve this issue more robustly
TRANSACTION_APPLICATION_PROMOTION_DESC_ITEMS = "Marketplace promotion on items"
TRANSACTION_APPLICATION_PROMOTION_DESC_SHIPPING = "Marketplace promotion on shipping"
TRANSACTION_APPLICATION_PROMOTION_REFUND_DESC_ITEMS = (
    "Marketplace promotion refund on items"
)
