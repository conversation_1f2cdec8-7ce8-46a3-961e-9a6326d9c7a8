{% block content %}

<head>
  <title>Global Stats</title>
  <style>
    html {
      font-family: 'Arial', sans-serif;
      color: #333;
    }

    /* Styling the table */
    table {
      width: 100%;
      border-collapse: collapse;
      margin: 20px 0;
      font-size: 16px;
      text-align: left;
      background-color: #f9f9f9;
      border: 1px solid #ddd;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    /* Styling the table headers */
    thead th {
      background-color: #3a996d;
      /* Light blue color */
      color: white;
      padding: 12px 15px;
      text-transform: uppercase;
      letter-spacing: 0.05em;
      cursor: pointer;
      -webkit-user-select: none;
      /* Chrome, Safari, Opera */
      -moz-user-select: none;
      /* Firefox */
      -ms-user-select: none;
      /* Internet Explorer/Edge */
      user-select: none;
      /* Standard */
    }

    thead th:hover {
      background-color: #33815d;
    }

    thead th:active {
      background-color: #256849;
    }

    /* Styling the table rows */
    tbody tr {
      border-bottom: 1px solid #ddd;
    }

    tbody tr:nth-of-type(even) {
      background-color: #f2f2f2;
      /* Light grey alternate rows */
    }

    /* Styling the table cells */
    td {
      padding: 12px 15px;
      color: #333;
    }

    /* Hover effect on rows */
    tbody tr:hover {
      background-color: #f1f1f1;
      cursor: pointer;
    }

    /* Styling the table border */
    table,
    th,
    td {
      border: none;
    }

    /* Container for status label and button */
    .status-container {
      display: flex;
      align-items: center;
    }
    
    /* Styling the status text */
    .status-text {
      padding: 9px;
      width: 55px;
      color: #333;
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
      font-size: 16px;
      border: 1px solid #ccc;
      background: repeating-linear-gradient(
        135deg, /* Angle of the stripes */
        #e1e1e1, /* Color of the first stripe */
        #e1e1e1 10px, /* Height of the first stripe */
        #f1f1f1 10px, /* Color of the gap */
        #f1f1f1 20px /* Height of the gap */
      );
    }

    .status-text-syncing {
      color: #3a996d;
    }

    .status-text-paused {
      color: #ea9336
    }
    
    /* Styling the button */
    button {
      padding: 10px 20px;
      background-color: #3a996d;
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 16px;
      cursor: pointer;
      transition: background-color 0.3s ease;
    }

    .status-button {
      border-top-left-radius: 0px;
      border-bottom-left-radius: 0px;
    }
    
    button:hover {
      background-color: #33815d;
    }
    
    button:active {
      background-color: #215e41;
    }

    .enabled {
      background-color: #27684a;
    }

    .disabled {
      background-color: #8a8a8a;
    }
     
    .paused {
      background-color: #ea9336;
    }

    .paused:hover {
      background-color: #d5842d;
    }
    
    .paused:active {
      background-color: #bd7323;
    }
    

    /* Styling the number input */
    input[type="number"] {
      width: 80px;
      padding: 5px;
      font-size: 16px;
      border: 1px solid #ddd;
      border-radius: 4px;
      background-color: #f9f9f9;
      color: #333;
      box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.1);
      transition: border-color 0.3s ease, box-shadow 0.3s ease;
    }

    /* Hover effect */
    input[type="number"]:hover {
      border-color: #3a996d;
    }

    /* Focus effect */
    input[type="number"]:focus {
      border-color: #3a996d;
      box-shadow: 0 0 5px #256849;
      outline: none;
    }

    /* Input with active arrow controls */
    input[type="number"]::-webkit-inner-spin-button,
    input[type="number"]::-webkit-outer-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }

    #app-name-filter {
      padding: 4px;
      margin-top: 3px;
      border: 1px solid #ccc;
      border-radius: 4px;
      font-size: 14px;
      outline: none;
      transition: border-color 0.3s ease, box-shadow 0.3s ease;
    }
    
    #app-name-filter:focus {
      border-color: #4CAF50;
      box-shadow: 0 0 5px rgba(76, 175, 80, 0.5);
    }
    
  </style>
</head>
<html>

<body>
  <h2>Product Channel Queues Manager</h2>

  <!-- Table to display the data -->
  <table id="data-table" border="1">
    <thead>
      <tr>
        <th onclick="sortTable(0)">App ID&#x25B2;&#x25BC;</th>
        <th>
          App Name&#x25B2;&#x25BC;
          <br>
          <input type="text" id="app-name-filter" placeholder="Filter by App Name" oninput="filterByAppName()">
        </th>
        <th onclick="sortTable(2)">Channel ID &#x25B2;&#x25BC;</th>
        <th onclick="sortTable(3)">Item Count &#x25B2;&#x25BC;</th>
        <th onclick="sortTable(4)">Channel Priority &#x25B2;&#x25BC;</th>
        <th onclick="sortTable(5)">Latency (sec) &#x25B2;&#x25BC;</th>
        <th onclick="sortTable(6)">Manual Priority &#x25B2;&#x25BC;</th>
        <th onclick="sortTable(7)">Status &#x25B2;&#x25BC;</th>
      </tr>
    </thead>
    <tbody>
      <!-- Data will be inserted dynamically here -->
    </tbody>
  </table>

  <!-- Refresh button -->
  <div>
    <button onclick="autoRefresh()" id="autoRefresh" class="enabled">Auto reload</button>
    <button onclick="refreshTable()">Refresh</button>
    <button onclick="updateManualPrio()">Update Manual Priorities</button>
  </div>
  <div style="margin-top: 5px;">
  <input type="number" name="refreshCD" id="refreshCD" onchange="updateCD()" value="15" max="1000" min="2">
  <label for="refreshCD" >Refresh each X.sec</label>
  </div>
  <p id="content"></p>

  <script>
    const PREFIX = window.location.origin + "/v1/product_channel";
    let channels_to_update = {};
    let doRefresh = true;
    let refresh_cd = 15

    function updateCD() {
      refresh_cd = parseInt(document.getElementById("refreshCD").value)
      console.log("refresh index is now at ", refresh_cd)
    }

    function r() {
      setTimeout(() => {
        if (doRefresh) {
          refreshTable();
        }
        r();
      }, refresh_cd * 1000);
    }

    function GET(path) {
      return fetch(PREFIX + path)
        .then(response => response.json());
    }

    function update_channel_manual_priority(event) {
      channels_to_update[event.target.id] = +event.target.value;
    }

    function autoRefresh() {
      doRefresh = !doRefresh
      document.getElementById(`autoRefresh`).setAttribute("class", doRefresh? "enabled": "disabled")
    }

    async function updateManualPrio(event) {
      let updates_promises = [];
      for (const [id, manual_p] of Object.entries(channels_to_update)) {
        updates_promises.push(fetch(PREFIX + `/${id}/`, {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ "manual_priority": manual_p })
        }));
      }
      channels_to_update = {};
      await Promise.all(updates_promises);
      refreshTable();
    }

    async function changeChannelStatus(id, s) {
      const status = s !== "paused" ? "paused" : "to_sync";
      await fetch(PREFIX + `/${id}/`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ "status": status })
      });
      refreshTable();
    }

    function compute_latency(date_str) {
      const targetDate = new Date(date_str);
      const currentDate = new Date();
      return Math.floor((currentDate - targetDate) / 1000);
    }

    function formatDuration(seconds) {
      const hours = Math.floor(seconds / 3600);
      seconds %= 3600;
      const minutes = Math.floor(seconds / 60);
      seconds %= 60;
      let readableTime = '';
      if (hours > 0) {
        readableTime += hours + ' h ';
      }
      if (minutes > 0) {
        readableTime += minutes + ' m ';
      }
      if (seconds > 0 || readableTime === '') {
        readableTime += seconds + ' s ';
      }
      return readableTime.trim();
    }

    let current_sort_index = 0;
    let current_sort = "asc";

    function buildTable(data) {
      const tableBody = document.querySelector("#data-table tbody");
      tableBody.innerHTML = "";

      for (const [priority, priorityData] of Object.entries(data)) {
        const channels = priorityData.channels;

        for (const [channelId, channelData] of Object.entries(channels)) {
          const row = document.createElement("tr");

          row.innerHTML = `
            <td>${channelData.application_id}</td>
            <td>${channelData.application_name}</td>
            <td>${channelId}</td>
            <td>${channelData.items_count}</td>
            <td>${priority}</td>
            <td>${formatDuration(compute_latency(channelData.oldest_sync_request))}</td>
            <td><input type="number" name="" id="${channelId}" value="${channelData.manual_priority}" max="10" min="0"></td>
            <td>
              <div class="status-container">
                <span class="status-text status-text-${channelData.status}" id="status-label-${channelId}">${channelData.status}</span>
                <button onclick="changeChannelStatus('${channelId}', '${channelData.status}')" class="status-button ${channelData.status}">${channelData.status !== "paused"? "Stop":"Sync"}</button>
              </div>
            </td>
          `;

          tableBody.appendChild(row);
          document.getElementById(`${channelId}`).addEventListener('input', update_channel_manual_priority);
        }
      }
      sortTable(current_sort_index, true);
      filterByAppName()
    }

    function refreshTable() {
      GET("/admin/global_stats").then(buildTable);
    }

    function sortTable(columnIndex, nr = false) {
      current_sort_index = columnIndex;

      const table = document.getElementById("data-table");
      const tbody = table.tBodies[0];
      const rows = Array.from(tbody.querySelectorAll("tr"));

      const isNumber = columnIndex === 0 || columnIndex === 2 || columnIndex === 3 || columnIndex === 5;
      const isInput = columnIndex === 6;
      let isAscending = current_sort === "asc";

      if (!nr) {
        isAscending = !isAscending;
      }

      rows.sort((rowA, rowB) => {
        let cellA, cellB, channelIdA, channelIdB;

        if (isInput) {
          cellA = rowA.cells[columnIndex].querySelector('input').value;
          cellB = rowB.cells[columnIndex].querySelector('input').value;
        } else {
          cellA = rowA.cells[columnIndex].innerText;
          cellB = rowB.cells[columnIndex].innerText;
        }

        channelIdA = rowA.cells[2].innerText;
        channelIdB = rowB.cells[2].innerText;

        if (isNumber || isInput) {
          const numA = parseFloat(cellA.replace(/[^\d.-]/g, '')) || 0;
          const numB = parseFloat(cellB.replace(/[^\d.-]/g, '')) || 0;

          if (numA === numB) {
            return isAscending ? channelIdA.localeCompare(channelIdB) : channelIdB.localeCompare(channelIdA);
          }
          return isAscending ? numA - numB : numB - numA;
        } else {
          if (cellA === cellB) {
            return isAscending ? channelIdA.localeCompare(channelIdB) : channelIdB.localeCompare(channelIdA);
          }
          return isAscending ? cellA.localeCompare(cellB) : cellB.localeCompare(cellA);
        }
      });

      rows.forEach(row => tbody.appendChild(row));

      current_sort = isAscending ? "asc" : "desc";
      table.setAttribute("data-sort-direction", current_sort);
    }

    function filterByAppName() {
      const input = document.getElementById("app-name-filter").value.toLowerCase();
      const table = document.getElementById("data-table");
      const tbody = table.tBodies[0];
      const rows = Array.from(tbody.querySelectorAll("tr"));

      rows.forEach(row => {
        const appName = row.cells[1].innerText.toLowerCase();
        row.style.display = appName.includes(input) ? "" : "none";
      });
    }

    refreshTable();
    r();
  </script>
</body>

</html>

{% endblock %}