Logical Replication + new cluster 
=================================


## KMS key

Créer une nouvelle clé KMS de tpe Symmetric avec comme alias:

`izb/aurora-cluster-XXX`

> XXX étant environment: ci, sandbox, prod. 

Les permissions sont:
- AWSServiceRoleForRDS
- rds-monitoring-role

Les administrateurs de la clés sont:
- les devs
- Vincent?

> Permettre aux administrateurs de la clé de la supprimer.

Ajouté le tag suivant:
- izberg:service -> izberg-cluster-ci







## DB cluster parameter group

**PLUTON**:

nom:
- postgresql-15-parameter-group-XXX
> XXX étant environment: ci, sandbox, prod. 

Paramter group family:
- aurora-postgresql15

Group name:
- DB cluster parameter group

Description:
- List of parameters changed for the logical replication: logical_replication, max_replication_slots, max_logical_replication_workers, max_worker_processes, max_wal_senders



Valeur à modifier:
- max_replication_slots = 28 (`GREATEST(${DBInstanceVCPU*2-4},6)`)
- max_logical_replication_workers = 28 (`GREATEST(${DBInstanceVCPU*2-4},6)`)
- max_worker_processes = 64   (`GREATEST(${DBInstanceVCPU*2},8)`)

- max_wal_senders = 30 (`GREATEST(${DBInstanceVCPU*2},8)`)
- logical_replication = 1



| Param name                      | CI | Sandbox | Prod |
|---------------------------------|----|---------|------|
| max_replication_slots           | 6  | 10      | 64   |
| max_logical_replication_workers | 6  | 10      | 64   |
| max_worker_processes            | 8  | 32      | 96   |
| max_wal_senders                 | 8  | 20      | 72   |
| logical_replication             | 1  | 1       | 1    |




**OLD CLUSTER**:

nom:
- postgres-13-parameter-group-xxx-old
> XXX étant environment: ci, sandbox, prod. 

type:
- Custom

Resource type:
- DB cluster

Valeur à modifier:
- logical_replication = 1
- max_replication_slots = 28
- max_wal_senders = 30
> Valeur sujet à modification en sandbox et prod
> 
> ATTENTION: Il faut ABSOLUMENT associer ce parameter group à l'ancien cluster.
> MAIS cela va entrainer un arrêt de service de quelques minutes
> 






## Subnet

VPC ID:
- internal (vpc-9388baf8)

Subnet settings:

name:
- aurora-pg-private-subnet-sandbox-1a
- aurora-pg-private-subnet-sandbox-1b
- aurora-pg-private-subnet-sandbox-1c

Availability Zone
- Prendre en fonction du nom: 1a = eu-west-1a


Ipv4 CIDR block:
**Sandbox:**
- **********/24
- **********/24
- **********/24


**production:**
- **********/24
- **********/24
- **********/24

tags:
- Name: aurora-pg-private-subnet-sandbox-1a
- izberg:logical-id -> PrivateSubnet1
- izberg:environment -> sandbox
- izberg:service -> aurora-network-sandbox


## Subnet group


name:
- aurora-pg-subnet-group-sandbox

description:
- Subnet group of sandbox aurora database

VPC:
- internal (vpc-9388baf8)

Availability zones:
- A
- B
- C

Subnets:
- Chercher les subnet précédemment créer :) 


## Security group

Copier à partir du security group existant afin de mettre au propre.

name:
- aurora-pg-security-group-sandbox

description:
- Security group of sandbox aurora database. Allows access to Database from Core, Exporter and Analytics.

tags:
- Name: aurora-pg-security-group-sandbox
- izberg:environment -> sandbox
- izberg:service -> aurora-network-sandbox


**IMPORTANT:**
Security group Pluton:
- Ajouter la règle d'inbound de l'ancienne DB

Security group Old DB:
- Ajouter la règle d'inbound de la nouvelle DB Pluton



## Cluster Aurora + RDS

### Engine options:
- Aurora (PostgreSQL Compatible)

Version:
- 15.3 ou au dessus

Templates:
- Production (prendre Dev/test pour CI)

### Settings:

Identifiant:
- izberg-cluster-XXX
> XXX étant environment: ci, sandbox, prod. 


Credentials:
- postgres

> Cocher la case pour laisser AWS gérer le mot de passe via AWS Secret Manager

Clé d'encryption (KMS):
- Prend la clé KMS créer au dessus.


### Configuration du stockage

Prendre Aurora I/O optimized mais attention car cee type d'aurora est rentable que si l'I/O est ce qui coute le plus cher.


### Configuration de l'instance

DB instance class:
- Burtable classes 

> Ici choisir en fonction de l'environnement. Il faut estimer ce qui sera nécessaire.


Availability:
- Créer une replica


### Connectivity

Compute resource:
- Ne pas connecté à un EC2

Network:
- IPv4

Virtual private cloud:
- Prend le VPC déjà utilisé par l'ancien cluster.
> Attention, si le VPC n'est pas le bon le DB ne sera pas accessible via ECS
> De plus, le VPC n'est pas modifiable une fois que le cluster est créer


Subnet:
- Prendre celui qui a été créer au dessus

Public access:
- no
> IMPORTANT


VPC security group:
- Prendre celui qui a été créer au dessus

Security group:
- Prendre celui qui a été créer au dessus



### Database authentication

- Cocher IAM database authentication


### Monitoring

Activer performance insights
> IMPORTANT

- 7 jours de rétention

KMS:
- Prendre aws/rds


### Configuration Additional

Le nom:
- izberg_core_XXX
> XXX étant environment: ci, sandbox, prod. 


DB cluster parameter group:
- Utilisé celui créer plus haut


failover priority:
- reprendre la valeur de sandbox en sandbox et prod en prod


Backup:
- même chose, dépend si sandbox ou prod


Encryption:
- Oui, cocher la case


AWS KMS Key:
- prendre la clé KMS créer plus haut


Log Exports:
- oui, cocher la case


Maintenance:
- NON, décocher la case

Deletion protection:
- Cocher la case





## Setup de la DB

à partir d'ici le cluster existe mais il faut un moyen de si connecter.
pour cela le plus simple reste de faire un psql sur le nouveau cluster à partir d'api-debug


```bash
psql -h HOST -U USER -d DATABASENAME
```
> il faut utilisé les infos de login du secret générer par AWS, celui du compte admin


Ensuite il faut créer un user et lui assigner la DB
```postgresql
\c template1
CREATE USER izberg_sandbox WITH ENCRYPTED PASSWORD '****' CREATEDB;
ALTER ROLE izberg_sandbox WITH login;

-- GRANT izberg_sandbox TO postgres;

ALTER DATABASE izberg_core_sandbox OWNER TO izberg_sandbox; 
GRANT ALL PRIVILEGES ON DATABASE izberg_core_sandbox TO izberg_sandbox;
```
> peut être voir pour modifier le nom en fonction de CI/Sandbox/Prod.
> De même, attention avec le mot de passe, il faut le noté ;)


## Préparation api-debug

- Modifier le manifest
  - CI: Pour Ci on a modifier l'image du sidecar debug de la tache api
  - Sandbox: Pour sandbox on modifie l'image du manifest api-debug
  - Prod: Pour prod on modifie l'image du manifest api-debug 

> Pour se connecter au sidecar ci
> aws ecs execute-command  \
    --region eu-west-1 \
    --cluster ecs-exec-demo-cluster \
    --task 1234567890123456789 \
    --container debug \
    --command "/bin/bash" \
    --interactive
> Non nécessaire sur Sandbox et Prod car on utilise copilot


## Application du schema via django migrations initial

> ATTENTION: On doit freeze le code pour matcher ci/sandbox/prod sur les models et la DB

Sur api-debug:
- Mettre à jours les settings DATABASES (Update secret manager)
- Pour se connecter à la DB pour check: `./manage.py dbshell --database new_default`
- A faire avant pour check: `./manage.py migrate --database new_default --plan`
- A faire dans l'ordre à cause de migration de data dans la lib iprestrict :/
- `./manage.py migrate --database new_default iprestrict 0001` 
- `./manage.py migrate --database new_default iprestrict 0002 --fake`
- `./manage.py migrate --database new_default iprestrict`
- Lancer migration: `./manage.py migrate --database new_default`
- Editer le fichier `iceberg/settings_base/pre_overrides/090_database.py` pour remplacer la valeur de WEBHOOK_TRIGGERS_DB avec:
`WEBHOOK_TRIGGERS_DB = "webhooktriggers"`
- Lancer migration webhook: `./manage.py migrate --database new_webhooktriggers webhooktriggers`

> ATTENTION
> ATTENTION
> ATTENTION
> ATTENTION
> Maintenant il faut vider la DB des saloperies créer par django.
> Donc on truncate tout.
> Pour cela voir la commande qui génère l'ensemble des truncate pour totues les tables:

```postgresql
CREATE OR REPLACE FUNCTION truncate_tables() RETURNS void AS $$
DECLARE
    statements CURSOR FOR
        SELECT tablename FROM pg_tables WHERE schemaname = 'public';
BEGIN
    FOR stmt IN statements LOOP
        EXECUTE 'TRUNCATE TABLE ' || quote_ident(stmt.tablename) || ' CASCADE;';
    END LOOP;
END;
$$ LANGUAGE plpgsql;



SELECT truncate_tables();
```


## Logical Replication

### OLD CLUSTER (Postgres13) - PUBLICATION

CORE DB:
```postgresql
CREATE PUBLICATION pluton_pub FOR TABLE address_address,address_country,address_countrygroup,address_countrygroup_countries,address_state,address_stategroup,address_stategroup_states,address_zone,address_zone_countries,address_zone_states,api_access_accesstoken,api_access_accesstokenapp,assets_image,assets_imageassignment,attributes_customerattribute,attributes_customerattributevalue,attributes_customerattributevalue_selected_values,attributes_customerattributevaluechoice,attributes_localizedcustomerattributeinfo,attributes_localizedorderattributeinfo,attributes_localizedproductattributeinfo,attributes_orderattribute,attributes_orderattributevalue,attributes_productattribute,attributes_productattributegroup,attributes_productattributegroup_application_categories,attributes_productattributegroupassignment,attributes_productattributevalue,attributes_productattributevalue_selected_values,attributes_productattributevaluechoice,auth_group,auth_group_permissions,auth_permission,auth_user,auth_user_groups,auth_user_user_permissions,cart_notifications_cartnotification,cart_notifications_cartnotificationtransitionlog,categories_appcategorieslocalesconfig,categories_appcategoryassignment,categories_applicationcategory,categories_applicationcategory_parents,categories_applocalizedcategory,channels_algoliachanneloutput,channels_algoliachanneloutputtransitionlog,channels_cachestoragebackend,channels_dbloggingbackend,channels_dyndbbackend,channels_productchannel,channels_productchannellogevent,channels_productchanneltransitionlog,commissions_commissionline,commissions_itemcommissionrule,commissions_itemcommissionrule_destination_countries,commissions_itemcommissionrule_merchant_groups,commissions_itemcommissionrule_merchants,commissions_itemcommissionrule_origin_countries,commissions_itemcommissionrule_product_brands,commissions_itemcommissionrule_product_categories,commissions_itemcommissionrule_product_families,commissions_merchantcommissionrule,commissions_merchantcommissionrule_destination_countries,commissions_merchantcommissionrule_merchant_groups,commissions_merchantcommissionrule_merchants,commissions_merchantcommissionrule_origin_countries,commissions_merchantlicencerule,commissions_merchantlicencerule_merchant_groups,commissions_merchantlicencerule_merchants,companies_company,companies_companyaddress,companies_companyimage,constance_config,currencies_currency,deprecation_deprecatedfeature,django_admin_log,django_content_type,django_session,django_site,external_payment_externalpaymentlog,guardian_groupobjectpermission,guardian_userobjectpermission,hipay_tpp_hipayapicalllog,hipay_tpp_hipayappconfiguration,hipay_tpp_hipayappconfiguration_payment_methods,hipay_tpp_hipaybankaccount,hipay_tpp_hipaypaymentpagecss,hipay_tpp_hipaytransaction,hipay_tpp_hipaywalletaccount,ice_applications_appbalancetransaction,ice_applications_application,ice_applications_application_currencies,ice_applications_applicationbankaccount,ice_applications_applicationcommissionsettings,ice_applications_applicationconfiguration,ice_applications_applicationconfigurationtransitionlog,ice_applications_applicationmerchantpolicies,ice_applications_applicationpaymentsettings,ice_applications_applicationsetting,ice_applications_applicationsettingtransitionlog,ice_applications_applicationtransitionlog,ice_applications_applicationuserpermission,ice_applications_apppaymentbalance,ice_applications_apppaymentbalance_transactions,invoices_invoice,invoices_invoicelineitem,invoicing_creditnote,invoicing_creditnoteline,invoicing_customerinvoice,invoicing_customerinvoiceline,invoicing_customerinvoicetransitionlog,invoicing_externalcreditnotefile,invoicing_externalinvoicefile,iprestrict_ipgroup,iprestrict_iplocation,iprestrict_iprange,iprestrict_reloadrulesrequest,iprestrict_rule,kyc_kycdocumentpart,kyc_kycinformation,kyc_kycinformationtransitionlog,kyc_kyctag,kyc_kyctype,kyc_kyctypetransitionlog,mailer_action,mailer_actionexclusion,mailer_actionlog,mailer_configuration,mailer_emailattachment,mailer_template,mapper_action,mapper_analysis,mapper_analysistransitionlog,mapper_cleaner,mapper_configuration,mapper_configurationexport,mapper_configurationupload,mapper_feedfile,mapper_feedtemplate,mapper_feedtemplatetransitionlog,mapper_mapper,mapper_mapperdetail,mapper_matcher,mapper_matcherdetail,mapper_parser,mapper_transformationlog,mapper_transformationlogdetail,mapper_transformationlogtransitionlog,marketplace_permissions_permissionsummary,merchant_groups_merchantgroup,merchant_groups_merchantgrouptransitionlog,merchant_groups_merchanttomerchantgrouprelationship,mp_messages_message,mp_messages_messageattachment,mp_messages_messagelabel,mp_messages_messagetransitionlog,mp_messages_messagetype,orders_cart,orders_cart_discounts,orders_cartitem,orders_cartitem_discounts,orders_cartitemtransitionlog,orders_carttransitionlog,orders_merchantorder,orders_merchantorder_discounts,orders_merchantorderincidenttransitionlog,orders_merchantordertransitionlog,orders_order,orders_orderitem,orders_orderitem_discounts,orders_orderitemtransitionlog,orders_ordertransitionlog,orders_workflowmigration,otp_static_staticdevice,otp_static_statictoken,otp_totp_totpdevice,otp_yubikey_remoteyubikeydevice,otp_yubikey_validationservice,otp_yubikey_yubikeydevice,payment_orderitempaymentterm,payment_payment,payment_paymentbackendmigration,payment_paymentconsistencystatuslog,payment_paymentinconsistency,payment_paymentinconsistencytransitionlog,payment_paymentmethod,payment_paymentterm,payment_paymenttermline,payment_paymenttermtransitionlog,payment_paymenttransitionlog,products_brand,products_familyselector,products_localizedofferinfo,products_localizedproductinfo,products_localizedvariationinfo,products_product,products_productfamily,products_productfamily_selectors,products_productoffer,products_productoffertransitionlog,products_producttransitionlog,products_productvariation,promotions_cartitemexternaldiscountuse,promotions_discount,promotions_discount_eligible_customer_tax_groups,promotions_discount_eligible_users,promotions_discount_product_families,promotions_discount_shipping_providers,promotions_discounttransitionlog,promotions_discountuse,promotions_orderitemexternaldiscountuse,psp_gateway_pspgateway,psp_gateway_pspgatewaypaymentlog,returns_refund,returns_refund_return_requests,returns_refunditem,returns_refundtransitionlog,returns_returnrequest,returns_returnrequesttransitionlog,reviews_merchantreview,reviews_merchantreviewvote,shipping2_carrier,shipping2_carrierassignment,shipping2_carriertransitionlog,shipping2_cartshippingchoice,shipping2_cartshippingchoicegroup,shipping2_cartshippingchoicegroup_cart_items,shipping2_deliverydate,shipping2_offershipping,shipping2_ordershippingchoice,shipping2_ordershippingchoice_order_items,shipping2_parcel,shipping2_parcel_order_items,shipping2_parceltransitionlog,shipping2_shippingprovider,shipping2_shippingprovider_carrier,shipping2_shippingproviderassignment,shipping2_shippingproviderassignmenttransitionlog,shipping2_shippingprovidertransitionlog,shipping_packagetracking,shipping_packagetracking_order_items,slip_returnslip,slip_returnslip_return_requests,slip_returnslipattacheddocument,slip_returnslipattacheddocumenttransitionlog,slip_returnsliptransitionlog,stores_balancetransaction,stores_identitydocumentpart,stores_localizedmerchantattributeinfo,stores_merchant,stores_merchant_currencies,stores_merchantaddress,stores_merchantattribute,stores_merchantattributevalue,stores_merchantattributevalue_selected_values,stores_merchantattributevaluechoice,stores_merchantbankaccount,stores_merchantcommissionsettings,stores_merchantidentitydocument,stores_merchantimage,stores_merchanttransitionlog,stores_paymentbalance,stores_paymentbalance_transactions,tastypie_apiaccess,tastypie_apikey,tax_customertaxgroup,tax_producttaxgroup,tax_taxapplicationsettings,tax_taxmerchantsettings,tax_taxrate,tax_taxrate2,tax_taxrateassignment,tax_taxratetransitionlog,tax_taxrule,tax_taxzone,tax_taxzonetransitionlog,transactions_transaction,two_factor_phonedevice,uploader_filechunk,user_profile_appprofile,user_profile_userprofile,webhooks_webhook,zone_zone,zone_zonetransitionlog;

```
> la liste des tables est voulu. On ne prend pas les tables non pertinentes.

WEBHOOK DB:
```postgresql
CREATE PUBLICATION pluton_webhook_pub FOR TABLE webhooktriggers_webhooktrigger,webhooktriggers_webhooktriggerattempt;

```


### NEW CLUSTER PLUTON (Postgres15) - SUBSCRIPTION

CORE DB:
```postgresql
CREATE SUBSCRIPTION pluton_sub
CONNECTION '************************************/DATABASE_NAME'
PUBLICATION pluton_pub;


CREATE SUBSCRIPTION pluton_webhook_sub
CONNECTION '************************************/DATABASE_NAME'
PUBLICATION pluton_webhook_pub;
```



# LE JOUR J (yolo)

## Pingdom
Mettre en mode maintenance
-> Voir avec Vincent car la maintenance est derrière un paywall
https://my.pingdom.com/app/account/subscription

## Pager duty
On a override le planning de 2h à 8h avec Clément.


## Arrêts des services:

### Core et identity
On désactive les 16 services à la main.
En prennant soin de désactiver l'autoscaling et de modifier le desired count à 0.



### DB prod

On modifie le mot de passe de la DB pour bloquer l'accès à tout le monde sauf nous.
- al1s0n
- core_api_ro

> Règle: On ajoute un ! à la fin du mot de passe







### On laisse la replication logique terminé et on vérifie que la commande suivante affiche les bon wal:
```postgresql
SELECT slot_name, datoid, temporary, active, active_pid, catalog_xmin, restart_lsn, confirmed_flush_lsn, pg_current_wal_lsn() as current_lsn, wal_status FROM pg_replication_slots;

-- Meilleur manière de voir le lag
SELECT confirmed_flush_lsn, pg_current_wal_lsn(), (pg_current_wal_lsn() - confirmed_flush_lsn) AS lsn_distance
       FROM pg_catalog.pg_replication_slots;
```









### Stop replication:

**Pluton**:
```postgresql
DROP SUBSCRIPTION pluton_sub;
DROP SUBSCRIPTION pluton_webhook_sub;
```


**old cluster**:
Attention, le mot de passe a changé ! :) 
CORE DB:
```postgresql
DROP PUBLICATION pluton_pub;
```

WEBHOOK DB:
```postgresql
DROP PUBLICATION pluton_webhook_pub;
```


# On vérifie que plus rien ne traîne
CORE DB:
```postgresql
SELECT * FROM pg_replication_slots;
```

WEBHOOK DB:
```postgresql
SELECT * FROM pg_replication_slots;
```




### Fix les séquences:
**Récupération des séquences et appliqué**:
Attention, le mot de passe de la DB old prod a changé ! :) 

```bash
bash /.config/scripts/synchronize_sequences.sh
```

bonus webhook:
Voir sur la DB old prod les valeurs des séquences pour les remettres sur pluton
```bash
ALTER SEQUENCE webhooktriggers_webhooktrigger_id_seq RESTART WITH XXXXX;
ALTER SEQUENCE webhooktriggers_webhooktriggerattempt_id_seq RESTART WITH XXXXX;
```



### Security Group

**IMPORTANT:**
Security group Pluton:
- Retirer la règle d'inbound de l'ancienne DB

Security group Old DB:
- Retirer la règle d'inbound de la nouvelle DB Pluton



### Log - TRES IMPORTANT 
- Désactiver les logs postgres sur l'instance de prod



### Modification du secret Core

Il faut modifier les infos de DB master/replica

**CI**:
https://eu-west-1.console.aws.amazon.com/secretsmanager/secret?name=core%2Fci%2Fconfig&region=eu-west-1

**Sandbox**:
https://eu-west-1.console.aws.amazon.com/secretsmanager/secret?name=core%2Fsandbox%2Fconfig&region=eu-west-1

**Prod**:
https://eu-west-1.console.aws.amazon.com/secretsmanager/secret?name=core%2Fproduction%2Fconfig&region=eu-west-1


Modifier les secrets des lambda:

Exporter:
- exporter/prod/config
- exporter/production/config
- exporter/beta/config

Analytics:
- analytics/prod/config


**On peut encore rollback**


### Core: Deploy avec fast_deploy.sh 

Une fois déployé, on fake toutes les migrations django.

### Identity:

On relance le desired count à la main


**On ne peut plus rollback sans perte de données**



### On check que ca marche :) 




# Action index Sandbox

> NOTE : faire une doc sur la nomenclature des index et précisé qu'on met les index au niveau de la classe concrete (pas les classe Abstract/Base ...)

Méthodologie :
- connection quelque soit l'env:
  - ancienne DB: `python manage.py dbshell --database old_default`
  - nouvelle DB pluton: `python manage.py dbshell --database new_default`
- Rajouter le nouvel index dans le Meta.indexes de la classe pertinente
- Génèrer la migration dans la branche `env-indexes` avec `python manage.py makemigrations`
- Récupèrer le SQL à appliquer avec `python manage.py sqlmigrate app_name id_mig`
- Rajouter la commande jouée à la liste ci-dessous

On fait un reset de l'ensemble A LA FIN


Ajouts :
- CREATE INDEX "attributes_productattrval_e_id_et_status_language_attr_idx" ON "attributes_productattributevalue" ("entity_id", "entity_type_id", "status", "language", "attribute_id") WHERE "status" = 'visible';
- CREATE INDEX "shipping2_cart_shipping_choice_cart_id_idx" ON "shipping2_cartshippingchoice" ("cart_id");
- CREATE INDEX "shipping2_cart_shipping_choice_group_cart_id_idx" ON "shipping2_cartshippingchoicegroup" ("cart_id");
- CREATE INDEX "shipping2_cart_shipping_choice_group_selected_choice_idx" ON "shipping2_cartshippingchoicegroup" ("selected_choice_id");
- CREATE INDEX "products_localized_product_variation_info_product_variation_idx" ON "products_localizedvariationinfo" ("product_variation_id");  # Naming : id missing; Todo : fix it
- CREATE INDEX "products_product_parent_id_idx" ON "products_product" ("parent_id");
- CREATE INDEX "products_productvariation_product_offer_id_idx" ON "products_productvariation" ("product_offer_id");
- CREATE INDEX "promotions_discountuse_discount_id_idx" ON "promotions_discountuse" ("discount_id");
- CREATE INDEX "promotions_discountuse_cart_item_id_idx" ON "promotions_discountuse" ("cart_item_id");
- CREATE INDEX "promotions_discountuse_cart_id_idx" ON "promotions_discountuse" ("cart_id");
- CREATE INDEX "promotions_discountuse_merchant_id_idx" ON "promotions_discountuse" ("merchant_id");
- CREATE INDEX "promotions_discountuse__idx" ON "promotions_discountuse" ("order_id");
- CREATE INDEX "promotions_discountuse_order_id_idx" ON "promotions_discountuse" ("user_id");
- CREATE INDEX "promotions_discountuse_application_id_idx" ON "promotions_discountuse" ("application_id");
- CREATE INDEX "promotions_discountuse_cart_shipping_choice_id_idx" ON "promotions_discountuse" ("cart_shipping_choice_id");
- CREATE INDEX "promotions_discountuse_order_shipping_choice_id_idx" ON "promotions_discountuse" ("order_shipping_choice_id");
- CREATE INDEX "stores_merchanttransitionlog_merchant_id_idx" ON "stores_merchanttransitionlog" ("merchant_id");
- CREATE INDEX "stores_paymentbalance_merchant_id_currency_id_closed_on_idx" ON "stores_paymentbalance" ("merchant_id", "currency_id", "closed_on");
- CREATE INDEX "stores_paymentbalance_created_on_idx" ON "stores_paymentbalance" ("created_on");
- CREATE INDEX "stores_paymentbalance_merchant_id_idx" ON "stores_paymentbalance" ("merchant_id");
- CREATE INDEX "auth_user_username_idx" ON "auth_user" ("username" varchar_pattern_ops);
- CREATE INDEX "api_access_accesstokenapp_user_id_revoked_idx" ON "api_access_accesstokenapp" ("user_id", "revoked") WHERE NOT "revoked";
- CREATE INDEX "attributes_productattribute_application_id_status_key_idx" ON "attributes_productattribute" ("status", "key", "application_id");
- CREATE INDEX "promotions_discount_app_id_currency_id_start_date_end_date_idx" ON "promotions_discount" ("application_id", "currency_id", "start_date", "end_date");
- CREATE INDEX "attributes_productattributevaluechoice_attribute_id_idx" ON "attributes_productattributevaluechoice" ("attribute_id");
- CREATE INDEX "invoices_invoice_a_id__mb_id__v_idx" ON "invoices_invoice" ("application_id", "merchant_balance_id", "visibility") WHERE NOT ("visibility" = 'deleted');
- CREATE INDEX "invoices_invoice_a_id__ab_id__v_idx" ON "invoices_invoice" ("application_id", "application_balance_id", "visibility") WHERE NOT ("visibility" = 'deleted');
- CREATE INDEX "orders_cartitem_cart_id_idx" ON "orders_cartitem" ("cart_id");
- CREATE INDEX "mp_messages_message_sent_at_idx" ON "mp_messages_message" ("sent_at");
- CREATE INDEX "shipping2_cart_shipping_choice_group_id_idx" ON "shipping2_cartshippingchoice" ("group_id");
- CREATE INDEX "orders_order_cart_id_idx" ON "orders_order" ("cart_id");
- CREATE INDEX "orders_carts_session_idx" ON "orders_cart" ("session_id");
- CREATE INDEX "mapper_transformationlogdetail_transformation_log_id_idx" ON "mapper_transformationlogdetail" ("transformation_log_id");
- CREATE INDEX "mapper_mapperdetail_mapper_id_idx" ON "mapper_mapperdetail" ("mapper_id");
- CREATE INDEX "attributes_localizedproductattributeinfo_a_id__a_id__l_idx" ON "attributes_localizedproductattributeinfo" ("attribute_id", "application_id", "language");
- CREATE INDEX "returns_refund_user_id_idx" ON "returns_refund" ("user_id");
- CREATE INDEX "returns_returnrequest_user_id_idx" ON "returns_returnrequest" ("user_id");
- il en manque ?
- CREATE INDEX "mp_messages_message_merchant_order_id_idx" ON "mp_messages_message" ("merchant_order_id");
- CREATE INDEX "orders_carts_shipping_address__status_pidx" ON "orders_cart" ("shipping_address_id", "status") WHERE "status" IN ('20', '10');
- CREATE INDEX "payment_payment_customer_invoice_id_idx" ON "payment_payment" ("customer_invoice_id");
- CREATE INDEX CONCURRENTLY "cart_notifications_cartnotification_cart_id__status_pidx" ON "cart_notifications_cartnotification" ("cart_id", "status") WHERE "status" = 'initial';
- CREATE INDEX CONCURRENTLY "mp_messages_message_parent_msg_id__status_pidx" ON "mp_messages_message" ("parent_msg_id", "status") WHERE NOT ("status" = 'deleted');
- CREATE INDEX CONCURRENTLY "webhooktriggers_webhooktrigger_t_at__s__app_id_pidx" ON "webhooktriggers_webhooktrigger" ("triggered_at", "status", "application_id") WHERE NOT ("status" IN ('failed', 'aborted'));
- ALTER TABLE "shipping2_cartshippingchoicegroup_cart_items" ADD CONSTRAINT "shipping2_cartshippingchoicegroup_cart_i_c_s_c_g__c_id_uidx" UNIQUE ("cartshippingchoicegroup_id", "cartitem_id");
- CREATE INDEX "auth_user_email_oidx" ON "auth_user" ((UPPER("email") varchar_pattern_ops));
- CREATE INDEX "shipping2_cartshippingchoicegroup_cart_cartitem_id_idx" ON "shipping2_cartshippingchoicegroup_cart_items" ("cartitem_id");
- CREATE INDEX "shipping2_cart_shipping_choice_group_status_pidx" ON "shipping2_cartshippingchoicegroup" ("status") WHERE "status" = 'initial';
- CREATE INDEX "auth_user_application_idx" ON "auth_user" ("application_id");
- CREATE INDEX "promotions_discountuse_order_item_id_idx" ON "promotions_discountuse" ("order_item_id");
- CREATE INDEX "invoicing_customerinvoice_app_id__mo_id_status_pidx" ON "invoicing_customerinvoice" ("application_id", "merchant_order_id", "status") WHERE NOT ("status" = 'deleted');
- CREATE INDEX "mp_messages_message_parent_msg_id__idx" ON "mp_messages_message" ("parent_msg_id");
- CREATE INDEX "orders_orderitem_cart_item_id_idx" ON "orders_orderitem" ("cart_item_id");
- CREATE INDEX "promotions_discountuse_merchant_order_id__status_pidx" ON "promotions_discountuse" ("merchant_order_id", "status") WHERE NOT ("status" = 'cancelled');
- CREATE INDEX "payment_payment_order_id_idx" ON "payment_payment" ("order_id");
- CREATE INDEX "payment_payment_status_idx" ON "payment_payment" ("status");
- CREATE INDEX CONCURRENTLY "products_productvariation_status_idx" ON "products_productvariation" ("status") WHERE NOT ("status" = 'deleted');
- CREATE INDEX CONCURRENTLY "payment_paymenttransitionlog_payment_id_idx" ON "payment_paymenttransitionlog" ("payment_id");
- CREATE INDEX CONCURRENTLY "channels_productchannel_app_id__s__c_pidx" ON "channels_productchannel" ("application_id", "status", "currency_id") WHERE NOT ("status" IN ('paused', 'deleted'));
- CREATE INDEX CONCURRENTLY "attributes_orderattributevalue_attribute_id_idx" ON "attributes_orderattributevalue" ("attribute_id");
- CREATE INDEX CONCURRENTLY "attributes_orderattributevalue_merchant_order_id_idx" ON "attributes_orderattributevalue" ("merchant_order_id");
- CREATE INDEX CONCURRENTLY "cart_notifications_cartnotification_cart_item_id_idx" ON "cart_notifications_cartnotification" ("cart_item_id");
- CREATE INDEX "webhooktriggers_webhooktrigger_triggered_at_idx" ON "webhooktriggers_webhooktrigger" ("triggered_at");
- CREATE INDEX "shipping2_parcel_order_items_parcel_id__orderitem_id_idx" ON "shipping2_parcel_order_items" ("parcel_id", "orderitem_id");
- CREATE INDEX "shipping2_parcel_merchant_order_id_pidx" ON "shipping2_parcel" ("merchant_order_id", "status") WHERE NOT ("status" = 'deleted');
- CREATE INDEX "webhooktriggers_webhooktriggerattempt_webhook_trigger_id_idx" ON "webhooktriggers_webhooktriggerattempt" ("webhook_trigger_id");
- CREATE INDEX CONCURRENTLY "orders_orderitem_cart_id_idx" ON "orders_orderitem" ("cart_id");
- CREATE INDEX CONCURRENTLY "invoicing_customerinvoiceline_invoice_id__status_pidx" ON "invoicing_customerinvoiceline" ("invoice_id", "status") WHERE NOT ("status" = 'deleted');
- CREATE INDEX "products_productoffertransitionlog_offer_id_idx" ON "products_productoffertransitionlog" ("offer_id");
- CREATE INDEX CONCURRENTLY "orders_merchantorder_user_id_idx" ON "orders_merchantorder" ("user_id");
- CREATE INDEX CONCURRENTLY "products_productvariation_sku__status_pidx" ON "products_productvariation" ("sku", "status") WHERE NOT ("status" = 'deleted');
- CREATE INDEX CONCURRENTLY "cart_notifications_cartnotification_cart_id_idx" ON "cart_notifications_cartnotification" ("cart_id");
- CREATE INDEX CONCURRENTLY "mp_messages_message_application_id_idx" ON "mp_messages_message" ("application_id");
- CREATE INDEX "shipping_packagetracking_merchant_order_id_idx" ON "shipping_packagetracking" ("merchant_order_id");
- CREATE INDEX CONCURRENTLY "orders_cartitem_product_offer_id__status_pidx" ON "orders_cartitem" ("product_offer_id", "status") WHERE "status" = '10';
- CREATE INDEX CONCURRENTLY "returns_refund_status__customer_invoice_id_pidx" ON "returns_refund" ("status", "customer_invoice_id") WHERE "status" = 'manual_process';
- CREATE INDEX CONCURRENTLY "orders_cartitem_product_offer_variation_id__status_pidx" ON "orders_cartitem" ("product_offer_variation_id", "status") WHERE "status" = '10';
- CREATE INDEX CONCURRENTLY "shipping2_deliverydate_cart_item_id_idx" ON "shipping2_deliverydate" ("cart_item_id");
- CREATE INDEX CONCURRENTLY "products_productoffer_application_id__status_pidx" ON "products_productoffer" ("application_id", "status") WHERE NOT ("status" = 'deleted');
- CREATE INDEX CONCURRENTLY "auth_user_username_upper_oidx" ON "auth_user" (UPPER("username") varchar_pattern_ops);
- CREATE INDEX CONCURRENTLY "shipping2_ordershippingchoice_group_id_idx" ON "shipping2_ordershippingchoice" ("group_id");
- CREATE INDEX CONCURRENTLY "transactions_transaction_refund_id_idx" ON "transactions_transaction" ("refund_id");
- CREATE INDEX CONCURRENTLY "orders_cartitemtransitionlog_cart_item_id_idx" ON "orders_cartitemtransitionlog" ("cart_item_id");
- CREATE INDEX CONCURRENTLY "orders_merchantorder_application_id__status__last_modified_pidx" ON "orders_merchantorder" ("application_id", "status", "last_modified") WHERE NOT ("status" = '3000');
- CREATE INDEX CONCURRENTLY "hipay_tpp_hipaytransaction_transaction_reference_idx" ON "hipay_tpp_hipaytransaction" ("transaction_reference");
- CREATE INDEX "address_address_user_id_idx" ON "address_address" ("user_id");
- CREATE INDEX CONCURRENTLY "returns_refund_customer_invoice_id_idx" ON "returns_refund" ("customer_invoice_id");
- CREATE INDEX CONCURRENTLY "shipping2_offershipping_offer_id__status_pidx" ON "shipping2_offershipping" ("offer_id", "status") WHERE "status" = 'initial';
- CREATE INDEX CONCURRENTLY "stores_merchantimage_image_type_idx" ON "stores_merchantimage" ("image_type");
- CREATE INDEX CONCURRENTLY "mp_messages_message_root_msg_id__status_pidx" ON "mp_messages_message" ("root_msg_id", "status") WHERE NOT ("status" = 'deleted');
 
- CREATE INDEX CONCURRENTLY "products_product_gtin_idx" ON "products_product" ("gtin");
- CREATE INDEX CONCURRENTLY "stores_merchant_id__status_idx" ON "stores_merchant" ("id", "status") WHERE (NOT ("status" = '90') AND NOT ("status" = '30'));
- CREATE INDEX CONCURRENTLY "products_productoffer_a_id__status__m_id__id_pidx" ON "products_productoffer" ("application_id", "status", "merchant_id", "id") WHERE NOT ("status" = 'deleted');
- CREATE INDEX CONCURRENTLY "products_product_external_id_idx" ON "products_product" ("external_id") WHERE "external_id" IS NOT NULL;
- CREATE INDEX CONCURRENTLY "shipping2_deliverydate_order_item_id_idx" ON "shipping2_deliverydate" ("order_item_id");
- CREATE INDEX CONCURRENTLY "categories_applicationcategory_external_id_idx" ON "categories_applicationcategory" ("external_id") WHERE "external_id" IS NOT NULL;

- CREATE INDEX CONCURRENTLY "shipping2_parcel_last_modified_idx" ON "shipping2_parcel" ("last_modified");

- CREATE INDEX CONCURRENTLY "orders_carts_billing_address__status_pidx" ON "orders_cart" ("billing_address_id", "status") WHERE "status" IN ('20', '10');
- CREATE INDEX CONCURRENTLY "commissions_itemcommissionrule_max_t__min_t__p__s_pidx" ON "commissions_itemcommissionrule" ("max_threshold", "min_threshold", "priority", "status") WHERE "status" = 'active';
- CREATE INDEX CONCURRENTLY "commissions_itemcommissionrule_external_id_pidx" ON "commissions_itemcommissionrule" ("external_id") WHERE "external_id" IS NOT NULL;
- CREATE INDEX CONCURRENTLY "address_address_state_id_idx" ON "address_address" ("state_id");
- CREATE INDEX "commissions_merchantcommissionrule_application_id_idx" ON "commissions_merchantcommissionrule" ("application_id");
- CREATE INDEX "commissions_merchantcommissionrule_max_t__min_t__p__s_pidx" ON "commissions_merchantcommissionrule" ("max_threshold", "min_threshold", "priority", "status") WHERE "status" = 'active';
- CREATE INDEX "commissions_merchantcommissionrule_external_id_pidx" ON "commissions_merchantcommissionrule" ("external_id") WHERE "external_id" IS NOT NULL;
- CREATE INDEX CONCURRENTLY "invoicing_customerinvoice_issued_on_status_pidx" ON "invoicing_customerinvoice" ("issued_on", "status") WHERE NOT ("status" = 'deleted');
- CREATE INDEX CONCURRENTLY "transactions_transaction_transaction_type_idx" ON "transactions_transaction" ("transaction_type");
- CREATE INDEX CONCURRENTLY "transactions_transaction_settlement_notification_date_pidx" ON "transactions_transaction" ("settlement_notification_date") WHERE "settlement_notification_date" IS NOT NULL;

==== 



Retraits :
- DROP INDEX IF EXISTS "attributes_productattributevalue_status_idx";
- DROP INDEX shipping2_cartshippingch_cart_id_status_selected__0fc44567_idx;
- DROP INDEX IF EXISTS "shipping2_cartshippingch_cart_id_status_selected__0fc44567_idx";



# Actions prod

## hipaywalletaccount

iceberg=> DELETE FROM hipay_tpp_hipaywalletaccount WHERE id IN (76,78,144,230,77,387);
DELETE 6

## cartshippingchoicegroup

-- Relicat du passé car `default=1, validators=[MinValueValidator(1)],` selon le modèle
iceberg=> UPDATE shipping2_cartshippingchoicegroup_cart_items SET quantity = 1 WHERE quantity IS NULL;
UPDATE 4495

## ordershippingchoice

-- Relicat du passé car `default=1, validators=[MinValueValidator(1)],` selon le modèle
iceberg=> \copy (SELECT id FROM shipping2_ordershippingchoice_order_items WHERE quantity IS NULL) to 'shipping2_ordershippingchoice_order_items_quantity_is_null.csv' with csv
COPY 15320
iceberg=> UPDATE shipping2_ordershippingchoice_order_items SET quantity=1 WHERE quantity IS NULL;
UPDATE 15320

## mapper_action

```
        null=False,
        blank=False,
        default=None, <= ¯\_(ツ)_/¯
```

iceberg=> SELECT count(id) FROM mapper_action;
 count 
-------
  3625
(1 row)

iceberg=> SELECT count(id) FROM mapper_action WHERE configuration_id IS NULL;
 count 
-------
   100
(1 row)

2016-09-19 15:30 => on shoot

iceberg=> \copy (SELECT * FROM mapper_action WHERE configuration_id IS NULL) to 'mapper_action_configuration_id_is_null.csv' with csv
COPY 100
iceberg=> DELETE FROM mapper_action WHERE configuration_id IS NULL;
DELETE 100


## shipping2_parcel_order_items

iceberg=> SELECT count(*), max(last_modified) FROM shipping2_parcel_order_items JOIN shipping2_parcel ON shipping2_parcel.id = shipping2_parcel_order_items.parcel_id WHERE quantity IS NULL;
 count |              max              
-------+-------------------------------
 10831 | 2020-12-17 15:42:01.829995+00
(1 row)

iceberg=> SELECT DISTINCT status, max(last_modified) FROM shipping2_parcel_order_items JOIN shipping2_parcel ON shipping2_parcel.id = shipping2_parcel_order_items.parcel_id WHERE quantity IS NULL GROUP BY status;
   status   |              max              
------------+-------------------------------
 deleted    | 2020-12-17 15:42:01.829995+00
 initial    | 2018-01-24 09:11:35.179103+00
 in_transit | 2018-10-25 10:12:18.467059+00
 received   | 2017-12-06 13:35:44.004009+00
(4 rows)

iceberg=> \copy (SELECT * FROM shipping2_parcel_order_items WHERE quantity IS NULL) to 'shipping2_parcel_order_items_quantity_is_null.csv' with csv
COPY 10831

iceberg=> UPDATE shipping2_parcel_order_items SET quantity = 1 WHERE quantity IS NULL;
UPDATE 10831


SELECT query FROM pg_stat_activity WHERE application_name='core-api' AND query like '%SELECT %cart%';


