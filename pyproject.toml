[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'

# reference/status/order.py ignored as contains ascii art to explain workflow v1/v2 diff
# may be dropped once workflow v1 is dropped
exclude = 'migrations|\.mypy_cache|iceberg/core*|reference/status/order.py'
extend-exclude = '''
# A regex preceded with ^/ will apply only to files and directories
# in the root of the project.
^/foo.py  # exclude a file named foo.py in the root of the project (in addition to the defaults)
'''



[tool.vulture]
exclude = [
    # Maintenance scripts
    "/maintenance/", "maintenance.py", "/scripts/",
    # Mapper dynamically-loaded methods
    "mapper/generators/custom_faker.py",
    # Vendored lib
    "modules/django_measurement/",
    "modules/dbconnectionretrier/",
]
ignore_decorators = [
    "@current_app.task", "@shared_task",
    "@celery.signals.*",
    "@receiver", "@whook_receiver",
    "@xworkflows.*",
    "@api_view",
    "@register.filter",
]
ignore_names = [
    # IZBERG inheritance
    "sub_transaction", "subtransaction", "pre_hook_result",
    # Mocks
    "*mock*",
    # Django method overrides or code loaded by settings
    "from_field", "model_admin", "modeladmin", "change",
    "formsets", "view_func", "view_args",
    "handler500",
    # DRF method overrides
    "api_urls", "izberg_exception_handler",
    # Tastypie auto-generated methods
    "hydrate_*", "dehydrate_*",
]
make_whitelist = false
min_confidence = 70
paths = ["iceberg/"]
sort_by_size = false
verbose = false



[tool.isort]
profile = "black"
extend_skip="__init__.py"
skip_glob="**/migrations/*"



[tool.ruff]
select = ["E", "F", "PL"]
line-length = 88

extend-exclude = [
    ".git",
    "__pycache__",
    "iceberg/**/migrations/*.py",
    "iceberg/iceberg/settings_*/*.py",
    "iceberg/iceberg/settings_*.py",
    "iceberg/modules/django_measurement/*.py",
    ]

ignore = [
    # module level import not at top of file
    "E402",
    # comparison to True should be 'if cond is True:' or 'if cond:'
    "E712",
    # line too long
    "E501",
    # do not assign a lambda expression, use a def
    "E731",
    # class names should use CapWords convention
    "N801",
    # function name should be lowercase
    "N802",
    # argument name should be lowercase
    "N803",
    # variable in function should be lowercase
    "N806",
    # Too many arguments to function call
    "PLR0913",
    # Too many statements
    # Mostly an issue in tests
    "PLR0915",
    # Magic value used in comparison
    # Mostly http status 400, 500 ect
    "PLR2004",
    # Too many branches
    "PLR0912",
    # Too many return statements
    "PLR0911",
    # Variable overwritten by assignment target
    "PLW2901",
    # Consider using `elif` instead of `else` then `if` to remove one indentation level
    "PLR5501",
    # can be simplified to XXXX as an empty string is falsey
    # TODO: This one should be checked.
    "PLC1901",
    #  Using the global statement to update `XXXX` is discouraged
    "PLW0603",
]
