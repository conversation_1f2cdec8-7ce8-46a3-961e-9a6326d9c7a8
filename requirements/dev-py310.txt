#
# This file is autogenerated by pip-compile with Python 3.10
# by the following command:
#
#    pip-compile --output-file=dev-py310.txt specs/dev.in
#
--index-url https://kini-walid:<EMAIL>/simple/

algoliasearch==1.15.2
    # via -r specs/base.in
amqp==5.2.0
arabic-reshaper==3.0.0
argcomplete==3.2.1
asgiref==3.7.2
asn1crypto==1.5.1
aspectlib==2.0.0
    # via -r specs/base.in
asttokens==2.4.1
async-timeout==4.0.3
attrs==23.2.0
    # via
    #   jsonschema
    #   referencing
    #   wmctrl
awscli==1.32.61
    # via
    #   -r specs/aws.in
    #   -r specs/dev.in
bcrypt==4.1.2
    # via paramiko
beautifulsoup4==4.12.3
    # via
    #   -r specs/base.in
    #   html-sanitizer
billiard==*******
    # via celery
black==24.2.0
    # via -r specs/dev.in
boto3==1.34.61
    # via
    #   -r specs/aws.in
    #   izberg-core-lambda
    #   kappa
    #   kombu
    #   zappa
botocore==1.34.61
    # via
    #   -r specs/aws.in
    #   awscli
    #   boto3
    #   pynamodb
    #   s3transfer
bumpr==0.3.8
    # via -r specs/dev.in
cchardet==2.1.7
    # via -r specs/base.in
celery[sqs]==5.2.7
    # via
    #   -r specs/base.in
    #   django-celery-email
certifi==2024.2.2
    # via
    #   elastic-transport
    #   requests
    #   sentry-sdk
cffi==1.16.0
cfn-flip==1.3.0
chardet==5.2.0
charset-normalizer==3.3.2
click==8.1.7
click-didyoumean==0.3.0
click-plugins==1.1.1
click-repl==0.3.0
colander==2.0
    # via -r specs/base.in
colorama==0.4.4
contexttimer==0.3.3
    # via -r specs/base.in
coverage==4.5.4
    # via -r specs/tests.in
cryptography==42.0.5
    # via
    #   izberg-authenticator
    #   paramiko
    #   pyhanko
    #   pyhanko-certvalidator
cssselect2==0.7.0
decorator==5.1.1
    # via
    #   -r specs/base.in
    #   fabric
    #   ipdb
    #   ipython
defusedcsv==2.0.0
    # via -r specs/base.in
defusedxml==0.7.1
    # via -r specs/base.in
deprecated==1.2.14
    # via fabric
dicttoxml==1.7.16
    # via -r specs/base.in
django==4.0.10
    # via
    #   -r specs/django.in
    #   django-appconf
    #   django-cachalot
    #   django-celery-email
    #   django-cors-headers
    #   django-csp
    #   django-dynamic-db-router
    #   django-filter
    #   django-formtools
    #   django-guardian
    #   django-otp
    #   django-phonenumber-field
    #   django-picklefield
    #   django-pymemcache
    #   django-redis
    #   django-slowtests
    #   django-storages
    #   django-timezone-field
    #   django-two-factor-auth
    #   django-xworkflows
    #   djangorestframework
    #   jsonfield
    #   mock-django
django-appconf==1.0.6
django-cachalot==2.5.3
    # via -r specs/base.in
django-celery-email==3.0.0
    # via -r specs/base.in
django-cloneable==0.1.0
    # via -r specs/base.in
django-constance[database]==3.1.0
    # via -r specs/base.in
django-cors-headers==4.3.1
    # via -r specs/base.in
django-csp==3.8
    # via -r specs/base.in
django-dynamic-db-router==0.3.0
    # via -r specs/base.in
django-filter==23.5
    # via -r specs/base.in
django-formtools==2.5.1
    # via
    #   -r specs/base.in
    #   django-two-factor-auth
django-guardian==2.4.0
    # via -r specs/base.in
django-iprestrict-redux==1.9.1
    # via -r specs/base.in
django-json-widget==1.1.1
    # via -r specs/base.in
django-jsonschema==0.2.0
    # via -r specs/base.in
# django-kombu
    # via -r specs/base.in
django-nose==1.4.7
    # via -r specs/tests.in
django-object-actions==4.2.0
    # via -r specs/base.in
django-otp==1.3.0
    # via
    #   -r specs/base.in
    #   django-otp-yubikey
    #   django-two-factor-auth
django-otp-yubikey==1.0.1
    # via -r specs/base.in
django-phonenumber-field==7.3.0
django-picklefield==3.1
django-postgres-copy==2.7.4
    # via -r specs/base.in
django-pymemcache==1.0.0
    # via -r specs/base.in
django-redis==5.4.0
    # via -r specs/base.in
django-slowtests==1.1.1
    # via -r specs/tests.in
django-storages==1.14.2
    # via -r specs/base.in
django-tastypie==0.14.6
    # via -r specs/base.in
django-timezone-field==6.1.0
    # via -r specs/base.in
django-two-factor-auth==1.16.0
    # via -r specs/base.in
django-xworkflows==1.0.0
    # via -r specs/base.in
djangorestframework==3.14.0
    # via -r specs/base.in
docker==2.6.1
    # via -r specs/tests.in
docker-pycreds==0.4.0
docutils==0.16
durationpy==0.6
elastic-transport==8.13.0
elasticsearch==8.13.1
    # via
    #   -r specs/base.in
    #   elasticsearch-dsl
elasticsearch-dsl==8.13.1
    # via -r specs/base.in
exceptiongroup==1.2.0
execnet==2.0.2
executing==2.0.1
fabric==3.2.2
    # via -r specs/dev.in
factory-boy==3.3.0
    # via -r specs/base.in
faker==24.1.0
    # via factory-boy
fakeredis[lua]==2.21.3
    # via -r specs/tests.in
fancycompleter==0.9.1
fields==5.0.0
fluent-logger==0.3.4
    # via -r specs/base.in
freezegun==1.4.0
    # via -r specs/tests.in
future==1.0.0
    # via django-json-widget
gitdb==4.0.11
    # via
    #   -r specs/dev.in
    #   gitpython
gitpython==3.1.42
    # via -r specs/dev.in
hjson==3.1.0
html-sanitizer==1.9.1
    # via -r specs/base.in
html2text==2024.2.26
    # via -r specs/base.in
html5lib==1.1
    # via
    #   -r specs/base.in
    #   xhtml2pdf
idna==3.6
ijson==3.2.3
    # via -r specs/base.in
iniconfig==2.0.0
invoke==2.2.0
    # via
    #   -r specs/base.in
    #   fabric
ipdb==0.13.13
    # via -r specs/base.in
ipython==8.22.2
    # via ipdb
iso8601==2.1.0
isort==5.13.2
    # via -r specs/dev.in
izberg-authenticator==5.1.7
    # via -r specs/base.in
izberg-core-lambda==1.18.2
    # via -r specs/dev.in
izberg-correlation-id==0.0.7
    # via -r specs/base.in
izberg-data-logger==0.2.2
    # via -r specs/base.in
jedi==0.19.1
jmespath==1.0.1
jsonfield==3.1.0
    # via -r specs/base.in
jsonschema==4.21.1
    # via -r specs/base.in
jsonschema-specifications==2023.12.1
kappa==0.6.0
    # via zappa
kombu[sqs]==5.2.3
    # via
    #   -r specs/base.in
    #   celery
lupa==2.0
    # via fakeredis
lxml==5.1.0
    # via
    #   -r specs/base.in
    #   html-sanitizer
    #   svglib
m2crypto==0.41.0
    # via -r specs/base.in
markupsafe==2.1.5
    # via werkzeug
matplotlib-inline==0.1.6
measurement==3.2.2
    # via -r specs/base.in
mock==5.1.0
    # via
    #   -r specs/base.in
    #   -r specs/tests.in
    #   mock-django
mock-django==0.6.10
    # via -r specs/tests.in
mpmath==1.3.0
msgpack-python==0.5.6
mypy-extensions==1.0.0
names==0.3.0
    # via -r specs/tests.in
nose==1.3.7
oscrypto==1.3.0
packaging==23.2
parameterized==0.9.0
    # via -r specs/tests.in
paramiko==3.4.0
    # via
    #   -r specs/base.in
    #   fabric
parso==0.8.3
pathspec==0.12.1
pdbpp==0.10.3
    # via -r specs/base.in
pexpect==4.9.0
    # via ipython
phonenumbers==8.13.32
    # via -r specs/base.in
pillow==10.2.0
    # via
    #   -r specs/base.in
    #   reportlab
    #   xhtml2pdf
placebo==0.9.0
platformdirs==4.1.0
pluggy==1.4.0
prompt-toolkit==3.0.43
psycopg2==2.8.6
    # via -r specs/base.in
ptyprocess==0.7.0
pure-eval==0.2.2
pyasn1==0.5.1
pycairo==1.25.1
pycparser==2.21
pycryptodome==3.19.1
pycurl==7.44.1
    # via kombu
pyexecjs==1.5.1
    # via -r specs/base.in
pygments==2.17.2
    # via
    #   ipython
    #   pdbpp
pyhanko==0.23.0
    # via xhtml2pdf
pyhanko-certvalidator==0.26.3
    # via
    #   pyhanko
    #   xhtml2pdf
pyinstrument==4.6.2
    # via -r specs/dev.in
pyjwt==2.8.0
    # via
    #   -r specs/base.in
    #   izberg-authenticator
pymemcache==4.0.0
    # via
    #   -r specs/base.in
    #   django-pymemcache
pynacl==1.5.0
    # via paramiko
pynamodb==6.0.0
    # via -r specs/base.in
pypdf==4.1.0
    # via xhtml2pdf
pypng==0.20220715.0
pyrepl==0.9.0
    # via fancycompleter
pytest==8.1.1
    # via
    #   -r specs/tests.in
    #   pytest-django
    #   pytest-xdist
pytest-django==4.8.0
    # via -r specs/tests.in
pytest-xdist==3.5.0
    # via -r specs/tests.in
python-bidi==0.4.2
    # via xhtml2pdf
python-dateutil==2.9.0.post0
    # via
    #   -r specs/base.in
    #   botocore
    #   django-tastypie
    #   elasticsearch-dsl
    #   faker
    #   freezegun
    #   izberg-core-lambda
    #   izberg-data-logger
    #   zappa
python-magic==0.4.27
    # via -r specs/base.in
python-mimeparse==1.6.0
python-slugify==8.0.1
python-stdnum==1.19
    # via -r specs/base.in
pytz==2024.1
    # via
    #   -r specs/base.in
    #   celery
    #   djangorestframework
pyyaml==6.0.1
    # via
    #   awscli
    #   cfn-flip
    #   kappa
    #   pyhanko
    #   responses
    #   zappa
qrcode==7.4.2
    # via
    #   django-two-factor-auth
    #   pyhanko
redis==5.0.3
    # via
    #   -r specs/base.in
    #   django-redis
    #   fakeredis
referencing==0.33.0
    # via
    #   jsonschema
    #   jsonschema-specifications
reportlab==4.0.9
    # via
    #   svglib
    #   xhtml2pdf
requests[security]==2.31.0
    # via
    #   -r specs/base.in
    #   algoliasearch
    #   docker
    #   izberg-authenticator
    #   pyhanko
    #   pyhanko-certvalidator
    #   requests-file
    #   requests-mock
    #   responses
    #   zappa
requests-file==1.4.3
    # via -r specs/tests.in
requests-mock==1.11.0
    # via -r specs/tests.in
responses==0.23.1
    # via -r specs/tests.in
rpds-py==0.18.0
    # via
    #   jsonschema
    #   referencing
rsa==4.7.2
    # via awscli
ruff==0.3.2
    # via -r specs/dev.in
s3transfer==0.10.0
    # via
    #   awscli
    #   boto3
sentry-sdk==1.41.0
    # via -r specs/base.in
simplejson==3.19.2
    # via -r specs/base.in
six==1.16.0
    # via
    #   -r specs/base.in
    #   asttokens
    #   cfn-flip
    #   docker
    #   docker-pycreds
    #   html5lib
    #   izberg-core-lambda
    #   izberg-data-logger
    #   pyexecjs
    #   python-bidi
    #   python-dateutil
    #   requests-file
    #   requests-mock
smmap==5.0.1
sortedcontainers==2.4.0
soupsieve==2.5
sqlparse==0.4.4
stack-data==0.6.3
supervisor==4.2.5
    # via -r specs/base.in
svglib==1.5.1
sympy==1.12
tblib==3.0.0
    # via -r specs/tests.in
text-unidecode==1.3
tinycss2==1.2.1
toml==0.10.2
tomli==2.0.1
tqdm==4.66.1
traitlets==5.14.1
translationstring==1.4
    # via colander
troposphere==4.7.0
    # via zappa
types-pyyaml==6.0.12.20240311
    # via responses
typing-extensions==4.10.0
    # via
    #   asgiref
    #   black
    #   pynamodb
    #   qrcode
tzdata==2024.1
    # via -r specs/base.in
tzlocal==5.2
    # via pyhanko
unidecode==1.3.8
    # via -r specs/base.in
uritools==4.0.2
urllib3==2.0.7
    # via
    #   -r specs/base.in
    #   botocore
    #   elastic-transport
    #   kombu
    #   requests
    #   responses
    #   sentry-sdk
uwsgi==2.0.21
    # via -r specs/base.in
vine==5.1.0
    # via
    #   amqp
    #   celery
    #   kombu
vulture==2.11
    # via -r specs/dev.in
wcwidth==0.2.13
    # via prompt-toolkit
webencodings==0.5.1
websocket-client==1.7.0
Werkzeug==3.0.1
wmctrl==0.5
wrapt==1.16.0
    # via deprecated
xhtml2pdf==0.2.15
    # via -r specs/base.in
xlwt==1.3.0
    # via -r specs/base.in
xmltodict==0.13.0
    # via -r specs/base.in
xworkflows==1.1.0
    # via
    #   -r specs/base.in
    #   django-xworkflows
yubiotp==1.0.0
    # via django-otp-yubikey
zappa==0.58.0
