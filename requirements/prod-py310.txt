algoliasearch==1.15.2
    # via -r specs/base.in
amqp==5.2.0
    # via kombu
arabic-reshaper==3.0.0
    # via xhtml2pdf
argcomplete==3.2.3
    # via zappa
asgiref==3.7.2
    # via
    #   django
    #   django-cors-headers
asn1crypto==1.5.1
    # via
    #   izberg-authenticator
    #   oscrypto
    #   pyhanko
    #   pyhanko-certvalidator
aspectlib==2.0.0
    # via -r specs/base.in
asttokens==2.4.1
    # via stack-data
async-timeout==4.0.3
    # via redis
attrs==23.2.0
    # via
    #   jsonschema
    #   referencing
    #   wmctrl
awscli==1.32.61
    # via -r specs/aws.in
bcrypt==4.1.2
    # via paramiko
beautifulsoup4==4.12.3
    # via
    #   -r specs/base.in
    #   html-sanitizer
billiard==*******
    # via celery
boto3==1.34.61
    # via
    #   -r specs/aws.in
    #   izberg-core-lambda
    #   kappa
    #   zappa
botocore==1.34.61
    # via
    #   -r specs/aws.in
    #   awscli
    #   boto3
    #   pynamodb
    #   s3transfer
cchardet==2.1.7
    # via -r specs/base.in
celery[sqs]==5.2.7
    # via
    #   -r specs/base.in
    #   django-celery-email
certifi==2024.2.2
    # via
    #   requests
    #   sentry-sdk
cffi==1.16.0
    # via
    #   cryptography
    #   pynacl
cfn-flip==1.3.0
    # via troposphere
chardet==5.2.0
    # via reportlab
charset-normalizer==3.3.2
    # via requests
click==8.1.7
    # via
    #   celery
    #   cfn-flip
    #   click-didyoumean
    #   click-plugins
    #   click-repl
    #   kappa
    #   pyhanko
click-didyoumean==0.3.0
    # via celery
click-plugins==1.1.1
    # via celery
click-repl==0.3.0
    # via celery
colander==2.0
    # via -r specs/base.in
colorama==0.4.4
    # via awscli
contexttimer==0.3.3
    # via -r specs/base.in
cryptography==42.0.5
    # via
    #   izberg-authenticator
    #   paramiko
    #   pyhanko
    #   pyhanko-certvalidator
cssselect2==0.7.0
    # via svglib
decorator==5.1.1
    # via
    #   -r specs/base.in
    #   ipdb
    #   ipython
defusedcsv==2.0.0
    # via -r specs/base.in
defusedxml==0.7.1
    # via -r specs/base.in
dicttoxml==1.7.16
    # via -r specs/base.in
django==4.0.10
    # via
    #   -r specs/django.in
    #   django-appconf
    #   django-cachalot
    #   django-celery-email
    #   django-cors-headers
    #   django-csp
    #   django-dynamic-db-router
    #   django-filter
    #   django-formtools
    #   django-guardian
    #   django-otp
    #   django-phonenumber-field
    #   django-picklefield
    #   django-pymemcache
    #   django-redis
    #   django-storages
    #   django-timezone-field
    #   django-two-factor-auth
    #   django-xworkflows
    #   djangorestframework
    #   jsonfield
django-appconf==1.0.6
    # via django-celery-email
django-cachalot==2.5.3
    # via -r specs/base.in
django-celery-email==3.0.0
    # via -r specs/base.in
django-cloneable==0.1.0
    # via -r specs/base.in
django-constance[database]==3.1.0
    # via -r specs/base.in
django-cors-headers==4.3.1
    # via -r specs/base.in
django-csp==3.8
    # via -r specs/base.in
django-dynamic-db-router==0.3.0
    # via -r specs/base.in
django-filter==23.5
    # via -r specs/base.in
django-formtools==2.5.1
    # via
    #   -r specs/base.in
    #   django-two-factor-auth
django-guardian==2.4.0
    # via -r specs/base.in
django-iprestrict-redux==1.9.1
    # via -r specs/base.in
django-json-widget==1.1.1
    # via -r specs/base.in
django-jsonschema==0.2.0
    # via -r specs/base.in
django-kombu==0.9.4
    # via -r specs/base.in
django-object-actions==4.2.0
    # via -r specs/base.in
django-otp==1.3.0
    # via
    #   -r specs/base.in
    #   django-otp-yubikey
    #   django-two-factor-auth
django-otp-yubikey==1.0.1
    # via -r specs/base.in
django-phonenumber-field==7.3.0
    # via django-two-factor-auth
django-picklefield==3.1
    # via django-constance
django-postgres-copy==2.7.4
    # via -r specs/base.in
django-pymemcache==1.0.0
    # via -r specs/base.in
django-redis==5.4.0
    # via -r specs/base.in
django-storages==1.14.2
    # via -r specs/base.in
django-tastypie==0.14.6
    # via -r specs/base.in
django-timezone-field==6.1.0
    # via -r specs/base.in
django-two-factor-auth==1.16.0
    # via -r specs/base.in
django-xworkflows==1.0.0
    # via -r specs/base.in
djangorestframework==3.14.0
    # via -r specs/base.in
docutils==0.16
    # via awscli
durationpy==0.6
    # via zappa
elastic-transport==8.13.0

elasticsearch==8.13.1
    # via
    #   -r specs/base.in
    #   elasticsearch-dsl
elasticsearch-dsl==8.13.1

exceptiongroup==1.2.0
    # via ipython
executing==2.0.1
    # via stack-data
factory-boy==3.3.0
    # via -r specs/base.in
faker==24.1.0
    # via factory-boy
fancycompleter==0.9.1
    # via pdbpp
fields==5.0.0
    # via aspectlib
fluent-logger==0.3.4
    # via -r specs/base.in
future==1.0.0
    # via django-json-widget
hjson==3.1.0
    # via zappa
html-sanitizer==1.9.1
    # via -r specs/base.in
html2text==2024.2.26
    # via -r specs/base.in
html5lib==1.1
    # via
    #   -r specs/base.in
    #   xhtml2pdf
idna==3.6
    # via requests
ijson==3.2.3
    # via -r specs/base.in
invoke==2.2.0
    # via -r specs/base.in
ipdb==0.13.13
    # via -r specs/base.in
ipython==8.22.2
    # via ipdb
iso8601==2.1.0
    # via colander
izberg-authenticator==5.1.7
    # via -r specs/base.in
izberg-core-lambda==1.18.2
    # via -r specs/prod.in
izberg-correlation-id==0.0.7
    # via -r specs/base.in
izberg-data-logger==0.2.2
    # via -r specs/base.in
izberg-tastypie-paginator==0.4.3
    # via -r specs/prod.in
jedi==0.19.1
    # via ipython
jmespath==1.0.1
    # via
    #   boto3
    #   botocore
    #   zappa
jsonfield==3.1.0
    # via -r specs/base.in
jsonschema==4.21.1
    # via -r specs/base.in
jsonschema-specifications==2023.12.1
    # via jsonschema
kappa==0.6.0
    # via zappa
kombu==5.2.3
    # via
    #   -r specs/base.in
    #   celery
lxml==5.1.0
    # via
    #   -r specs/base.in
    #   html-sanitizer
    #   svglib
m2crypto==0.41.0
    # via -r specs/base.in
markupsafe==2.1.5
    # via werkzeug
matplotlib-inline==0.1.6
    # via ipython
measurement==3.2.2
    # via -r specs/base.in
mock==5.1.0
    # via -r specs/base.in
mpmath==1.3.0
    # via sympy
msgpack-python==0.5.6
    # via fluent-logger
oscrypto==1.3.0
    # via pyhanko-certvalidator
paramiko==3.4.0
    # via -r specs/base.in
parso==0.8.3
    # via jedi
pdbpp==0.10.3
    # via -r specs/base.in
pexpect==4.9.0
    # via ipython
phonenumbers==8.13.32
    # via -r specs/base.in
pillow==10.2.0
    # via
    #   -r specs/base.in
    #   reportlab
    #   xhtml2pdf
placebo==0.9.0
    # via
    #   izberg-core-lambda
    #   kappa
    #   zappa
prompt-toolkit==3.0.43
    # via
    #   click-repl
    #   ipython
psycopg2==2.8.6
    # via -r specs/base.in
ptyprocess==0.7.0
    # via pexpect
pure-eval==0.2.2
    # via stack-data
pyasn1==0.5.1
    # via rsa
pycparser==2.21
    # via cffi
pycryptodome==3.20.0
    # via yubiotp
pyexecjs==1.5.1
    # via -r specs/base.in
pygments==2.17.2
    # via
    #   ipython
    #   pdbpp
pyhanko==0.23.0
    # via xhtml2pdf
pyhanko-certvalidator==0.26.3
    # via
    #   pyhanko
    #   xhtml2pdf
pyjwt==2.8.0
    # via
    #   -r specs/base.in
    #   izberg-authenticator
pymemcache==4.0.0
    # via
    #   -r specs/base.in
    #   django-pymemcache
pynacl==1.5.0
    # via paramiko
pynamodb==6.0.0
    # via -r specs/base.in
pypdf==4.1.0
    # via xhtml2pdf
pypng==0.20220715.0
    # via qrcode
pyrepl==0.9.0
    # via fancycompleter
python-bidi==0.4.2
    # via xhtml2pdf
python-dateutil==2.9.0.post0
    # via
    #   -r specs/base.in
    #   botocore
    #   django-tastypie
    #   faker
    #   izberg-core-lambda
    #   izberg-data-logger
    #   zappa
python-magic==0.4.27
    # via -r specs/base.in
python-mimeparse==1.6.0
    # via django-tastypie
python-slugify==8.0.4
    # via zappa
python-stdnum==1.19
    # via -r specs/base.in
pytz==2024.1
    # via
    #   -r specs/base.in
    #   celery
    #   djangorestframework
pyyaml==6.0.1
    # via
    #   awscli
    #   cfn-flip
    #   kappa
    #   pyhanko
    #   zappa
qrcode==7.4.2
    # via
    #   django-two-factor-auth
    #   pyhanko
redis==5.0.3
    # via
    #   -r specs/base.in
    #   django-redis
referencing==0.33.0
    # via
    #   jsonschema
    #   jsonschema-specifications
reportlab==4.0.9
    # via
    #   svglib
    #   xhtml2pdf
requests[security]==2.31.0
    # via
    #   -r specs/base.in
    #   algoliasearch
    #   izberg-authenticator
    #   pyhanko
    #   pyhanko-certvalidator
    #   zappa
rpds-py==0.18.0
    # via
    #   jsonschema
    #   referencing
rsa==4.7.2
    # via awscli
s3transfer==0.10.0
    # via
    #   awscli
    #   boto3
sentry-sdk==1.41.0
    # via -r specs/base.in
simplejson==3.19.2
    # via -r specs/base.in
six==1.16.0
    # via
    #   -r specs/base.in
    #   asttokens
    #   cfn-flip
    #   html5lib
    #   izberg-core-lambda
    #   izberg-data-logger
    #   pyexecjs
    #   python-bidi
    #   python-dateutil
soupsieve==2.5
    # via beautifulsoup4
sqlparse==0.4.4
    # via django
stack-data==0.6.3
    # via ipython
supervisor==4.2.5
    # via -r specs/base.in
svglib==1.5.1
    # via xhtml2pdf
sympy==1.12
    # via measurement
text-unidecode==1.3
    # via python-slugify
tinycss2==1.2.1
    # via
    #   cssselect2
    #   svglib
toml==0.10.2
    # via zappa
tomli==2.0.1
    # via ipdb
tqdm==4.66.2
    # via zappa
traitlets==5.14.2
    # via
    #   ipython
    #   matplotlib-inline
translationstring==1.4
    # via colander
troposphere==4.7.0
    # via zappa
typing-extensions==4.10.0
    # via
    #   asgiref
    #   pynamodb
    #   qrcode
tzdata==2024.1
    # via -r specs/base.in
tzlocal==5.2
    # via pyhanko
unidecode==1.3.8
    # via -r specs/base.in
uritools==4.0.2
    # via pyhanko-certvalidator
urllib3==2.0.7
    # via
    #   -r specs/base.in
    #   botocore
    #   requests
    #   sentry-sdk
uwsgi==2.0.21
    # via -r specs/base.in
vine==5.1.0
    # via
    #   amqp
    #   celery
    #   kombu
wcwidth==0.2.13
    # via prompt-toolkit
webencodings==0.5.1
    # via
    #   cssselect2
    #   html5lib
    #   tinycss2
werkzeug==3.0.1
    # via zappa
wheel==0.43.0
    # via zappa
wmctrl==0.5
    # via pdbpp
xhtml2pdf==0.2.15
    # via -r specs/base.in
xlwt==1.3.0
    # via -r specs/base.in
xmltodict==0.13.0
    # via -r specs/base.in
xworkflows==1.1.0
    # via
    #   -r specs/base.in
    #   django-xworkflows
yubiotp==1.0.0
    # via django-otp-yubikey
zappa==0.58.0
    # via izberg-core-lambda

# The following packages are considered to be unsafe in a requirements file:
# pip
# setuptools
