Pillow

lxml
defusedxml
paramiko  # mapper sftp

python-dateutil
pymemcache
django-pymemcache


xworkflows
django_xworkflows

####
# Payment
####
M2Crypto<1

celery[sqs]>=5.2,<5.3
django-celery-email

measurement
fluent-logger==0.3.4
django-storages

# kombu.transport.SQS.Channel._create_queue is monkeypatched in settings to auto
# tag created queues.
# Make sure method does not change before upgrading kombu package.
kombu==5.2.3
django-kombu

sentry-sdk

pytz

html2text

django-object-actions
django-json-widget  # better json fields display in admin

python-stdnum
six
simplejson

Unidecode

# Phone numbers parsing API used in addresses to generate international phone number
phonenumbers

contexttimer==0.3.3

django-otp
django-formtools
django-two-factor-auth
django-otp-yubikey

redis
django-redis

django-constance[database]

django-cloneable
html-sanitizer==1.9.1

django-guardian
django-timezone-field

psycopg2>=2.8,<2.9
uwsgi<2.0.22  # 502 on /chunk https://github.com/unbit/uwsgi/pull/2311 & https://uwsgi-docs.readthedocs.io/en/latest/Changelog-2.0.22.html#changes

# debuggers
ipdb
pdbpp

colander>=1.4

html5lib

requests>=2.31.0  # older versions have CVEs
algoliasearch==1.15.2
elasticsearch
elasticsearch-dsl

jsonschema
urllib3
cchardet
pyexecjs
beautifulsoup4
xmltodict
pynamodb
dicttoxml
ijson

xhtml2pdf

defusedcsv   # Protects against formula injections

django-jsonschema
django-tastypie
djangorestframework
django-cors-headers
django-filter==23.5
django-iprestrict-redux

-r django.in
-r aws.in

jsonfield


invoke
mock
factory-boy
python-magic

xlwt<2

pyjwt>=2.4

django-postgres-copy
izberg-authenticator>=5.1.1,<6.0.0

supervisor>=4,<5
django-dynamic-db-router


aspectlib
django-csp
decorator
django-cachalot==2.5.3

izberg-data-logger
izberg-correlation-id==0.0.7

tzdata
