import logging
import sys

from invoke import task

logger = logging.getLogger(__name__)

BASE_COMMAND = (
    "pip-compile --extra-index-url $PYPI_DOWNLOAD_URL --no-header --max-rounds 30 --resolver=legacy "
    "--no-emit-trusted-host --no-emit-index-url -U -v -o"
)


@task
def build_dev(ctx):
    """Build dev requirements"""
    print("*----------------------------------------------------------*")
    print(
        f"[Python {sys.version.split()[0]}] Building flatten requirement file"
        f" for dev environment."
    )
    print("*----------------------------------------------------------*")
    ctx.run(f"{BASE_COMMAND} dev-py3{sys.version_info.minor}.txt specs/dev.in")


@task
def build_prod(ctx):
    print("*----------------------------------------------------------*")
    print(
        f"[Python {sys.version.split()[0]}] Building flatten requirement file"
        f" for prod & sandbox environments."
    )
    print("*----------------------------------------------------------*")
    ctx.run(f"{BASE_COMMAND} prod-py3{sys.version_info.minor}.txt specs/prod.in")


@task
def build_test(ctx):
    print("*----------------------------------------------------------*")
    print(
        f"[Python {sys.version.split()[0]}] Building tests requirement file "
        "for travis."
    )
    print("*----------------------------------------------------------*")
    ctx.run(f"{BASE_COMMAND} tests-py3{sys.version_info.minor}.txt specs/tests.in")


@task
def build_all(ctx):
    """Build requirements for all environments"""
    build_dev(ctx)
    build_prod(ctx)
    build_test(ctx)
