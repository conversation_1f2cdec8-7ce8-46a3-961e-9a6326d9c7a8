#!/bin/bash

# IZBERG Core Local Development Startup Script
# This script sets up and starts the complete local development environment

set -e

echo "🚀 Starting IZBERG Core Local Development Environment"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

print_success "Docker is running"

# Check if required files exist
if [ ! -f "docker-compose.local.yml" ]; then
    print_error "docker-compose.local.yml not found. Please ensure you're in the project root directory."
    exit 1
fi

if [ ! -f ".env.local" ]; then
    print_error ".env.local not found. Please ensure you have the local environment configuration."
    exit 1
fi

print_success "Configuration files found"

# Set environment variables
export COMPOSE_FILE=docker-compose.local.yml
export COMPOSE_PROJECT_NAME=izberg-local

print_status "Using local environment configuration..."

# Copy local environment file
cp .env.local .env

# Create media directory for local file storage
print_status "Creating local media directory..."
mkdir -p ./media
chmod 755 ./media

# Stop any existing containers
print_status "Stopping any existing containers..."
docker compose down --remove-orphans

# Pull latest images (requires AWS credentials - one time only)
if [ "$1" = "--pull-images" ]; then
    print_status "Pulling latest Docker images from ECR..."
    print_warning "This requires AWS credentials and internet connection"
    
    # Check if AWS credentials are configured
    if ! aws sts get-caller-identity > /dev/null 2>&1; then
        print_error "AWS credentials not configured. Please run 'aws configure' first."
        exit 1
    fi
    
    # Login to ECR
    aws ecr get-login-password --region eu-west-1 | docker login --username AWS --password-stdin 171946876898.dkr.ecr.eu-west-1.amazonaws.com
    
    # Pull images
    docker compose pull
    print_success "Images pulled successfully"
fi

# Start infrastructure services first
print_status "Starting infrastructure services (PostgreSQL, Redis, DynamoDB, MinIO)..."
docker compose up -d postgres redis dynamodb minio

# Wait for services to be ready
print_status "Waiting for infrastructure services to be ready..."
sleep 10

# Check if PostgreSQL is ready
print_status "Checking PostgreSQL connection..."
until docker compose exec postgres pg_isready -U iceberg > /dev/null 2>&1; do
    print_status "Waiting for PostgreSQL..."
    sleep 2
done
print_success "PostgreSQL is ready"

# Check if Redis is ready
print_status "Checking Redis connection..."
until docker compose exec redis redis-cli ping > /dev/null 2>&1; do
    print_status "Waiting for Redis..."
    sleep 2
done
print_success "Redis is ready"

# Setup MinIO buckets
print_status "Setting up MinIO buckets..."
docker compose up -d minio-setup
sleep 5
print_success "MinIO buckets created"

# Start API services
print_status "Starting API services..."
docker compose up -d api.local.domains.izbgtech.com api.local.izberg.me

# Wait for Identity and Domains APIs to be ready
print_status "Waiting for Identity and Domains APIs..."
sleep 15

# Start Core API
print_status "Starting Core API..."
docker compose up -d api.local.core.izbgtech.com

# Wait for Core API to be ready
print_status "Waiting for Core API to be ready..."
sleep 20

# Check service health
print_status "Checking service health..."

# Check Core API
if curl -f http://api.local.core.izbgtech.com:8080/health/ > /dev/null 2>&1; then
    print_success "Core API is healthy"
else
    print_warning "Core API health check failed - it may still be starting up"
fi

# Check Identity API
if curl -f http://api.local.izberg.me:8081/health/ > /dev/null 2>&1; then
    print_success "Identity API is healthy"
else
    print_warning "Identity API health check failed - it may still be starting up"
fi

# Check Domains API
if curl -f http://api.local.domains.izbgtech.com:8082/health/ > /dev/null 2>&1; then
    print_success "Domains API is healthy"
else
    print_warning "Domains API health check failed - it may still be starting up"
fi

# Display service status
echo ""
echo "🎉 Local Development Environment Started!"
echo "========================================"
echo ""
echo "📊 Service Status:"
echo "  • PostgreSQL:    http://localhost:5432"
echo "  • Redis:         http://localhost:6379"
echo "  • DynamoDB:      http://localhost:8008"
echo "  • MinIO (S3):    http://localhost:9000 (admin: minioadmin/minioadmin123)"
echo "  • MinIO Console: http://localhost:9001"
echo ""
echo "🌐 API Endpoints:"
echo "  • Core API:      http://api.local.core.izbgtech.com:8080"
echo "  • Identity API:  http://api.local.izberg.me:8081"
echo "  • Domains API:   http://api.local.domains.izbgtech.com:8082"
echo ""
echo "🔧 Admin Interfaces:"
echo "  • Django Admin:  http://api.local.core.izbgtech.com:8080/_a_d_m_i_n_/"
echo "  • API Docs:      http://api.local.core.izbgtech.com:8080/api/docs/"
echo ""
echo "📁 Local Storage:"
echo "  • Media files:   ./media/"
echo "  • Database:      Docker volume (postgres_data)"
echo ""
echo "🔍 Useful Commands:"
echo "  • View logs:     docker compose logs -f"
echo "  • Stop all:      docker compose down"
echo "  • Restart:       docker compose restart"
echo "  • Shell access:  docker compose exec api.local.core.izbgtech.com bash"
echo ""
echo "✅ All services are running locally - no AWS dependencies!"

# Optional: Run database migrations and setup
if [ "$1" = "--setup-data" ] || [ "$2" = "--setup-data" ]; then
    echo ""
    print_status "Setting up database and initial data..."
    
    # Wait a bit more for the API to be fully ready
    sleep 10
    
    # Run migrations
    print_status "Running database migrations..."
    docker compose exec api.local.core.izbgtech.com python manage.py migrate
    
    # Create initial data
    print_status "Creating initial data..."
    docker compose exec api.local.core.izbgtech.com invoke setup --applications 1 --merchants 3 --orders 1
    
    print_success "Database setup completed!"
fi

echo ""
print_success "🎯 Ready for development! All services are running locally."
