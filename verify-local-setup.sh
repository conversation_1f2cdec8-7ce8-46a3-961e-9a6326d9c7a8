#!/bin/bash

# IZBERG Core Local Development Verification Script
# This script verifies that the local development environment is running correctly
# and that no AWS services are being accessed

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[CHECK]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

echo "🔍 IZBERG Core Local Development Environment Verification"
echo "======================================================="
echo ""

# Check if containers are running
print_status "Checking if containers are running..."

CONTAINERS=(
    "postgres"
    "redis" 
    "dynamodb"
    "minio"
    "api.local.core.izbgtech.com"
    "api.local.izberg.me"
    "api.local.domains.izbgtech.com"
)

ALL_RUNNING=true
for container in "${CONTAINERS[@]}"; do
    if docker compose ps | grep -q "$container.*Up"; then
        print_success "Container $container is running"
    else
        print_error "Container $container is not running"
        ALL_RUNNING=false
    fi
done

if [ "$ALL_RUNNING" = false ]; then
    print_error "Some containers are not running. Please run './start-local-dev.sh' first."
    exit 1
fi

echo ""

# Check service connectivity
print_status "Checking service connectivity..."

# PostgreSQL
if docker compose exec postgres pg_isready -U iceberg > /dev/null 2>&1; then
    print_success "PostgreSQL is accessible"
else
    print_error "PostgreSQL is not accessible"
fi

# Redis
if docker compose exec redis redis-cli ping > /dev/null 2>&1; then
    print_success "Redis is accessible"
else
    print_error "Redis is not accessible"
fi

# DynamoDB Local
if curl -s http://localhost:8008/ > /dev/null 2>&1; then
    print_success "DynamoDB Local is accessible"
else
    print_error "DynamoDB Local is not accessible"
fi

# MinIO
if curl -s http://localhost:9000/minio/health/live > /dev/null 2>&1; then
    print_success "MinIO is accessible"
else
    print_error "MinIO is not accessible"
fi

echo ""

# Check API endpoints
print_status "Checking API endpoints..."

# Core API
if curl -s -o /dev/null -w "%{http_code}" http://api.local.core.izbgtech.com:8080/api/v1/ | grep -q "200\|401\|403"; then
    print_success "Core API is responding"
else
    print_warning "Core API is not responding (may still be starting up)"
fi

# Identity API
if curl -s -o /dev/null -w "%{http_code}" http://api.local.izberg.me:8081/api/v1/ | grep -q "200\|401\|403"; then
    print_success "Identity API is responding"
else
    print_warning "Identity API is not responding (may still be starting up)"
fi

# Domains API
if curl -s -o /dev/null -w "%{http_code}" http://api.local.domains.izbgtech.com:8082/api/v1/ | grep -q "200\|401\|403"; then
    print_success "Domains API is responding"
else
    print_warning "Domains API is not responding (may still be starting up)"
fi

echo ""

# Check local file storage
print_status "Checking local file storage..."

if [ -d "./media" ]; then
    print_success "Local media directory exists"
    if [ -w "./media" ]; then
        print_success "Local media directory is writable"
    else
        print_error "Local media directory is not writable"
    fi
else
    print_error "Local media directory does not exist"
fi

echo ""

# Check environment configuration
print_status "Checking environment configuration..."

if [ -f ".env" ]; then
    if grep -q "DJANGO_SETTINGS_MODULE=iceberg.settings_local_dev" .env; then
        print_success "Using local development settings"
    else
        print_warning "Not using local development settings"
    fi
    
    if grep -q "USE_SQS_QUEUE=false" .env; then
        print_success "SQS queue disabled"
    else
        print_warning "SQS queue may be enabled"
    fi
    
    if grep -q "DEFAULT_FILE_STORAGE=django.core.files.storage.FileSystemStorage" .env; then
        print_success "Using local file storage"
    else
        print_warning "May be using S3 file storage"
    fi
    
    if grep -q "DYNAMODB_ENDPOINT=http://dynamodb:8000" .env; then
        print_success "Using local DynamoDB"
    else
        print_warning "May be using AWS DynamoDB"
    fi
else
    print_error "Environment file (.env) not found"
fi

echo ""

# Check for AWS service calls (basic check)
print_status "Checking for potential AWS service calls..."

# Check if any containers are trying to connect to AWS
AWS_CALLS=false

# Check logs for AWS-related errors or connections
if docker compose logs --tail=50 2>/dev/null | grep -i "amazonaws\|aws\|s3\|sqs\|sns" | grep -v "fake\|local\|dynamodb:8000\|minio" > /dev/null 2>&1; then
    print_warning "Found potential AWS service calls in logs"
    AWS_CALLS=true
else
    print_success "No obvious AWS service calls detected"
fi

echo ""

# Test basic functionality
print_status "Testing basic functionality..."

# Test file upload to local storage
TEST_FILE="/tmp/test_upload.txt"
echo "test content" > "$TEST_FILE"

# Test database connection through API (if API is ready)
if curl -s http://api.local.core.izbgtech.com:8080/api/v1/ > /dev/null 2>&1; then
    print_success "API database connection working"
else
    print_warning "Cannot test API database connection (API may not be ready)"
fi

# Clean up test file
rm -f "$TEST_FILE"

echo ""

# Network isolation test
print_status "Testing network isolation..."

# Check if containers can reach external AWS services (they shouldn't in local mode)
if docker compose exec api.local.core.izbgtech.com ping -c 1 -W 2 s3.amazonaws.com > /dev/null 2>&1; then
    print_warning "Container can reach external AWS services (internet access available)"
else
    print_success "Container cannot reach external AWS services (good for isolation)"
fi

echo ""

# Summary
echo "📋 Verification Summary"
echo "======================"

if [ "$ALL_RUNNING" = true ]; then
    print_success "✅ All containers are running"
else
    print_error "❌ Some containers are not running"
fi

if [ -f ".env" ] && grep -q "settings_local_dev" .env; then
    print_success "✅ Local development configuration active"
else
    print_error "❌ Local development configuration not active"
fi

if [ -d "./media" ] && [ -w "./media" ]; then
    print_success "✅ Local file storage configured"
else
    print_error "❌ Local file storage not properly configured"
fi

echo ""
echo "🎯 Quick Access URLs:"
echo "  • Core API:      http://api.local.core.izbgtech.com:8080"
echo "  • Django Admin:  http://api.local.core.izbgtech.com:8080/_a_d_m_i_n_/"
echo "  • API Docs:      http://api.local.core.izbgtech.com:8080/api/docs/"
echo "  • MinIO Console: http://localhost:9001 (minioadmin/minioadmin123)"
echo ""
echo "🔧 Useful Commands:"
echo "  • View logs:     docker compose logs -f [service_name]"
echo "  • Django shell:  docker compose exec api.local.core.izbgtech.com python manage.py shell"
echo "  • Database shell: docker compose exec postgres psql -U iceberg -d izberg_core"
echo "  • Redis CLI:     docker compose exec redis redis-cli"
echo ""

if [ "$ALL_RUNNING" = true ] && [ -f ".env" ]; then
    print_success "🎉 Local development environment is ready!"
    echo "   You can now develop completely offline (after initial image pull)."
else
    print_error "❌ Local development environment has issues."
    echo "   Please check the errors above and run './start-local-dev.sh' if needed."
fi
